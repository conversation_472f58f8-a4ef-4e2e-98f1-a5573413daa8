<?php

namespace App\Jobs;

use App\Models\AssessmentResponse;
use App\Models\AssessmentAnalysis;
use App\Services\AiAnalysisService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class GenerateAiAnalysisJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $responseId;

    /**
     * 任务最大尝试次数
     */
    public $tries = 3;

    /**
     * 任务超时时间（秒）
     */
    public $timeout = 120;

    /**
     * 创建新的任务实例
     */
    public function __construct(int $responseId)
    {
        $this->responseId = $responseId;
    }

    /**
     * 执行任务
     */
    public function handle(AiAnalysisService $aiAnalysisService): void
    {
        try {
            // 获取测评回答记录
            $response = AssessmentResponse::with(['questionnaire', 'analysis'])
                ->find($this->responseId);

            if (!$response) {
                Log::warning("测评回答记录不存在: {$this->responseId}");
                return;
            }

            if (!$response->analysis) {
                Log::warning("测评分析记录不存在: {$this->responseId}");
                return;
            }

            // 检查是否已有AI分析
            if ($response->analysis->ai_analysis) {
                Log::info("AI分析已存在，跳过生成: {$this->responseId}");
                return;
            }

            // 检查AI分析服务是否可用
            if (!$aiAnalysisService->isAvailable()) {
                Log::warning("AI分析服务不可用");
                return;
            }

            // 生成AI分析
            $aiAnalysis = $aiAnalysisService->generateAnalysis($response);

            if ($aiAnalysis) {
                // 更新分析记录
                $response->analysis->update([
                    'ai_analysis' => $aiAnalysis
                ]);

                Log::info("AI分析生成成功: {$this->responseId}");
            } else {
                Log::warning("AI分析生成失败，返回空结果: {$this->responseId}");
            }

        } catch (\Exception $e) {
            Log::error("AI分析任务执行失败: {$e->getMessage()}", [
                'response_id' => $this->responseId,
                'error' => $e->getTraceAsString()
            ]);

            // 重新抛出异常，让队列系统处理重试
            throw $e;
        }
    }

    /**
     * 任务失败时的处理
     */
    public function failed(\Throwable $exception): void
    {
        Log::error("AI分析任务最终失败: {$exception->getMessage()}", [
            'response_id' => $this->responseId,
            'error' => $exception->getTraceAsString()
        ]);

        // 可以在这里发送通知给管理员
        // 或者标记该记录的AI分析状态为失败
    }
} 