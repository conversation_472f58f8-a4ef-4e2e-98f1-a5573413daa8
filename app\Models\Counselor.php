<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Dcat\Admin\Traits\HasDateTimeFormatter;

class Counselor extends Model
{
    use HasFactory, SoftDeletes, HasDateTimeFormatter;

    protected $fillable = [
        'name',
        'avatar',
        'gender',
        'title',
        'phone',
        'email',
        'password',
        'introduction',
        'expertise',
        'experience',
        'education',
        'price',
        'support_text',
        'support_voice',
        'support_video',
        'is_active',
        'sort_order'
    ];
    
    protected $hidden = [
        'password',
    ];

    // 性别常量
    const GENDER_UNKNOWN = 0;
    const GENDER_MALE = 1;
    const GENDER_FEMALE = 2;

    // 性别映射
    public static function getGenderMap()
    {
        return [
            self::GENDER_UNKNOWN => '未知',
            self::GENDER_MALE => '男',
            self::GENDER_FEMALE => '女',
        ];
    }

    // 获取性别文本
    public function getGenderTextAttribute()
    {
        return self::getGenderMap()[$this->gender] ?? '未知';
    }

    // 与排班的关联
    public function schedules()
    {
        return $this->hasMany(CounselorSchedule::class);
    }

    // 与预约的关联
    public function appointments()
    {
        return $this->hasMany(ConsultationAppointment::class);
    }

    // 与线下课程的关联
    public function courses()
    {
        return $this->hasMany(OfflineCourse::class, 'lecturer_id');
    }

    // 获取可用于预约的时间段
    public function getAvailableSchedules($date = null)
    {
        $query = $this->schedules()->where('is_available', true);
        
        if ($date) {
            $query->where('date', $date);
        } else {
            $query->where('date', '>=', now()->format('Y-m-d'));
        }
        
        return $query->orderBy('date')->orderBy('start_time')->get();
    }
}
