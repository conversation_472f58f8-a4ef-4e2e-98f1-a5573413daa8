<?php
namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Model;

/**
 * 心理测评模块 AI 配置模型
 * 对应表 assessment_ai_settings，字段职责单一
 */
class AssessmentAiSetting extends Model
{
    use HasDateTimeFormatter;
    // 指定表名
    protected $table = 'assessment_ai_settings';

    // 允许批量赋值字段
    protected $fillable = [
        'api_key',
        'endpoint',
        'model',
        'system_prompt',
        'user_prompt_template',
        'temperature',
        'max_tokens',
        'is_active',
    ];

    // 类型转换
    protected $casts = [
        'temperature' => 'float',
        'max_tokens' => 'integer',
        'is_active'   => 'boolean',
    ];

    /**
     * 获取 AI 设置记录，一般只有一条
     *
     * @return self|null
     */
    public static function getSettings(): ?self
    {
        return static::first();
    }
}
