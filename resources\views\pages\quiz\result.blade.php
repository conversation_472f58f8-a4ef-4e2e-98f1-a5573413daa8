@extends('layouts.app')

@section('title', $quiz->title . ' - 答题结果')

@section('custom-styles')
<link rel="stylesheet" href="{{ asset('css/quiz.css') }}">
<style>
    .header-container {
        background: linear-gradient(135deg, #5b7cef, #3c67e3);
        color: white;
        padding: 15px;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 1000;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 50px;
    }
    
    .content-container {
        margin-top: 65px;
        padding: 15px;
        padding-bottom: 120px; /* 增加底部空间，预留导航栏和底部安全距离 */
    }
    
    .result-card {
        background: white;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        border: 1px solid #f0f0f0;
        text-align: center;
    }
    
    .result-icon {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 15px;
        font-size: 32px;
        color: white;
    }
    
    .pass-icon {
        background: linear-gradient(135deg, #28a745, #20c997);
        box-shadow: 0 4px 10px rgba(40, 167, 69, 0.2);
    }
    
    .fail-icon {
        background: linear-gradient(135deg, #dc3545, #f74b5c);
        box-shadow: 0 4px 10px rgba(220, 53, 69, 0.2);
    }
    
    .result-title {
        font-size: 20px;
        font-weight: 600;
        margin-bottom: 10px;
        color: #333;
    }
    
    .result-description {
        font-size: 15px;
        color: #666;
        line-height: 1.5;
        margin-bottom: 10px;
    }
    
    .result-stats {
        display: flex;
        justify-content: center;
        gap: 15px;
        margin: 15px 0;
    }
    
    .stat-item {
        text-align: center;
        flex: 1;
        max-width: 100px;
    }
    
    .stat-value {
        font-size: 24px;
        font-weight: 600;
        color: #3c67e3;
        line-height: 1;
        margin-bottom: 5px;
    }
    
    .stat-label {
        font-size: 12px;
        color: #666;
    }
    
    .score-circle {
        position: relative;
        width: 120px;
        height: 120px;
        margin: 0 auto 15px;
    }
    
    .score-circle svg {
        width: 100%;
        height: 100%;
        transform: rotate(-90deg);
    }
    
    .score-circle-bg {
        fill: none;
        stroke: #f0f0f0;
        stroke-width: 12;
    }
    
    .score-circle-progress {
        fill: none;
        stroke-width: 12;
        stroke-linecap: round;
        transition: stroke-dashoffset 1s ease;
    }
    
    .score-text {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;
    }
    
    .score-value {
        font-size: 24px;
        font-weight: 600;
        color: #333;
        line-height: 1;
    }
    
    .score-label {
        font-size: 12px;
        color: #666;
    }
    
    .action-button {
        display: block;
        width: 100%;
        padding: 12px;
        text-align: center;
        border-radius: 8px;
        margin-bottom: 10px;
        font-size: 16px;
        font-weight: 500;
        text-decoration: none;
        transition: all 0.3s ease;
    }
    
    .primary-button {
        background-color: #3c67e3;
        color: white;
        box-shadow: 0 3px 6px rgba(60, 103, 227, 0.2);
    }
    
    .primary-button:hover {
        background-color: #3559c7;
        color: white;
    }
    
    .secondary-button {
        background-color: #f0f4fe;
        color: #3c67e3;
        border: 1px solid #d8e3fc;
    }
    
    .secondary-button:hover {
        background-color: #e6ecfd;
        color: #3c67e3;
    }
    
    .question-list {
        margin-top: 20px;
    }
    
    .section-title {
        font-size: 17px;
        font-weight: 500;
        margin-bottom: 15px;
        color: #333;
        display: flex;
        align-items: center;
    }
    
    .section-title-icon {
        margin-right: 8px;
        color: #3c67e3;
    }
    
    .question-item {
        background: white;
        border-radius: 12px;
        padding: 15px;
        margin-bottom: 15px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        border: 1px solid #f0f0f0;
    }
    
    .question-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
    }
    
    .question-number {
        font-size: 14px;
        color: #3c67e3;
        font-weight: 600;
    }
    
    .question-status {
        font-size: 12px;
        padding: 3px 8px;
        border-radius: 12px;
        color: white;
    }
    
    .status-correct {
        background-color: #28a745;
    }
    
    .status-incorrect {
        background-color: #dc3545;
    }
    
    .question-text {
        font-size: 16px;
        color: #333;
        line-height: 1.6;
        margin-bottom: 15px;
    }
    
    .options-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }
    
    .option-item {
        margin-bottom: 10px;
        padding: 10px 12px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        font-size: 14px;
    }
    
    .option-item:last-child {
        margin-bottom: 0;
    }
    
    .option-marker {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        flex-shrink: 0;
        font-weight: 500;
    }
    
    .normal-option {
        background-color: #f8f9fa;
        color: #495057;
    }
    
    .normal-option .option-marker {
        background-color: white;
        border: 2px solid #ced4da;
        color: #ced4da;
    }
    
    .correct-option {
        background-color: #e7f3ee;
        color: #1a7348;
    }
    
    .correct-option .option-marker {
        background-color: #28a745;
        border: 2px solid #28a745;
        color: white;
    }
    
    .incorrect-option {
        background-color: #f8eaed;
        color: #a12a43;
    }
    
    .incorrect-option .option-marker {
        background-color: #dc3545;
        border: 2px solid #dc3545;
        color: white;
    }
    
    .user-option {
        background-color: #e7f3ff;
        color: #0c63e4;
    }
    
    .user-option .option-marker {
        background-color: #3c67e3;
        border: 2px solid #3c67e3;
        color: white;
    }
    
    .explanation-box {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 12px;
        margin-top: 15px;
        font-size: 13px;
        color: #666;
        border-left: 3px solid #6c757d;
    }
    
    .explanation-title {
        font-weight: 600;
        margin-bottom: 5px;
        color: #495057;
    }
    
    .prize-gained {
        background: linear-gradient(135deg, #FFC107, #FF9800);
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 20px;
        color: white;
        text-align: center;
        box-shadow: 0 4px 15px rgba(255, 152, 0, 0.2);
    }
    
    .prize-icon {
        font-size: 30px;
        margin-bottom: 10px;
    }
    
    .prize-title {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 5px;
    }
    
    .prize-text {
        font-size: 14px;
        opacity: 0.9;
    }
    
    .prize-button {
        display: inline-block;
        background-color: white;
        color: #FF9800;
        padding: 8px 20px;
        border-radius: 20px;
        margin-top: 10px;
        font-weight: 500;
        text-decoration: none;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }
</style>
@endsection

@section('content')
<div class="header-container">
    <a href="{{ route('quiz.show', $quiz->id) }}" class="back-button" style="display: flex; align-items: center; justify-content: center; width: 30px; height: 30px;">
        <div class="back-icon" style="border-top: 2px solid white; border-left: 2px solid white; width: 12px; height: 12px; transform: rotate(-45deg);"></div>
    </a>
    <h1 style="font-size: 18px; font-weight: 500; margin: 0 auto; text-align: center; flex-grow: 1; margin-left: -30px; text-shadow: 0 1px 2px rgba(0,0,0,0.1);">答题结果</h1>
    <div style="width: 30px;"></div>
</div>

<div class="content-container">
    <!-- 如果得分为满分，显示奖品领取按钮 -->
    @if($passed && $attempt->score == $attempt->total_questions)
    <div class="prize-gained">
        <div class="prize-icon">
            <i class="fas fa-trophy"></i>
        </div>
        <div class="prize-title">恭喜您获得奖品！</div>
        <div class="prize-text">您已经完美完成测试，可以领取奖品了</div>
        
        <!-- 直接显示领取按钮 -->
        <a href="{{ route('quiz.claim_prize', ['id' => $quiz->id, 'winner_id' => 1]) }}" class="prize-button">
            立即领取
        </a>
    </div>
    @endif

    <!-- 结果卡片 -->
    <div class="result-card">
        <div class="result-icon {{ $passed ? 'pass-icon' : 'fail-icon' }}">
            <i class="fas {{ $passed ? 'fa-check' : 'fa-times' }}"></i>
        </div>
        
        <h2 class="result-title">{{ $passed ? '恭喜您通过了测试！' : '很遗憾，未能通过测试' }}</h2>
        
        <p class="result-description">
            {{ $passed ? '您的表现非常出色，成功获得了及格分数！' : '再接再厉，您下次一定能做得更好！' }}
        </p>
        
        <div class="score-circle">
            <svg viewBox="0 0 100 100">
                <circle class="score-circle-bg" cx="50" cy="50" r="40"></circle>
                <circle class="score-circle-progress" cx="50" cy="50" r="40"
                        stroke="{{ $passed ? '#28a745' : '#dc3545' }}"
                        stroke-dasharray="251.2"
                        stroke-dashoffset="{{ 251.2 - ($scorePercentage / 100 * 251.2) }}">
                </circle>
            </svg>
            <div class="score-text">
                <div class="score-value">{{ $scorePercentage }}%</div>
                <div class="score-label">得分率</div>
            </div>
        </div>
        
        <div class="result-stats">
            <div class="stat-item">
                <div class="stat-value">{{ $attempt->score }}/{{ $attempt->total_questions }}</div>
                <div class="stat-label">总分数</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">{{ $attempt->correct_count }}</div>
                <div class="stat-label">正确题数</div>
            </div>
            <div class="stat-item">
                <div class="stat-value">{{ $attempt->total_questions }}</div>
                <div class="stat-label">总题数</div>
            </div>
        </div>
    </div>
    
    <!-- 问题列表 -->
    <h2 class="section-title">
        <i class="fas fa-list-ul section-title-icon"></i> 答题详情
    </h2>
    
    <div class="question-list">
        @foreach($attempt->answers as $answer)
        <div class="question-item">
            <div class="question-header">
                <div class="question-number">问题 {{ $loop->iteration }}</div>
                <div class="question-status {{ $answer->is_correct ? 'status-correct' : 'status-incorrect' }}">
                    {{ $answer->is_correct ? '答对了' : '答错了' }}
                </div>
            </div>
            
            <div class="question-text">{{ $answer->question->question_text }}</div>
            
            <ul class="options-list">
                @foreach($answer->question->options as $option)
                @php
                    $userAnswerArray = is_array($answer->user_answer) ? $answer->user_answer : [$answer->user_answer];
                    $isUserChoice = in_array($option->id, $userAnswerArray);
                    
                    if($option->is_correct && $isUserChoice) {
                        $optionClass = 'correct-option';
                    } elseif($option->is_correct && !$isUserChoice) {
                        $optionClass = 'correct-option';
                    } elseif(!$option->is_correct && $isUserChoice) {
                        $optionClass = 'incorrect-option';
                    } else {
                        $optionClass = 'normal-option';
                    }
                @endphp
                <li class="option-item {{ $optionClass }}">
                    <span class="option-marker">{{ chr(65 + $loop->index) }}</span>
                    <span>{{ $option->option_text }}</span>
                    
                    @if($isUserChoice)
                        <span style="margin-left: auto;"><i class="fas fa-user" style="font-size: 12px; margin-left: 5px;"></i></span>
                    @endif
                </li>
                @endforeach
            </ul>
            
            @if($answer->question->explanation)
            <div class="explanation-box">
                <div class="explanation-title">答案解析：</div>
                <div>{{ $answer->question->explanation }}</div>
            </div>
            @endif
        </div>
        @endforeach
    </div>
    
    <!-- 底部按钮 -->
    <a href="{{ route('quiz.show', $quiz->id) }}" style="display: block; width: 100%; background-color: #4169E1; color: white; padding: 10px 0; border-radius: 5px; text-align: center; margin-bottom: 10px; text-decoration: none; font-weight: 500;">返回问答详情</a>
    
    <a href="{{ route('quiz.start', $quiz->id) }}" style="display: block; width: 100%; background-color: #f5f5f5; color: #333; padding: 10px 0; border-radius: 5px; text-align: center; margin-bottom: 10px; text-decoration: none; border: 1px solid #ddd; font-weight: 500;">再次尝试</a>
    
    <a href="{{ route('quiz.my_prizes') }}" style="display: block; width: 100%; background-color: #f5f5f5; color: #333; padding: 10px 0; border-radius: 5px; text-align: center; text-decoration: none; border: 1px solid #ddd; font-weight: 500;">查看我的奖品</a>
</div>
@endsection
