<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;
use App\Facades\Sms;
use Illuminate\Support\Facades\Log;

class SmsCode extends Model
{
    use HasFactory, HasDateTimeFormatter;

    protected $fillable = [
        'phone',
        'code',
        'type',
        'expires_at',
        'used',
    ];

    protected $dates = [
        'expires_at',
        'created_at',
        'updated_at',
    ];

    // 验证码类型映射表
    const TYPE_REGISTER = 1; // 注册验证码
    const TYPE_LOGIN = 2;    // 登录验证码
    const TYPE_PASSWORD = 3; // 修改密码验证码
    const TYPE_BINDING = 4;  // 绑定手机验证码

    // 类型名称映射
    protected static $typeMap = [
        'register' => self::TYPE_REGISTER,
        'login' => self::TYPE_LOGIN,
        'password' => self::TYPE_PASSWORD,
        'binding' => self::TYPE_BINDING,
    ];

    /**
     * 创建一个新的验证码并发送
     *
     * @param string $phone 手机号
     * @param string $type 验证码类型
     * @return string 返回生成的验证码
     */
    public static function createCode($phone, $type = 'login')
    {
        // 过期旧验证码
        self::where('phone', $phone)
            ->where('type', $type)
            ->where('used', false)
            ->update(['used' => true]);

        // 生成新验证码
        $code = mt_rand(100000, 999999);
        $expiresAt = Carbon::now()->addMinutes(10);

        // 保存验证码到数据库
        self::create([
            'phone' => $phone,
            'code' => $code,
            'type' => $type,
            'expires_at' => $expiresAt,
            'used' => false,
        ]);

        // 发送短信验证码
        self::sendVerificationSms($phone, $code, $type);

        return $code;
    }

    /**
     * 发送验证码短信
     *
     * @param string $phone 手机号
     * @param string $code 验证码
     * @param string $type 验证码类型
     * @return bool 是否发送成功
     */
    protected static function sendVerificationSms($phone, $code, $type)
    {
        try {
            // 将字符串类型转换为数字类型
            $smsType = self::$typeMap[$type] ?? self::TYPE_REGISTER;

            // 调用短信服务发送验证码
            $result = Sms::sendVerificationCode($phone, $code, $smsType);

            Log::channel('sms')->info('验证码发送结果', [
                'phone' => $phone,
                'type' => $type,
                'success' => $result['success'],
                'message' => $result['message'],
                'provider' => $result['provider'] ?? 'unknown',
            ]);

            return $result['success'];
        } catch (\Exception $e) {
            Log::channel('sms')->error('验证码发送异常', [
                'phone' => $phone,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * 验证短信验证码
     *
     * @param string $phone 手机号
     * @param string $code 验证码
     * @param string $type 验证码类型
     * @return bool 是否验证通过
     */
    public static function verifyCode($phone, $code, $type = 'login')
    {
        $smsCode = self::where('phone', $phone)
            ->where('code', $code)
            ->where('type', $type)
            ->where('used', false)
            ->where('expires_at', '>', Carbon::now())
            ->first();

        if (!$smsCode) {
            return false;
        }

        // 标记为已使用
        $smsCode->used = true;
        $smsCode->save();

        return true;
    }
}
