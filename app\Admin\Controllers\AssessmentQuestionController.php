<?php
namespace App\Admin\Controllers;

use App\Models\AssessmentQuestion;
use App\Models\AssessmentQuestionnaire;
use Dcat\Admin\Grid;
use Dcat\Admin\Form;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class AssessmentQuestionController extends AdminController
{
    protected $title = '心理测评题目';

    protected function grid(): Grid
    {
        return Grid::make(new AssessmentQuestion(), function (Grid $grid) {
            $grid->column('id', '编号')->sortable();
            $grid->column('questionnaire.title', '问卷标题');
            $grid->column('type', '题型')->using(['single' => '单选', 'multiple' => '多选', 'scale' => '量表'])->label();
            $grid->column('content', '题干');
            $grid->column('sort_order', '排序')->sortable();
            $grid->column('created_at', '创建时间');
            $grid->column('updated_at', '更新时间');

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');
                $filter->equal('questionnaire_id', '问卷')->select(AssessmentQuestionnaire::pluck('title', 'id'));
                $filter->equal('type', '类型')->select(['single' => '单选', 'multiple' => '多选', 'scale' => '量表']);
                $filter->between('created_at', '创建时间')->datetime();
                $filter->between('updated_at', '更新时间')->datetime();
            });
        });
    }

    protected function detail($id): Show
    {
        return Show::make($id, new AssessmentQuestion(), function (Show $show) {
            $show->field('id', '编号');
            $show->field('questionnaire.title', '问卷标题');
            $show->field('type', '题型')->using(['single' => '单选', 'multiple' => '多选', 'scale' => '量表']);
            $show->field('content', '题干');
            $show->field('sort_order', '排序');
            $show->field('created_at', '创建时间');
            $show->field('updated_at', '更新时间');
        });
    }

    protected function form(): Form
    {
        return Form::make(new AssessmentQuestion(), function (Form $form) {
            $form->display('id');
            $form->select('questionnaire_id', '问卷')->options(AssessmentQuestionnaire::pluck('title', 'id'))->required();
            $form->radio('type', '题型')->options(['single' => '单选', 'multiple' => '多选', 'scale' => '量表'])->required();
            $form->textarea('content', '题干')->required();
            $form->number('sort_order', '排序')->min(0)->default(0);
            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
