@extends('layouts.app')

@section('custom-styles')
<style>
/* 直播详情页面专用样式 */
.live-detail-page {
    min-height: 100vh;
    background-color: #ffffff;
    padding-bottom: 80px; /* 为底部导航预留空间 */
}

.live-detail-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0;
}

.live-detail-header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
}

.detail-back-btn {
    display: flex;
    align-items: center;
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: background-color 0.2s;
}

.detail-back-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.detail-back-icon {
    width: 24px;
    height: 24px;
    stroke: currentColor;
    fill: none;
    stroke-width: 2;
    stroke-linecap: round;
    stroke-linejoin: round;
}

.detail-page-title {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
}

.live-cover-container {
    position: relative;
}

.live-cover-image {
    width: 100%;
    height: 280px;
    object-fit: cover;
}

.cover-status-badge {
    position: absolute;
    top: 16px;
    left: 16px;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
}

.cover-status-badge.live {
    background-color: #ef4444;
    color: white;
}

.cover-status-badge.upcoming {
    background-color: #f97316;
    color: white;
}

.cover-status-badge.ended {
    background-color: #6b7280;
    color: white;
}

.cover-status-dot {
    width: 8px;
    height: 8px;
    background-color: white;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

.cover-featured-badge {
    position: absolute;
    top: 16px;
    right: 16px;
    background-color: #eab308;
    color: white;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
}

.live-info-container {
    padding: 16px;
}

.live-main-title {
    font-size: 20px;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 16px;
    line-height: 1.4;
}

.counselor-card {
    display: flex;
    align-items: center;
    background-color: #f9fafb;
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 20px;
}

.counselor-avatar-large {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    margin-right: 12px;
    object-fit: cover;
}

.counselor-details h3 {
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 4px 0;
}

.counselor-details p {
    font-size: 14px;
    color: #6b7280;
    margin: 0;
}

.info-section {
    margin-bottom: 20px;
}

.info-section-title {
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    margin-bottom: 8px;
}

.time-card {
    background-color: #dbeafe;
    border: 1px solid #93c5fd;
    border-radius: 12px;
    padding: 16px;
}

.time-text {
    color: #1e40af;
    font-weight: 500;
    font-size: 16px;
    margin: 0;
}

.description-card {
    background-color: #f9fafb;
    border-radius: 12px;
    padding: 16px;
}

.description-text {
    color: #374151;
    font-size: 14px;
    line-height: 1.6;
    margin: 0;
    white-space: pre-wrap; /* 保持换行格式 */
}

.notice-card {
    background-color: #fef3c7;
    border: 1px solid #fcd34d;
    border-radius: 12px;
    padding: 16px;
}

.notice-content {
    display: flex;
    align-items: flex-start;
    gap: 8px;
}

.notice-icon {
    width: 20px;
    height: 20px;
    color: #d97706;
    fill: currentColor;
    flex-shrink: 0;
    margin-top: 2px;
}

.notice-text {
    color: #92400e;
    font-size: 14px;
    line-height: 1.5;
    margin: 0;
    white-space: pre-wrap; /* 保持换行格式 */
}

.qr-section {
    margin-bottom: 24px;
}

.qr-container {
    display: flex;
    justify-content: center;
    background-color: #f9fafb;
    border-radius: 12px;
    padding: 20px;
}

.qr-image {
    width: 192px;
    height: 192px;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.qr-tip {
    text-align: center;
    font-size: 12px;
    color: #6b7280;
    margin-top: 8px;
}

.sort-order-section {
    margin-bottom: 20px;
}

.sort-order-card {
    background-color: #f3f4f6;
    border-radius: 12px;
    padding: 16px;
    text-align: center;
}

.sort-order-text {
    color: #374151;
    font-size: 14px;
    margin: 0;
}

/* 响应式调整 */
@media (max-width: 480px) {
    .live-detail-page {
        padding-bottom: 70px; /* 移动端稍微减少一点空间 */
    }
    
    .live-info-container {
        padding: 12px;
    }
    
    .live-main-title {
        font-size: 18px;
    }
    
    .counselor-card {
        padding: 12px;
    }
    
    .info-section {
        margin-bottom: 16px;
    }
}
</style>
@endsection

@section('content')
<div class="live-detail-page">
    <!-- 头部 -->
    <div class="live-detail-header">
        <div class="live-detail-header-content">
            <button class="detail-back-btn" onclick="history.back()">
                <svg class="detail-back-icon" viewBox="0 0 24 24">
                    <path d="M15 19l-7-7 7-7"></path>
                </svg>
            </button>
            <h1 class="detail-page-title">直播详情</h1>
            <div style="width: 40px;"></div>
        </div>
    </div>

    <!-- 封面图片 -->
    <div class="live-cover-container">
        <img src="{{ $liveShow->cover_image ? asset('storage/' . $liveShow->cover_image) : asset('images/default-live-cover.jpg') }}" 
             alt="{{ $liveShow->title }}" 
             class="live-cover-image">
        
        <!-- 直播状态标签 -->
        <div class="cover-status-badge {{ $liveShow->status }}">
            @if($liveShow->status === 'live')
                <span class="cover-status-dot"></span>
                <span>直播中</span>
            @elseif($liveShow->status === 'upcoming')
                <span>即将开始</span>
            @else
                <span>已结束</span>
            @endif
        </div>

        <!-- 精选标签 -->
        @if($liveShow->is_featured)
        <div class="cover-featured-badge">
            精选
        </div>
        @endif
    </div>

    <!-- 直播信息 -->
    <div class="live-info-container">
        <!-- 标题 -->
        <h1 class="live-main-title">{{ $liveShow->title }}</h1>

        <!-- 咨询师信息 -->
        @if($liveShow->counselor)
        <div class="counselor-card">
            <img src="{{ $liveShow->counselor->avatar ? asset('storage/' . $liveShow->counselor->avatar) : asset('images/default-avatar.jpg') }}" 
                 alt="{{ $liveShow->counselor->name }}" 
                 class="counselor-avatar-large">
            <div class="counselor-details">
                <h3>{{ $liveShow->counselor->name }}</h3>
                @if($liveShow->counselor->title)
                <p>{{ $liveShow->counselor->title }}</p>
                @endif
            </div>
        </div>
        @endif

        <!-- 时间信息 -->
        <div class="info-section">
            <h3 class="info-section-title">直播时间</h3>
            <div class="time-card">
                <p class="time-text" style="margin-top: 8px;">
                    <strong>开始时间：</strong>{{ $liveShow->formatted_scheduled_at }}
                </p>
                 @if($liveShow->ended_at)
                <p class="time-text" style="margin-top: 8px;">
                    <strong>结束时间：</strong>{{ $liveShow->formatted_ended_at }}
                </p>
                @endif
            </div>
        </div>

        <!-- 直播描述 -->
        @if($liveShow->description)
        <div class="info-section">
            <h3 class="info-section-title">直播介绍</h3>
            <div class="description-card">
                <p class="description-text">{{ $liveShow->description }}</p>
            </div>
        </div>
        @endif

        <!-- 特别通知 -->
        @if($liveShow->notice)
        <div class="info-section">
            <h3 class="info-section-title">特别通知</h3>
            <div class="notice-card">
                <div class="notice-content">
                    <svg class="notice-icon" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                    </svg>
                    <p class="notice-text">{{ $liveShow->notice }}</p>
                </div>
            </div>
        </div>
        @endif

        <!-- 预约二维码 -->
        @if($liveShow->qr_code_image)
        <div class="qr-section">
            <h3 class="info-section-title">扫码预约(保存二维码打开微信扫码预约)</h3>
            <div class="qr-container">
                <img src="{{ asset('storage/' . $liveShow->qr_code_image) }}" 
                     alt="预约二维码" 
                     class="qr-image">
            </div>
            <p class="qr-tip">
                长按保存二维码，分享给朋友一起观看
            </p>
        </div>
        @endif

        <!-- 排序权重信息（仅在开发时可见，可选） -->
        @if(config('app.debug') && $liveShow->sort_order > 0)
        <div class="sort-order-section">
            <h3 class="info-section-title">排序权重</h3>
            <div class="sort-order-card">
                <p class="sort-order-text">权重: {{ $liveShow->sort_order }}</p>
            </div>
        </div>
        @endif
    </div>
</div>
@endsection 