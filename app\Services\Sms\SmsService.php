<?php

namespace App\Services\Sms;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;

/**
 * 短信服务类
 * 封装常用的短信功能，方便调用
 */
class SmsService
{
    /**
     * 短信服务接口实例
     * @var SmsServiceInterface
     */
    protected $smsProvider;
    
    /**
     * 验证码长度
     * @var int
     */
    protected $codeLength;
    
    /**
     * 验证码有效期（分钟）
     * @var int
     */
    protected $codeExpiration;
    
    /**
     * 构造函数
     *
     * @param string|null $provider 指定短信提供商
     */
    public function __construct(?string $provider = null)
    {
        // 初始化短信提供商
        $this->smsProvider = SmsServiceFactory::create($provider);
        
        // 从配置中加载验证码设置
        $this->codeLength = config('sms.verification.code_length', 6);
        $this->codeExpiration = config('sms.verification.expiration', 10);
    }
    
    /**
     * 发送验证码短信
     *
     * @param string $mobile 手机号
     * @param int $type 验证码类型 (1:注册, 2:登录, 3:修改密码, 4:绑定手机)
     * @param bool $useCache 是否缓存验证码
     * @return array 发送结果和验证码信息
     */
    public function sendVerificationCode(string $mobile, int $type = 1, bool $useCache = true): array
    {
        // 生成验证码
        $code = $this->generateVerificationCode();
        
        // 发送验证码
        $result = $this->smsProvider->sendVerificationCode($mobile, $code, $type);
        
        // 如果发送成功且需要缓存验证码
        if ($result['success'] && $useCache) {
            $this->storeVerificationCode($mobile, $code, $type);
        }
        
        return array_merge($result, ['code' => $code]);
    }
    
    /**
     * 验证验证码是否正确
     *
     * @param string $mobile 手机号
     * @param string $code 验证码
     * @param int $type 验证码类型
     * @param bool $deleteIfValid 验证成功后是否删除
     * @return bool
     */
    public function verifyCode(string $mobile, string $code, int $type = 1, bool $deleteIfValid = true): bool
    {
        $key = $this->getVerificationCodeCacheKey($mobile, $type);
        $cachedCode = Cache::get($key);
        
        // 验证码匹配成功
        if ($cachedCode && $cachedCode === $code) {
            // 验证成功后删除缓存
            if ($deleteIfValid) {
                Cache::forget($key);
            }
            return true;
        }
        
        return false;
    }
    
    /**
     * 发送通知短信
     *
     * @param string $mobile 手机号
     * @param array $params 短信参数
     * @param string $templateCode 短信模板代码
     * @return array 发送结果
     */
    public function sendNotification(string $mobile, array $params, string $templateCode): array
    {
        return $this->smsProvider->sendNotification($mobile, $params, $templateCode);
    }
    
    /**
     * 生成随机验证码
     *
     * @return string
     */
    protected function generateVerificationCode(): string
    {
        // 纯数字验证码
        return (string) mt_rand(pow(10, $this->codeLength - 1), pow(10, $this->codeLength) - 1);
    }
    
    /**
     * 存储验证码到缓存
     *
     * @param string $mobile 手机号
     * @param string $code 验证码
     * @param int $type 验证码类型
     * @return void
     */
    protected function storeVerificationCode(string $mobile, string $code, int $type): void
    {
        $key = $this->getVerificationCodeCacheKey($mobile, $type);
        Cache::put($key, $code, now()->addMinutes($this->codeExpiration));
    }
    
    /**
     * 获取验证码缓存的键名
     *
     * @param string $mobile 手机号
     * @param int $type 验证码类型
     * @return string
     */
    protected function getVerificationCodeCacheKey(string $mobile, int $type): string
    {
        return "sms:verification:{$type}:{$mobile}";
    }
    
    /**
     * 获取当前使用的短信提供商名称
     *
     * @return string
     */
    public function getProviderName(): string
    {
        return $this->smsProvider->getProviderName();
    }
    
    /**
     * 切换短信提供商
     *
     * @param string $provider 提供商名称
     * @return self
     */
    public function useProvider(string $provider): self
    {
        $this->smsProvider = SmsServiceFactory::create($provider);
        return $this;
    }
    
    /**
     * 设置验证码长度
     *
     * @param int $length 验证码长度
     * @return self
     */
    public function setCodeLength(int $length): self
    {
        $this->codeLength = $length;
        return $this;
    }
    
    /**
     * 设置验证码有效期（分钟）
     *
     * @param int $minutes 有效期分钟数
     * @return self
     */
    public function setCodeExpiration(int $minutes): self
    {
        $this->codeExpiration = $minutes;
        return $this;
    }
}
