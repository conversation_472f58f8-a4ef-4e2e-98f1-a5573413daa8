<?php

namespace App\Services;

use App\Models\AssessmentQuestionnaire;
use App\Models\AssessmentQuestion;
use App\Models\AssessmentOption;
use App\Models\AssessmentResponse;
use App\Models\AssessmentResultConfig;
use Illuminate\Support\Facades\DB;

class QuestionnaireService
{
    /**
     * 获取所有激活的问卷列表
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getActiveQuestionnaires()
    {
        return AssessmentQuestionnaire::where('is_active', true)->get();
    }

    /**
     * 根据 ID 获取问卷及其题目和选项
     *
     * @param int $id
     * @return AssessmentQuestionnaire
     */
    public function findWithDetails(int $id): AssessmentQuestionnaire
    {
        return AssessmentQuestionnaire::with(['questions.options'])->findOrFail($id);
    }

    /**
     * 复制问卷
     */
    public function duplicateQuestionnaire($id, $newTitle = null)
    {
        $originalQuestionnaire = AssessmentQuestionnaire::with(['questions.options', 'resultConfigs'])->findOrFail($id);
        
        DB::beginTransaction();
        
        try {
            // 创建新问卷
            $newQuestionnaire = AssessmentQuestionnaire::create([
                'title' => $newTitle ?: $originalQuestionnaire->title . ' (副本)',
                'description' => $originalQuestionnaire->description,
                'domain' => $originalQuestionnaire->domain,
                'question_count' => $originalQuestionnaire->question_count,
                'est_duration' => $originalQuestionnaire->est_duration,
                'cover_image' => $originalQuestionnaire->cover_image,
                'is_active' => false // 副本默认不激活
            ]);

            // 复制题目和选项
            foreach ($originalQuestionnaire->questions as $question) {
                $newQuestion = AssessmentQuestion::create([
                    'questionnaire_id' => $newQuestionnaire->id,
                    'type' => $question->type,
                    'content' => $question->content,
                    'sort_order' => $question->sort_order
                ]);

                // 复制选项
                foreach ($question->options as $option) {
                    AssessmentOption::create([
                        'question_id' => $newQuestion->id,
                        'content' => $option->content,
                        'score_value' => $option->score_value
                    ]);
                }
            }

            // 复制结果配置
            foreach ($originalQuestionnaire->resultConfigs as $resultConfig) {
                AssessmentResultConfig::create([
                    'questionnaire_id' => $newQuestionnaire->id,
                    'dimension_name' => $resultConfig->dimension_name,
                    'min_score' => $resultConfig->min_score,
                    'max_score' => $resultConfig->max_score,
                    'level_name' => $resultConfig->level_name,
                    'description' => $resultConfig->description,
                    'suggestion' => $resultConfig->suggestion,
                    'color' => $resultConfig->color,
                    'sort_order' => $resultConfig->sort_order
                ]);
            }

            DB::commit();
            
            return $newQuestionnaire;
            
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 复制问卷 (别名方法)
     */
    public function duplicate($id, $newTitle = null)
    {
        return $this->duplicateQuestionnaire($id, $newTitle);
    }

    /**
     * 切换问卷状态
     */
    public function toggleQuestionnaireStatus($id)
    {
        $questionnaire = AssessmentQuestionnaire::findOrFail($id);
        $questionnaire->is_active = !$questionnaire->is_active;
        $questionnaire->save();
        
        return $questionnaire;
    }

    /**
     * 获取问卷预览数据
     */
    public function getQuestionnairePreview($id)
    {
        $questionnaire = AssessmentQuestionnaire::with(['questions.options'])->findOrFail($id);
        
        $preview = [
            'questionnaire' => [
                'id' => $questionnaire->id,
                'title' => $questionnaire->title,
                'description' => $questionnaire->description,
                'domain' => $questionnaire->domain,
                'est_duration' => $questionnaire->est_duration,
                'is_active' => $questionnaire->is_active
            ],
            'questions' => []
        ];

        foreach ($questionnaire->questions->sortBy('sort_order') as $question) {
            $questionData = [
                'id' => $question->id,
                'type' => $question->type,
                'content' => $question->content,
                'sort_order' => $question->sort_order,
                'options' => []
            ];

            foreach ($question->options as $option) {
                $questionData['options'][] = [
                    'id' => $option->id,
                    'content' => $option->content,
                    'score_value' => $option->score_value
                ];
            }

            $preview['questions'][] = $questionData;
        }

        return $preview;
    }

    /**
     * 获取问卷统计信息
     */
    public function getQuestionnaireStats($id)
    {
        $questionnaire = AssessmentQuestionnaire::with(['questions.options'])->findOrFail($id);
        
        $totalQuestions = $questionnaire->questions->count();
        $totalOptions = $questionnaire->questions->sum(function ($question) {
            return $question->options->count();
        });
        
        $avgOptionsPerQuestion = $totalQuestions > 0 ? round($totalOptions / $totalQuestions, 1) : 0;
        
        // 获取回答次数
        $totalResponses = AssessmentResponse::where('questionnaire_id', $id)->count();
        
        return [
            'total_questions' => $totalQuestions,
            'total_options' => $totalOptions,
            'avg_options_per_question' => $avgOptionsPerQuestion,
            'total_responses' => $totalResponses
        ];
    }

    /**
     * 验证问卷完整性
     */
    public function validateQuestionnaireIntegrity($id)
    {
        $questionnaire = AssessmentQuestionnaire::with(['questions.options'])->findOrFail($id);
        
        $errors = [];
        
        // 检查是否有题目
        if ($questionnaire->questions->count() === 0) {
            $errors[] = '问卷至少需要一个题目';
        }
        
        // 检查每个题目是否有选项
        foreach ($questionnaire->questions as $question) {
            if ($question->options->count() === 0) {
                $errors[] = "题目「{$question->content}」缺少选项";
            }
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * 批量更新题目排序
     */
    public function updateQuestionOrder($questionnaireId, $questionOrders)
    {
        DB::beginTransaction();
        
        try {
            foreach ($questionOrders as $questionId => $sortOrder) {
                AssessmentQuestion::where('id', $questionId)
                    ->where('questionnaire_id', $questionnaireId)
                    ->update(['sort_order' => $sortOrder]);
            }
            
            DB::commit();
            
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
}
