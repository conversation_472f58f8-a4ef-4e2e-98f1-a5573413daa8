<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use App\Models\CounselorSchedule;

class GenerateCounselorSchedules extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @counselor_ids 咨询师 ID 列表，用逗号分隔，例如 "1,2,3"
     * @start_date    开始日期，例如 "2025-06-10"
     * @end_date      结束日期，例如 "2025-06-15"
     * @--start_time  每天的开始时间，格式 HH:MM，默认 00:00
     * @--end_time    每天的结束时间，格式 HH:MM，默认 23:45
     */
    protected $signature = 'generate:counselor-schedules
                            {counselor_ids   : 咨询师 ID 列表，用逗号分隔}
                            {start_date      : 开始日期，格式 YYYY-MM-DD}
                            {end_date        : 结束日期，格式 YYYY-MM-DD}
                            {--start_time=00:00 : 每天的开始时间，HH:MM}
                            {--end_time=23:45   : 每天的结束时间，HH:MM}';

    /**
     * The console command description.
     */
    protected $description = '按 15 分钟间隔，批量为指定咨询师在日期范围内生成排班';

    public function handle()
    {
        // 把逗号分隔的字符串拆成数组
        $ids = array_filter(explode(',', $this->argument('counselor_ids')), fn($v)=>trim($v)!=='');
        $startDate = Carbon::parse($this->argument('start_date'))->startOfDay();
        $endDate   = Carbon::parse($this->argument('end_date'))->startOfDay();
        $startTime = $this->option('start_time');
        $endTime   = $this->option('end_time');
    
        if ($endDate->lt($startDate)) {
            return $this->error('结束日期不能早于开始日期。');
        }
    
        foreach ($ids as $counselorId) {
            $this->info("正在生成咨询师 #{$counselorId} 的排班...");
    
            $datePeriod = CarbonPeriod::create($startDate, $endDate);
            foreach ($datePeriod as $date) {
                $dateString = $date->toDateString();
                $timePeriod = CarbonPeriod::create(
                    $date->copy()->setTimeFromTimeString($startTime),
                    '15 minutes',
                    $date->copy()->setTimeFromTimeString($endTime)
                );
                foreach ($timePeriod as $slotStart) {
                    $slotEnd = $slotStart->copy()->addMinutes(15);
                    $exists = CounselorSchedule::where('counselor_id', $counselorId)
                        ->where('date', $dateString)
                        ->where('start_time', $slotStart->format('H:i:s'))
                        ->exists();
                    if (! $exists) {
                        CounselorSchedule::create([
                            'counselor_id' => $counselorId,
                            'date'         => $dateString,
                            'start_time'   => $slotStart->format('H:i:s'),
                            'end_time'     => $slotEnd->format('H:i:s'),
                            'is_available' => true,
                        ]);
                    }
                }
            }
    
            $this->info("咨询师 #{$counselorId} 的排班生成完毕。");
        }
    
        $this->info('全部排班已生成。');
        return 0;
    }
}
