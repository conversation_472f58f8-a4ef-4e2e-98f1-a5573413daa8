<?php

namespace App\Admin\Controllers;

use App\Models\News;
use App\Models\NewsCategory;
use App\Models\NewsTag;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Widgets\Card;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Widgets\ApexCharts\Chart;
use Dcat\Admin\Admin;
use Carbon\Carbon;

class NewsController extends AdminController
{
    /**
     * 页面标题
     *
     * @return string
     */
    protected function title()
    {
        return '新闻资讯管理';
    }

    /**
     * 仪表板
     *
     * @param Content $content
     * @return Content
     */
    public function dashboard(Content $content)
    {
        return $content
            ->header('新闻统计')
            ->description('数据总览')
            ->body(function ($row) {
                $row->column(6, $this->categoryPieChart());
                $row->column(6, $this->trendsLineChart());
            })
            ->body(function ($row) {
                $row->column(12, $this->recentNewsList());
            });
    }

    /**
     * 分类分布饼图
     *
     * @return Card
     */
    protected function categoryPieChart()
    {
        $categories = NewsCategory::withCount('news')->get();
        
        $names = $categories->pluck('name')->toArray();
        $values = $categories->pluck('news_count')->toArray();

        $chart = new Chart(null, [
            'colors' => [Admin::color()->primary(), Admin::color()->success(), Admin::color()->warning(), Admin::color()->danger()],
            'chart' => [
                'type' => 'pie',
                'height' => 400
            ],
            'series' => $values,
            'labels' => $names,
            'legend' => [
                'position' => 'bottom'
            ],
            'title' => [
                'text' => '分类分布',
                'align' => 'center'
            ]
        ]);

        return Card::make('新闻分类分布', $chart->render());
    }

    /**
     * 趋势折线图
     *
     * @return Card
     */
    protected function trendsLineChart()
    {
        $days = 30;
        $startDate = Carbon::now()->subDays($days);

        $dates = collect(range(0, $days - 1))->map(function ($day) use ($startDate) {
            return $startDate->copy()->addDays($day)->format('Y-m-d');
        })->toArray();

        $publishedNews = collect($dates)->map(function ($date) {
            return News::whereDate('published_at', $date)->count();
        })->toArray();

        $views = collect($dates)->map(function ($date) {
            return \App\Models\NewsView::whereDate('created_at', $date)->count();
        })->toArray();

        $chart = new Chart(null, [
            'colors' => [Admin::color()->primary(), Admin::color()->info()],
            'chart' => [
                'type' => 'line',
                'height' => 400,
                'zoom' => [
                    'enabled' => true
                ],
            ],
            'series' => [
                [
                    'name' => '发布新闻数',
                    'data' => $publishedNews,
                ],
                [
                    'name' => '浏览次数',
                    'data' => $views,
                ]
            ],
            'xaxis' => [
                'categories' => $dates
            ],
            'title' => [
                'text' => '最近30天趋势',
                'align' => 'center'
            ],
            'yaxis' => [
                'min' => 0
            ]
        ]);

        return Card::make('新闻趋势', $chart->render());
    }

    /**
     * 最新新闻列表
     *
     * @return Card
     */
    protected function recentNewsList()
    {
        $grid = Grid::make(new News(), function (Grid $grid) {
            $grid->model()->with(['category'])->orderBy('id', 'desc')->limit(10);

            $grid->column('id', 'ID');
            $grid->column('title', '标题');
            $grid->column('category.name', '分类');
            $grid->column('is_published', '状态')->using([
                0 => '草稿',
                1 => '已发布',
            ])->label([
                0 => 'default',
                1 => 'success',
            ]);
            $grid->column('published_at', '发布时间');
            $grid->column('view_count', '浏览次数');
            $grid->column('share_count', '分享次数');

            $grid->disableCreateButton();
            $grid->disableActions();
            $grid->disableBatchDelete();
            $grid->disableRowSelector();
            $grid->disablePagination();
        });

        return Card::make('最新发布的新闻', $grid);
    }

    /**
     * 列表页面
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new News(), function (Grid $grid) {
            $grid->model()->with(['category']);

            $grid->column('id', 'ID')->sortable();
            $grid->column('title', '标题');
            $grid->column('category.name', '分类');
            
            $grid->column('cover_image', '封面图')->image('', 80, 60);
            
            $grid->column('is_published', '状态')->switch();
            $grid->column('is_featured', '推荐')->switch();
            
            $grid->column('published_at', '发布时间')->sortable();
            $grid->column('view_count', '浏览次数')->sortable();
            $grid->column('share_count', '分享次数')->sortable();
            $grid->column('created_at', '创建时间')->sortable();
            
            // 快速编辑功能
            $grid->showQuickEditButton();
            
            // 快速搜索
            $grid->quickSearch(['title', 'summary']);
            
            // 表格过滤器
            $grid->filter(function (Grid\Filter $filter) {
                $filter->panel();
                $filter->like('title', '标题');
                $filter->equal('category_id', '分类')->select(function () {
                    return NewsCategory::pluck('name', 'id');
                });
                // 正确的实现方式是使用select过滤器
                $filter->select('news_tag.news_tag_id', '标签')
                    ->options(NewsTag::pluck('name', 'id'))
                    ->whereHas('tags');
                $filter->equal('is_published', '状态')->select([
                    0 => '草稿',
                    1 => '已发布',
                ]);
                $filter->equal('is_featured', '是否推荐')->select([
                    0 => '普通',
                    1 => '推荐',
                ]);
                $filter->between('published_at', '发布时间')->datetime();
            });
            
            // 添加行操作
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableView();
                $actions->append('<a href="'.url('/news/'.$this->slug).'" class="btn btn-sm btn-primary" target="_blank"><i class="fa fa-eye"></i> 预览</a>');
            });
        });
    }

    /**
     * 详情页面
     *
     * @param mixed $id
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new News(), function (Show $show) {
            $show->field('id', 'ID');
            $show->field('title', '标题');
            $show->field('slug', 'URL别名');
            $show->field('category.name', '分类');
            $show->field('summary', '摘要');
            $show->field('content', '内容')->unescape()->as(function ($content) {
                return "<div class='content-preview'>{$content}</div>";
            });
            $show->field('cover_image', '封面图')->image();
            $show->field('source', '来源');
            $show->field('author', '作者');
            $show->field('is_published', '发布状态')->using([
                0 => '草稿',
                1 => '已发布',
            ])->label();
            $show->field('is_featured', '是否推荐')->using([
                0 => '普通',
                1 => '推荐',
            ])->label();
            $show->field('published_at', '发布时间');
            $show->field('view_count', '浏览次数');
            $show->field('share_count', '分享次数');
            $show->field('created_at', '创建时间');
            $show->field('updated_at', '更新时间');
            
            // 显示标签
            $show->field('tags', '标签')->as(function ($tags) {
                return collect($tags)->pluck('name')->implode(', ');
            });
            
            // 阅读记录
            $show->relation('views', '阅读记录', function ($model) {
                $grid = new Grid(new \App\Models\NewsView());
                $grid->model()->where('news_id', $model->id)->orderBy('id', 'desc')->limit(20);
                
                $grid->column('id', 'ID');
                $grid->column('user.name', '用户');
                $grid->column('ip_address', 'IP地址');
                $grid->column('created_at', '阅读时间');
                
                $grid->disableCreateButton();
                $grid->disableActions();
                
                return $grid;
            });
            
            // 工具按钮
            $show->panel()->tools(function (Show\Tools $tools) {
                // 移除批量操作按钮
            });
        });
    }

    /**
     * 表单页面
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new News(), function (Form $form) {
            $form->tab('基本信息', function (Form $form) {
                $form->display('id', 'ID');
                
                $form->text('title', '标题')
                    ->required()
                    ->rules('required|max:200');
                    
                $form->text('slug', 'URL别名')
                    ->required()
                    ->rules('required|max:200|unique:news,slug,'.$form->getKey())
                    ->help('用于URL访问，仅限英文、数字、中横线');
                    
                $form->select('category_id', '分类')
                    ->required()
                    ->options(function () {
                        return NewsCategory::pluck('name', 'id');
                    })
                    ->rules('required|exists:news_categories,id');
                    
                $form->textarea('summary', '摘要')
                    ->rows(3)
                    ->rules('max:500')
                    ->help('最多500字符');
                    
                $form->image('cover_image', '封面图')
                    ->dir('news/covers')
                    ->accept('jpg,png,gif,jpeg')
                    ->maxSize(2048);
                    
                $form->text('source', '来源')
                    ->rules('max:100')
                    ->help('新闻来源，如《人民日报》');
                    
                $form->text('author', '记者/作者')
                    ->rules('max:50')
                    ->help('新闻记者或作者名称');
                    
                $form->multipleSelect('tags', '标签')
                    ->options(function () {
                        return NewsTag::pluck('name', 'id');
                    })
                    ->saving(function ($value) use ($form) {
                        return $value;
                    });
            });
            
            $form->tab('内容', function (Form $form) {
                $form->editor('content', '内容')
                    ->required()
                    ->rules('required')
                    ->imageDirectory('news/content');
            });
            
            $form->tab('发布设置', function (Form $form) {
                $form->switch('is_published', '是否发布')
                    ->default(false);
                    
                $form->switch('is_featured', '是否推荐')
                    ->default(false);
                    
                $form->datetime('published_at', '发布时间')
                    ->default(now());
                    
                $form->display('created_at', '创建时间');
                $form->display('updated_at', '更新时间');
            });
            
            // 保存前回调
            $form->saving(function (Form $form) {
                // 生成slug
                if (!$form->slug && $form->title) {
                    $form->slug = \Illuminate\Support\Str::slug($form->title);
                }
                
                // 如果发布但没有设置发布时间，则设为当前时间
                if ($form->is_published && !$form->published_at) {
                    $form->published_at = now();
                }
            });
        });
    }
}
