<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class VideoTag extends Model
{
    use HasFactory,HasDateTimeFormatter;

    protected $fillable = [
        'name'
    ];

    // 关联视频
    public function videos()
    {
        return $this->belongsToMany(Video::class, 'video_tag', 'tag_id', 'video_id');
    }
}
