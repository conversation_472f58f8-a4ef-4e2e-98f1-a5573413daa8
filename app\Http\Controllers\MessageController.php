<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Message;
use App\Models\Counselor;
use App\Models\ConsultationAppointment;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;

class MessageController extends Controller
{
    /**
     * 显示消息列表（会话列表）
     */
    public function index()
    {
        // 获取当前用户的所有预约对应的咨询师
        $appointments = ConsultationAppointment::with('counselor')
            ->where('user_id', Auth::id())
            ->whereIn('status', [2, 3, 4]) // 只显示已确认、进行中或已完成的预约
            ->orderBy('appointment_time', 'desc')
            ->get();
            
        // 获取所有与用户有消息交流的咨询师ID
        $counselorIds = Message::where('user_id', Auth::id())
            ->select('counselor_id')
            ->distinct()
            ->pluck('counselor_id')
            ->toArray();
            
        // 合并两组咨询师ID（避免重复）
        $appointmentCounselorIds = $appointments->pluck('counselor_id')->toArray();
        $allCounselorIds = array_unique(array_merge($counselorIds, $appointmentCounselorIds));
        
        // 获取这些咨询师的信息
        $counselors = Counselor::whereIn('id', $allCounselorIds)->get();
        
        // 计算每个咨询师的未读消息数量
        $unreadCounts = [];
        $lastMessages = [];
        
        foreach ($counselors as $counselor) {
            $unreadCounts[$counselor->id] = Message::where('user_id', Auth::id())
                ->where('counselor_id', $counselor->id)
                ->where('sender_type', Message::SENDER_COUNSELOR)
                ->where('is_read', false)
                ->count();
                
            // 获取最后一条消息
            $lastMessage = Message::where('user_id', Auth::id())
                ->where('counselor_id', $counselor->id)
                ->orderBy('created_at', 'desc')
                ->first();
                
            $lastMessages[$counselor->id] = $lastMessage;
        }
        
        // 计算总未读消息数量
        $totalUnread = array_sum($unreadCounts);
        session(['unread_messages' => $totalUnread]);
        
        return view('pages.messages.index', [
            'counselors' => $counselors,
            'unreadCounts' => $unreadCounts,
            'lastMessages' => $lastMessages,
            'appointments' => $appointments,
            'unreadMessages' => $totalUnread
        ]);
    }
    
    /**
     * 显示与特定咨询师的聊天页面
     */
    public function show($counselorId)
    {
        // 获取咨询师信息
        $counselor = Counselor::findOrFail($counselorId);
        
        // 获取与该咨询师关联的预约
        $appointments = ConsultationAppointment::where('user_id', Auth::id())
            ->where('counselor_id', $counselorId)
            ->whereIn('status', [2, 3, 4]) // 只允许已确认、进行中或已完成的预约进行消息沟通
            ->orderBy('appointment_time', 'desc')
            ->get();
        
        // 如果没有相关预约，检查是否有历史消息记录
        $hasMessages = false;
        if ($appointments->isEmpty()) {
            $hasMessages = Message::where('user_id', Auth::id())
                ->where('counselor_id', $counselorId)
                ->exists();
                
            // 如果既没有预约也没有消息记录，则不允许发起聊天
            if (!$hasMessages) {
                return redirect()->route('consultation.counselors')
                    ->with('error', '您需要先预约该咨询师的服务才能开始聊天');
            }
        }
        
        // 获取聊天记录
        $messages = Message::where('user_id', Auth::id())
            ->where('counselor_id', $counselorId)
            ->orderBy('created_at', 'desc')
            ->paginate(30);
            
        // 将未读消息标记为已读
        Message::where('user_id', Auth::id())
            ->where('counselor_id', $counselorId)
            ->where('sender_type', Message::SENDER_COUNSELOR)
            ->where('is_read', false)
            ->update(['is_read' => true, 'read_at' => now()]);
            
        // 重新计算总未读消息数
        $totalUnread = Message::where('user_id', Auth::id())
            ->where('sender_type', Message::SENDER_COUNSELOR)
            ->where('is_read', false)
            ->count();
        session(['unread_messages' => $totalUnread]);
        
        return view('pages.messages.chat', [
            'counselor' => $counselor,
            'messages' => $messages,
            'appointments' => $appointments,
            'unreadMessages' => $totalUnread
        ]);
    }
    
    /**
     * 发送消息
     */
    public function send(Request $request, $counselorId)
    {
        $validator = Validator::make($request->all(), [
            'content' => 'required|string|max:2000',
            'appointment_id' => 'nullable|exists:consultation_appointments,id',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }
        
        // 验证咨询师是否存在
        $counselor = Counselor::findOrFail($counselorId);
        
        // 如果提供了预约ID，验证是否有效
        if ($request->appointment_id) {
            $appointment = ConsultationAppointment::where('id', $request->appointment_id)
                ->where('user_id', Auth::id())
                ->where('counselor_id', $counselorId)
                ->whereIn('status', [2, 3, 4])
                ->first();
                
            if (!$appointment) {
                return response()->json([
                    'success' => false,
                    'message' => '无效的预约ID'
                ], 400);
            }
        }
        
        // 创建消息
        $message = new Message();
        $message->user_id = Auth::id();
        $message->counselor_id = $counselorId;
        $message->appointment_id = $request->appointment_id;
        $message->sender_type = Message::SENDER_USER;
        $message->content = $request->content;
        $message->is_read = false;
        $message->save();
        
        // 返回新消息
        return response()->json([
            'success' => true,
            'message' => $message,
            'formatted_time' => Carbon::parse($message->created_at)->format('H:i')
        ]);
    }
    
    /**
     * 获取未读消息数
     */
    public function getUnreadCount(Request $request)
    {
        $user = auth()->user();
        $count = Message::where('user_id', $user->id)
            ->where('sender_type', 'counselor')
            ->where('is_read', false)
            ->count();
            
        $request->session()->put('unread_messages', $count);
        
        return $count;
    }
    
    /**
     * 检查是否有新消息
     */
    public function checkNewMessages(Request $request, $counselorId)
    {
        $user = auth()->user();
        
        // 获取用户最后看到的消息 ID
        $lastMessageIdKey = 'last_message_id_'.$user->id.'_'.$counselorId;
        
        // 找到已知的最后一条消息
        $lastMessage = Message::where('user_id', $user->id)
            ->where('counselor_id', $counselorId)
            ->orderBy('id', 'desc')
            ->first();
            
        $lastKnownId = $request->session()->get($lastMessageIdKey, 0);
        
        // 如果服务器上有消息且客户端没有最后的ID，则使用服务器最新的消息ID
        if ($lastMessage && $lastKnownId == 0) {
            $lastKnownId = $lastMessage->id;
            $request->session()->put($lastMessageIdKey, $lastKnownId);
        }
        
        // 查询新消息 - 只获取咨询师发送的未读消息
        $newMessages = Message::where('user_id', $user->id)
            ->where('counselor_id', $counselorId)
            ->where('sender_type', Message::SENDER_COUNSELOR)
            ->where('id', '>', $lastKnownId)
            ->orderBy('id')
            ->get();
            
        // 更新最后看到的消息ID
        if ($newMessages->isNotEmpty()) {
            $lastNewId = $newMessages->last()->id;
            $request->session()->put($lastMessageIdKey, $lastNewId);
        }
            
        // 构建返回数据
        $formattedMessages = [];
        foreach ($newMessages as $message) {
            $formattedMessages[] = [
                'id' => $message->id,
                'content' => $message->content,
                'created_at' => $message->created_at->format('Y-m-d H:i:s'),
                'formatted_time' => Carbon::parse($message->created_at)->format('H:i'),
                'is_read' => $message->is_read,
                'sender_type' => $message->sender_type
            ];
            
            // 标记为已读
            if (!$message->is_read) {
                $message->markAsRead();
            }
        }
        
        return response()->json([
            'has_new' => count($newMessages) > 0,
            'messages' => $formattedMessages,
            'debug' => [
                'last_known_id' => $lastKnownId,
                'new_messages_count' => count($newMessages)
            ]
        ]);
    }
}
