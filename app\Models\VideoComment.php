<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class VideoComment extends Model
{
    use HasFactory, SoftDeletes,HasDateTimeFormatter;

    protected $fillable = [
        'video_id',
        'user_id',
        'parent_id',
        'content',
        'is_approved'
    ];

    protected $dates = [
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    // 关联视频
    public function video()
    {
        return $this->belongsTo(Video::class, 'video_id');
    }

    // 关联用户
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    // 关联父评论
    public function parent()
    {
        return $this->belongsTo(VideoComment::class, 'parent_id');
    }

    // 关联回复
    public function replies()
    {
        return $this->hasMany(VideoComment::class, 'parent_id');
    }
}
