<?php

namespace App\Console\Commands;

use App\Models\Article;
use App\Models\Video;
use App\Models\CourseLesson;
use App\Models\ContentIndex;
use App\Jobs\GenerateContentVector;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Exception;

/**
 * 内容索引管理命令
 */
class IndexContentCommand extends Command
{
    /**
     * 命令签名
     *
     * @var string
     */
    protected $signature = 'content:index 
                            {--type= : 指定内容类型 (article,video,course_lesson)}
                            {--id= : 指定内容ID}
                            {--force : 强制重新索引}
                            {--vector : 同时生成向量}
                            {--batch=100 : 批处理大小}
                            {--dry-run : 仅显示将要处理的内容，不实际执行}';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = '索引内容到搜索系统';

    /**
     * 支持的内容类型映射
     *
     * @var array
     */
    protected array $typeMapping = [
        'article' => Article::class,
        'video' => Video::class,
        'course_lesson' => CourseLesson::class,
    ];

    /**
     * 执行命令
     *
     * @return int
     */
    public function handle(): int
    {
        $this->info('开始内容索引任务...');
        
        try {
            $type = $this->option('type');
            $id = $this->option('id');
            $force = $this->option('force');
            $generateVector = $this->option('vector');
            $batchSize = (int) $this->option('batch');
            $dryRun = $this->option('dry-run');

            // 验证参数
            if ($type && !isset($this->typeMapping[$type])) {
                $this->error("不支持的内容类型: {$type}");
                $this->info("支持的类型: " . implode(', ', array_keys($this->typeMapping)));
                return 1;
            }

            if ($dryRun) {
                $this->warn('这是一次试运行，不会实际执行索引操作');
            }

            // 处理单个内容
            if ($id) {
                return $this->indexSingleContent($type, $id, $force, $generateVector, $dryRun);
            }

            // 批量处理
            return $this->indexBatchContent($type, $force, $generateVector, $batchSize, $dryRun);

        } catch (Exception $e) {
            $this->error("索引失败: {$e->getMessage()}");
            Log::error('内容索引命令失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return 1;
        }
    }

    /**
     * 索引单个内容
     *
     * @param string|null $type 内容类型
     * @param string $id 内容ID
     * @param bool $force 是否强制重新索引
     * @param bool $generateVector 是否生成向量
     * @param bool $dryRun 是否试运行
     * @return int
     */
    protected function indexSingleContent(
        ?string $type,
        string $id,
        bool $force,
        bool $generateVector,
        bool $dryRun
    ): int {
        if (!$type) {
            $this->error('索引单个内容时必须指定类型');
            return 1;
        }

        $modelClass = $this->typeMapping[$type];
        $model = $modelClass::find($id);

        if (!$model) {
            $this->error("找不到 {$type} ID: {$id}");
            return 1;
        }

        $this->info("处理 {$type} ID: {$id} - {$model->getContentIndexTitle()}");

        if ($dryRun) {
            $this->info('试运行模式：跳过实际索引操作');
            return 0;
        }

        try {
            // 检查是否需要索引
            if (!$force && !$this->shouldIndex($model)) {
                $this->info('内容无需索引，跳过');
                return 0;
            }

            // 执行索引
            $model->updateContentIndex();
            $this->info('✓ 索引完成');

            // 生成向量
            if ($generateVector) {
                $contentIndex = $model->contentIndex;
                if ($contentIndex) {
                    GenerateContentVector::dispatch($contentIndex);
                    $this->info('✓ 向量生成任务已加入队列');
                }
            }

            return 0;

        } catch (Exception $e) {
            $this->error("索引失败: {$e->getMessage()}");
            return 1;
        }
    }

    /**
     * 批量索引内容
     *
     * @param string|null $type 内容类型
     * @param bool $force 是否强制重新索引
     * @param bool $generateVector 是否生成向量
     * @param int $batchSize 批处理大小
     * @param bool $dryRun 是否试运行
     * @return int
     */
    protected function indexBatchContent(
        ?string $type,
        bool $force,
        bool $generateVector,
        int $batchSize,
        bool $dryRun
    ): int {
        $types = $type ? [$type] : array_keys($this->typeMapping);
        $totalProcessed = 0;
        $totalErrors = 0;

        foreach ($types as $currentType) {
            $this->info("处理类型: {$currentType}");
            
            $result = $this->processTypeContent(
                $currentType,
                $force,
                $generateVector,
                $batchSize,
                $dryRun
            );
            
            $totalProcessed += $result['processed'];
            $totalErrors += $result['errors'];
        }

        $this->info("批量索引完成:");
        $this->info("- 总处理数: {$totalProcessed}");
        $this->info("- 错误数: {$totalErrors}");

        return $totalErrors > 0 ? 1 : 0;
    }

    /**
     * 处理特定类型的内容
     *
     * @param string $type 内容类型
     * @param bool $force 是否强制重新索引
     * @param bool $generateVector 是否生成向量
     * @param int $batchSize 批处理大小
     * @param bool $dryRun 是否试运行
     * @return array
     */
    protected function processTypeContent(
        string $type,
        bool $force,
        bool $generateVector,
        int $batchSize,
        bool $dryRun
    ): array {
        $modelClass = $this->typeMapping[$type];
        $query = $modelClass::query();

        // 如果不是强制模式，只处理需要索引的内容
        if (!$force) {
            $query->where(function ($q) {
                // 这里可以添加更复杂的条件来判断哪些内容需要重新索引
                // 例如：最近更新的内容、没有索引的内容等
            });
        }

        $total = $query->count();
        
        if ($total === 0) {
            $this->info("没有需要处理的 {$type} 内容");
            return ['processed' => 0, 'errors' => 0];
        }

        $this->info("找到 {$total} 个 {$type} 内容需要处理");

        if ($dryRun) {
            $this->info('试运行模式：显示前10个将要处理的内容');
            $samples = $query->limit(10)->get();
            foreach ($samples as $sample) {
                $this->line("- ID: {$sample->id} - {$sample->getContentIndexTitle()}");
            }
            return ['processed' => $total, 'errors' => 0];
        }

        $processed = 0;
        $errors = 0;
        $progressBar = $this->output->createProgressBar($total);
        $progressBar->start();

        $query->chunk($batchSize, function ($models) use (
            &$processed,
            &$errors,
            $generateVector,
            $progressBar,
            $force
        ) {
            foreach ($models as $model) {
                try {
                    // 检查是否需要索引
                    if (!$force && !$this->shouldIndex($model)) {
                        $progressBar->advance();
                        continue;
                    }

                    // 执行索引
                    $model->updateContentIndex();
                    $processed++;

                    // 生成向量
                    if ($generateVector) {
                        $contentIndex = $model->contentIndex;
                        if ($contentIndex) {
                            GenerateContentVector::dispatch($contentIndex);
                        }
                    }

                } catch (Exception $e) {
                    $errors++;
                    Log::error("索引内容失败", [
                        'type' => get_class($model),
                        'id' => $model->id,
                        'error' => $e->getMessage(),
                    ]);
                }

                $progressBar->advance();
            }
        });

        $progressBar->finish();
        $this->newLine();

        $this->info("类型 {$type} 处理完成:");
        $this->info("- 处理数: {$processed}");
        $this->info("- 错误数: {$errors}");

        return ['processed' => $processed, 'errors' => $errors];
    }

    /**
     * 检查模型是否需要索引
     *
     * @param mixed $model
     * @return bool
     */
    protected function shouldIndex($model): bool
    {
        // 检查模型是否应该被索引
        if (method_exists($model, 'shouldBeIndexed') && !$model->shouldBeIndexed()) {
            return false;
        }

        // 检查是否已有最新的索引
        $contentIndex = $model->contentIndex;
        if ($contentIndex && $contentIndex->updated_at >= $model->updated_at) {
            return false;
        }

        return true;
    }
} 