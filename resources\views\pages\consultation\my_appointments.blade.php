@extends('layouts.app')

@section('title', '我的预约 - 心理咨询平台')

@section('custom-styles')
<style>
    body {
        background-color: #f8f9fa;
    }
    
    .container {
        max-width: 900px;
        margin: 0 auto;
        padding-bottom: 80px !important; /* 为底部菜单栏预留空间 */
    }
    
    .page-header {
        display: flex;
        align-items: center;
        background: linear-gradient(135deg, #5b7cef 0%, #3c67e3 100%);
        color: white;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        border-radius: 0 0 10px 10px;
        padding: 12px 15px;
        margin-bottom: 15px;
    }
    
    .back-button {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 30px;
        height: 30px;
    }
    
    .back-icon {
        border-top: 2px solid white;
        border-left: 2px solid white;
        width: 12px;
        height: 12px;
        transform: rotate(-45deg);
    }
    
    .page-header h1 {
        font-size: 18px;
        font-weight: 500;
        margin: 0 auto;
        text-align: center;
        flex-grow: 1;
        margin-left: -30px;
        text-shadow: 0 1px 2px rgba(0,0,0,0.1);
    }
    
    .appointment-list {
        padding: 15px;
    }
    
    .appointment-item {
        background-color: #fff;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        margin-bottom: 18px;
        overflow: hidden;
        border: 1px solid rgba(0, 0, 0, 0.03);
    }
    
    .appointment-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 14px 16px;
        border-bottom: 1px solid #f0f0f0;
        background-color: #fafafa;
    }
    
    .appointment-title {
        font-weight: 600;
        font-size: 16px;
        color: #333;
    }
    
    .appointment-status {
        padding: 4px 10px;
        border-radius: 16px;
        font-size: 12px;
        font-weight: 500;
    }
    
    .status-pending {
        background-color: #FFF3E0;
        color: #FF9800;
    }
    
    .status-confirmed {
        background-color: #E8F5E9;
        color: #4CAF50;
    }
    
    .status-cancelled {
        background-color: #FFEBEE;
        color: #F44336;
    }
    
    .status-ongoing {
        background-color: #E3F2FD;
        color: #2196F3;
    }
    
    .status-completed {
        background-color: #E0E0E0;
        color: #757575;
    }
    
    .appointment-content {
        padding: 16px;
    }
    
    .appointment-info {
        display: flex;
        margin-bottom: 14px;
    }
    
    .counselor-avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        overflow: hidden;
        margin-right: 14px;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
        border: 2px solid #fff;
    }
    
    .counselor-avatar img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    
    .appointment-details {
        flex-grow: 1;
    }
    
    .counselor-name {
        font-weight: 600;
        margin-bottom: 6px;
        font-size: 16px;
        color: #333;
    }
    
    .appointment-meta {
        font-size: 14px;
        color: #666;
        margin-bottom: 6px;
        line-height: 1.5;
    }
    
    .type-badge {
        display: inline-block;
        padding: 3px 10px;
        border-radius: 20px;
        background-color: #eef3ff;
        font-size: 12px;
        margin-right: 8px;
        color: #3c67e3;
        font-weight: 500;
    }
    
    .appointment-issue {
        background-color: #f8f9fa;
        padding: 14px;
        border-radius: 8px;
        margin: 12px 0;
        font-size: 14px;
        color: #444;
        line-height: 1.6;
        border: 1px solid #eee;
    }
    
    .appointment-actions {
        display: flex;
        justify-content: flex-end;
        gap: 12px;
        margin-top: 15px;
    }
    
    .action-btn {
        padding: 8px 16px;
        border-radius: 8px;
        font-size: 14px;
        text-decoration: none;
        text-align: center;
        font-weight: 500;
        transition: all 0.2s ease;
    }
    
    .start-btn {
        background-color: #3c67e3;
        color: white;
        border: none;
        box-shadow: 0 2px 4px rgba(60, 103, 227, 0.2);
    }
    
    .start-btn:hover {
        background-color: #345bd4;
        box-shadow: 0 3px 6px rgba(60, 103, 227, 0.3);
    }
    
    .cancel-btn {
        background-color: #f5f5f5;
        color: #555;
        border: 1px solid #e0e0e0;
    }
    
    .cancel-btn:hover {
        background-color: #eee;
    }
    
    .no-appointments {
        text-align: center;
        padding: 40px 30px;
        color: #666;
        background-color: #fff;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        margin-top: 20px;
    }
    
    .empty-appointment-icon {
        font-size: 60px;
        color: #ccc;
        margin-bottom: 15px;
    }
    
    .pagination-container {
        margin: 20px 0;
        display: flex;
        justify-content: center;
    }
    
    .pagination-container .pagination {
        display: flex;
        list-style: none;
        padding: 0;
        margin: 0;
        gap: 5px;
    }
    
    .pagination-container .page-item {
        margin: 0 2px;
    }
    
    .pagination-container .page-link {
        padding: 8px 12px;
        border-radius: 6px;
        background-color: #fff;
        color: #333;
        text-decoration: none;
        border: 1px solid #eee;
        font-size: 14px;
    }
    
    .pagination-container .active .page-link {
        background-color: #3c67e3;
        color: #fff;
        border-color: #3c67e3;
    }
</style>
@endsection

@section('content')
<div class="page-header">
    <a href="{{ route('my') }}" class="back-button">
        <div class="back-icon"></div>
    </a>
    <h1>我的预约</h1>
    <div style="width: 30px;"></div>
</div>

<div class="container">
    <div class="appointment-list">
        @if(count($appointments) > 0)
            @foreach($appointments as $appointment)
            <div class="appointment-item">
                <div class="appointment-header">
                    <div class="appointment-title">
                        心理咨询预约
                    </div>
                    <div class="appointment-status 
                        @if($appointment->status == 0) status-cancelled
                        @elseif($appointment->status == 1) status-pending
                        @elseif($appointment->status == 2) status-confirmed
                        @elseif($appointment->status == 3) status-ongoing
                        @elseif($appointment->status == 4) status-completed
                        @endif">
                        {{ $appointment->status_text }}
                    </div>
                </div>
                <div class="appointment-content">
                    <div class="appointment-info">
                        <div class="counselor-avatar">
                            <img src="{{ $appointment->counselor->avatar ? asset('storage/'.$appointment->counselor->avatar) : asset('images/default-avatar.jpg') }}" alt="{{ $appointment->counselor->name }}">
                        </div>
                        <div class="appointment-details">
                            <div class="counselor-name">{{ $appointment->counselor->name }}</div>
                            <div class="appointment-meta">
                                <span class="type-badge">
                                    @if($appointment->consultation_type == 1)
                                        文字咨询
                                    @elseif($appointment->consultation_type == 2)
                                        语音咨询
                                    @elseif($appointment->consultation_type == 3)
                                        视频咨询
                                    @endif
                                </span>
                                <span>{{ date('Y-m-d H:i', strtotime($appointment->appointment_time)) }}</span>
                            </div>
                            <div class="appointment-meta">
                                <!--咨询时长：{{ $appointment->duration }}分钟-->
                                回复时间: 最晚24小时| 
                                价格：{{ $appointment->price > 0 ? '¥'.$appointment->price : '免费' }}
                            </div>
                        </div>
                    </div>
                    
                    <div class="appointment-issue">
                        <strong>问题描述：</strong>
                        {{ $appointment->issue_description }}
                    </div>
                    
                    <div class="appointment-actions">
                        @if($appointment->status == 2 && $appointment->canStart())
                            <a href="{{ route('consultation.start_consultation', ['id' => $appointment->id]) }}" class="action-btn start-btn">进入咨询</a>
                        @endif
                        
                        @if(($appointment->status == 1 || $appointment->status == 2) && $appointment->canCancel())
                            <form action="{{ route('consultation.cancel_appointment', ['id' => $appointment->id]) }}" method="POST" style="display: inline;">
                                @csrf
                                <button type="submit" class="action-btn cancel-btn" onclick="return confirm('确定要取消预约吗？')">取消预约</button>
                            </form>
                        @endif
                    </div>
                </div>
            </div>
            @endforeach
            
            <!-- 分页 -->
            <div class="pagination-container">
                {{ $appointments->links() }}
            </div>
        @else
            <div class="no-appointments">
                <div class="empty-appointment-icon">📅</div>
                <p>暂无预约记录</p>
                <a href="{{ route('consultation.counselors') }}" class="action-btn start-btn" style="display: inline-block; margin-top: 10px;">去预约咨询</a>
            </div>
        @endif
    </div>
</div>
@endsection
