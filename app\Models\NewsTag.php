<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class NewsTag extends Model
{
    use HasFactory, HasDateTimeFormatter;

    /**
     * 表名
     *
     * @var string
     */
    protected $table = 'news_tags';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'name', 'slug'
    ];

    /**
     * 获取拥有此标签的新闻
     */
    public function news()
    {
        return $this->belongsToMany(News::class, 'news_tag', 'news_tag_id', 'news_id')
            ->withTimestamps();
    }
}
