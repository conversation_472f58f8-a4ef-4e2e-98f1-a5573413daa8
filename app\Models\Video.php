<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Dcat\Admin\Models\Administrator;
use App\Models\Traits\HasContentIndex;
use App\Contracts\HasContentIndex as HasContentIndexContract;

class Video extends Model implements HasContentIndexContract
{
    use HasFactory, SoftDeletes, HasDateTimeFormatter, HasContentIndex;

    protected $fillable = [
        'title',
        'description',
        'content',
        'image',
        'video_url',
        'video_duration',
        'category_id',
        'author_id',
        'views',
        'is_recommended',
        'status',
        'publish_time'
    ];

    protected $dates = [
        'publish_time',
        'created_at',
        'updated_at',
        'deleted_at'
    ];

    // 状态常量
    const STATUS_DRAFT = 0;      // 草稿
    const STATUS_PENDING = 1;    // 待审核
    const STATUS_PUBLISHED = 2;  // 已发布
    const STATUS_REJECTED = 3;   // 已拒绝
    
    /**
     * 获取状态列表
     */
    public static function getStatusMap()
    {
        return [
            self::STATUS_DRAFT => '草稿',
            self::STATUS_PENDING => '待审核',
            self::STATUS_PUBLISHED => '已发布',
            self::STATUS_REJECTED => '已拒绝',
        ];
    }

    // 关联分类
    public function category()
    {
        return $this->belongsTo(VideoCategory::class, 'category_id');
    }

    // 关联作者
    public function author()
    {
        return $this->belongsTo(Administrator::class, 'author_id');
    }

    // 关联标签
    public function tags()
    {
        return $this->belongsToMany(VideoTag::class, 'video_tag', 'video_id', 'tag_id');
    }

    // 关联审核记录
    public function auditLogs()
    {
        return $this->hasMany(VideoAuditLog::class, 'video_id');
    }

    // 关联评论
    public function comments()
    {
        return $this->hasMany(VideoComment::class, 'video_id')->whereNull('parent_id');
    }

    // 获取状态文本
    public function getStatusTextAttribute()
    {
        $statusMap = [
            self::STATUS_DRAFT => '草稿',
            self::STATUS_PENDING => '待审核',
            self::STATUS_PUBLISHED => '已发布',
            self::STATUS_REJECTED => '已拒绝'
        ];

        return $statusMap[$this->status] ?? '未知状态';
    }

    // 增加观看次数
    public function incrementViews()
    {
        $this->increment('views');
        return $this->views;
    }

    /**
     * 实现HasContentIndex接口：获取内容索引类型
     */
    public function getContentIndexType(): string
    {
        return 'video';
    }

    /**
     * 实现HasContentIndex接口：获取内容索引标题
     */
    public function getContentIndexTitle(): string
    {
        return $this->title ?? '';
    }

    /**
     * 实现HasContentIndex接口：获取内容索引摘要
     */
    public function getContentIndexSummary(): ?string
    {
        return $this->description;
    }

    /**
     * 实现HasContentIndex接口：获取内容索引元数据
     */
    public function getContentIndexMetadata(): array
    {
        $metadata = [
            'category' => $this->category?->name,
            'author' => $this->author?->name,
            'views' => $this->views,
            'is_recommended' => $this->is_recommended,
            'status' => $this->status,
            'status_text' => $this->status_text,
            'publish_time' => $this->publish_time ? (is_string($this->publish_time) ? $this->publish_time : $this->publish_time->toISOString()) : null,
            'video_duration' => $this->video_duration,
            'video_url' => $this->video_url,
        ];

        // 添加标签信息
        if ($this->relationLoaded('tags')) {
            $metadata['tags'] = $this->tags->pluck('name')->toArray();
        }

        return array_filter($metadata, fn($value) => $value !== null);
    }

    /**
     * 实现HasContentIndex接口：检查是否应该被索引
     */
    public function shouldBeIndexed(): bool
    {
        return $this->status === self::STATUS_PUBLISHED;
    }

    /**
     * 实现HasContentIndex接口：获取内容索引URL
     */
    public function getContentIndexUrl(): ?string
    {
        try {
            // 尝试生成路由，如果失败则返回null
            if (\Route::has('videos.show')) {
                return route('videos.show', $this->id);
            }
            // 如果路由不存在，返回一个简单的URL
            return url("/videos/{$this->id}");
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * 实现HasContentIndex接口：获取用于向量生成的内容
     */
    public function getContentForVector(): string
    {
        $parts = [];
        
        if ($this->title) {
            $parts[] = $this->title;
        }
        
        if ($this->description) {
            $parts[] = $this->description;
        }
        
        if ($this->content) {
            // 清理HTML标签并截取前2000字符
            $cleanContent = strip_tags($this->content);
            $parts[] = mb_substr($cleanContent, 0, 2000);
        }
        
        return implode(' ', $parts);
    }
}
