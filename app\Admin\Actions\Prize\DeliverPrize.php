<?php

namespace App\Admin\Actions\Prize;

use Dcat\Admin\Grid\RowAction;
use Illuminate\Http\Request;
use Dcat\Admin\Admin;

class DeliverPrize extends RowAction
{
    protected $title = '标记送达';
    
    public function handle(Request $request)
    {
        // 获取当前行数据的ID
        $id = $this->getKey();
        
        // 查询数据库
        $model = \App\Models\QuizPrizeWinner::find($id);
        
        if (!$model) {
            return $this->response()->error('找不到对应的奖品记录');
        }
        
        // 仅在已发货状态才能标记为已送达
        if ($model->status !== 'shipped') {
            return $this->response()->error('只有已发货的奖品才能标记为送达');
        }
        
        // 更新模型
        $model->status = 'delivered';
        $model->admin_notes = ($model->admin_notes ? $model->admin_notes . "\n" : '') . 
                              Admin::user()->name . ' 于 ' . now() . ' 标记为已送达';
        $model->save();
        
        return $this->response()
                    ->success('奖品已标记为已送达')
                    ->refresh();
    }
    
    /**
     * 确认消息
     */
    public function confirm()
    {
        return ['送达确认', '您确定要将该奖品标记为已送达吗？'];
    }
}
