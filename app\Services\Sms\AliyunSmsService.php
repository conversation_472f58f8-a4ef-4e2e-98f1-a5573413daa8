<?php

namespace App\Services\Sms;

use Illuminate\Support\Facades\Log;
use AlibabaCloud\Client\AlibabaCloud;
use AlibabaCloud\Client\Exception\ClientException;
use AlibabaCloud\Client\Exception\ServerException;
use Exception;

/**
 * 阿里云短信服务实现
 * 实际接入阿里云短信服务
 */
class AliyunSmsService implements SmsServiceInterface
{
    /**
     * 阿里云访问密钥ID
     * @var string
     */
    protected $accessKeyId;

    /**
     * 阿里云访问密钥Secret
     * @var string
     */
    protected $accessKeySecret;

    /**
     * 短信签名
     * @var string
     */
    protected $signName;

    /**
     * 验证码短信模板
     * @var array
     */
    protected $verificationTemplates = [];

    /**
     * 构造函数，初始化参数
     */
    public function __construct()
    {
        $this->accessKeyId = config('sms.aliyun.access_key_id');
        $this->accessKeySecret = config('sms.aliyun.access_key_secret');
        $this->signName = config('sms.aliyun.sign_name');
        
        // 从配置中加载验证码模板
        $this->verificationTemplates = [
            1 => config('sms.aliyun.templates.verification.register'),
            2 => config('sms.aliyun.templates.verification.login'),
            3 => config('sms.aliyun.templates.verification.password'),
            4 => config('sms.aliyun.templates.verification.binding'),
        ];
    }

    /**
     * 发送验证码短信
     *
     * @param string $mobile 手机号
     * @param string $code 验证码
     * @param int $type 验证码类型 (1:注册, 2:登录, 3:修改密码, 4:绑定手机)
     * @return array 返回发送结果
     */
    public function sendVerificationCode(string $mobile, string $code, int $type = 1): array
    {
        try {
            // 获取模板编码
            $templateCode = $this->verificationTemplates[$type] ?? $this->verificationTemplates[1];
            
            // 构建请求参数
            $params = [
                'code' => $code
            ];
            
            // 调用阿里云发送方法
            return $this->send($mobile, $params, $templateCode);
        } catch (Exception $e) {
            Log::error('阿里云短信发送失败', [
                'mobile' => $mobile,
                'code' => $code,
                'type' => $type,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'message' => '短信发送失败: ' . $e->getMessage(),
                'provider' => $this->getProviderName()
            ];
        }
    }
    
    /**
     * 发送通知短信
     *
     * @param string $mobile 手机号
     * @param array $params 短信参数
     * @param string $templateCode 短信模板代码
     * @return array 返回发送结果
     */
    public function sendNotification(string $mobile, array $params, string $templateCode): array
    {
        try {
            // 调用阿里云发送方法
            return $this->send($mobile, $params, $templateCode);
        } catch (Exception $e) {
            Log::error('阿里云通知短信发送失败', [
                'mobile' => $mobile,
                'params' => $params,
                'template_code' => $templateCode,
                'error' => $e->getMessage()
            ]);
            
            return [
                'success' => false,
                'message' => '短信发送失败: ' . $e->getMessage(),
                'provider' => $this->getProviderName()
            ];
        }
    }
    
    /**
     * 调用阿里云SDK发送短信
     * 
     * @param string $mobile 手机号
     * @param array $params 参数
     * @param string $templateCode 模板代码
     * @return array
     * @throws ClientException
     * @throws ServerException
     */
    protected function send(string $mobile, array $params, string $templateCode): array
    {
        try {
            // 初始化AlibabaCloud
            AlibabaCloud::accessKeyClient($this->accessKeyId, $this->accessKeySecret)
                ->regionId('cn-hangzhou')
                ->asDefaultClient();
                
            // 调用云通信发送短信
            $result = AlibabaCloud::rpc()
                ->product('Dysmsapi')
                ->scheme('https')
                ->version('2017-05-25')
                ->action('SendSms')
                ->method('POST')
                ->host('dysmsapi.aliyuncs.com')
                ->options([
                    'query' => [
                        'RegionId' => 'cn-hangzhou',
                        'PhoneNumbers' => $mobile,
                        'SignName' => $this->signName,
                        'TemplateCode' => $templateCode,
                        'TemplateParam' => json_encode($params, JSON_UNESCAPED_UNICODE),
                    ],
                ])
                ->request();
                
            $resultArr = $result->toArray();
            
            // 记录发送结果
            Log::info('阿里云短信发送成功', [
                'mobile' => $mobile,
                'template_code' => $templateCode,
                'result' => $resultArr,
            ]);
            
            // 检查响应结果
            if (isset($resultArr['Code']) && $resultArr['Code'] === 'OK') {
                return [
                    'success' => true,
                    'message' => '短信发送成功',
                    'provider' => $this->getProviderName(),
                    'request_id' => $resultArr['RequestId'] ?? '',
                    'bizId' => $resultArr['BizId'] ?? '',
                ];
            } else {
                $errorMessage = $resultArr['Message'] ?? '短信发送失败';
                $errorCode = $resultArr['Code'] ?? 'UnknownError';
                
                Log::error('阿里云短信发送失败', [
                    'code' => $errorCode,
                    'message' => $errorMessage,
                    'mobile' => $mobile,
                    'template_code' => $templateCode,
                ]);
                
                return [
                    'success' => false,
                    'message' => $errorMessage,
                    'code' => $errorCode,
                    'provider' => $this->getProviderName(),
                ];
            }
        } catch (ClientException | ServerException $e) {
            Log::error('阿里云SDK调用异常', [
                'error' => $e->getMessage(),
                'mobile' => $mobile,
                'template_code' => $templateCode,
            ]);
            
            return [
                'success' => false,
                'message' => '短信服务异常: ' . $e->getMessage(),
                'provider' => $this->getProviderName(),
            ];
        }
    }

    /**
     * 获取服务商名称
     * 
     * @return string
     */
    public function getProviderName(): string
    {
        return 'aliyun';
    }
}
