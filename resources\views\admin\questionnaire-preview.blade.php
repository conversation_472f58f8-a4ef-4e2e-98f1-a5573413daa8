<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>问卷预览 - {{ $preview['questionnaire']['title'] }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .preview-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .questionnaire-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .questionnaire-title {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .questionnaire-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-top: 20px;
        }
        .meta-item {
            background: rgba(255,255,255,0.2);
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
        }
        .question-card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-left: 4px solid #667eea;
        }
        .question-number {
            background: #667eea;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
        }
        .question-type {
            display: inline-block;
            background: #e3f2fd;
            color: #1976d2;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            margin-left: 10px;
        }
        .question-content {
            font-size: 1.1rem;
            font-weight: 500;
            margin: 15px 0;
            line-height: 1.6;
        }
        .options-list {
            margin-top: 20px;
        }
        .option-item {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 12px 15px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            transition: all 0.3s ease;
        }
        .option-item:hover {
            background: #e3f2fd;
            border-color: #2196f3;
        }
        .option-radio {
            width: 18px;
            height: 18px;
            border: 2px solid #ddd;
            border-radius: 50%;
            margin-right: 12px;
            background: white;
        }
        .option-checkbox {
            width: 18px;
            height: 18px;
            border: 2px solid #ddd;
            border-radius: 3px;
            margin-right: 12px;
            background: white;
        }
        .option-content {
            flex: 1;
            font-size: 1rem;
        }
        .option-score {
            background: #fff3e0;
            color: #f57c00;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        .stats-panel {
            background: white;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        .stat-item {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
        }
        .stat-label {
            color: #666;
            margin-top: 5px;
        }
        .action-buttons {
            text-align: center;
            margin-top: 30px;
        }
        .btn-custom {
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 500;
            margin: 0 10px;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }
        .btn-primary-custom {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
        }
        .btn-primary-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
            color: white;
        }
        .btn-secondary-custom {
            background: #6c757d;
            color: white;
            border: none;
        }
        .btn-secondary-custom:hover {
            background: #5a6268;
            color: white;
        }
    </style>
</head>
<body>
    <div class="preview-container">
        <!-- 问卷头部信息 -->
        <div class="questionnaire-header">
            <div class="questionnaire-title">
                <i class="fas fa-clipboard-list me-3"></i>
                {{ $preview['questionnaire']['title'] }}
            </div>
            @if($preview['questionnaire']['description'])
                <div class="questionnaire-description">
                    {{ $preview['questionnaire']['description'] }}
                </div>
            @endif
            <div class="questionnaire-meta">
                <div class="meta-item">
                    <i class="fas fa-tag me-2"></i>
                    {{ $preview['questionnaire']['domain'] }}
                </div>
                <div class="meta-item">
                    <i class="fas fa-clock me-2"></i>
                    预计 {{ $preview['questionnaire']['est_duration'] }} 分钟
                </div>
                <div class="meta-item">
                    <i class="fas fa-list-ol me-2"></i>
                    共 {{ count($preview['questions']) }} 题
                </div>
            </div>
        </div>

        <!-- 统计信息面板 -->
        <div class="stats-panel">
            <h5 class="mb-4">
                <i class="fas fa-chart-bar me-2"></i>
                问卷统计信息
            </h5>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">{{ $stats['total_questions'] }}</div>
                    <div class="stat-label">题目总数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ $stats['total_options'] }}</div>
                    <div class="stat-label">选项总数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ $stats['avg_options_per_question'] }}</div>
                    <div class="stat-label">平均选项数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">{{ $stats['total_responses'] }}</div>
                    <div class="stat-label">回答次数</div>
                </div>
            </div>
        </div>

        <!-- 题目列表 -->
        @foreach($preview['questions'] as $index => $question)
            <div class="question-card">
                <div class="d-flex align-items-center mb-3">
                    <span class="question-number">{{ $index + 1 }}</span>
                    <span class="question-type">
                        @switch($question['type'])
                            @case('single')
                                <i class="fas fa-dot-circle me-1"></i>单选题
                                @break
                            @case('multiple')
                                <i class="fas fa-check-square me-1"></i>多选题
                                @break
                            @case('scale')
                                <i class="fas fa-star me-1"></i>量表题
                                @break
                        @endswitch
                    </span>
                </div>
                
                <div class="question-content">
                    {{ $question['content'] }}
                </div>
                
                <div class="options-list">
                    @foreach($question['options'] as $optionIndex => $option)
                        <div class="option-item">
                            @if($question['type'] === 'multiple')
                                <div class="option-checkbox"></div>
                            @else
                                <div class="option-radio"></div>
                            @endif
                            <div class="option-content">
                                {{ chr(65 + $optionIndex) }}. {{ $option['content'] }}
                            </div>
                            <div class="option-score">
                                {{ $option['score_value'] }}分
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        @endforeach

        <!-- 操作按钮 -->
        <div class="action-buttons">
            <a href="{{ admin_url('assessment-questionnaires/'.$preview['questionnaire']['id'].'/edit') }}" 
               class="btn-custom btn-primary-custom">
                <i class="fas fa-edit me-2"></i>编辑问卷
            </a>
            <a href="{{ admin_url('assessment-questionnaires') }}" 
               class="btn-custom btn-secondary-custom">
                <i class="fas fa-arrow-left me-2"></i>返回列表
            </a>
            <button onclick="window.print()" class="btn-custom btn-secondary-custom">
                <i class="fas fa-print me-2"></i>打印预览
            </button>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 打印样式优化
        window.addEventListener('beforeprint', function() {
            document.body.style.background = 'white';
            document.querySelectorAll('.action-buttons').forEach(el => el.style.display = 'none');
        });
        
        window.addEventListener('afterprint', function() {
            document.body.style.background = '#f8f9fa';
            document.querySelectorAll('.action-buttons').forEach(el => el.style.display = 'block');
        });
    </script>
</body>
</html> 