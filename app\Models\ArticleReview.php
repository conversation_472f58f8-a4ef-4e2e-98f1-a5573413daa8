<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class ArticleReview extends Model
{
    protected $fillable = [
        'article_id', 'reviewer_id', 'status', 'comments', 'reviewed_at'
    ];
    
    protected $dates = [
        'reviewed_at',
    ];
    
    // 状态常量
    const STATUS_PENDING = 'pending'; // 待审核
    const STATUS_APPROVED = 'approved'; // 已批准
    const STATUS_REJECTED = 'rejected'; // 已拒绝
    
    /**
     * 获取状态列表
     */
    public static function getStatusMap()
    {
        return [
            self::STATUS_PENDING => '待审核',
            self::STATUS_APPROVED => '已批准',
            self::STATUS_REJECTED => '已拒绝',
        ];
    }
    
    /**
     * 获取关联的文章
     */
    public function article()
    {
        return $this->belongsTo(Article::class);
    }
    
    /**
     * 获取审核人
     */
    public function reviewer()
    {
        return $this->belongsTo(User::class, 'reviewer_id');
    }
}
