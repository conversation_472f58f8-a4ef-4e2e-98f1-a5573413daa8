<?php

namespace App\Admin\Actions;

use App\Models\CounselorSchedule;
use Dcat\Admin\Grid\BatchAction;
use Illuminate\Http\Request;
use Illuminate\Database\Eloquent\Collection;

class MarkScheduleUnavailable extends BatchAction
{
    // 确认弹窗信息
    public function confirm()
    {
        return '您确定要将所选记录标记为不可预约吗？';
    }

    // 处理请求
    public function handle(Collection $collection)
    {
        foreach ($collection as $model) {
            $model->is_available = 0;
            $model->save();
        }

        return $this->response()
            ->success('成功标记' . $collection->count() . '条记录为不可预约')
            ->refresh();
    }
}
