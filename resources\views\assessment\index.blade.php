@extends('layouts.app')

@section('title', '心理测评')

@section('custom-styles')
<link rel="stylesheet" href="{{ asset('css/assessment.css') }}">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
@endsection

@section('content')
<div class="assessment-container">
    <!-- 页面头部 -->
    <div class="assessment-header">
        <div class="header-content">
            <button class="back-btn" onclick="history.back()">
                <i class="fas fa-arrow-left"></i>
            </button>
            <h1 class="header-title">心理测评</h1>
            <div class="header-action"></div>
        </div>
    </div>

    <!-- 统计信息 -->
    <div class="stats-section">
        <div class="stats-grid">
            <div class="stat-card">
                <span class="stat-number">{{ $totalQuestionnaires }}</span>
                <span class="stat-label">测评量表</span>
            </div>
            <div class="stat-card">
                <span class="stat-number">{{ $totalResponses }}</span>
                <span class="stat-label">参与人次</span>
            </div>
            <div class="stat-card">
                <span class="stat-number">{{ count($groupedQuestionnaires) }}</span>
                <span class="stat-label">测评领域</span>
            </div>
            <!--<div class="stat-card">-->
            <!--    <span class="stat-string">心理减压</span>-->
            <!--    <span class="stat-label">减压模块</span>-->
            <!--</div>-->
        </div>
    </div>

    <!-- 问卷列表 -->
    <div class="questionnaire-list">
        @foreach($groupedQuestionnaires as $domain => $questionnaires)
            <h2 class="section-title">{{ $domain }}</h2>
            
            @foreach($questionnaires as $questionnaire)
                <div class="questionnaire-card">
                    <div class="card-header">
                        <div class="card-icon">
                            <i class="{{ App\Http\Controllers\AssessmentController::getDomainIcon($questionnaire->domain) }}"></i>
                        </div>
                        <div class="domain-badge">{{ App\Http\Controllers\AssessmentController::getDomainName($questionnaire->domain) }}</div>
                    </div>
                    
                    <div class="card-content">
                        <h3 class="card-title">{{ $questionnaire->title }}</h3>
                        <p class="card-description">{{ $questionnaire->description }}</p>
                        
                        <div class="card-meta">
                            <div class="meta-item">
                                <i class="fas fa-question-circle"></i>
                                <span>{{ $questionnaire->question_count }} 题</span>
                            </div>
                            <div class="meta-item">
                                <i class="fas fa-clock"></i>
                                <span>约 {{ $questionnaire->est_duration }} 分钟</span>
                            </div>
                        </div>
                        
                        <div class="card-footer">
                            <button class="qr-btn" onclick="showQRCode({{ $questionnaire->id }})">
                                <i class="fas fa-qrcode"></i>
                            </button>
                            <span class="response-count">{{ $questionnaire->responses_count ?? 0 }} 人已测评</span>
                            <button class="start-btn" onclick="startAssessment({{ $questionnaire->id }})">
                                开始测评
                            </button>
                        </div>
                    </div>
                </div>
            @endforeach
        @endforeach
    </div>
</div>
<div id="qrModal" class="qr-modal" style="display:none;">
    <div class="qr-modal-content">
        <span class="close" onclick="closeQRModal()">&times;</span>
        <div id="qrCode"></div>
    </div>
</div>
@endsection

@section('scripts')
<script src="https://cdnjs.cloudflare.com/ajax/libs/qrcodejs/1.0.0/qrcode.min.js"></script>

<script>

function startAssessment(id) {
    // 添加加载动画
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<span class="loading"></span> 加载中...';
    btn.disabled = true;
    
    // 跳转到问卷详情页
    setTimeout(() => {
        window.location.href = `/assessment/${id}`;
    }, 500);
}

// 页面加载完成后的动画
document.addEventListener('DOMContentLoaded', function() {
    // 为卡片添加交错动画
    const cards = document.querySelectorAll('.questionnaire-card');
    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
    });
    
    // 统计数字动画
    const statNumbers = document.querySelectorAll('.stat-number');
    statNumbers.forEach(number => {
        const finalValue = parseInt(number.textContent);
        let currentValue = 0;
        const increment = finalValue / 30;
        
        const timer = setInterval(() => {
            currentValue += increment;
            if (currentValue >= finalValue) {
                currentValue = finalValue;
                clearInterval(timer);
            }
            number.textContent = Math.floor(currentValue);
        }, 50);
    });
});

// 添加触摸反馈
document.querySelectorAll('.questionnaire-card').forEach(card => {
    card.addEventListener('touchstart', function() {
        this.style.transform = 'scale(0.98)';
    });
    
    card.addEventListener('touchend', function() {
        this.style.transform = '';
    });
});

// 生成并展示二维码
function showQRCode(id) {
    const modal   = document.getElementById('qrModal');
    const qrDiv   = document.getElementById('qrCode');

    // 目标链接
    const url = `${window.location.origin}/assessment/${id}`;
    
    // 清空上一次内容并生成新二维码
    qrDiv.innerHTML = '';
    new QRCode(qrDiv, {
        text: url,
        width: 200,
        height: 200,
        correctLevel: QRCode.CorrectLevel.H
    });

    
    // 显示弹窗
    modal.style.display = 'flex';
}

// 关闭弹窗
function closeQRModal() {
    document.getElementById('qrModal').style.display = 'none';
}

// 允许点击蒙层关闭
document.getElementById('qrModal').addEventListener('click', e => {
    if (e.target.id === 'qrModal') closeQRModal();
});

</script>
@endsection 