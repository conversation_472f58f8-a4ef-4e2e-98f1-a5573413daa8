@extends('layouts.app')

@section('title', $questionnaire->title . ' - 测评进行中')

@section('custom-styles')
<link rel="stylesheet" href="{{ asset('css/assessment.css') }}">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
@endsection

@section('content')
<div class="start-container">
    <!-- 页面头部 -->
    <div class="assessment-header">
        <div class="header-content">
            <button class="back-btn" onclick="confirmExit()">
                <i class="fas fa-arrow-left"></i>
            </button>
            <h1 class="header-title">{{ $questionnaire->title }}</h1>
            <div class="timer-display">
                <i class="fas fa-clock"></i>
                <span id="timer">00:00</span>
            </div>
        </div>
    </div>

    @if(!Auth::check())
    <!-- 登录提示 -->
    <div class="questionnaire-list">
        <div class="questionnaire-card">
            <div class="card-content" style="text-align: center; padding: 30px 20px;">
                <i class="fas fa-user-lock" style="font-size: 48px; color: #667eea; margin-bottom: 20px;"></i>
                <h3 style="color: #333; margin-bottom: 15px;">需要登录才能进行测评</h3>
                <p style="color: #666; margin-bottom: 25px;">为了保存您的测评结果，请先登录您的账户</p>
                <div style="display: flex; gap: 15px; justify-content: center;">
                    <button class="start-btn" onclick="goToLogin()" style="background: #667eea;">
                        <i class="fas fa-sign-in-alt"></i> 立即登录
                    </button>
                    <button class="start-btn" onclick="history.back()" style="background: #6c757d;">
                        <i class="fas fa-arrow-left"></i> 返回
                    </button>
                </div>
            </div>
        </div>
    </div>
    @else
    <!-- 进度条 -->
    <div class="progress-container">
        <div class="progress-bar">
            <div class="progress-fill" id="progressFill"></div>
        </div>
    </div>

    <!-- 问题区域 -->
    <div class="question-container">
        <div id="questionContent">
            <!-- 问题内容将通过JavaScript动态加载 -->
        </div>
    </div>

    <!-- 导航按钮 -->
    <div class="navigation-buttons">
        <button id="prevBtn" class="nav-btn secondary-btn" onclick="previousQuestion()" style="display: none;">
            <i class="fas fa-chevron-left"></i> 上一题
        </button>
        
        <button id="nextBtn" class="nav-btn primary-btn" onclick="nextQuestion()" disabled>
            下一题 <i class="fas fa-chevron-right"></i>
        </button>
        
        <button id="submitBtn" class="nav-btn submit-btn" onclick="submitAssessment()" style="display: none;" disabled>
            <i class="fas fa-check"></i> 提交测评
        </button>
    </div>
    @endif
</div>

<!-- 确认退出对话框 -->
<div id="exitModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; align-items: center; justify-content: center;">
    <div style="background: white; padding: 30px; border-radius: 15px; max-width: 400px; margin: 20px; text-align: center;">
        <i class="fas fa-exclamation-triangle" style="font-size: 48px; color: #f39c12; margin-bottom: 20px;"></i>
        <h3 style="margin-bottom: 15px; color: #333;">确认退出测评？</h3>
        <p style="color: #666; margin-bottom: 25px;">退出后当前进度将不会保存，需要重新开始测评。</p>
        <div style="display: flex; gap: 15px;">
            <button onclick="hideExitModal()" style="flex: 1; padding: 12px; border: 1px solid #ddd; background: #f8f9fa; color: #666; border-radius: 8px; cursor: pointer;">
                继续测评
            </button>
            <button onclick="exitAssessment()" style="flex: 1; padding: 12px; border: none; background: #dc3545; color: white; border-radius: 8px; cursor: pointer;">
                确认退出
            </button>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
// 测评数据
const questionnaire = @json($questionnaire);
const questions = @json($questions);
let currentQuestionIndex = 0;
let answers = {};
let startTime = new Date();

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    @if(Auth::check())
    loadQuestion(0);
    startTimer();
    @endif
});

// 加载问题
function loadQuestion(index) {
    const question = questions[index];
    const totalQuestions = questions.length;
    
    // 更新进度条
    const progress = ((index + 1) / totalQuestions) * 100;
    document.getElementById('progressFill').style.width = progress + '%';
    
    // 构建问题HTML
    const questionHtml = `
        <div class="question-number">第 ${index + 1} 题 / 共 ${totalQuestions} 题</div>
        <div class="question-text">${question.content}</div>
        <ul class="options-list">
            ${question.options.map((option, optionIndex) => `
                <li class="option-item">
                    <input type="radio" 
                           id="option_${option.id}" 
                           name="question_${question.id}" 
                           value="${option.id}" 
                           class="option-input"
                           ${answers[question.id] == option.id ? 'checked' : ''}
                           onchange="selectOption(${question.id}, ${option.id})">
                    <label for="option_${option.id}" class="option-label">
                        ${option.content}
                    </label>
                </li>
            `).join('')}
        </ul>
    `;
    
    document.getElementById('questionContent').innerHTML = questionHtml;
    
    // 更新按钮状态
    updateButtonStates();
}

// 选择选项
function selectOption(questionId, optionId) {
    answers[questionId] = optionId;
    updateButtonStates();
}

// 更新按钮状态
function updateButtonStates() {
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');
    const submitBtn = document.getElementById('submitBtn');
    
    // 上一题按钮
    if (currentQuestionIndex > 0) {
        prevBtn.style.display = 'block';
    } else {
        prevBtn.style.display = 'none';
    }
    
    // 检查当前题目是否已回答
    const currentQuestion = questions[currentQuestionIndex];
    const isAnswered = answers[currentQuestion.id] !== undefined;
    
    if (currentQuestionIndex < questions.length - 1) {
        // 不是最后一题
        nextBtn.style.display = 'block';
        submitBtn.style.display = 'none';
        nextBtn.disabled = !isAnswered;
    } else {
        // 最后一题
        nextBtn.style.display = 'none';
        submitBtn.style.display = 'block';
        submitBtn.disabled = !isAnswered;
    }
}

// 上一题
function previousQuestion() {
    if (currentQuestionIndex > 0) {
        currentQuestionIndex--;
        loadQuestion(currentQuestionIndex);
    }
}

// 下一题
function nextQuestion() {
    if (currentQuestionIndex < questions.length - 1) {
        currentQuestionIndex++;
        loadQuestion(currentQuestionIndex);
    }
}

// 提交测评
function submitAssessment() {
    // 检查是否所有题目都已回答
    const unansweredQuestions = questions.filter(q => answers[q.id] === undefined);
    
    if (unansweredQuestions.length > 0) {
        alert('请完成所有题目后再提交');
        return;
    }
    
    const submitBtn = document.getElementById('submitBtn');
    submitBtn.innerHTML = '<span class="loading"></span> 提交中...';
    submitBtn.disabled = true;
    
    // 准备提交数据
    const formData = new FormData();
    formData.append('questionnaire_id', questionnaire.id);
    
    // 添加答案
    Object.keys(answers).forEach(questionId => {
        formData.append(`answers[${questionId}]`, answers[questionId]);
    });
    
    console.log('开始提交测评', {
        questionnaire_id: questionnaire.id,
        answers: answers,
        total_questions: questions.length,
        answered_questions: Object.keys(answers).length
    });
    
    // 提交到服务器
    fetch('/assessment/submit', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': '{{ csrf_token() }}',
            'Accept': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: formData
    })
    .then(response => {
        console.log('收到服务器响应', {
            status: response.status,
            ok: response.ok,
            statusText: response.statusText
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('服务器返回数据', data);
        
        if (data.success) {
            console.log('提交成功，准备跳转到结果页');
            
            // 显示AI分析加载提示
            showAiAnalysisLoading();
            
            // 延迟跳转，让用户看到加载提示
            setTimeout(() => {
                console.log('跳转到结果页', `/assessment/result/${data.response_id}`);
                window.location.href = `/assessment/result/${data.response_id}`;
            }, 1000);
        } else {
            console.error('提交失败', data);
            
            if (data.redirect) {
                // 需要登录
                if (confirm(data.message + '\n\n点击确定前往登录页面')) {
                    window.location.href = data.redirect;
                } else {
                    submitBtn.innerHTML = '<i class="fas fa-check"></i> 提交测评';
                    submitBtn.disabled = false;
                }
            } else {
                alert(data.message || '提交失败，请重试');
                submitBtn.innerHTML = '<i class="fas fa-check"></i> 提交测评';
                submitBtn.disabled = false;
            }
        }
    })
    .catch(error => {
        console.error('提交过程中发生错误', error);
        alert('提交失败，请重试: ' + error.message);
        submitBtn.innerHTML = '<i class="fas fa-check"></i> 提交测评';
        submitBtn.disabled = false;
    });
}

// 显示AI分析加载提示
function showAiAnalysisLoading() {
    // 创建全屏加载遮罩
    const loadingOverlay = document.createElement('div');
    loadingOverlay.id = 'aiAnalysisLoading';
    loadingOverlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        z-index: 9999;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: white;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `;
    
    loadingOverlay.innerHTML = `
        <div style="text-align: center; max-width: 400px; padding: 20px;">
            <div style="margin-bottom: 30px;">
                <div style="
                    width: 80px;
                    height: 80px;
                    border: 4px solid rgba(255,255,255,0.3);
                    border-top: 4px solid white;
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                    margin: 0 auto 20px;
                "></div>
                <style>
                    @keyframes spin {
                        0% { transform: rotate(0deg); }
                        100% { transform: rotate(360deg); }
                    }
                </style>
            </div>
            
            <h2 style="margin: 0 0 15px 0; font-size: 24px; font-weight: 600;">
                <i class="fas fa-brain" style="margin-right: 10px;"></i>
                AI 正在分析您的答案
            </h2>
            
            <p style="margin: 0 0 20px 0; font-size: 16px; opacity: 0.9; line-height: 1.5;">
                我们的人工智能正在深度分析您的测评结果<br>
                为您生成个性化的心理健康报告
            </p>
            
            <div style="display: flex; align-items: center; justify-content: center; gap: 8px; font-size: 14px; opacity: 0.8;">
                <div style="
                    width: 8px;
                    height: 8px;
                    background: white;
                    border-radius: 50%;
                    animation: pulse 1.5s ease-in-out infinite;
                "></div>
                <div style="
                    width: 8px;
                    height: 8px;
                    background: white;
                    border-radius: 50%;
                    animation: pulse 1.5s ease-in-out 0.2s infinite;
                "></div>
                <div style="
                    width: 8px;
                    height: 8px;
                    background: white;
                    border-radius: 50%;
                    animation: pulse 1.5s ease-in-out 0.4s infinite;
                "></div>
                <span style="margin-left: 10px;">预计需要几秒钟</span>
                <style>
                    @keyframes pulse {
                        0%, 80%, 100% { opacity: 0.3; transform: scale(0.8); }
                        40% { opacity: 1; transform: scale(1); }
                    }
                </style>
            </div>
        </div>
    `;
    
    document.body.appendChild(loadingOverlay);
}

// 计时器
function startTimer() {
    setInterval(() => {
        const now = new Date();
        const elapsed = Math.floor((now - startTime) / 1000);
        const minutes = Math.floor(elapsed / 60);
        const seconds = elapsed % 60;
        document.getElementById('timer').textContent = 
            `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }, 1000);
}

// 确认退出
function confirmExit() {
    document.getElementById('exitModal').style.display = 'flex';
}

function hideExitModal() {
    document.getElementById('exitModal').style.display = 'none';
}

function exitAssessment() {
    window.location.href = '/assessment';
}

// 防止意外离开
window.addEventListener('beforeunload', function(e) {
    if (Object.keys(answers).length > 0) {
        e.preventDefault();
        e.returnValue = '您的测评进度将会丢失，确定要离开吗？';
    }
});

// 键盘快捷键
document.addEventListener('keydown', function(e) {
    if (e.key === 'ArrowLeft' && currentQuestionIndex > 0) {
        previousQuestion();
    } else if (e.key === 'ArrowRight' && currentQuestionIndex < questions.length - 1) {
        const currentQuestion = questions[currentQuestionIndex];
        if (answers[currentQuestion.id] !== undefined) {
            nextQuestion();
        }
    }
});

// 跳转到登录页面
function goToLogin() {
    window.location.href = '{{ route("login") }}';
}
</script>
@endsection 