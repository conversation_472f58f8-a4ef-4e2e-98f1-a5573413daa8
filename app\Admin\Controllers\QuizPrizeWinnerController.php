<?php

namespace App\Admin\Controllers;

use App\Models\QuizPrizeWinner;
use App\Models\QuizActivity;
use App\Models\QuizPrize;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Admin;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Content;

class QuizPrizeWinnerController extends AdminController
{
    protected $title = '获奖记录';

    protected function grid()
    {
        return Grid::make(new QuizPrizeWinner(), function (Grid $grid) {
            $grid->model()->with(['quizActivity', 'prize', 'user', 'attempt']);
            
            // 获取问答活动ID过滤
            $quizActivityId = request()->get('quiz_activity_id');
            if ($quizActivityId) {
                $grid->model()->where('quiz_activity_id', $quizActivityId);
                $quizActivity = QuizActivity::find($quizActivityId);
                if ($quizActivity) {
                    $this->title = "获奖记录 - {$quizActivity->title}";
                }
            }
            
            $grid->column('id', 'ID')->sortable();
            $grid->column('quiz_activity_id', '所属活动')->display(function () {
                return $this->quizActivity ? $this->quizActivity->title : '-';
            });
            $grid->column('prize_id', '奖品')->display(function () {
                return $this->prize ? $this->prize->name : '-';
            });
            $grid->column('user_id', '用户')->display(function () {
                $username = $this->user ? $this->user->name : '-';
                $userId = $this->user ? $this->user->id : '-';
                return "{$username} (ID: {$userId})";
            });
            $grid->column('winner_name', '获奖者姓名');
            $grid->column('contact_info', '联系方式');
            $grid->column('status', '状态')->using([
                'unclaimed' => '待领取',
                'pending' => '处理中',
                'claimed' => '已申领',
                'shipped' => '已发货',
                'delivered' => '已送达',
                'cancelled' => '已取消',
            ])->dot([
                'unclaimed' => 'warning',
                'pending' => 'info',
                'claimed' => 'primary',
                'shipped' => 'info',
                'delivered' => 'success',
                'cancelled' => 'danger',
            ]);
            
            // 按照Dcat Admin文档使用Row Action类
            $grid->actions(function ($actions) {
                // 移除默认按钮
                $actions->disableDelete();
                $actions->disableEdit();
                $actions->disableQuickEdit();
                $actions->disableView();
                
                // 对于待领取的奖品，显示查看按钮
                if ($actions->row->status === 'unclaimed') {
                    $actions->append(new \App\Admin\Actions\Prize\ViewPrize());
                }
                
                // 对于处理中或已申领的奖品，显示发货按钮
                if (in_array($actions->row->status, ['pending', 'claimed'])) {
                    $actions->append(new \App\Admin\Actions\Prize\ShipPrize());
                }
                
                // 对于已发货的奖品，显示标记送达按钮
                if ($actions->row->status === 'shipped') {
                    $actions->append(new \App\Admin\Actions\Prize\DeliverPrize());
                }
            });
            $grid->column('claimed_at', '领取时间');
            $grid->column('created_at', '获奖时间')->sortable();
            
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id', 'ID');
                $filter->equal('quiz_activity_id', '所属活动')->select(function () {
                    return QuizActivity::pluck('title', 'id');
                });
                $filter->equal('prize_id', '奖品')->select(function () {
                    return QuizPrize::pluck('name', 'id');
                });
                $filter->equal('user_id', '用户ID');
                $filter->like('winner_name', '获奖者姓名');
                $filter->like('contact_info', '联系方式');
                $filter->equal('status', '状态')->select([
                    'pending' => '待领取',
                    'claimed' => '已申领',
                    'shipped' => '已发货',
                    'delivered' => '已送达',
                    'cancelled' => '已取消',
                ]);
                $filter->between('claimed_at', '领取时间')->datetime();
                $filter->between('created_at', '获奖时间')->datetime();
            });
            
            // 添加返回按钮
            if ($quizActivityId) {
                $grid->tools(function (Grid\Tools $tools) use ($quizActivityId) {
                    $tools->append('<a href="' . admin_url('quiz-activities') . '" class="btn btn-sm btn-white"><i class="feather icon-arrow-left"></i> 返回活动列表</a>');
                });
            }
            
            // 添加批量操作
            $grid->batchActions(function (Grid\Tools\BatchActions $batch) {
                $batch->add(new \App\Admin\Actions\BatchShipPrizes());
                $batch->add(new \App\Admin\Actions\BatchDeliverPrizes());
            });
            
            // 添加JavaScript处理发货和送达操作 - 翻新实现
            $grid->script = <<<JS
            $(function () {
                // 等页面完全加载后再注册事件
                setTimeout(function() {
                    // 全局CSRF设置
                    $.ajaxSetup({
                        headers: {
                            'X-CSRF-TOKEN': LA.token
                        }
                    });
                    
                    // 查看按钮点击事件
                    $('body').on('click', '.view-prize', function() {
                        var id = $(this).data('id');
                        LA.success('查看奖品详情');
                    });
                    
                    // 发货按钮点击事件
                    $('body').on('click', '.ship-prize', function() {
                        var id = $(this).data('id');
                        LA.confirm('发货确认', '确定要将该奖品标记为已发货吗？', function () {
                            LA.loading();
                            $.ajax({
                                url: '/admin/quiz-prize-winners/' + id + '/ship',
                                type: 'POST',
                                dataType: 'json',
                                data: {
                                    _token: LA.token,
                                    _method: 'POST'
                                },
                                success: function(data) {
                                    LA.close();
                                    if (data.status) {
                                        LA.success(data.message);
                                        // 刷新数据表格
                                        LA.reload();
                                    } else {
                                        LA.error(data.message || '操作失败');
                                    }
                                },
                                error: function(xhr) {
                                    LA.close();
                                    LA.error('操作失败: ' + (xhr.responseJSON.message || xhr.statusText));
                                    console.error(xhr);
                                }
                            });
                        });
                    });
                    
                    // 标记送达按钮点击事件
                    $('body').on('click', '.deliver-prize', function() {
                        var id = $(this).data('id');
                        LA.confirm('送达确认', '确定要将该奖品标记为已送达吗？', function () {
                            LA.loading();
                            $.ajax({
                                url: '/admin/quiz-prize-winners/' + id + '/deliver',
                                type: 'POST',
                                dataType: 'json',
                                data: {
                                    _token: LA.token,
                                    _method: 'POST'
                                },
                                success: function(data) {
                                    LA.close();
                                    if (data.status) {
                                        LA.success(data.message);
                                        // 刷新数据表格
                                        LA.reload();
                                    } else {
                                        LA.error(data.message || '操作失败');
                                    }
                                },
                                error: function(xhr) {
                                    LA.close();
                                    LA.error('操作失败: ' + (xhr.responseJSON.message || xhr.statusText));
                                    console.error(xhr);
                                }
                            });
                        });
                    });
                    
                    console.log('奖品管理脚本已加载并注册事件');
                }, 100);
            });
            JS;
        });
    }

    protected function form()
    {
        return Form::make(new QuizPrizeWinner(), function (Form $form) {
            $form->display('id', 'ID');
            
            $form->display('quiz_activity_id', '所属活动')->with(function ($value) {
                $quizActivity = QuizActivity::find($value);
                return $quizActivity ? $quizActivity->title : '-';
            });
            
            $form->display('prize_id', '奖品')->with(function ($value) {
                $prize = QuizPrize::find($value);
                return $prize ? $prize->name : '-';
            });
            
            $form->display('user_id', '用户ID');
            
            $form->text('winner_name', '获奖者姓名')
                ->required();
                
            $form->text('contact_info', '联系方式')
                ->required();
                
            $form->textarea('shipping_address', '收货地址')
                ->required();
                
            $form->select('status', '状态')
                ->options([
                    'pending' => '待领取',
                    'claimed' => '已申领',
                    'shipped' => '已发货',
                    'delivered' => '已送达',
                    'cancelled' => '已取消',
                ])
                ->required()
                ->when('cancelled', function (Form $form) {
                    $form->textarea('admin_notes', '取消原因')
                        ->required();
                });
                
            $form->textarea('admin_notes', '管理员备注')
                ->rows(3);
                
            $form->display('claimed_at', '领取时间');
            $form->display('created_at', '获奖时间');
            $form->display('updated_at', '更新时间');
            
            // 禁用创建和查看按钮
            $form->disableCreation();
            $form->disableViewCheck();
            
            $form->saving(function (Form $form) {
                // 如果用户修改了状态为已发货且之前不是已发货
                if ($form->status === 'shipped' && $form->model()->status !== 'shipped') {
                    // 设置发货时间
                    $form->claimed_at = now();
                }
            });
        });
    }
    
    /**
     * 将奖品标记为已发货
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function shipPrize($id)
    {
        $winner = QuizPrizeWinner::findOrFail($id);
        
        // 只有处理中或已申领状态才能标记为已发货
        if (!in_array($winner->status, ['pending', 'claimed'])) {
            return response()->json(['status' => false, 'message' => '只有处理中或已申领的奖品才能标记为发货']);
        }
        
        $winner->status = 'shipped';
        $winner->admin_notes = ($winner->admin_notes ? $winner->admin_notes . "\n" : '') . Admin::user()->name . ' 于 ' . now() . ' 标记为已发货';
        $winner->save();
        
        return response()->json(['status' => true, 'message' => '奖品已标记为已发货']);
    }
    
    /**
     * 将奖品标记为已送达
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function deliverPrize($id)
    {
        $winner = QuizPrizeWinner::findOrFail($id);
        
        // 仅在已发货状态才能标记为已送达
        if ($winner->status !== 'shipped') {
            return response()->json(['status' => false, 'message' => '只有已发货的奖品才能标记为送达']);
        }
        
        $winner->status = 'delivered';
        $winner->admin_notes = ($winner->admin_notes ? $winner->admin_notes . "\n" : '') . Admin::user()->name . ' 于 ' . now() . ' 标记为已送达';
        $winner->save();
        
        return response()->json(['status' => true, 'message' => '奖品已标记为已送达']);
    }
    
    /**
     * 取消奖品
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function cancelPrize($id)
    {
        $winner = QuizPrizeWinner::findOrFail($id);
        
        // 已送达的奖品不能取消
        if ($winner->status === 'delivered') {
            return response()->json(['status' => false, 'message' => '已送达的奖品不能取消']);
        }
        
        $winner->status = 'cancelled';
        $winner->admin_notes = $winner->admin_notes . '\n' . admin_user()->name . ' 于 ' . now() . ' 取消了此奖品';
        $winner->save();
        
        return response()->json(['status' => true, 'message' => '奖品已取消']);
    }

    /**
     * 申领奖品
     *
     * @param int $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function claimPrize($id)
    {
        $winner = \App\Models\QuizPrizeWinner::find($id);
        
        if (!$winner) {
            return response()->json(['status' => false, 'message' => '找不到该奖品记录']);
        }
        
        if ($winner->status !== 'unclaimed') {
            return response()->json(['status' => false, 'message' => '该奖品状态不正确，无法申领']);
        }
        
        $winner->status = 'pending';
        $winner->admin_notes = ($winner->admin_notes ? $winner->admin_notes . "\n" : '') . 
                               admin_user()->name . ' 于 ' . now() . ' 标记为已申领';
        $winner->save();
        
        return response()->json(['status' => true, 'message' => '奖品已成功标记为已申领']);
    }


}
