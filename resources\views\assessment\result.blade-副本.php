@extends('layouts.app')

@section('title', '测评结果 - ' . $response->questionnaire->title)

@section('custom-styles')
<link rel="stylesheet" href="{{ asset('css/assessment.css') }}">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
<!-- Markdown 解析库 -->
<script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
@endsection

@section('content')
<div class="result-container">
    <!-- 页面头部 -->
    <div class="assessment-header">
        <div class="header-content">
            <button class="back-btn" onclick="goToAssessmentList()">
                <i class="fas fa-arrow-left"></i>
            </button>
            <h1 class="header-title">测评结果</h1>
            <button class="back-btn" onclick="shareResult()">
                <i class="fas fa-share-alt"></i>
            </button>
        </div>
    </div>

    <!-- 结果卡片 -->
    <div class="result-card">
        <!-- 结果头部 -->
        <div class="result-header">
            <h1 class="result-title">{{ $response->questionnaire->title }}</h1>
            <p class="result-subtitle">测评完成时间：{{ $response->created_at->format('Y-m-d H:i') }}</p>
        </div>

        <!-- 分数展示 -->
        <div class="score-display">
            <div class="score-circle" id="scoreCircle">
                <div class="score-inner">
                    <span class="score-number" id="scoreNumber">0</span>
                    <span class="score-total">/ {{ $maxScore ?? 100 }}</span>
                </div>
            </div>
            <div class="result-level" id="resultLevel">{{ $analysis->level_name ?? '正在分析...' }}</div>
        </div>

        <!-- 结果描述 -->
        @if($analysis && $analysis->description)
        <div class="result-description">
            <p>{{ $analysis->description }}</p>
        </div>
        @endif

        <!-- 建议 -->
        @if($analysis && $analysis->suggestions)
        <div class="suggestions">
            <h3 class="suggestions-title">
                <i class="fas fa-lightbulb" style="color: #667eea; margin-right: 10px;"></i>
                专业建议
            </h3>
            <div class="suggestions-content">
                {!! nl2br(e($analysis->suggestions)) !!}
            </div>
        </div>
        @endif

        <!-- 详细分析 -->
        @if($analysis && $analysis->detailed_analysis)
        <div class="suggestions">
            <h3 class="suggestions-title">
                <i class="fas fa-chart-line" style="color: #667eea; margin-right: 10px;"></i>
                详细分析
            </h3>
            <div class="suggestions-content">
                {!! nl2br(e($analysis->detailed_analysis)) !!}
            </div>
        </div>
        @endif

        <!-- AI智能分析 -->
        @if($analysis && $analysis->ai_analysis)
        <div class="suggestions ai-analysis-section">
            <h3 class="suggestions-title">
                <i class="fas fa-robot"></i> AI智能分析
            </h3>
            <div class="suggestions-content ai-analysis-content">
                <!-- 加载动画 -->
                <div class="ai-analysis-loading" id="aiAnalysisLoading" style="
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    padding: 40px 20px;
                    background: rgba(102, 126, 234, 0.05);
                    border-radius: 12px;
                    margin-bottom: 15px;
                ">
                    <div style="text-align: center;">
                        <div style="
                            width: 40px;
                            height: 40px;
                            border: 3px solid rgba(102, 126, 234, 0.2);
                            border-top: 3px solid #667eea;
                            border-radius: 50%;
                            animation: spin 1s linear infinite;
                            margin: 0 auto 15px;
                        "></div>
                        <p style="color: #667eea; font-size: 14px; margin: 0;">正在解析AI分析内容...</p>
                        <small style="color: #999; font-size: 12px;">请稍候，内容正在渲染中</small>
                    </div>
                </div>
                
                <div class="ai-analysis-text" id="aiAnalysisText" style="display: none;">
                    <!-- AI 分析内容将通过 JavaScript 渲染 -->
                </div>
                <div class="ai-analysis-footer" id="aiAnalysisFooter" style="display: none;">
                    <small><i class="fas fa-info-circle"></i> 此分析由AI生成，仅供参考</small>
                </div>
            </div>
        </div>
        @elseif($analysis)
        <div class="suggestions ai-loading">
            <h3 class="suggestions-title">
                <i class="fas fa-robot" style="color: #764ba2; margin-right: 10px;"></i>
                AI智能分析
                <span style="font-size: 12px; color: #666; font-weight: normal; margin-left: 10px;">
                    <i class="fas fa-spinner fa-spin"></i> 正在生成中...
                </span>
            </h3>
            <div class="suggestions-content">
                <div class="ai-loading-placeholder">
                    <div class="loading-dots">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                    <p>AI正在分析您的测评结果，请稍候...</p>
                    <small>通常需要10-30秒完成分析</small>
                </div>
            </div>
        </div>
        @endif

        <!-- 操作按钮 -->
        <div class="result-actions">
            <button class="action-btn secondary-btn" onclick="retakeAssessment()">
                <i class="fas fa-redo"></i> 重新测评
            </button>
            <button class="action-btn primary-btn" onclick="viewMyRecords()">
                <i class="fas fa-history"></i> 我的记录
            </button>
        </div>
    </div>

    <!-- 相关推荐 -->
    @if($relatedQuestionnaires && $relatedQuestionnaires->count() > 0)
    <div class="questionnaire-list">
        <h2 class="section-title">推荐测评</h2>
        @foreach($relatedQuestionnaires as $related)
            <div class="questionnaire-card">
                <div class="card-header" style="height: 80px;">
                    <div class="card-icon" style="font-size: 30px;">
                        <i class="{{ App\Http\Controllers\AssessmentController::getDomainIcon($related->domain) }}"></i>
                    </div>
                    <div class="domain-badge">{{ App\Http\Controllers\AssessmentController::getDomainName($related->domain) }}</div>
                </div>
                
                <div class="card-content">
                    <h3 class="card-title">{{ $related->title }}</h3>
                    <p class="card-description">{{ $related->description }}</p>
                    
                    <div class="card-meta">
                        <div class="meta-item">
                            <i class="fas fa-question-circle"></i>
                            <span>{{ $related->question_count }} 题</span>
                        </div>
                        <div class="meta-item">
                            <i class="fas fa-clock"></i>
                            <span>约 {{ $related->est_duration }} 分钟</span>
                        </div>
                    </div>
                    
                    <div class="card-footer">
                        <span class="response-count">{{ $related->responses_count ?? 0 }} 人已测评</span>
                        <button class="start-btn" onclick="goToQuestionnaire({{ $related->id }})">
                            开始测评
                        </button>
                    </div>
                </div>
            </div>
        @endforeach
    </div>
    @endif
</div>
@endsection

@section('scripts')
<script>
// 结果数据
const resultData = {
    score: {{ $response->total_score ?? 0 }},
    maxScore: {{ $maxScore ?? 100 }},
    level: '{{ $analysis->level_name ?? '' }}',
    questionnaireId: {{ $response->questionnaire_id }},
    responseId: {{ $response->id }}
};

// AI分析轮询相关变量
let aiAnalysisPolling = null;
let pollingAttempts = 0;
const maxPollingAttempts = 60; // 最多轮询60次（5分钟）

// 页面加载动画
document.addEventListener('DOMContentLoaded', function() {
    // 移除之前的AI分析加载遮罩（如果存在）
    const previousLoadingOverlay = document.getElementById('aiAnalysisLoading');
    if (previousLoadingOverlay) {
        previousLoadingOverlay.remove();
    }
    
    animateScore();
    animateCircle();
    
    // 初始化 AI 分析显示
    @if($analysis && $analysis->ai_analysis)
    initializeAiAnalysis(`{!! addslashes($analysis->ai_analysis) !!}`);
    @endif
    
    // 如果AI分析不存在，开始轮询
    @if(!$analysis->ai_analysis)
    startAiAnalysisPolling();
    @endif
});

// 初始化 AI 分析显示
function initializeAiAnalysis(aiAnalysisText) {
    const aiTextElement = document.getElementById('aiAnalysisText');
    const aiLoadingElement = document.getElementById('aiAnalysisLoading');
    const aiFooterElement = document.getElementById('aiAnalysisFooter');
    
    if (aiTextElement) {
        // 模拟解析延迟，让用户看到加载动画
        setTimeout(() => {
            // 解析 Markdown 内容
            if (typeof marked !== 'undefined') {
                aiTextElement.innerHTML = marked.parse(aiAnalysisText);
            } else {
                aiTextElement.innerHTML = aiAnalysisText.replace(/\n/g, '<br>');
            }
            
            // 隐藏加载动画
            if (aiLoadingElement) {
                aiLoadingElement.style.opacity = '0';
                aiLoadingElement.style.transition = 'opacity 0.3s ease-out';
                setTimeout(() => {
                    aiLoadingElement.style.display = 'none';
                }, 300);
            }
            
            // 显示内容和底部信息
            aiTextElement.style.display = 'block';
            aiTextElement.style.opacity = '0';
            aiTextElement.style.transition = 'opacity 0.5s ease-in';
            
            if (aiFooterElement) {
                aiFooterElement.style.display = 'block';
                aiFooterElement.style.opacity = '0';
                aiFooterElement.style.transition = 'opacity 0.5s ease-in';
            }
            
            // 淡入动画
            setTimeout(() => {
                aiTextElement.style.opacity = '1';
                if (aiFooterElement) {
                    aiFooterElement.style.opacity = '1';
                }
            }, 100);
            
        }, 800); // 延迟800ms显示解析结果
    }
}

// 开始AI分析状态轮询
function startAiAnalysisPolling() {
    if (aiAnalysisPolling) {
        clearInterval(aiAnalysisPolling);
    }
    
    pollingAttempts = 0;
    aiAnalysisPolling = setInterval(checkAiAnalysisStatus, 5000); // 每5秒检查一次
}

// 检查AI分析状态
function checkAiAnalysisStatus() {
    pollingAttempts++;
    
    // 超过最大尝试次数，停止轮询
    if (pollingAttempts > maxPollingAttempts) {
        clearInterval(aiAnalysisPolling);
        showAiAnalysisTimeout();
        return;
    }
    
    fetch(`/assessment/ai-analysis-status/${resultData.responseId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.ai_analysis) {
                // AI分析完成，更新页面
                clearInterval(aiAnalysisPolling);
                updateAiAnalysisDisplay(data.ai_analysis);
            }
        })
        .catch(error => {
            console.error('检查AI分析状态失败:', error);
            // 继续轮询，不中断
        });
}

// 更新AI分析显示
function updateAiAnalysisDisplay(aiAnalysis) {
    const aiSection = document.querySelector('.ai-loading');
    if (aiSection) {
        // 更新整个 AI 分析区域的 HTML 结构
        aiSection.className = 'suggestions ai-analysis-section';
        aiSection.innerHTML = `
            <h3 class="suggestions-title">
                <i class="fas fa-robot"></i> AI智能分析
            </h3>
            <div class="suggestions-content ai-analysis-content">
                <!-- 加载动画 -->
                <div class="ai-analysis-loading" id="aiAnalysisLoadingDynamic" style="
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    padding: 40px 20px;
                    background: rgba(102, 126, 234, 0.05);
                    border-radius: 12px;
                    margin-bottom: 15px;
                ">
                    <div style="text-align: center;">
                        <div style="
                            width: 40px;
                            height: 40px;
                            border: 3px solid rgba(102, 126, 234, 0.2);
                            border-top: 3px solid #667eea;
                            border-radius: 50%;
                            animation: spin 1s linear infinite;
                            margin: 0 auto 15px;
                        "></div>
                        <p style="color: #667eea; font-size: 14px; margin: 0;">正在解析AI分析内容...</p>
                        <small style="color: #999; font-size: 12px;">请稍候，内容正在渲染中</small>
                    </div>
                </div>
                
                <div class="ai-analysis-text" id="aiAnalysisTextDynamic" style="display: none;">
                    <!-- 内容将通过 JavaScript 渲染 -->
                </div>
                <div class="ai-analysis-footer" id="aiAnalysisFooterDynamic" style="display: none;">
                    <small><i class="fas fa-info-circle"></i> 此分析由AI生成，仅供参考</small>
                </div>
            </div>
        `;
        
        // 延迟解析和显示内容
        setTimeout(() => {
            const aiTextElement = document.getElementById('aiAnalysisTextDynamic');
            const aiLoadingElement = document.getElementById('aiAnalysisLoadingDynamic');
            const aiFooterElement = document.getElementById('aiAnalysisFooterDynamic');
            
            // 渲染 markdown 内容
            if (aiTextElement) {
                if (typeof marked !== 'undefined') {
                    aiTextElement.innerHTML = marked.parse(aiAnalysis);
                } else {
                    aiTextElement.innerHTML = aiAnalysis.replace(/\n/g, '<br>');
                }
                
                // 隐藏加载动画
                if (aiLoadingElement) {
                    aiLoadingElement.style.opacity = '0';
                    aiLoadingElement.style.transition = 'opacity 0.3s ease-out';
                    setTimeout(() => {
                        aiLoadingElement.style.display = 'none';
                    }, 300);
                }
                
                // 显示内容
                aiTextElement.style.display = 'block';
                aiTextElement.style.opacity = '0';
                aiTextElement.style.transition = 'opacity 0.5s ease-in';
                
                if (aiFooterElement) {
                    aiFooterElement.style.display = 'block';
                    aiFooterElement.style.opacity = '0';
                    aiFooterElement.style.transition = 'opacity 0.5s ease-in';
                }
                
                // 淡入动画
                setTimeout(() => {
                    aiTextElement.style.opacity = '1';
                    if (aiFooterElement) {
                        aiFooterElement.style.opacity = '1';
                    }
                }, 100);
            }
        }, 800); // 延迟800ms显示解析结果
        
        // 添加整体淡入动画
        aiSection.style.opacity = '0';
        setTimeout(() => {
            aiSection.style.transition = 'opacity 0.5s ease-in';
            aiSection.style.opacity = '1';
        }, 100);
    }
}

// 显示AI分析超时提示
function showAiAnalysisTimeout() {
    const aiSection = document.querySelector('.ai-analysis-section');
    if (aiSection) {
        const title = aiSection.querySelector('h3');
        title.innerHTML = '<i class="fas fa-exclamation-triangle"></i> AI分析';
        
        const content = aiSection.querySelector('.suggestions-content');
        content.innerHTML = `
            <div class="ai-analysis-content">
                <div class="ai-analysis-text">
                    <p style="color: #666; text-align: center;">
                        <i class="fas fa-clock"></i> AI分析生成时间较长，请稍后刷新页面查看
                    </p>
                    <div style="text-align: center; margin-top: 15px;">
                        <button class="action-btn secondary-btn" onclick="location.reload()">
                            <i class="fas fa-refresh"></i> 刷新页面
                        </button>
                    </div>
                </div>
            </div>
        `;
    }
}

// 分数动画
function animateScore() {
    const scoreElement = document.getElementById('scoreNumber');
    const targetScore = resultData.score;
    let currentScore = 0;
    const increment = targetScore / 50;
    
    const timer = setInterval(() => {
        currentScore += increment;
        if (currentScore >= targetScore) {
            currentScore = targetScore;
            clearInterval(timer);
        }
        scoreElement.textContent = Math.floor(currentScore);
    }, 30);
}

// 圆形进度条动画
function animateCircle() {
    const circle = document.getElementById('scoreCircle');
    const percentage = (resultData.score / resultData.maxScore) * 100;
    
    // 根据分数设置颜色
    let color1, color2;
    if (percentage >= 80) {
        color1 = '#28a745';
        color2 = '#20c997';
    } else if (percentage >= 60) {
        color1 = '#ffc107';
        color2 = '#fd7e14';
    } else {
        color1 = '#dc3545';
        color2 = '#e83e8c';
    }
    
    setTimeout(() => {
        const angle = (percentage / 100) * 360;
        circle.style.background = `conic-gradient(${color1} 0deg, ${color2} ${angle}deg, #f0f0f0 ${angle}deg)`;
    }, 500);
}

// 重新测评
function retakeAssessment() {
    if (confirm('确定要重新进行测评吗？')) {
        window.location.href = `/assessment/${resultData.questionnaireId}/start`;
    }
}

// 查看我的记录
function viewMyRecords() {
    window.location.href = '/assessment/my-records';
}

// 返回测评列表
function goToAssessmentList() {
    window.location.href = '/assessment';
}

// 跳转到其他问卷
function goToQuestionnaire(id) {
    window.location.href = `/assessment/${id}`;
}

// 分享结果
function shareResult() {
    if (navigator.share) {
        navigator.share({
            title: '我的心理测评结果',
            text: `我在"{{ $response->questionnaire->title }}"测评中获得了 ${resultData.score} 分，等级：${resultData.level}`,
            url: window.location.href
        });
    } else {
        // 复制链接到剪贴板
        navigator.clipboard.writeText(window.location.href).then(() => {
            alert('结果链接已复制到剪贴板');
        });
    }
}

// 添加触摸反馈
document.querySelectorAll('.action-btn, .start-btn').forEach(btn => {
    btn.addEventListener('touchstart', function() {
        this.style.transform = 'scale(0.95)';
    });
    
    btn.addEventListener('touchend', function() {
        this.style.transform = '';
    });
});

// 结果保存提示
setTimeout(() => {
    if (!localStorage.getItem('result_saved_tip')) {
        const tip = document.createElement('div');
        tip.className = 'alert alert-success';
        tip.innerHTML = '<i class="fas fa-info-circle"></i> 您的测评结果已自动保存，可在"我的记录"中查看';
        tip.style.position = 'fixed';
        tip.style.top = '20px';
        tip.style.left = '20px';
        tip.style.right = '20px';
        tip.style.zIndex = '1000';
        document.body.appendChild(tip);
        
        setTimeout(() => {
            tip.remove();
        }, 3000);
        
        localStorage.setItem('result_saved_tip', 'shown');
    }
}, 2000);
</script>
@endsection
