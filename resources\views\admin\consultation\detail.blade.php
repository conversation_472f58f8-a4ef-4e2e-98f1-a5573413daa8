<div class="row">
    <div class="col-md-8">
        <!-- 留言信息卡片 -->
        <div class="card">
            <div class="card-header">
                <h4 class="card-title">留言详情</h4>
                <div class="card-tools">
                    <span class="badge badge-{{ $consultation->status == 'pending' ? 'warning' : ($consultation->status == 'replied' ? 'success' : 'secondary') }}">
                        {{ $consultation->status_text }}
                    </span>
                </div>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-sm-3"><strong>留言主题：</strong></div>
                    <div class="col-sm-9">{{ $consultation->subject }}</div>
                </div>
                <div class="row mb-3">
                    <div class="col-sm-3"><strong>留言人：</strong></div>
                    <div class="col-sm-9">
                        {{ $consultation->name }}
                        @if($consultation->user)
                            <small class="text-muted">(用户ID: {{ $consultation->user->id }})</small>
                        @endif
                    </div>
                </div>
                @if($consultation->phone)
                <div class="row mb-3">
                    <div class="col-sm-3"><strong>联系电话：</strong></div>
                    <div class="col-sm-9">{{ $consultation->phone }}</div>
                </div>
                @endif
                @if($consultation->email)
                <div class="row mb-3">
                    <div class="col-sm-3"><strong>邮箱地址：</strong></div>
                    <div class="col-sm-9">{{ $consultation->email }}</div>
                </div>
                @endif
                <div class="row mb-3">
                    <div class="col-sm-3"><strong>提交时间：</strong></div>
                    <div class="col-sm-9">{{ $consultation->created_at->format('Y-m-d H:i:s') }}</div>
                </div>
                @if($consultation->replied_at)
                <div class="row mb-3">
                    <div class="col-sm-3"><strong>回复时间：</strong></div>
                    <div class="col-sm-9">{{ $consultation->replied_at->format('Y-m-d H:i:s') }}</div>
                </div>
                @endif
                <div class="row mb-3">
                    <div class="col-sm-3"><strong>留言内容：</strong></div>
                    <div class="col-sm-9">
                        <div class="p-3 bg-light rounded">
                            {!! nl2br(e($consultation->content)) !!}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 回复记录 -->
        @if($consultation->replies->count() > 0)
        <div class="card mt-3">
            <div class="card-header">
                <h4 class="card-title">回复记录</h4>
            </div>
            <div class="card-body">
                <div class="timeline" id="replies-timeline">
                    @foreach($consultation->replies as $reply)
                    <div class="timeline-item">
                        <div class="timeline-marker {{ $reply->is_admin_reply ? 'bg-primary' : 'bg-info' }}">
                            <i class="fas {{ $reply->is_admin_reply ? 'fa-user-shield' : 'fa-user' }}"></i>
                        </div>
                        <div class="timeline-content">
                            <div class="timeline-header">
                                <h6 class="timeline-title">
                                    {{ $reply->is_admin_reply ? ($reply->identity??'管理员回复') : '用户回复' }}
                                    @if($reply->is_admin_reply && $reply->admin)
                                        <small class="text-muted">({{ $reply->admin->name }})</small>
                                    @endif
                                </h6>
                                <small class="text-muted">{{ $reply->created_at->format('Y-m-d H:i:s') }}</small>
                            </div>
                            <div class="timeline-body">
                                {!! nl2br(e($reply->content)) !!}
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
        @endif

        <!-- 添加回复 -->
        @if($consultation->status !== 'closed')
        <div class="card mt-3">
            <div class="card-header">
                <h4 class="card-title">添加回复</h4>
            </div>
            <div class="card-body">
                <form id="replyForm">
                    {{ csrf_field() }}
                    <div class="form-group">
                        <label for="replyIdentity">回复身份</label>
                        <input type="text"
                               class="form-control"
                               id="replyIdentity"
                               name="identity"
                               placeholder="如：心理咨询师张三 / 客服 / 系统通知"
                               maxlength="50">
                        <small class="form-text text-muted">可选，最多50字；留空则后台默认“管理员”</small>
                    </div>
                    <div class="form-group">
                        <label for="replyContent">回复内容</label>
                        <textarea id="replyContent" name="content" class="form-control" rows="5" 
                                  placeholder="请输入回复内容..." required></textarea>
                        <small class="form-text text-muted">最多1000字</small>
                    </div>
                    <div class="form-group">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-reply"></i> 发送回复
                        </button>
                        <button type="button" class="btn btn-secondary ml-2" onclick="clearReply()">
                            <i class="fas fa-eraser"></i> 清空
                        </button>
                    </div>
                </form>
            </div>
        </div>
        @endif
    </div>

    <div class="col-md-4">
        <!-- 操作面板 -->
        <div class="card">
            <div class="card-header">
                <h4 class="card-title">操作</h4>
            </div>
            <div class="card-body">
                <div class="form-group">
                    <label>状态管理</label>
                    <select id="statusSelect" class="form-control">
                        <option value="pending" {{ $consultation->status == 'pending' ? 'selected' : '' }}>待回复</option>
                        <option value="replied" {{ $consultation->status == 'replied' ? 'selected' : '' }}>已回复</option>
                        <option value="closed" {{ $consultation->status == 'closed' ? 'selected' : '' }}>已关闭</option>
                    </select>
                </div>
                <button type="button" class="btn btn-info btn-block" onclick="updateStatus()">
                    <i class="fas fa-save"></i> 更新状态
                </button>
                
                <hr>
                
                <div class="text-center">
                    <a href="{{ admin_url('consultations') }}" class="btn btn-default">
                        <i class="fas fa-arrow-left"></i> 返回列表
                    </a>
                </div>
            </div>
        </div>

        <!-- 统计信息 -->
        <div class="card mt-3">
            <div class="card-header">
                <h4 class="card-title">统计信息</h4>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="info-box-content">
                            <span class="info-box-text">回复数量</span>
                            <span class="info-box-number">{{ $consultation->replies->count() }}</span>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="info-box-content">
                            <span class="info-box-text">管理员回复</span>
                            <span class="info-box-number">{{ $consultation->replies->where('is_admin_reply', true)->count() }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 30px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 0;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
}

.timeline-content {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    border-left: 3px solid #007bff;
}

.timeline-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.timeline-title {
    margin: 0;
    font-weight: 600;
}

.timeline-body {
    line-height: 1.6;
}
</style>

<script>
// 获取CSRF token
function getToken() {
    return document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || 
           document.querySelector('input[name="_token"]')?.value || 
           '{{ csrf_token() }}';
}

// 添加回复
document.getElementById('replyForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const content = document.getElementById('replyContent').value.trim();
    if (!content) {
        alert('请输入回复内容');
        return;
    }
    
    if (content.length > 1000) {
        alert('回复内容不能超过1000字');
        return;
    }
    
    const identity = document.getElementById('replyIdentity').value.trim();
    
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 发送中...';
    
    const formData = new FormData();
    
    formData.append('identity', identity);   // 新增
    formData.append('content', content);
    formData.append('_token', getToken());
    
    fetch('{{ admin_url("consultations/" . $consultation->id . "/reply") }}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.status) {
            alert('回复成功');
            // 刷新页面显示新回复
            location.reload();
        } else {
            alert(data.message || '回复失败');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('回复失败，请重试');
    })
    .finally(() => {
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    });
});

// 更新状态
function updateStatus() {
    const status = document.getElementById('statusSelect').value;
    
    const formData = new FormData();
    formData.append('status', status);
    formData.append('_token', getToken());
    
    fetch('{{ admin_url("consultations/" . $consultation->id . "/status") }}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.status) {
            alert('状态更新成功');
            // 刷新页面
            setTimeout(() => location.reload(), 1000);
        } else {
            alert(data.message || '更新失败');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('更新失败，请重试');
    });
}

// 清空回复
function clearReply() {
    document.getElementById('replyContent').value = '';
}
</script> 