@extends('layouts.app')

@section('title', '心理测评 - 问卷列表')

@section('custom-styles')
<style>
    .page-header {
        padding: 15px;
        background-color: #f8f9fa;
        text-align: center;
        position: relative;
    }
    
    .back-button {
        position: absolute;
        left: 15px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 20px;
        color: #333;
    }
    
    .assessment-tabs {
        display: flex;
        background-color: #fff;
        border-bottom: 1px solid #eee;
    }
    
    .assessment-tab {
        flex: 1;
        text-align: center;
        padding: 15px 0;
        font-size: 15px;
        color: #666;
        position: relative;
    }
    
    .assessment-tab.active {
        color: #4e9cff;
        font-weight: 500;
    }
    
    .assessment-tab.active::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 30px;
        height: 3px;
        background-color: #4e9cff;
        border-radius: 3px;
    }
    
    .assessment-list {
        padding: 15px;
    }
    
    .assessment-item {
        background-color: #fff;
        border-radius: 10px;
        overflow: hidden;
        margin-bottom: 20px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    }
    
    .assessment-image {
        height: 150px;
        background-size: cover;
        background-position: center;
        position: relative;
    }
    
    .assessment-badge {
        position: absolute;
        top: 10px;
        right: 10px;
        background-color: rgba(255, 106, 0, 0.9);
        color: #fff;
        padding: 3px 10px;
        border-radius: 15px;
        font-size: 12px;
    }
    
    .assessment-content {
        padding: 15px;
    }
    
    .assessment-title {
        font-size: 18px;
        font-weight: 500;
        margin-bottom: 10px;
        color: #333;
    }
    
    .assessment-desc {
        font-size: 14px;
        color: #666;
        margin-bottom: 15px;
        line-height: 1.5;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
    
    .assessment-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 13px;
        color: #999;
    }
    
    .assessment-stats {
        display: flex;
        align-items: center;
    }
    
    .assessment-stat {
        margin-right: 15px;
        display: flex;
        align-items: center;
    }
    
    .assessment-stat i {
        margin-right: 5px;
    }
    
    .assessment-button {
        background-color: #4e9cff;
        color: #fff;
        border: none;
        padding: 8px 20px;
        border-radius: 20px;
        font-size: 14px;
        cursor: pointer;
    }
    
    .assessment-tags {
        display: flex;
        flex-wrap: wrap;
        margin-top: 10px;
    }
    
    .assessment-tag {
        background-color: #f0f7ff;
        color: #4e9cff;
        padding: 2px 10px;
        border-radius: 15px;
        font-size: 12px;
        margin-right: 8px;
        margin-bottom: 8px;
    }
</style>
@endsection

@section('content')
<div class="page-header">
    <a href="{{ route('home') }}" class="back-button">
        <i class="fas fa-arrow-left"></i>
    </a>
    <h1 style="font-size: 18px; margin: 0;">心理测评</h1>
</div>

<div class="assessment-tabs">
    <div class="assessment-tab active">心理问卷</div>
    <div class="assessment-tab">趣味测评</div>
</div>

<div class="assessment-list">
    <!-- 心理健康综合评估 -->
    <div class="assessment-item">
        <div class="assessment-image" style="background-image: url('https://img.freepik.com/free-photo/mental-health-care-sketch-diagram_53876-121365.jpg?w=1380&t=st=**********~exp=**********~hmac=b1b6a9b6b9b6b9b6b9b6b9b6b9b6b9b6b9b6b9b6b9b6b9b6b9b6b9b6b9b6b9')">
            <div class="assessment-badge">专业测评</div>
        </div>
        <div class="assessment-content">
            <h3 class="assessment-title">心理健康综合评估</h3>
            <p class="assessment-desc">全面评估您在事业、家庭与健康三大领域的心理状态，帮助您了解自己的心理健康水平，并提供针对性的改善建议。</p>
            <div class="assessment-tags">
                <span class="assessment-tag">综合评估</span>
                <span class="assessment-tag">45题</span>
                <span class="assessment-tag">约15分钟</span>
            </div>
            <div class="assessment-meta">
                <div class="assessment-stats">
                    <div class="assessment-stat">
                        <i class="fas fa-user"></i>
                        <span>12,345人已测</span>
                    </div>
                    <div class="assessment-stat">
                        <i class="fas fa-star"></i>
                        <span>4.8分</span>
                    </div>
                </div>
                <button class="assessment-button">开始测评</button>
            </div>
        </div>
    </div>
    
    <!-- 职场压力指数测评 -->
    <div class="assessment-item">
        <div class="assessment-image" style="background-image: url('https://img.freepik.com/free-photo/businesswoman-stressed-out-work_53876-138140.jpg?w=1380&t=st=**********~exp=**********~hmac=b1b6a9b6b9b6b9b6b9b6b9b6b9b6b9b6b9b6b9b6b9b6b9b6b9b6b9b6b9b6b9')">
            <div class="assessment-badge">热门</div>
        </div>
        <div class="assessment-content">
            <h3 class="assessment-title">职场压力指数测评</h3>
            <p class="assessment-desc">评估您当前的职场压力水平，分析压力来源，并提供有效的压力管理策略，帮助您在工作中保持最佳状态。</p>
            <div class="assessment-tags">
                <span class="assessment-tag">压力管理</span>
                <span class="assessment-tag">30题</span>
                <span class="assessment-tag">约10分钟</span>
            </div>
            <div class="assessment-meta">
                <div class="assessment-stats">
                    <div class="assessment-stat">
                        <i class="fas fa-user"></i>
                        <span>9,876人已测</span>
                    </div>
                    <div class="assessment-stat">
                        <i class="fas fa-star"></i>
                        <span>4.7分</span>
                    </div>
                </div>
                <button class="assessment-button">开始测评</button>
            </div>
        </div>
    </div>
    
    <!-- 情绪管理能力测评 -->
    <div class="assessment-item">
        <div class="assessment-image" style="background-image: url('https://img.freepik.com/free-photo/woman-meditating-beach-sunset_53876-138145.jpg?w=1380&t=st=**********~exp=**********~hmac=b1b6a9b6b9b6b9b6b9b6b9b6b9b6b9b6b9b6b9b6b9b6b9b6b9b6b9b6b9b6b9')">
        </div>
        <div class="assessment-content">
            <h3 class="assessment-title">情绪管理能力测评</h3>
            <p class="assessment-desc">测评您识别、理解和调节情绪的能力，帮助您提高情商，更好地应对工作和生活中的情绪挑战。</p>
            <div class="assessment-tags">
                <span class="assessment-tag">情绪管理</span>
                <span class="assessment-tag">35题</span>
                <span class="assessment-tag">约12分钟</span>
            </div>
            <div class="assessment-meta">
                <div class="assessment-stats">
                    <div class="assessment-stat">
                        <i class="fas fa-user"></i>
                        <span>7,654人已测</span>
                    </div>
                    <div class="assessment-stat">
                        <i class="fas fa-star"></i>
                        <span>4.6分</span>
                    </div>
                </div>
                <button class="assessment-button">开始测评</button>
            </div>
        </div>
    </div>
    
    <!-- 职场人际关系测评 -->
    <div class="assessment-item">
        <div class="assessment-image" style="background-image: url('https://img.freepik.com/free-photo/business-people-shaking-hands_53876-138150.jpg?w=1380&t=st=**********~exp=**********~hmac=b1b6a9b6b9b6b9b6b9b6b9b6b9b6b9b6b9b6b9b6b9b6b9b6b9b6b9b6b9b6b9')">
        </div>
        <div class="assessment-content">
            <h3 class="assessment-title">职场人际关系测评</h3>
            <p class="assessment-desc">评估您在职场中的人际交往模式和沟通风格，帮助您建立更加和谐、高效的职场人际关系。</p>
            <div class="assessment-tags">
                <span class="assessment-tag">人际关系</span>
                <span class="assessment-tag">40题</span>
                <span class="assessment-tag">约13分钟</span>
            </div>
            <div class="assessment-meta">
                <div class="assessment-stats">
                    <div class="assessment-stat">
                        <i class="fas fa-user"></i>
                        <span>6,543人已测</span>
                    </div>
                    <div class="assessment-stat">
                        <i class="fas fa-star"></i>
                        <span>4.5分</span>
                    </div>
                </div>
                <button class="assessment-button">开始测评</button>
            </div>
        </div>
    </div>
    
    <!-- 工作与家庭平衡测评 -->
    <div class="assessment-item">
        <div class="assessment-image" style="background-image: url('https://img.freepik.com/free-photo/family-spending-time-together-home_53876-138155.jpg?w=1380&t=st=**********~exp=**********~hmac=b1b6a9b6b9b6b9b6b9b6b9b6b9b6b9b6b9b6b9b6b9b6b9b6b9b6b9b6b9b6b9')">
        </div>
        <div class="assessment-content">
            <h3 class="assessment-title">工作与家庭平衡测评</h3>
            <p class="assessment-desc">评估您在工作与家庭之间的平衡状况，发现潜在的冲突点，并提供实用的平衡策略，帮助您兼顾事业和家庭。</p>
            <div class="assessment-tags">
                <span class="assessment-tag">工作家庭平衡</span>
                <span class="assessment-tag">35题</span>
                <span class="assessment-tag">约12分钟</span>
            </div>
            <div class="assessment-meta">
                <div class="assessment-stats">
                    <div class="assessment-stat">
                        <i class="fas fa-user"></i>
                        <span>5,432人已测</span>
                    </div>
                    <div class="assessment-stat">
                        <i class="fas fa-star"></i>
                        <span>4.6分</span>
                    </div>
                </div>
                <button class="assessment-button">开始测评</button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 测评类型标签切换
        const tabs = document.querySelectorAll('.assessment-tab');
        tabs.forEach(tab => {
            tab.addEventListener('click', function() {
                // 移除所有标签的active类
                tabs.forEach(t => t.classList.remove('active'));
                // 为当前点击的标签添加active类
                this.classList.add('active');
                
                // 显示对应类型的测评
                const tabText = this.textContent;
                if (tabText === '趣味测评') {
                    // 这里应该是切换到趣味测评列表的逻辑
                    // 目前只是显示一个提示
                    alert('趣味测评功能正在开发中，敬请期待！');
                    // 恢复到心理问卷标签
                    setTimeout(() => {
                        tabs.forEach(t => t.classList.remove('active'));
                        tabs[0].classList.add('active');
                    }, 500);
                }
            });
        });
        
        // 开始测评按钮点击事件
        const startButtons = document.querySelectorAll('.assessment-button');
        startButtons.forEach((button, index) => {
            button.addEventListener('click', function() {
                // 模拟测评ID
                const assessmentId = index + 1;
                // 跳转到测评详情页
                window.location.href = `{{ route('assessment.questionnaires') }}/${assessmentId}`;
            });
        });
    });
</script>
@endsection
