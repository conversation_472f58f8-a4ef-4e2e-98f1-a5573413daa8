<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Dcat\Admin\Traits\HasDateTimeFormatter;

class AiConsultationSetting extends Model
{
    use HasFactory, HasDateTimeFormatter;

    protected $fillable = [
        'provider',
        'api_key',
        'api_url',
        'model',
        'max_tokens',
        'temperature',
        'system_prompt',
        'is_active'
    ];

    protected $casts = [
        'max_tokens' => 'integer',
        'temperature' => 'float',
        'is_active' => 'boolean'
    ];

    // 获取当前活跃的配置
    public static function getActiveSettings()
    {
        return self::where('is_active', true)->first();
    }

    // 敏感字段（不在前端显示）
    public function getApiKeyAttribute($value)
    {
        if (empty($value)) {
            return null;
        }
        
        return substr($value, 0, 4) . str_repeat('*', 8) . substr($value, -4);
    }
}
