<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\SearchController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

/*
|--------------------------------------------------------------------------
| 搜索相关路由
|--------------------------------------------------------------------------
*/

// 公开搜索接口
Route::prefix('search')->group(function () {
    // 内容搜索
    Route::get('/', [SearchController::class, 'search']);
    
    // 搜索建议
    Route::get('/suggestions', [SearchController::class, 'suggestions']);
    
    // 相似内容
    Route::get('/similar/{type}/{id}', [SearchController::class, 'similar'])
        ->where(['type' => 'article|video|course_lesson', 'id' => '[0-9]+']);
    
    // 搜索统计（可能需要权限控制）
    Route::get('/stats', [SearchController::class, 'stats']);
});

// 需要认证的搜索管理接口
Route::middleware('auth:sanctum')->prefix('search')->group(function () {
    // 清除搜索缓存
    Route::delete('/cache', [SearchController::class, 'clearCache']);
});
