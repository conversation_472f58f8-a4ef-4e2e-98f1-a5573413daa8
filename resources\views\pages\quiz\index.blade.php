@extends('layouts.app')

@section('title', '有奖问答')

@section('custom-styles')
<link rel="stylesheet" href="{{ asset('css/quiz.css') }}">
<style>
    /* 页面特定样式 */
    .quiz-section {
        margin-bottom: 25px;
    }
    
    .section-title {
        font-size: 17px;
        font-weight: 500;
        margin-bottom: 15px;
        color: #333;
        display: flex;
        align-items: center;
    }
    
    .section-title-icon {
        margin-right: 8px;
        color: var(--quiz-primary);
    }
    
    .quiz-prizes {
        font-size: 12px;
        color: #ff6b6b;
        display: flex;
        align-items: center;
        margin-top: 5px;
    }
    
    .empty-message {
        text-align: center;
        padding: 20px;
        color: #999;
        background-color: #f9f9f9;
        border-radius: 10px;
        font-size: 14px;
        margin: 10px 0;
    }
    
    /* 添加一些动画效果 */
    .quiz-card {
        animation: fadeIn 0.5s ease forwards;
        animation-delay: calc(var(--animation-order) * 0.1s);
        opacity: 0;
    }
    
    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
</style>
@endsection

@section('content')
<div class="quiz-header">
    <div style="width: 30px;">
        <a href="{{ route('home') }}" class="quiz-back-button">
            <div class="quiz-back-icon"></div>
        </a>
    </div>
    <h1>有奖问答</h1>
    <div style="width: 30px;"></div>
</div>

<div class="quiz-content">
    @if(session('success'))
    <div class="alert alert-success" style="background-color: #e7f3ee; color: #1a7348; border-left: 4px solid #28a745; border-radius: 8px; padding: 12px 15px; margin-bottom: 15px; font-size: 15px; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
        <i class="fas fa-check-circle" style="margin-right: 8px;"></i> {{ session('success') }}
    </div>
    @endif

    @if(session('error'))
    <div class="alert alert-danger" style="background-color: #f8eaed; color: #a12a43; border-left: 4px solid #dc3545; border-radius: 8px; padding: 12px 15px; margin-bottom: 15px; font-size: 15px; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
        <i class="fas fa-exclamation-circle" style="margin-right: 8px;"></i> {{ session('error') }}
    </div>
    @endif

    <!-- 进行中的问答活动 -->
    <div class="quiz-section">
        <h2 class="section-title">
            <i class="fas fa-fire section-title-icon"></i> 进行中的问答
        </h2>
        
        @if($ongoingQuizzes->isEmpty())
            <div class="empty-message">
                <i class="fas fa-info-circle" style="margin-right: 5px;"></i> 暂无进行中的问答活动
            </div>
        @else
            @foreach($ongoingQuizzes as $quiz)
            <a href="{{ route('quiz.show', $quiz->id) }}" class="quiz-card">
                <div class="quiz-image">
                    <div class="quiz-status quiz-status-ongoing">进行中</div>
                    <img src="{{ $quiz->cover_image ? asset('storage/'.$quiz->cover_image) : asset('images/default-quiz.jpg') }}" alt="{{ $quiz->title }}">
                </div>
                <div class="quiz-info">
                    <div>
                        <h3 class="quiz-title">{{ $quiz->title }}</h3>
                        <p class="quiz-description">{{ Str::limit($quiz->description, 50) }}</p>
                        <div class="quiz-prizes">
                            <i class="fas fa-gift" style="margin-right: 5px;"></i> {{ $quiz->prizes->count() }} 种奖品
                        </div>
                    </div>
                    <div class="quiz-meta">
                        <div class="quiz-meta-item">
                            <i class="far fa-calendar-alt quiz-meta-icon"></i> 
                            {{ $quiz->end_time->format('m-d H:i') }}结束
                        </div>
                        <div class="quiz-meta-item">
                            <i class="fas fa-users quiz-meta-icon"></i> 
                            {{ $quiz->attempts()->where('is_completed', true)->count() }}人参与
                        </div>
                    </div>
                </div>
            </a>
            @endforeach
        @endif
    </div>

    <!-- 即将开始的问答活动 -->
    <div class="quiz-section">
        <h2 class="section-title">
            <i class="fas fa-calendar-alt section-title-icon"></i> 即将开始
        </h2>
        
        @if($upcomingQuizzes->isEmpty())
            <div class="empty-message">
                <i class="fas fa-info-circle" style="margin-right: 5px;"></i> 暂无即将开始的问答活动
            </div>
        @else
            @foreach($upcomingQuizzes as $quiz)
            <a href="{{ route('quiz.show', $quiz->id) }}" class="quiz-card">
                <div class="quiz-image">
                    <div class="quiz-status quiz-status-upcoming">即将开始</div>
                    <img src="{{ $quiz->cover_image ? asset('storage/'.$quiz->cover_image) : asset('images/default-quiz.jpg') }}" alt="{{ $quiz->title }}">
                </div>
                <div class="quiz-info">
                    <div>
                        <h3 class="quiz-title">{{ $quiz->title }}</h3>
                        <p class="quiz-description">{{ Str::limit($quiz->description, 50) }}</p>
                        <div class="quiz-prizes">
                            <i class="fas fa-gift" style="margin-right: 5px;"></i> {{ $quiz->prizes->count() }} 种奖品
                        </div>
                    </div>
                    <div class="quiz-meta">
                        <div class="quiz-meta-item">
                            <i class="far fa-calendar-alt quiz-meta-icon"></i> 
                            {{ $quiz->start_time->format('m-d H:i') }}开始
                        </div>
                        <div class="quiz-meta-item">
                            <i class="fas fa-hourglass-start quiz-meta-icon"></i> 
                            {{ now()->diffForHumans($quiz->start_time) }}
                        </div>
                    </div>
                </div>
            </a>
            @endforeach
        @endif
    </div>

    <!-- 往期问答活动 -->
    <div class="quiz-section">
        <h2 class="section-title">
            <i class="fas fa-history section-title-icon"></i> 往期问答
        </h2>
        
        @if($pastQuizzes->isEmpty())
            <div class="empty-message">
                <i class="fas fa-info-circle" style="margin-right: 5px;"></i> 暂无往期问答活动
            </div>
        @else
            @foreach($pastQuizzes as $quiz)
            <a href="{{ route('quiz.show', $quiz->id) }}" class="quiz-card">
                <div class="quiz-image">
                    <div class="quiz-status quiz-status-ended">已结束</div>
                    <img src="{{ $quiz->cover_image ? asset('storage/'.$quiz->cover_image) : asset('images/default-quiz.jpg') }}" alt="{{ $quiz->title }}">
                </div>
                <div class="quiz-info">
                    <div>
                        <h3 class="quiz-title">{{ $quiz->title }}</h3>
                        <p class="quiz-description">{{ Str::limit($quiz->description, 50) }}</p>
                    </div>
                    <div class="quiz-meta">
                        <div class="quiz-meta-item">
                            <i class="far fa-calendar-alt quiz-meta-icon"></i> 
                            {{ $quiz->end_time->format('m-d') }}结束
                        </div>
                        <div class="quiz-meta-item">
                            <i class="fas fa-users quiz-meta-icon"></i> 
                            {{ $quiz->attempts()->where('is_completed', true)->count() }}人参与
                        </div>
                    </div>
                </div>
            </a>
            @endforeach
        @endif
    </div>

    <div style="margin-top: 20px; text-align: center;">
        <a href="{{ route('quiz.my_prizes') }}" class="quiz-btn quiz-btn-primary" style="max-width: 90%; display: inline-block;">
            <i class="fas fa-gift" style="margin-right: 5px;"></i> 查看我的奖品
        </a>
    </div>
    
    <!-- 添加底部空间，避免被底部菜单遮挡 -->
    <div class="quiz-bottom-space"></div>
</div>
@endsection
