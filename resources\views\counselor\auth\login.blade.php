<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>咨询师登录 - 心理健康平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: "SF Pro Text", "SF Pro Icons", "Helvetica Neue", Helvetica, Arial, sans-serif;
        }
        .login-container {
            max-width: 400px;
            margin: 80px auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .login-header img {
            width: 80px;
            height: 80px;
            margin-bottom: 20px;
        }
        .login-header h1 {
            font-size: 24px;
            color: #333;
            font-weight: 600;
        }
        .btn-primary {
            background: linear-gradient(45deg, #3498db, #8e44ad);
            border: none;
            padding: 12px;
            font-size: 16px;
            border-radius: 10px;
            width: 100%;
            font-weight: 500;
        }
        .form-control {
            height: 50px;
            padding: 10px 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            font-size: 16px;
            border: 1px solid #e1e1e1;
        }
        .form-control:focus {
            border-color: #3498db;
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }
        .back-link {
            text-align: center;
            margin-top: 20px;
        }
        .back-link a {
            color: #6c757d;
            text-decoration: none;
        }
        .back-link a:hover {
            color: #3498db;
        }
        .alert {
            border-radius: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="login-container">
            <div class="login-header">
                <h1 class="mb-3">心理健康平台</h1>
                <h2 class="h5 text-muted mb-4">咨询师工作台登录</h2>
            </div>
            
            @if ($errors->any())
            <div class="alert alert-danger">
                <ul class="mb-0">
                    @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
            @endif
            
            <form method="POST" action="{{ route('counselor.login.post') }}">
                @csrf
                <div class="form-group">
                    <input type="text" name="phone" class="form-control" placeholder="手机号" value="{{ old('phone') }}" required autofocus>
                </div>
                
                <div class="form-group">
                    <input type="password" name="password" class="form-control" placeholder="密码" required>
                </div>
                
                <div class="form-group">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <div></div>
                        <a href="{{ route('counselor.forgot_password') }}" class="text-decoration-none" style="color: #3498db; font-size: 14px;">忘记密码？</a>
                    </div>
                    <button type="submit" class="btn btn-primary">登录</button>
                </div>
            </form>
            
            <div class="back-link">
                <a href="{{ route('home') }}">返回主页</a>
            </div>
        </div>
    </div>
</body>
</html>
