<?php

namespace App\Admin\Controllers;

use App\Admin\Actions\MarkScheduleAvailable;
use App\Admin\Actions\MarkScheduleUnavailable;
use App\Models\Counselor;
use App\Models\CounselorSchedule;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Carbon\Carbon;

class CounselorScheduleController extends AdminController
{
    /**
     * 设置标题
     */
    protected $title = '咨询师排班';

    /**
     * 列表页面
     */
    protected function grid()
    {
        return Grid::make(new CounselorSchedule(), function (Grid $grid) {
            $grid->model()->with('counselor');

            // 获取URL参数，用于筛选特定咨询师的排班
            $counselorId = request()->get('counselor_id');
            if ($counselorId) {
                $grid->model()->where('counselor_id', $counselorId);
                $counselor = Counselor::find($counselorId);
                if ($counselor) {
                    $grid->tools(function (Grid\Tools $tools) use ($counselor) {
                        $tools->append('<div class="alert alert-info">当前显示: ' . $counselor->name . ' 的排班</div>');
                    });
                }
            }

            // 设置表格字段
            $grid->column('id')->sortable();
            $grid->column('counselor.name', '咨询师');
            $grid->column('date', '日期')->sortable()->display(function () {
                return Carbon::parse($this->date)->format('Y-m-d');
            });
            $grid->column('time_range', '时间段')->display(function () {
                return Carbon::parse($this->start_time)->format('H:i') . ' - ' .
                       Carbon::parse($this->end_time)->format('H:i');
            });
            $grid->column('is_available', '是否可预约')->switch();
            // 您也可以使用下面的扩展版本，但需要Dcat Admin支持
            /* 
            $grid->column('is_available', '是否可预约')->switch([
                'on'  => ['value' => 1, 'text' => '可预约',  'color' => 'success'],
                'off' => ['value' => 0, 'text' => '不可预约', 'color' => 'danger'],
            ]);
            */

            // 添加色彩提示：过期的排班显示灰色
            $grid->column('status', '状态')->display(function () {
                if (Carbon::parse($this->date)->lt(Carbon::today())) {
                    return '<span class="badge badge-secondary">已过期</span>';
                }

                if ($this->isBooked()) {
                    return '<span class="badge badge-warning">已预约</span>';
                }

                if ($this->is_available) {
                    return '<span class="badge badge-success">可预约</span>';
                } else {
                    return '<span class="badge badge-danger">不可预约</span>';
                }
            });

            $grid->column('created_at', '创建时间')->sortable();

            // 筛选功能
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');
                $filter->equal('counselor_id', '咨询师')->select(Counselor::all()->pluck('name', 'id'));
                $filter->date('date', '日期');
                $filter->equal('is_available', '是否可预约')->select([
                    0 => '不可预约',
                    1 => '可预约'
                ]);
            });

            // 默认按日期排序
            $grid->model()->orderBy('date', 'desc')->orderBy('start_time', 'asc');
            
            // 禁用批量操作
            $grid->disableBatchActions();
        });
    }

    /**
     * 批量标记为可预约
     */
    public function markAsAvailable($ids)
    {
        CounselorSchedule::whereIn('id', $ids)->update(['is_available' => true]);
        return $this->response()->success('操作成功')->refresh();
    }

    /**
     * 批量标记为不可预约
     */
    public function markAsUnavailable($ids)
    {
        CounselorSchedule::whereIn('id', $ids)->update(['is_available' => false]);
        return $this->response()->success('操作成功')->refresh();
    }
    
    /**
     * 处理开关状态更新
     */
    public function switchAvailability()
    {
        $id = request()->get('id');
        $value = request()->get('value');
        
        if (!$id) {
            return response()->json([
                'status' => false,
                'message' => 'ID不能为空'
            ]);
        }
        
        $schedule = CounselorSchedule::find($id);
        if (!$schedule) {
            return response()->json([
                'status' => false,
                'message' => '排班不存在'
            ]);
        }
        
        $schedule->is_available = $value;
        $schedule->save();
        
        return response()->json([
            'status' => true,
            'message' => '更新成功'
        ]);
    }

    /**
     * 详情页面
     */
    protected function detail($id)
    {
        return Show::make($id, new CounselorSchedule(), function (Show $show) {
            $show->field('id');
            $show->field('counselor.name', '咨询师');
            $show->field('date', '日期');
            $show->field('start_time', '开始时间');
            $show->field('end_time', '结束时间');
            $show->field('is_available', '是否可预约')->as(function ($value) {
                return $value ? '可预约' : '不可预约';
            });
            $show->field('created_at', '创建时间');
            $show->field('updated_at', '更新时间');
        });
    }

    /**
     * 表单页面
     */
    protected function form()
    {
        return Form::make(new CounselorSchedule(), function (Form $form) {
            $form->display('id');

            // 如果是从特定咨询师页面进入，默认选择该咨询师
            $counselorId = request()->get('counselor_id');
            $form->select('counselor_id', '咨询师')
                ->options(Counselor::all()->pluck('name', 'id'))
                ->default($counselorId);

            $form->date('date', '日期');
            $form->timeRange('start_time', 'end_time', '时间范围');
            $form->switch('is_available', '是否可预约')
                ->default(true);

            // 表单验证和时间验证
            $form->saving(function (Form $form) {
                // 判断是否是开关状态更新操作
                $inputs = request()->all();
                
                // 检查是否只是开关操作（只更新is_available字段）
                if ($form->isEditing() && isset($inputs['is_available'])) {
                    // 计算有意义的输入项数量（排除_token和_method等）
                    $meaningfulInputs = array_diff_key($inputs, array_flip(['_token', '_method', '_previous_']));
                    
                    if (count($meaningfulInputs) === 1) {
                        // 只有is_available字段，这是开关状态更新，跳过验证
                        return;
                    }
                }
                
                // 正常表单提交验证
                if (!$form->counselor_id) {
                    return $form->response()->error('请选择咨询师');
                }
                if (!$form->date) {
                    return $form->response()->error('请选择日期');
                }
                if (!$form->start_time || !$form->end_time) {
                    return $form->response()->error('请设置时间范围');
                }
                // 检查日期是否在今天之后
                if ($form->date && Carbon::parse($form->date)->lt(Carbon::today())) {
                    return $form->response()->error('不能创建过去的日期排班');
                }

                // 检查时间冲突
                $existingSchedules = CounselorSchedule::where('counselor_id', $form->counselor_id)
                    ->where('date', $form->date)
                    ->where(function ($query) use ($form) {
                        $query->where(function ($q) use ($form) {
                            // 新时间段的开始时间在现有时间段内
                            $q->where('start_time', '<=', $form->start_time)
                              ->where('end_time', '>', $form->start_time);
                        })->orWhere(function ($q) use ($form) {
                            // 新时间段的结束时间在现有时间段内
                            $q->where('start_time', '<', $form->end_time)
                              ->where('end_time', '>=', $form->end_time);
                        })->orWhere(function ($q) use ($form) {
                            // 新时间段完全包含现有时间段
                            $q->where('start_time', '>=', $form->start_time)
                              ->where('end_time', '<=', $form->end_time);
                        });
                    });

                // 编辑模式下排除自身
                if ($form->isEditing()) {
                    $existingSchedules->where('id', '<>', $form->model()->id);
                }

                if ($existingSchedules->exists()) {
                    return $form->response()->error('该时间段与已有排班冲突');
                }
            });

            $form->display('created_at', '创建时间');
            $form->display('updated_at', '更新时间');
        });
    }
}
