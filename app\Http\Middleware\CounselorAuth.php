<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class CounselorAuth
{
    /**
     * 处理验证咨询师登录状态的请求
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        if (!$request->session()->has('counselor_id')) {
            return redirect()->route('counselor.login');
        }

        return $next($request);
    }
}
