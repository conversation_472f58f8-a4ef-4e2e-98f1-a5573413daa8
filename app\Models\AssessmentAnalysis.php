<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Dcat\Admin\Traits\HasDateTimeFormatter;

/**
 * 心理测评分析结果模型
 */
class AssessmentAnalysis extends Model
{
    use HasDateTimeFormatter;

    // 批量赋值字段
    protected $fillable = [
        'response_id', 'overall_score', 'level_name', 'description', 
        'suggestions', 'detailed_analysis', 'detail_json', 'ai_analysis'
    ];

    // 将 detail_json 字段自动转换为数组
    protected $casts = [
        'detail_json' => 'array',
    ];

    /**
     * 分析结果所属的答卷
     */
    public function response(): BelongsTo
    {
        return $this->belongsTo(AssessmentResponse::class, 'response_id');
    }
}
