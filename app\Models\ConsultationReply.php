<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Dcat\Admin\Traits\HasDateTimeFormatter;

class ConsultationReply extends Model
{
    use HasFactory, HasDateTimeFormatter;

    protected $fillable = [
        'consultation_id',
        'user_id',
        'admin_id',
        'content',
        'is_admin_reply',
        'identity'
    ];

    protected $casts = [
        'is_admin_reply' => 'boolean',
    ];

    // 关联留言
    public function consultation()
    {
        return $this->belongsTo(Consultation::class);
    }

    // 关联用户
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    // 关联管理员（如果是管理员回复）
    public function admin()
    {
        return $this->belongsTo(\Dcat\Admin\Models\Administrator::class, 'admin_id');
    }
} 