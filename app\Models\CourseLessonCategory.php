<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Dcat\Admin\Traits\HasDateTimeFormatter;

class CourseLessonCategory extends Model
{
    use HasFactory, HasDateTimeFormatter;

    protected $fillable = [
        'name',
        'description',
        'icon',
        'sort',
        'is_active'
    ];

    protected $casts = [
        'is_active' => 'boolean'
    ];

    // 与课程的关联
    public function lessons()
    {
        return $this->hasMany(CourseLesson::class, 'category_id');
    }

    // 获取已发布的课程
    public function publishedLessons()
    {
        return $this->lessons()->where('status', CourseLesson::STATUS_PUBLISHED);
    }

    // 范围查询：启用的分类
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}
