@extends('layouts.counselor')

@section('title', '个人资料 - 心理健康平台')

@section('custom-styles')
<style>
    /* 编辑器样式 */
    .rich-editor {
        border: 1px solid #ced4da;
        border-radius: 8px;
        font-family: "SF Pro Text", "SF Pro Icons", "Helvetica Neue", Helvetica, Arial, sans-serif;
        font-size: 15px;
    }
    .rich-editor:focus {
        outline: none;
        border-color: #3498db;
        box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
    }
    
    /* 工具栏样式 */
    .editor-toolbar {
        background: #f8f9fa;
        border: 1px solid #ced4da;
        border-bottom: none;
        border-radius: 8px 8px 0 0;
        padding: 5px;
        display: flex;
        flex-wrap: wrap;
    }
    
    .editor-toolbar button {
        background: transparent;
        border: none;
        border-radius: 4px;
        padding: 5px 8px;
        margin: 2px;
        cursor: pointer;
    }
    
    .editor-toolbar button:hover {
        background: #e9ecef;
    }
    
    .editor-toolbar .separator {
        width: 1px;
        background: #dee2e6;
        margin: 0 5px;
    }
    .profile-container {
        max-width: 800px;
        margin: 30px auto;
        padding: 25px;
        background: white;
        border-radius: 15px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    }
    
    .profile-header {
        display: flex;
        align-items: center;
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 1px solid #f0f0f0;
    }
    
    .avatar-container {
        position: relative;
        width: 120px;
        height: 120px;
        margin-right: 30px;
    }
    
    .avatar {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        object-fit: cover;
        border: 3px solid #fff;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .avatar-edit {
        position: absolute;
        bottom: 0;
        right: 0;
        background: #3498db;
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        cursor: pointer;
        border: 2px solid white;
    }
    
    .counselor-info h2 {
        font-size: 24px;
        margin-bottom: 8px;
    }
    
    .counselor-info p {
        color: #666;
        margin-bottom: 5px;
    }
    
    .profile-tabs {
        display: flex;
        border-bottom: 1px solid #e9ecef;
        margin-bottom: 25px;
    }
    
    .profile-tab {
        padding: 12px 20px;
        cursor: pointer;
        font-weight: 500;
        color: #495057;
        border-bottom: 2px solid transparent;
    }
    
    .profile-tab.active {
        color: #3498db;
        border-bottom: 2px solid #3498db;
    }
    
    .tab-content {
        display: none;
    }
    
    .tab-content.active {
        display: block;
    }
    
    .form-group {
        margin-bottom: 20px;
    }
    
    .form-label {
        display: block;
        margin-bottom: 8px;
        font-weight: 500;
        color: #495057;
    }
    
    .form-control {
        width: 100%;
        padding: 12px 15px;
        border: 1px solid #ced4da;
        border-radius: 8px;
        font-size: 15px;
    }
    
    .form-control:focus {
        outline: none;
        border-color: #3498db;
        box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
    }
    
    textarea.form-control {
        min-height: 120px;
        resize: vertical;
    }
    
    .btn-submit {
        background: #3498db;
        color: white;
        border: none;
        border-radius: 8px;
        padding: 12px 25px;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        transition: background 0.3s;
    }
    
    .btn-submit:hover {
        background: #2980b9;
    }
    
    .btn-cancel {
        background: #f1f1f1;
        color: #666;
        border: none;
        border-radius: 8px;
        padding: 12px 25px;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        margin-right: 10px;
        transition: background 0.3s;
    }
    
    .btn-cancel:hover {
        background: #e1e1e1;
    }
    
    .alert {
        padding: 15px 20px;
        border-radius: 8px;
        margin-bottom: 20px;
    }
    
    .alert-success {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }
    
    .alert-danger {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }
    
    .error-message {
        color: #dc3545;
        font-size: 14px;
        margin-top: 5px;
    }
    
    .form-divider {
        border-top: 1px solid #f0f0f0;
        margin: 30px 0;
        padding-top: 10px;
    }
    
    .divider-title {
        font-size: 18px;
        font-weight: 500;
        margin-bottom: 20px;
    }
    
    #avatar-file {
        display: none;
    }
</style>
@endsection

@section('content')
<div class="container">
    <div class="profile-container">
        <div class="profile-header">
            <div class="avatar-container">
                <img class="avatar" src="{{ $counselor->avatar ? asset('storage/'.$counselor->avatar) : asset('images/default-avatar.jpg') }}" alt="{{ $counselor->name }}">
                <label for="avatar-file" class="avatar-edit" id="avatar-trigger">
                    <i class="bi bi-pencil"></i>
                </label>
            </div>
            <div class="counselor-info">
                <h2>{{ $counselor->name }}</h2>
                <p><i class="bi bi-envelope"></i> {{ $counselor->email }}</p>
                <p><i class="bi bi-phone"></i> {{ $counselor->phone }}</p>
            </div>
        </div>
        
        @if(session('success'))
            <div class="alert alert-success">
                {{ session('success') }}
            </div>
        @endif
        
        @if($errors->any())
            <div class="alert alert-danger">
                <ul style="margin: 0; padding-left: 20px;">
                    @foreach($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif
        
        <div class="profile-tabs">
            <div class="profile-tab active" data-tab="basic-info">基本资料</div>
            <div class="profile-tab" data-tab="password">修改密码</div>
        </div>
        
        <form method="POST" action="{{ route('counselor.profile.update') }}" enctype="multipart/form-data">
            @csrf
            
            <input type="file" name="avatar" id="avatar-file" accept="image/*">
            
            <div class="tab-content active" id="basic-info">
                <div class="form-group">
                    <label class="form-label" for="name">姓名</label>
                    <input type="text" name="name" id="name" class="form-control" value="{{ old('name', $counselor->name) }}" required>
                    @error('name')
                        <div class="error-message">{{ $message }}</div>
                    @enderror
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="email">邮箱</label>
                    <input type="email" name="email" id="email" class="form-control" value="{{ old('email', $counselor->email) }}" required>
                    @error('email')
                        <div class="error-message">{{ $message }}</div>
                    @enderror
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="phone">手机号</label>
                    <input type="text" name="phone" id="phone" class="form-control" value="{{ old('phone', $counselor->phone) }}" required>
                    @error('phone')
                        <div class="error-message">{{ $message }}</div>
                    @enderror
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="content-editor">个人简介</label>
                    <div id="content-editor" class="form-control rich-editor" style="min-height:300px; overflow-y:auto; padding:10px;">{!! old('introduction', $counselor->introduction) !!}</div>
                    <textarea name="introduction" id="introduction" style="display:none;">{!! old('introduction', $counselor->introduction) !!}</textarea>
                    @error('introduction')
                        <div class="error-message">{{ $message }}</div>
                    @enderror
                </div>
            </div>
            
            <div class="tab-content" id="password">
                <div class="form-group">
                    <label class="form-label" for="current_password">当前密码</label>
                    <input type="password" name="current_password" id="current_password" class="form-control">
                    @error('current_password')
                        <div class="error-message">{{ $message }}</div>
                    @enderror
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="password">新密码</label>
                    <input type="password" name="password" id="password" class="form-control">
                    @error('password')
                        <div class="error-message">{{ $message }}</div>
                    @enderror
                </div>
                
                <div class="form-group">
                    <label class="form-label" for="password_confirmation">确认新密码</label>
                    <input type="password" name="password_confirmation" id="password_confirmation" class="form-control">
                </div>
                
                <div class="alert alert-success" style="background-color: #fff8e1; color: #856404; border-color: #ffe082;">
                    <i class="bi bi-info-circle"></i> 如不需要修改密码，请留空密码字段
                </div>
            </div>
            
            <div class="form-group" style="text-align: right; margin-top: 30px;">
                <a href="{{ route('counselor.dashboard') }}" class="btn-cancel">取消</a>
                <button type="submit" class="btn-submit">保存修改</button>
            </div>
        </form>
    </div>
</div>
@endsection

@section('scripts')
<!-- 只需要jQuery -->
<script src="https://code.jquery.com/jquery-3.6.4.min.js"></script>
<script>
    // 使用jQuery的ready函数确保DOM加载完成
    $(function() {
        // 标签切换
        const tabs = document.querySelectorAll('.profile-tab');
        const contents = document.querySelectorAll('.tab-content');
        
        tabs.forEach(tab => {
            tab.addEventListener('click', function() {
                // 移除所有标签和内容的 active 类
                tabs.forEach(t => t.classList.remove('active'));
                contents.forEach(c => c.classList.remove('active'));
                
                // 设置当前标签和对应内容为 active
                this.classList.add('active');
                document.getElementById(this.dataset.tab).classList.add('active');
            });
        });
        
        // 头像上传触发
        document.getElementById('avatar-trigger').addEventListener('click', function() {
            document.getElementById('avatar-file').click();
        });
        
        // 头像预览
        document.getElementById('avatar-file').addEventListener('change', function(e) {
            if (e.target.files && e.target.files[0]) {
                const reader = new FileReader();
                
                reader.onload = function(event) {
                    document.querySelector('.avatar').src = event.target.result;
                }
                
                reader.readAsDataURL(e.target.files[0]);
            }
        });
        
        // 将初始化代码移到document.addEventListener('DOMContentLoaded')之外
    });
    
    // 启用HTML编辑器
    $(window).on('load', function() {
        // 设置编辑器为contenteditable
        $('#content-editor').attr('contenteditable', 'true');
        
        // 表单提交前将编辑器内容同步到textarea
        $('form').on('submit', function() {
            $('#introduction').val($('#content-editor').html());
        });
        
        // 添加富文本工具栏
        var toolbar = $('<div class="editor-toolbar"></div>');
        
        // 编辑命令
        var commands = [
            {cmd: 'bold', icon: '<strong>B</strong>', title: '粗体'},
            {cmd: 'italic', icon: '<em>I</em>', title: '斜体'},
            {cmd: 'underline', icon: '<u>U</u>', title: '下划线'},
            {separator: true},
            {cmd: 'insertUnorderedList', icon: '•', title: '无序列表'},
            {cmd: 'insertOrderedList', icon: '1.', title: '有序列表'},
            {separator: true},
            {cmd: 'justifyLeft', icon: '←', title: '左对齐'},
            {cmd: 'justifyCenter', icon: '↔', title: '居中'},
            {cmd: 'justifyRight', icon: '→', title: '右对齐'},
            {separator: true},
            {cmd: 'createLink', icon: '🔗', title: '链接'},
            {cmd: 'removeFormat', icon: 'T×', title: '清除格式'}
        ];
        
        // 生成工具栏按钮
        $.each(commands, function(i, command) {
            if (command.separator) {
                toolbar.append('<div class="separator"></div>');
            } else {
                var button = $('<button type="button" title="' + command.title + '">' + command.icon + '</button>');
                button.click(function(e) {
                    e.preventDefault();
                    if (command.cmd === 'createLink') {
                        var url = prompt('请输入链接地址:', 'http://');
                        if (url) {
                            document.execCommand(command.cmd, false, url);
                        }
                    } else {
                        document.execCommand(command.cmd, false, null);
                    }
                    $('#content-editor').focus();
                });
                toolbar.append(button);
            }
        });
        
        // 插入工具栏
        toolbar.insertBefore('#content-editor');
        
        console.log('原生富文本编辑器初始化成功');
        
        // 禁用拖拽
        $('#content-editor').on('dragstart dragover', function(e) {
            e.preventDefault();
            return false;
        });
        
        // 提交前再次同步内容
        window.addEventListener('beforeunload', function() {
            $('#introduction').val($('#content-editor').html());
        });

    });
</script>
@endsection
