<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Dcat\Admin\Traits\HasDateTimeFormatter;

class ArticleCategory extends Model
{
    use HasDateTimeFormatter;
    
    protected $fillable = ['name', 'description', 'sort_order', 'is_active'];
    
    /**
     * 获取该分类下的所有文章
     */
    public function articles()
    {
        return $this->hasMany(Article::class, 'category_id');
    }
    
    /**
     * 获取所有可用的分类
     */
    public static function getActiveCategories()
    {
        return self::where('is_active', 1)
                ->orderBy('sort_order')
                ->get();
    }
}
