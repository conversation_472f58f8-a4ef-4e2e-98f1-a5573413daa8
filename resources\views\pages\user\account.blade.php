@extends('layouts.app')

@section('title', '账号管理 - 心理咨询平台')

@section('custom-styles')
<style>
    body {
        background-color: #f8f9fa;
    }
    
    .container {
        max-width: 900px;
        margin: 0 auto;
        padding-bottom: 80px !important; /* 为底部菜单栏预留空间 */
    }
    
    .page-header {
        display: flex;
        align-items: center;
        background: linear-gradient(135deg, #5b7cef 0%, #3c67e3 100%);
        color: white;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        border-radius: 0 0 10px 10px;
        padding: 12px 15px;
        margin-bottom: 15px;
    }
    
    .back-button {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 30px;
        height: 30px;
    }
    
    .back-icon {
        border-top: 2px solid white;
        border-left: 2px solid white;
        width: 12px;
        height: 12px;
        transform: rotate(-45deg);
    }
    
    .page-header h1 {
        font-size: 18px;
        font-weight: 500;
        margin: 0 auto;
        text-align: center;
        flex-grow: 1;
        margin-left: -30px;
        text-shadow: 0 1px 2px rgba(0,0,0,0.1);
    }
    
    .account-card {
        background-color: #fff;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        margin: 15px;
        padding: 20px;
        border: 1px solid rgba(0, 0, 0, 0.03);
    }
    
    .section-title {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 20px;
        color: #333;
        border-bottom: 1px solid #f0f0f0;
        padding-bottom: 10px;
    }
    
    .profile-form-group {
        margin-bottom: 20px;
    }
    
    .profile-form-label {
        display: block;
        margin-bottom: 8px;
        font-weight: 600;
        font-size: 14px;
        color: #444;
    }
    
    .profile-form-input {
        width: 100%;
        padding: 12px 15px;
        border: 1px solid #ddd;
        border-radius: 8px;
        font-size: 15px;
        transition: all 0.3s ease;
    }
    
    .profile-form-input:focus {
        border-color: #3c67e3;
        box-shadow: 0 0 0 2px rgba(60, 103, 227, 0.1);
        outline: none;
    }
    
    .btn-primary {
        display: block;
        width: 100%;
        padding: 14px;
        background: linear-gradient(135deg, #2c7ffc, #7b58ff);
        color: white;
        text-align: center;
        border: none;
        border-radius: 8px;
        font-size: 16px;
        font-weight: 600;
        text-decoration: none;
        box-shadow: 0 4px 10px rgba(44, 127, 252, 0.2);
        transition: all 0.3s ease;
        cursor: pointer;
        margin-top: 10px;
    }
    
    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 15px rgba(44, 127, 252, 0.25);
    }
    
    .account-info {
        background-color: #f8faff;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 20px;
    }
    
    .account-info p {
        margin: 8px 0;
        color: #555;
        font-size: 14px;
    }
    
    .account-info strong {
        color: #333;
        font-weight: 600;
    }
    
    .alert {
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 20px;
    }
    
    .alert-success {
        background-color: #e3fcef;
        color: #0d915c;
        border: 1px solid #c3f0da;
    }
    
    .alert-danger {
        background-color: #fee7e6;
        color: #d6292c;
        border: 1px solid #fcd0cf;
    }
</style>
@endsection

@section('content')
<div class="page-header">
    <a href="{{ route('my') }}" class="back-button">
        <div class="back-icon"></div>
    </a>
    <h1>账号管理</h1>
    <div style="width: 30px;"></div>
</div>

<div class="container">
    @if(session('success'))
    <div class="alert alert-success">
        {{ session('success') }}
    </div>
    @endif
    
    @if($errors->any())
    <div class="alert alert-danger">
        <ul style="margin: 0; padding-left: 15px;">
            @foreach($errors->all() as $error)
            <li>{{ $error }}</li>
            @endforeach
        </ul>
    </div>
    @endif
    
    <div class="account-card">
        <h2 class="section-title">账号信息</h2>
        
        <div class="account-info">
            <p><strong>当前登录账号：</strong>{{ Auth::user()->name }}</p>
            <p><strong>注册时间：</strong>{{ Auth::user()->created_at->format('Y-m-d H:i') }}</p>
            <p><strong>最后登录：</strong>{{ Auth::user()->last_login_at ? date('Y-m-d H:i', strtotime(Auth::user()->last_login_at)) : '未记录' }}</p>
            <p><strong>登录IP：</strong>{{ Auth::user()->last_login_ip ?? '未记录' }}</p>
        </div>
        
        <h2 class="section-title">修改密码</h2>
        
        <form action="{{ route('user.change_password') }}" method="POST">
            @csrf
            <div class="profile-form-group">
                <label for="current_password" class="profile-form-label">当前密码</label>
                <input type="password" id="current_password" name="current_password" class="profile-form-input" required>
            </div>
            
            <div class="profile-form-group">
                <label for="password" class="profile-form-label">新密码</label>
                <input type="password" id="password" name="password" class="profile-form-input" required>
            </div>
            
            <div class="profile-form-group">
                <label for="password_confirmation" class="profile-form-label">确认新密码</label>
                <input type="password" id="password_confirmation" name="password_confirmation" class="profile-form-input" required>
            </div>
            
            <button type="submit" class="btn-primary">修改密码</button>
        </form>
    </div>
</div>
@endsection
