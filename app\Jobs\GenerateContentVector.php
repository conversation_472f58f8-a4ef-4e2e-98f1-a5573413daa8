<?php

namespace App\Jobs;

use App\Models\ContentIndex;
use App\Services\AlibabaVectorService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Exception;

/**
 * 生成内容向量队列任务
 */
class GenerateContentVector implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 任务超时时间（秒）
     *
     * @var int
     */
    public int $timeout = 300;

    /**
     * 最大重试次数
     *
     * @var int
     */
    public int $tries = 3;

    /**
     * 重试延迟时间（秒）
     *
     * @var array
     */
    public array $backoff = [30, 60, 120];

    /**
     * 内容索引ID
     *
     * @var int
     */
    protected int $contentIndexId;

    /**
     * 创建新的任务实例
     *
     * @param ContentIndex $contentIndex
     */
    public function __construct(ContentIndex $contentIndex)
    {
        $this->contentIndexId = $contentIndex->id;
        
        // 设置队列和延迟
        $this->onQueue('vector-generation');
        
        Log::info('向量生成任务已创建', [
            'content_index_id' => $this->contentIndexId,
            'type' => $contentIndex->type,
            'title' => $contentIndex->title,
        ]);
    }

    /**
     * 执行任务
     *
     * @param AlibabaVectorService $vectorService
     * @return void
     * @throws Exception
     */
    public function handle(AlibabaVectorService $vectorService): void
    {
        $startTime = microtime(true);
        
        Log::info('开始生成向量', [
            'content_index_id' => $this->contentIndexId,
            'attempt' => $this->attempts(),
        ]);

        try {
            // 获取内容索引
            $contentIndex = ContentIndex::find($this->contentIndexId);
            
            if (!$contentIndex) {
                Log::warning('内容索引不存在，跳过向量生成', [
                    'content_index_id' => $this->contentIndexId,
                ]);
                return;
            }

            // 检查是否需要生成向量
            if ($contentIndex->hasVector && !$contentIndex->needsVectorRegeneration()) {
                Log::info('向量已存在且无需重新生成', [
                    'content_index_id' => $this->contentIndexId,
                ]);
                return;
            }

            // 准备文本内容
            $textContent = $this->prepareTextContent($contentIndex);
            
            if (empty($textContent)) {
                Log::warning('文本内容为空，无法生成向量', [
                    'content_index_id' => $this->contentIndexId,
                ]);
                return;
            }

            // 生成向量
            $vector = $vectorService->generateTextVector($textContent);
            
            // 保存向量数据
            $contentIndex->setVectorData($vector);
            $contentIndex->save();

            // 可选：存储到向量数据库
            if (config('services.alibaba_vector.store_in_database', false)) {
                $this->storeInVectorDatabase($vectorService, $contentIndex, $vector);
            }

            $duration = round((microtime(true) - $startTime) * 1000, 2);
            
            Log::info('向量生成成功', [
                'content_index_id' => $this->contentIndexId,
                'vector_dimension' => count($vector),
                'text_length' => mb_strlen($textContent),
                'duration_ms' => $duration,
                'attempt' => $this->attempts(),
            ]);

        } catch (Exception $e) {
            $duration = round((microtime(true) - $startTime) * 1000, 2);
            
            Log::error('向量生成失败', [
                'content_index_id' => $this->contentIndexId,
                'attempt' => $this->attempts(),
                'max_tries' => $this->tries,
                'duration_ms' => $duration,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            // 如果是最后一次尝试，标记为失败
            if ($this->attempts() >= $this->tries) {
                $this->markAsFailed($e->getMessage());
            }

            throw $e;
        }
    }

    /**
     * 准备文本内容用于向量生成
     *
     * @param ContentIndex $contentIndex
     * @return string
     */
    protected function prepareTextContent(ContentIndex $contentIndex): string
    {
        $textParts = [];
        
        // 添加标题
        if ($contentIndex->title) {
            $textParts[] = $contentIndex->title;
        }
        
        // 添加摘要
        if ($contentIndex->summary) {
            $textParts[] = $contentIndex->summary;
        }
        
        // 尝试从关联模型获取更多内容
        if ($contentIndex->indexable) {
            $additionalContent = $this->extractContentFromModel($contentIndex->indexable);
            if ($additionalContent) {
                $textParts[] = $additionalContent;
            }
        }
        
        // 添加元数据中的文本内容
        if ($contentIndex->metadata) {
            $metadataText = $this->extractTextFromMetadata($contentIndex->metadata);
            if ($metadataText) {
                $textParts[] = $metadataText;
            }
        }
        
        $fullText = implode(' ', $textParts);
        
        // 清理和优化文本
        return $this->cleanText($fullText);
    }

    /**
     * 从关联模型提取内容
     *
     * @param mixed $model
     * @return string|null
     */
    protected function extractContentFromModel($model): ?string
    {
        $content = '';
        
        // 根据模型类型提取不同字段
        if (method_exists($model, 'getContentForVector')) {
            return $model->getContentForVector();
        }
        
        // 通用字段提取
        $contentFields = ['content', 'description', 'body', 'text'];
        
        foreach ($contentFields as $field) {
            if (isset($model->$field) && !empty($model->$field)) {
                $content .= ' ' . $model->$field;
            }
        }
        
        return trim($content) ?: null;
    }

    /**
     * 从元数据提取文本内容
     *
     * @param array $metadata
     * @return string|null
     */
    protected function extractTextFromMetadata(array $metadata): ?string
    {
        $textParts = [];
        
        // 提取常见的文本字段
        $textFields = ['tags', 'keywords', 'category', 'description', 'notes'];
        
        foreach ($textFields as $field) {
            if (isset($metadata[$field])) {
                $value = $metadata[$field];
                
                if (is_array($value)) {
                    $textParts[] = implode(' ', $value);
                } elseif (is_string($value)) {
                    $textParts[] = $value;
                }
            }
        }
        
        return !empty($textParts) ? implode(' ', $textParts) : null;
    }

    /**
     * 清理文本内容
     *
     * @param string $text
     * @return string
     */
    protected function cleanText(string $text): string
    {
        // 移除HTML标签
        $text = strip_tags($text);
        
        // 移除多余的空白字符
        $text = preg_replace('/\s+/', ' ', $text);
        
        // 移除特殊字符（保留中文、英文、数字和基本标点）
        $text = preg_replace('/[^\p{L}\p{N}\s\.,!?;:()"\'-]/u', '', $text);
        
        // 限制长度（向量生成通常有文本长度限制）
        $maxLength = config('services.alibaba_vector.max_text_length', 8000);
        if (mb_strlen($text) > $maxLength) {
            $text = mb_substr($text, 0, $maxLength);
        }
        
        return trim($text);
    }

    /**
     * 存储到向量数据库
     *
     * @param AlibabaVectorService $vectorService
     * @param ContentIndex $contentIndex
     * @param array $vector
     * @return void
     */
    protected function storeInVectorDatabase(
        AlibabaVectorService $vectorService,
        ContentIndex $contentIndex,
        array $vector
    ): void {
        try {
            $metadata = [
                'id' => $contentIndex->id,
                'type' => $contentIndex->type,
                'title' => $contentIndex->title,
                'url' => $contentIndex->url,
                'created_at' => $contentIndex->created_at->toISOString(),
            ];
            
            $vectorService->storeVector(
                "content_index_{$contentIndex->id}",
                $vector,
                $metadata
            );
            
            Log::info('向量已存储到向量数据库', [
                'content_index_id' => $contentIndex->id,
            ]);
            
        } catch (Exception $e) {
            Log::warning('向量存储到数据库失败', [
                'content_index_id' => $contentIndex->id,
                'error' => $e->getMessage(),
            ]);
            // 不抛出异常，因为本地存储已成功
        }
    }

    /**
     * 标记为失败
     *
     * @param string $reason
     * @return void
     */
    protected function markAsFailed(string $reason): void
    {
        try {
            $contentIndex = ContentIndex::find($this->contentIndexId);
            
            if ($contentIndex) {
                // 可以添加失败标记字段到数据库
                $metadata = $contentIndex->metadata ?? [];
                $metadata['vector_generation_failed'] = true;
                $metadata['vector_generation_error'] = $reason;
                $metadata['vector_generation_failed_at'] = now()->toISOString();
                
                $contentIndex->update(['metadata' => $metadata]);
            }
            
        } catch (Exception $e) {
            Log::error('标记向量生成失败状态时出错', [
                'content_index_id' => $this->contentIndexId,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * 任务失败时的处理
     *
     * @param Exception $exception
     * @return void
     */
    public function failed(Exception $exception): void
    {
        Log::error('向量生成任务最终失败', [
            'content_index_id' => $this->contentIndexId,
            'attempts' => $this->attempts(),
            'error' => $exception->getMessage(),
        ]);
        
        $this->markAsFailed($exception->getMessage());
    }

    /**
     * 获取任务的唯一ID（用于防止重复任务）
     *
     * @return string
     */
    public function uniqueId(): string
    {
        return "generate_vector_{$this->contentIndexId}";
    }

    /**
     * 任务应该是唯一的
     *
     * @return bool
     */
    public function shouldBeUnique(): bool
    {
        return true;
    }

    /**
     * 唯一性锁的持续时间（秒）
     *
     * @return int
     */
    public function uniqueFor(): int
    {
        return 600; // 10分钟
    }
} 