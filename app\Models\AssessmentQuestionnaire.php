<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Dcat\Admin\Traits\HasDateTimeFormatter;

/**
 * 心理测评问卷模型
 */
class AssessmentQuestionnaire extends Model
{
    use HasDateTimeFormatter;

    // 批量赋值字段
    protected $fillable = [
        'title', 'description', 'domain',
        'question_count', 'est_duration',
        'cover_image', 'is_active'
    ];

    /**
     * 获取问卷下所有题目
     */
    public function questions(): HasMany
    {
        return $this->hasMany(AssessmentQuestion::class, 'questionnaire_id', 'id');
    }

    /**
     * 获取该问卷的所有答卷
     */
    public function responses(): HasMany
    {
        return $this->hasMany(AssessmentResponse::class, 'questionnaire_id', 'id');
    }

    /**
     * 获取该问卷的结果配置
     */
    public function resultConfigs(): HasMany
    {
        return $this->hasMany(AssessmentResultConfig::class, 'questionnaire_id', 'id')->orderBy('sort_order');
    }
}
