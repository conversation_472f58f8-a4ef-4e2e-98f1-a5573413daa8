<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Dcat\Admin\Traits\HasDateTimeFormatter;

class Message extends Model
{
    use HasFactory, HasDateTimeFormatter;

    // 发送者类型常量
    const SENDER_USER = 'user';
    const SENDER_COUNSELOR = 'counselor';

    protected $fillable = [
        'user_id',
        'counselor_id',
        'appointment_id',
        'sender_type',
        'content',
        'is_read',
        'read_at',
        'attachment'
    ];

    protected $casts = [
        'is_read' => 'boolean',
        'read_at' => 'datetime',
    ];

    // 与用户的关联
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    // 与咨询师的关联
    public function counselor()
    {
        return $this->belongsTo(Counselor::class);
    }

    // 与预约的关联
    public function appointment()
    {
        return $this->belongsTo(ConsultationAppointment::class, 'appointment_id');
    }

    // 是否是咨询师发送的
    public function isSentByCounselor()
    {
        return $this->sender_type === self::SENDER_COUNSELOR;
    }

    // 是否是用户发送的
    public function isSentByUser()
    {
        return $this->sender_type === self::SENDER_USER;
    }

    // 将消息标记为已读
    public function markAsRead()
    {
        if (!$this->is_read) {
            $this->is_read = true;
            $this->read_at = now();
            $this->save();
        }
    }
}
