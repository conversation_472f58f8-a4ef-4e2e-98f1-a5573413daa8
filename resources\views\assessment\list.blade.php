@extends('layouts.app')

@section('title', '心理测评问卷')

@section('custom-styles')
    <link rel="stylesheet" href="{{ asset('css/assessment.css') }}">
@endsection

@section('content')
<div class="container assessment-list">
    <h1 class="my-4">心理测评问卷</h1>
    <div class="row">
        @foreach($questionnaires as $questionnaire)
        <div class="col-md-6 mb-4">
            <div class="card assessment-card h-100">
                <div class="card-body">
                    <h5 class="card-title">{{ $questionnaire->title }}</h5>
                    <p class="card-text">{{ $questionnaire->description }}</p>
                    <p>领域：{{ $questionnaire->domain }}</p>
                    <p>题目数：{{ $questionnaire->question_count }}，预计时长：{{ $questionnaire->est_duration }} 分钟</p>
                    <a href="{{ route('assessments.show', $questionnaire->id) }}" class="btn btn-primary">开始测评</a>
                </div>
            </div>
        </div>
        @endforeach
    </div>
</div>
@endsection

@section('scripts')
    <script src="{{ asset('js/assessment.js') }}"></script>
@endsection
