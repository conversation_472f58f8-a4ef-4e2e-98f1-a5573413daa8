<?php
namespace App\Admin\Controllers;

use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use App\Models\SystemSetting;

class SystemConfigController extends AdminController
{
    protected $title = '系统配置';
    
    protected function grid()
    {
        return Grid::make(new SystemSetting(), function (Grid $grid) {
            $grid->column('id')->sortable();
            $grid->column('key','键')->help('不懂不要改动');
            $grid->column('label','描述')->editable();
            $grid->column('value','值')->display(function ($v) {
                // 显示简要
                return strlen($v) > 50 ? substr($v, 0, 50) . '…' : $v;
            })->editable();
            $grid->column('updated_at')->sortable();
            $grid->disableViewButton();
        });
    }

    protected function form()
    {
        return Form::make(new SystemSetting(), function (Form $form) {
            $form->display('id');
            $form->text('key','键')->rules('required|unique:system_settings,key,{{id}}')->help('不懂不要改动');
            $form->text('label','描述')->rules('required');
            // 这里用 textarea，后续如有复杂 JSON 项也能直接编辑
            $form->textarea('value','值')->rows(4)->help('纯文本或 JSON 字符串');
            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
