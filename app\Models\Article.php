<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Dcat\Admin\Traits\HasDateTimeFormatter;
use App\Models\User;
use App\Models\Traits\HasContentIndex;
use App\Contracts\HasContentIndex as HasContentIndexContract;

class Article extends Model implements HasContentIndexContract
{
    use HasDateTimeFormatter, HasContentIndex;
    
    protected $fillable = [
        'title', 'content', 'summary', 'image', 'category_id', 
        'author_id', 'is_recommended', 'status', 'publish_time', 'source'
    ];
    
    protected $attributes = [
        'views' => 0,
        'is_recommended' => 0,
        'author_id' => null,
    ];
    
    protected $dates = [
        'publish_time',
    ];
    
    // 状态常量
    const STATUS_DRAFT = 'draft'; // 草稿
    const STATUS_PENDING = 'pending'; // 待审核
    const STATUS_PUBLISHED = 'published'; // 已发布
    const STATUS_REJECTED = 'rejected'; // 已拒绝
    
    /**
     * 获取状态列表
     */
    public static function getStatusMap()
    {
        return [
            self::STATUS_DRAFT => '草稿',
            self::STATUS_PENDING => '待审核',
            self::STATUS_PUBLISHED => '已发布',
            self::STATUS_REJECTED => '已拒绝',
        ];
    }
    
    /**
     * 获取文章所属分类
     */
    public function category()
    {
        return $this->belongsTo(ArticleCategory::class, 'category_id');
    }
    
    /**
     * 获取文章作者
     */
    public function author()
    {
        return $this->belongsTo(User::class, 'author_id');
    }
    
    /**
     * 获取文章标签
     */
    public function tags()
    {
        return $this->belongsToMany(ArticleTag::class, 'article_tag', 'article_id', 'tag_id');
    }
    
    /**
     * 获取文章审核记录
     */
    public function reviews()
    {
        return $this->hasMany(ArticleReview::class);
    }
    
    /**
     * 文章是否已发布
     */
    public function isPublished()
    {
        return $this->status === self::STATUS_PUBLISHED;
    }
    
    /**
     * 增加文章浏览量
     */
    public function incrementViews()
    {
        $this->increment('views');
        return $this;
    }
    
    /**
     * 获取热门文章
     */
    public static function getHotArticles($limit = 5)
    {
        return self::where('status', self::STATUS_PUBLISHED)
                ->orderBy('views', 'desc')
                ->limit($limit)
                ->get();
    }
    
    /**
     * 获取推荐文章
     */
    public static function getRecommendedArticles($limit = 3)
    {
        return self::where('status', self::STATUS_PUBLISHED)
                ->where('is_recommended', 1)
                ->orderBy('publish_time', 'desc')
                ->limit($limit)
                ->get();
    }

    /**
     * 实现HasContentIndex接口：获取内容索引类型
     */
    public function getContentIndexType(): string
    {
        return 'article';
    }

    /**
     * 实现HasContentIndex接口：获取内容索引标题
     */
    public function getContentIndexTitle(): string
    {
        return $this->title ?? '';
    }

    /**
     * 实现HasContentIndex接口：获取内容索引摘要
     */
    public function getContentIndexSummary(): ?string
    {
        return $this->summary;
    }

    /**
     * 实现HasContentIndex接口：获取内容索引元数据
     */
    public function getContentIndexMetadata(): array
    {
        $metadata = [
            'category' => $this->category?->name,
            'author' => $this->author?->name,
            'views' => $this->views,
            'is_recommended' => $this->is_recommended,
            'status' => $this->status,
            'publish_time' => $this->publish_time ? (is_string($this->publish_time) ? $this->publish_time : $this->publish_time->toISOString()) : null,
            'source' => $this->source,
        ];

        // 添加标签信息
        if ($this->relationLoaded('tags')) {
            $metadata['tags'] = $this->tags->pluck('name')->toArray();
        }

        return array_filter($metadata, fn($value) => $value !== null);
    }

    /**
     * 实现HasContentIndex接口：检查是否应该被索引
     */
    public function shouldBeIndexed(): bool
    {
        return $this->status === self::STATUS_PUBLISHED;
    }

    /**
     * 实现HasContentIndex接口：获取用于向量生成的内容
     */
    public function getContentForVector(): string
    {
        $parts = [];
        
        if ($this->title) {
            $parts[] = $this->title;
        }
        
        if ($this->summary) {
            $parts[] = $this->summary;
        }
        
        if ($this->content) {
            // 清理HTML标签并截取前2000字符
            $cleanContent = strip_tags($this->content);
            $parts[] = mb_substr($cleanContent, 0, 2000);
        }
        
        return implode(' ', $parts);
    }

    /**
     * 实现HasContentIndex接口：获取内容索引URL
     */
    public function getContentIndexUrl(): ?string
    {
        try {
            // 尝试生成路由，如果失败则返回null
            if (\Route::has('articles.show')) {
                return route('articles.show', $this->id);
            }
            // 如果路由不存在，返回一个简单的URL
            return url("/articles/{$this->id}");
        } catch (\Exception $e) {
            return null;
        }
    }
}
