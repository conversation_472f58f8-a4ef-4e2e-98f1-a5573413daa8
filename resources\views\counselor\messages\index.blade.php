<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>消息管理 - 心理健康平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: "SF Pro Text", "SF Pro Icons", "Helvetica Neue", Helvetica, Arial, sans-serif;
        }
        .sidebar {
            position: fixed;
            top: 0;
            bottom: 0;
            left: 0;
            z-index: 100;
            padding: 48px 0 0;
            box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
            background: #fff;
        }
        .sidebar-sticky {
            height: calc(100vh - 48px);
            overflow-x: hidden;
            overflow-y: auto;
        }
        .nav-link {
            color: #333;
            font-weight: 500;
            padding: 15px 25px;
            display: flex;
            align-items: center;
        }
        .nav-link:hover {
            background: rgba(52, 152, 219, 0.1);
        }
        .nav-link.active {
            color: #3498db;
            background: rgba(52, 152, 219, 0.15);
            position: relative;
        }
        .nav-link.active::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: #3498db;
        }
        .nav-icon {
            margin-right: 10px;
            font-size: 20px;
        }
        .badge-notifications {
            background: #e74c3c;
            color: white;
            font-size: 12px;
            padding: 3px 6px;
            border-radius: 10px;
        }
        .navbar {
            background: white;
        }
        .page-header {
            background: linear-gradient(45deg, #3498db, #8e44ad);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
        }
        .page-header h1 {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 0;
        }
        .message-list {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .message-item {
            display: flex;
            padding: 20px;
            border-bottom: 1px solid #f1f1f1;
            transition: background-color 0.2s;
            text-decoration: none;
            color: inherit;
        }
        .message-item:hover {
            background-color: #f8f9fa;
        }
        .message-item.unread {
            background-color: rgba(52, 152, 219, 0.05);
        }
        .message-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            overflow: hidden;
            margin-right: 15px;
            flex-shrink: 0;
        }
        .message-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .message-content {
            flex-grow: 1;
            min-width: 0;
        }
        .message-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }
        .message-name {
            font-weight: 600;
            font-size: 16px;
            color: #333;
        }
        .message-time {
            color: #999;
            font-size: 14px;
        }
        .message-preview {
            color: #666;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-bottom: 8px;
            font-size: 15px;
        }
        .message-meta {
            display: flex;
            justify-content: space-between;
            font-size: 14px;
        }
        .message-appointment {
            color: #3498db;
        }
        .message-unread {
            background: #e74c3c;
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 12px;
            font-weight: 500;
        }
        .no-messages {
            text-align: center;
            padding: 50px 20px;
        }
        .no-messages i {
            font-size: 48px;
            color: #ddd;
            margin-bottom: 15px;
            display: block;
        }
        .no-messages p {
            color: #999;
            font-size: 16px;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-light bg-white fixed-top shadow-sm">
        <div class="container-fluid">
            <span class="navbar-brand">心理健康平台 - 咨询师工作台</span>
            <div class="d-flex">
                <div class="dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <span class="me-2">{{ session('counselor_name') }}</span>
                        <img src="{{ session('counselor_avatar') ? asset('storage/'.session('counselor_avatar')) : asset('images/default-avatar.jpg') }}" width="32" height="32" class="rounded-circle">
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                        <li>
                            <a href="{{ route('counselor.profile') }}" class="dropdown-item">
                                <i class="bi bi-person me-2"></i>个人资料
                            </a>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <form method="POST" action="{{ route('counselor.logout') }}">
                                @csrf
                                <button type="submit" class="dropdown-item">
                                    <i class="bi bi-box-arrow-right me-2"></i>退出登录
                                </button>
                            </form>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- 侧边导航 -->
            <nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block sidebar">
                <div class="position-sticky sidebar-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="{{ route('counselor.dashboard') }}">
                                <i class="bi bi-house-door nav-icon"></i>
                                首页
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ route('counselor.appointments') }}">
                                <i class="bi bi-calendar-check nav-icon"></i>
                                预约管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="{{ route('counselor.messages') }}">
                                <i class="bi bi-chat-dots nav-icon"></i>
                                消息中心
                                @if(array_sum($unreadCounts) > 0)
                                <span class="ms-auto badge-notifications">{{ array_sum($unreadCounts) }}</span>
                                @endif
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主要内容 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4" style="margin-top: 48px;">
                <div class="page-header d-flex justify-content-between align-items-center">
                    <h1>消息中心</h1>
                </div>

                <div class="message-list">
                    @if(count($users) > 0)
                        @foreach($users as $user)
                            <a href="{{ route('counselor.messages.show', $user->id) }}" class="message-item {{ $unreadCounts[$user->id] > 0 ? 'unread' : '' }}">
                                <div class="message-avatar">
                                    <img src="{{ $user->avatar ? url('storage/'.$user->avatar) : url('images/default-avatar.jpg') }}" alt="{{ $user->name }}">
                                </div>
                                <div class="message-content">
                                    <div class="message-header">
                                        <div class="message-name">{{ $user->name }}</div>
                                        <div class="message-time">
                                            @if(isset($lastMessages[$user->id]))
                                                {{ \Carbon\Carbon::parse($lastMessages[$user->id]->created_at)->diffForHumans() }}
                                            @endif
                                        </div>
                                    </div>
                                    <div class="message-preview">
                                        @if(isset($lastMessages[$user->id]))
                                            @if($lastMessages[$user->id]->sender_type == 'counselor')
                                                <span class="text-secondary">我: </span>
                                            @endif
                                            {{ $lastMessages[$user->id]->content }}
                                        @else
                                            <span class="text-secondary">暂无消息记录</span>
                                        @endif
                                    </div>
                                    <div class="message-meta">
                                        <div class="message-appointment">
                                            @if(isset($appointments[$user->id]))
                                                预约: {{ \Carbon\Carbon::parse($appointments[$user->id]->appointment_time)->format('Y-m-d H:i') }}
                                            @endif
                                        </div>
                                        @if($unreadCounts[$user->id] > 0)
                                            <div class="message-unread">{{ $unreadCounts[$user->id] }}</div>
                                        @endif
                                    </div>
                                </div>
                            </a>
                        @endforeach
                    @else
                        <div class="no-messages">
                            <i class="bi bi-chat-square"></i>
                            <p>暂无消息记录</p>
                        </div>
                    @endif
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
