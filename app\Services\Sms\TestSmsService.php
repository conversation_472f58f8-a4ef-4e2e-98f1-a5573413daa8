<?php

namespace App\Services\Sms;

use Illuminate\Support\Facades\Log;

/**
 * 测试环境短信服务实现
 * 不实际发送短信，仅记录日志
 */
class TestSmsService implements SmsServiceInterface
{
    /**
     * 发送验证码短信（测试环境，仅记录日志）
     *
     * @param string $mobile 手机号
     * @param string $code 验证码
     * @param int $type 验证码类型 (1:注册, 2:登录, 3:修改密码, 4:绑定手机)
     * @return array 返回发送结果
     */
    public function sendVerificationCode(string $mobile, string $code, int $type = 1): array
    {
        $typeText = $this->getVerificationTypeText($type);
        
        // 记录日志而不是真实发送
        Log::channel('sms')->info("【测试环境】向 {$mobile} 发送{$typeText}验证码: {$code}");
        
        // 返回成功响应
        return [
            'success' => true,
            'message' => "测试环境: 验证码 {$code} 已发送到手机 {$mobile}",
            'provider' => $this->getProviderName(),
            'code' => $code, // 测试环境直接返回验证码，方便测试
        ];
    }
    
    /**
     * 发送通知短信（测试环境，仅记录日志）
     *
     * @param string $mobile 手机号
     * @param array $params 短信参数
     * @param string $templateCode 短信模板代码
     * @return array 返回发送结果
     */
    public function sendNotification(string $mobile, array $params, string $templateCode): array
    {
        // 记录日志而不是真实发送
        Log::channel('sms')->info("【测试环境】向 {$mobile} 发送模板消息: {$templateCode}", [
            'params' => $params
        ]);
        
        // 返回成功响应
        return [
            'success' => true,
            'message' => "测试环境: 通知短信已发送到手机 {$mobile}",
            'provider' => $this->getProviderName(),
            'template' => $templateCode,
            'params' => $params,
        ];
    }
    
    /**
     * 获取验证码类型文本描述
     * 
     * @param int $type
     * @return string
     */
    private function getVerificationTypeText(int $type): string
    {
        switch ($type) {
            case 1:
                return '注册';
            case 2:
                return '登录';
            case 3:
                return '修改密码';
            case 4:
                return '绑定手机';
            default:
                return '验证';
        }
    }
    
    /**
     * 获取服务商名称
     * 
     * @return string
     */
    public function getProviderName(): string
    {
        return 'test';
    }
}
