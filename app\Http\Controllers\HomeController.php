<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Article;
use App\Models\Video;
use App\Models\News;
use App\Models\Counselor;
use App\Models\CounselorSchedule;
use Carbon\Carbon;
use App\Http\Controllers\LiveShowController;
use App\Models\SystemSetting;

class HomeController extends Controller
{
    /**
     * 显示首页
     */
    public function index()
    {
        $knowledgeData = $this->getKnowledgeData();
        $counselors = $this->getCounselors();
        $liveShows = LiveShowController::getHomeLiveShows(4);
        $scrollText = SystemSetting::get('scroll_text', '其他功能正在加急开发中，敬请期待！');
        $scrollLink = SystemSetting::get('scroll_link', '');

        return view('pages.home', [
            'articles' => $knowledgeData['articles'],
            'videos' => $knowledgeData['videos'],
            'courses' => $knowledgeData['courses'],
            'coursesForKnowledge' => $knowledgeData['coursesForKnowledge'],
            'counselors' => $counselors,
            'news' => $knowledgeData['news'],
            'liveShows' => $liveShows,
            'scrollText' => $scrollText,
            'scrollLink' => $scrollLink,
        ]);
    }

    /**
     * 学习页面 - 显示用户的课程学习进度
     */
    public function study()
    {
        // 需要登录才能访问
        if (!\Auth::check()) {
            return redirect()->route('login');
        }

        $userId = \Auth::id();

        // 获取用户正在学习的课程
        $inProgressCourses = \App\Models\CourseLesson::with(['category', 'progress' => function($query) use ($userId) {
            $query->where('user_id', $userId);
        }])
        ->whereHas('progress', function($query) use ($userId) {
            $query->where('user_id', $userId)
                  ->where('is_completed', false);
        })
        ->where('status', \App\Models\CourseLesson::STATUS_PUBLISHED)
        ->orderBy('updated_at', 'desc')
        ->get();

        // 获取用户已完成的课程
        $completedCourses = \App\Models\CourseLesson::with(['category', 'progress' => function($query) use ($userId) {
            $query->where('user_id', $userId);
        }])
        ->whereHas('progress', function($query) use ($userId) {
            $query->where('user_id', $userId)
                  ->where('is_completed', true);
        })
        ->where('status', \App\Models\CourseLesson::STATUS_PUBLISHED)
        ->orderBy('updated_at', 'desc')
        ->get();

        // 获取用户收藏的课程
        // 注意：这里假设没有收藏功能，先用空集合替代
        $favoriteCourses = collect();

        return view('pages.study', [
            'inProgressCourses' => $inProgressCourses,
            'completedCourses' => $completedCourses,
            'favoriteCourses' => $favoriteCourses
        ]);
    }

    /**
     * 获取咨询师数据
     */
    public function getCounselors()
    {
        // 当前时间
        $now = Carbon::now();
        $currentDate = $now->format('Y-m-d');
        $currentTime = $now->format('H:i:s');

        // 首先尝试获取状态为开启，在当前日期时间段有排班且为可咨询的咨询师
        $availableCounselorsWithSchedule = Counselor::query()
            ->where('is_active', 1)
            ->whereHas('schedules', function ($query) use ($currentDate, $currentTime) {
                $query->where('date', $currentDate)
                    ->where('start_time', '<=', $currentTime)
                    ->where('end_time', '>', $currentTime)
                    ->where('is_available', true);
            })
            ->take(4) // 首页只显示4个咨询师
            ->get();

        // 如果没有找到符合条件的咨询师，则获取所有状态为开启的咨询师
        if ($availableCounselorsWithSchedule->isEmpty()) {
            $availableCounselors = Counselor::where('is_active', 1)
                ->orderBy('sort_order', 'desc')
                ->take(4)
                ->get();

            return $availableCounselors;
        }

        return $availableCounselorsWithSchedule;
    }
    /**
     * 获取首页心理科普数据
     */
    public function getKnowledgeData()
    {
        
         // 获取最新的两篇已发布推荐新闻
        $news = News::with(['category'])
            ->where('is_published',true)
            ->where('is_featured',true)
            ->orderBy('published_at', 'desc')
            ->take(5)
            ->get()
            ->map(function($news) {
                return [
                    'id' => $news->id,
                    'title' => $news->title,
                    'slug' => $news->slug,
                    'image' => $this->getFormattedImageUrl($news->cover_image),
                    'date' => $this->formatArticleDate($news),
                    'views' => $news->view_count,
                    'author' => $news->author,
                    'description' => $news->summary,
                ];
            })->toArray();
        // 获取最新的两篇已发布文章
        $articles = Article::with(['category', 'author'])
            ->where('status', Article::STATUS_PUBLISHED)
            ->orderBy('publish_time', 'desc')
            ->take(5)
            ->get()
            ->map(function($article) {
                return [
                    'id' => $article->id,
                    'title' => $article->title,
                    'image' => $this->getFormattedImageUrl($article->image),
                    'date' => $this->formatArticleDate($article),
                    'views' => $article->views,
                ];
            })->toArray();

        // 心理视频
        $videos =   Video::with(['category', 'author'])
        ->where('status', Video::STATUS_PUBLISHED)
        ->where('is_recommended',1)
        ->orderBy('publish_time', 'desc')
        ->take(5)
        ->get()
        ->map(function($video) {
            return [
                'id' => $video->id,
                'title' => $video->title,
                'image' => $this->getFormattedImageUrl($video->image),
                'date' => $this->formatArticleDate($video),
                'views' => $video->views,
                'duration' => $video->video_duration
            ];
        })->toArray();
        if(!$videos){
            $videos = [
                [
                    'id' => 1,
                    'title' => '如何缓解焦虑情绪',
                    'image' => asset('images/video-thumbnail-1.jpg'),
                    'date' => '2025-05-01',
                    'views' => 328,
                    'duration' => '18:24'
                ],
                [
                    'id' => 2,
                    'title' => '睡眠质量提升指南',
                    'image' => 'https://img.freepik.com/free-photo/young-woman-sleeping-bed_23-2148441316.jpg',
                    'date' => '2025-04-28',
                    'views' => 256,
                    'duration' => '12:36'
                ]
            ];
        }


        // 获取心理课堂分类（只取前三个）
        $categories = \App\Models\CourseLessonCategory::orderBy('sort', 'asc')->take(3)->get();

        // 准备课程数据
        $coursesByCategory = [];
        $defaultCourses = [];

        // 预先准备一些默认数据，以防数据库没有足够数据
        $defaultCategories = [
            ['id' => 1, 'name' => '个人成长'],
            ['id' => 2, 'name' => '心理健康'],
            ['id' => 3, 'name' => '亲子教育']
        ];

        $defaultCoursesByCategory = [
            1 => [
                [
                    'id' => 1,
                    'title' => '认知行为疗法入门',
                    'description' => '认知行为疗法是一种帮助人们识别并改变引起痛苦情绪和行为问题的思维方式的疗法',
                    'image' => 'https://img.freepik.com/free-photo/psychologist-consulting-patient-during-therapy-session_23-**********.jpg',
                    'date' => '2025-05-05',
                    'views' => 412,
                    'type' => 1 // 图文
                ],
                [
                    'id' => 2,
                    'title' => '深度放松练习',
                    'description' => '学习如何通过深度呼吸和意识练习达到身心放松状态',
                    'image' => 'https://img.freepik.com/free-photo/woman-meditating-yoga-pose-beach_23-**********.jpg',
                    'date' => '2025-04-28',
                    'views' => 321,
                    'type' => 1 // 图文
                ]
            ],
            2 => [
                [
                    'id' => 3,
                    'title' => '情绪管理与自我调节',
                    'description' => '本课程教授各种情绪管理技巧，帮助你在日常生活和工作中更好地处理压力和负面情绪',
                    'image' => 'https://img.freepik.com/free-photo/woman-meditating-home_23-**********.jpg',
                    'date' => '2025-04-30',
                    'views' => 385,
                    'type' => 2 // 视频
                ],
                [
                    'id' => 4,
                    'title' => '抗焦虑训练营',
                    'description' => '通过一系列的实践性练习，帮助你减轻焦虑与焊煊的情绪',
                    'image' => 'https://img.freepik.com/free-photo/young-asian-woman-practicing-yoga-home_1098-3118.jpg',
                    'date' => '2025-05-02',
                    'views' => 289,
                    'type' => 1 // 图文
                ]
            ],
            3 => [
                [
                    'id' => 5,
                    'title' => '青少年心理成长指南',
                    'description' => '帮助家长了解青少年心理特点与成长规律，提供科学的教育方法',
                    'image' => 'https://img.freepik.com/free-photo/group-teenagers-having-therapy-session_23-2149190578.jpg',
                    'date' => '2025-05-10',
                    'views' => 410,
                    'type' => 2 // 视频
                ],
                [
                    'id' => 6,
                    'title' => '积极家庭沟通技巧',
                    'description' => '提供父母与孩子沟通的实用方法，建立健康的家庭关系',
                    'image' => 'https://img.freepik.com/free-photo/family-spending-time-together_23-2149070713.jpg',
                    'date' => '2025-05-08',
                    'views' => 356,
                    'type' => 1 // 图文
                ]
            ]
        ];

        // 如果分类数据少于3个，添加默认分类
        if ($categories->count() < 3) {
            foreach ($defaultCategories as $index => $defaultCategory) {
                if ($index >= $categories->count()) {
                    // 添加默认分类
                    $categories->push((object)$defaultCategory);
                }
            }
        }

        // 遍历分类，获取每个分类的推荐课程
        foreach ($categories as $category) {
            // 获取当前分类下推荐的课程
            $categoryCourses = \App\Models\CourseLesson::where('status', \App\Models\CourseLesson::STATUS_PUBLISHED)
                ->where('is_recommended', true)
                ->where('category_id', $category->id)
                ->orderBy('publish_time', 'desc')
                ->take(5)
                ->get();

            $formattedCourses = [];

            // 格式化课程数据
            foreach ($categoryCourses as $course) {
                $formattedCourses[] = [
                    'id' => $course->id,
                    'title' => $course->title,
                    'description' => $course->description,
                    'image' => $this->getFormattedImageUrl($course->image),
                    'date' => $this->formatArticleDate($course),
                    'views' => $course->views ?? 0,
                    'type' => $course->type // 1=图文, 2=视频
                ];
            }

            // 如果当前分类没有足够的推荐课程，使用默认数据
            if (count($formattedCourses) < 2 && isset($defaultCoursesByCategory[$category->id])) {
                // 添加缺少的课程
                for ($i = count($formattedCourses); $i < 2; $i++) {
                    if (isset($defaultCoursesByCategory[$category->id][$i])) {
                        $formattedCourses[] = $defaultCoursesByCategory[$category->id][$i];
                    }
                }
            }

            // 如果还是没有数据，使用第一个分类的默认数据
            if (empty($formattedCourses) && !empty($defaultCoursesByCategory[1])) {
                $formattedCourses = $defaultCoursesByCategory[1];
            }

            $coursesByCategory[] = [
                'category' => $category,
                'courses' => $formattedCourses
            ];
        }

        // 对于心理科普部分，仍然需要原来结构的课程数据
        $coursesForKnowledge = [];
        // 取出第一个分类下的课程作为心理科普的课程
        if (!empty($coursesByCategory) && !empty($coursesByCategory[0]['courses'])) {
            foreach ($coursesByCategory[0]['courses'] as $course) {
                // 添加lessons字段，保持兼容性
                $course['lessons'] = $course['lessons'] ?? rand(3, 8);
                $coursesForKnowledge[] = $course;
            }
        }

        // 如果还是没有数据，使用默认课程
        if (count($coursesForKnowledge) < 2) {
            $defaultKnowledgeCourses = [
                [
                    'id' => 1,
                    'title' => '认知行为疗法入门',
                    'image' => 'https://img.freepik.com/free-photo/psychologist-consulting-patient-during-therapy-session_23-**********.jpg',
                    'date' => '2025-05-05',
                    'views' => 412,
                    'lessons' => 8,
                    'type' => 1 // 图文
                ],
                [
                    'id' => 2,
                    'title' => '情绪管理与自我调节',
                    'image' => 'https://img.freepik.com/free-photo/woman-meditating-home_23-**********.jpg',
                    'date' => '2025-04-30',
                    'views' => 385,
                    'lessons' => 6,
                    'type' => 2 // 视频
                ]
            ];

            // 添加缺少的课程
            for ($i = count($coursesForKnowledge); $i < 2; $i++) {
                $coursesForKnowledge[] = $defaultKnowledgeCourses[$i];
            }
        }

        // 将分类和课程数据存入courses字段
        $courses = $coursesByCategory;

        return [
            'articles' => $articles,
            'videos' => $videos,
            'courses' => $courses,
            'coursesForKnowledge' => $coursesForKnowledge,
            'news' => $news,
        ];
    }

    /**
     * 格式化文章日期
     */
    private function formatArticleDate($article)
    {
        if ($article->publish_time && is_object($article->publish_time)) {
            return $article->publish_time->format('Y-m-d');
        } else if ($article->created_at && is_object($article->created_at)) {
            return $article->created_at->format('Y-m-d');
        } else if (is_string($article->publish_time) && !empty($article->publish_time)) {
            return date('Y-m-d', strtotime($article->publish_time));
        } else if (is_string($article->created_at) && !empty($article->created_at)) {
            return date('Y-m-d', strtotime($article->created_at));
        } else {
            return date('Y-m-d');
        }
    }

    /**
     * 格式化图片URL
     */
    private function getFormattedImageUrl($imagePath)
    {
        if (empty($imagePath)) {
            return asset('images/default-article.jpg');
        }

        // 如果是完整URL，直接返回
        if (filter_var($imagePath, FILTER_VALIDATE_URL)) {
            return $imagePath;
        }

        // 如果是相对路径，转换为完整URL
        if (strpos($imagePath, 'http') !== 0) {
            // 使用Storage来获取图片URL
            return \Illuminate\Support\Facades\Storage::disk(config('admin.upload.disk'))->url($imagePath);
        }

        return $imagePath;
    }

    /**
     * 显示个人资料页面
     */
    public function profile()
    {
        return view('pages.user.profile', [
            'user' => \Auth::user()
        ]);
    }

    /**
     * 更新个人资料
     */
    public function updateProfile(Request $request)
    {
        $user = \Auth::user();

        // 添加中文错误提示
        $messages = [
            'name.required' => '请输入用户名',
            'name.max' => '用户名不能超过255个字符',
            'avatar.image' => '头像文件必须是图片格式',
            'avatar.max' => '头像大小不能超过1MB',
            'phone.max' => '手机号码不能超过20个字符',
            'email.email' => '请输入有效的电子邮箱地址',
            'email.max' => '邮箱地址不能超过255个字符',
            'email.unique' => '该邮箱已被使用',
        ];

        // 如果请求中只有头像文件且没有其他字段，处理头像上传
        if ($request->hasFile('avatar') && !$request->has('name')) {
            try {
                $request->validate([
                    'avatar' => 'required|image|max:1024',
                ], [
                    'avatar.required' => '请选择头像文件',
                    'avatar.image' => '头像文件必须是图片格式',
                    'avatar.max' => '头像大小不能超过1MB',
                ]);

                $avatarPath = $request->file('avatar')->store('avatars', 'public');
                $user->avatar = $avatarPath;
                $user->save();

                return redirect()->route('user.profile')->with('success', '头像上传成功');
            } catch (\Exception $e) {
                return redirect()->route('user.profile')->withErrors([
                    'avatar' => '头像上传失败，请重试'
                ]);
            }
        }

        // 处理常规表单提交
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'avatar' => 'nullable|image|max:1024',
            'phone' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255|unique:users,email,'.$user->id,
        ], $messages);

        $user->name = $validated['name'];

        if ($request->hasFile('avatar')) {
            $avatarPath = $request->file('avatar')->store('avatars', 'public');
            $user->avatar = $avatarPath;
        }

        if (isset($validated['phone'])) {
            $user->phone = $validated['phone'];
        }

        if (isset($validated['email'])) {
            $user->email = $validated['email'];
        }

        $user->save();

        return redirect()->route('user.profile')->with('success', '个人资料更新成功');
    }

    /**
     * 显示账号管理页面
     */
    public function account()
    {
        return view('pages.user.account', [
            'user' => \Auth::user()
        ]);
    }

    /**
     * 修改密码
     */
    public function changePassword(Request $request)
    {
        $messages = [
            'current_password.required' => '请输入当前密码',
            'current_password.current_password' => '当前密码不正确',
            'password.required' => '请输入新密码',
            'password.min' => '密码长度至少为6位',
            'password.confirmed' => '两次输入的密码不一致'
        ];

        $validated = $request->validate([
            'current_password' => 'required|string|current_password',
            'password' => 'required|string|min:6|confirmed',
        ], $messages);

        $user = \Auth::user();
        $user->password = \Hash::make($validated['password']);
        $user->save();

        return redirect()->route('user.account')->with('success', '密码修改成功');
    }

    /**
     * 显示设置页面
     */
    public function settings()
    {
        return view('pages.user.settings', [
            'user' => \Auth::user()
        ]);
    }

    /**
     * 更新设置
     */
    public function updateSettings(Request $request)
    {
        $user = \Auth::user();

        $validated = $request->validate([
            'privacy_mode' => 'boolean',          // 隐私模式
            'notification_sms' => 'boolean',      // 短信通知
            'notification_email' => 'boolean',    // 邮件通知
            'auto_play_video' => 'boolean',      // 自动播放视频
        ]);

        // 这里需要创建一个新的user_settings表来保存这些设置
        // 或者可以使用Laravel的缓存系统来存储用户设置

        // 示例值储存设置
        if (isset($validated['privacy_mode'])) {
            // \Cache::put('user_'.$user->id.'_privacy_mode', $validated['privacy_mode'], now()->addYear());
        }

        if (isset($validated['notification_sms'])) {
            // \Cache::put('user_'.$user->id.'_notification_sms', $validated['notification_sms'], now()->addYear());
        }

        if (isset($validated['notification_email'])) {
            // \Cache::put('user_'.$user->id.'_notification_email', $validated['notification_email'], now()->addYear());
        }

        if (isset($validated['auto_play_video'])) {
            // \Cache::put('user_'.$user->id.'_auto_play_video', $validated['auto_play_video'], now()->addYear());
        }

        return redirect()->route('user.settings')->with('success', '设置更新成功');
    }

    /**
     * 显示帮助中心页面
     */
    public function help()
    {
        return view('pages.help');
    }

    /**
     * 显示关于我们页面
     */
    public function about()
    {
        return view('pages.about');
    }

    /**
     * 显示联系我们页面
     */
    public function contact()
    {
        return view('pages.contact');
    }

    /**
     * 处理联系表单提交
     */
    public function contactSubmit(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email',
            'phone' => 'nullable|string|max:20',
            'subject' => 'required|string',
            'message' => 'required|string',
        ], [
            'name.required' => '请输入您的姓名',
            'email.required' => '请输入您的电子邮箱',
            'email.email' => '请输入有效的电子邮箱地址',
            'subject.required' => '请选择留言主题',
            'message.required' => '请输入留言内容',
        ]);

        // 这里可以添加实际的表单处理逻辑，如存入数据库或发送电子邮件
        // 目前使用简单的成功提示

        return redirect()->route('contact')->with('success', '感谢您的留言，我们会尽快与您联系！');
    }
}
