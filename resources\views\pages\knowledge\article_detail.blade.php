@extends('layouts.app')

@section('title', $article['title'] . ' - 心理科普')

@section('custom-styles')
<style>
    .container {
        width: 100%;
        max-width: 100%;
        padding: 0;
        margin: 0;
        background-color: #f5f5f5;
    }
    
    .page-header {
        padding: 15px;
        background-color: #fff;
        text-align: center;
        position: relative;
        box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    }
    
    .back-button {
        position: absolute;
        left: 15px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 18px;
        color: #333;
        text-decoration: none;
        display: flex;
        align-items: center;
    }
    
    .back-icon {
        width: 20px;
        height: 20px;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23333'%3E%3Cpath d='M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z'/%3E%3C/svg%3E");
        background-size: contain;
        background-repeat: no-repeat;
    }
    
    .article-container {
        padding: 15px;
        background-color: #fff;
        margin: 10px;
        border-radius: 8px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    }
    
    /* 最后一个容器添加底部边距 */
    .article-container:last-child {
        margin-bottom: 80px; /* 确保底部有足够空间不被导航栏遮挡 */
    }
    
    .article-header {
        margin-bottom: 20px;
    }
    
    .article-title {
        font-size: 20px;
        font-weight: 600;
        color: #333;
        margin-bottom: 15px;
        line-height: 1.4;
    }
    
    .article-meta {
        display: flex;
        justify-content: space-between;
        color: #999;
        font-size: 14px;
        margin-bottom: 15px;
    }
    
    .article-info {
        display: flex;
        align-items: center;
        gap: 15px;
    }
    
    .article-author {
        color: #666;
    }
    
    .article-date {
        color: #999;
    }
    
    .article-views {
        display: flex;
        align-items: center;
        color: #999;
    }
    
    .views-icon {
        width: 14px;
        height: 14px;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999'%3E%3Cpath d='M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z'/%3E%3C/svg%3E");
        background-size: contain;
        background-repeat: no-repeat;
        margin-right: 3px;
    }
    
    .article-category {
        background-color: #4e9cff;
        color: #ffffff;
        padding: 2px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;
        display: inline-flex;
        align-items: center;
    }
    
    .article-category:before {
        content: '';
        display: inline-block;
        width: 10px;
        height: 10px;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23ffffff'%3E%3Cpath d='M10 3H4a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4a1 1 0 0 0-1-1zm10 0h-6a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4a1 1 0 0 0-1-1zM10 13H4a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1v-6a1 1 0 0 0-1-1zm10 0h-6a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1v-6a1 1 0 0 0-1-1z'/%3E%3C/svg%3E");
        background-size: contain;
        background-repeat: no-repeat;
        margin-right: 4px;
    }
    
    .article-image {
        width: 100%;
        border-radius: 8px;
        margin-bottom: 20px;
        max-height: 200px;
        object-fit: cover;
    }
    
    .article-content {
        font-size: 16px;
        line-height: 1.7;
        color: #333;
    }
    
    .article-content p {
        margin-bottom: 15px;
    }
    
    .article-content h2 {
        font-size: 18px;
        font-weight: 600;
        margin: 25px 0 15px;
        color: #333;
    }
    
    .article-footer {
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid #eee;
    }
    
    .article-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        margin-top: 20px;
    }
    
    .article-tag {
        background-color: #f0f7ff;
        color: #6a8caf;
        font-size: 12px;
        padding: 3px 10px;
        border-radius: 8px;
        border: 1px solid #e0eeff;
        display: inline-flex;
        align-items: center;
    }
    
    .article-tag:before {
        content: '';
        display: inline-block;
        width: 10px;
        height: 10px;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%236a8caf'%3E%3Cpath d='M21.41 11.58l-9-9C12.05 2.22 11.55 2 11 2H4c-1.1 0-2 .9-2 2v7c0 .55.22 1.05.59 1.42l9 9c.36.36.86.58 1.41.58.55 0 1.05-.22 1.41-.59l7-7c.37-.36.59-.86.59-1.41 0-.55-.23-1.06-.59-1.42zM5.5 7C4.67 7 4 6.33 4 5.5S4.67 4 5.5 4 7 4.67 7 5.5 6.33 7 5.5 7z'/%3E%3C/svg%3E");
        background-size: contain;
        background-repeat: no-repeat;
        margin-right: 5px;
    }
    
    .related-articles {
        margin-top: 20px;
    }
    
    .related-title {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 15px;
        color: #333;
    }
    
    .related-item {
        display: flex;
        margin-bottom: 15px;
        padding-bottom: 15px;
        border-bottom: 1px solid #f0f0f0;
    }
    
    .related-item:last-child {
        border-bottom: none;
        margin-bottom: 0;
        padding-bottom: 0;
    }
    
    .related-image {
        width: 80px;
        height: 60px;
        background-size: cover;
        background-position: center;
        border-radius: 4px;
        margin-right: 10px;
    }
    
    .related-content {
        flex: 1;
    }
    
    .related-article-title {
        font-size: 14px;
        font-weight: 500;
        color: #333;
        margin-bottom: 5px;
        line-height: 1.4;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
    
    .related-article-meta {
        font-size: 12px;
        color: #999;
    }
    
    @media (min-width: 768px) {
        .container {
            max-width: 750px;
            margin: 0 auto;
        }
        
        .article-container {
            padding: 25px;
            margin: 15px auto;
            max-width: 720px;
        }
    }
</style>
@endsection

@section('content')
<div class="page-header">
    <a href="{{ route('knowledge.articles') }}" class="back-button">
        <div class="back-icon"></div>
    </a>
    <h1 style="font-size: 18px; margin: 0;">文章详情</h1>
</div>

<div class="article-container">
    <div class="article-header">
        <h1 class="article-title">{{ $article['title'] }}</h1>
        <div class="article-meta">
            <div class="article-info">
                <span class="article-author">{{ $article['author'] }}</span>
                <span class="article-date">{{ $article['date'] }}</span>
                <span class="article-views">
                    <div class="views-icon"></div>
                    {{ $article['views'] }}
                </span>
            </div>
            <span class="article-category">{{ $article['category_name'] }}</span>
        </div>
    </div>
    
    <img src="{{ $article['image'] }}" alt="{{ $article['title'] }}" class="article-image">
    
    <div class="article-content">
        {!! $article['content'] !!}
    </div>
    
    <div class="article-footer">
        <div class="article-tags">
            @foreach($article['tags'] as $tag)
                <span class="article-tag">{{ $tag }}</span>
            @endforeach
        </div>
    </div>
</div>

<div class="article-container">
    <div class="related-articles">
        <h2 class="related-title">相关文章</h2>
        @foreach($relatedArticles as $relatedArticle)
        <div class="related-item" onclick="window.location.href='{{ route('knowledge.article_detail', ['id' => $relatedArticle['id']]) }}'">
            <div class="related-image" style="background-image: url('{{ $relatedArticle['image'] }}')"></div>
            <div class="related-content">
                <div class="related-article-title">{{ $relatedArticle['title'] }}</div>
                <div class="related-article-meta">{{ $relatedArticle['date'] }}</div>
            </div>
        </div>
        @endforeach
        
        @if(count($relatedArticles) == 0)
        <div class="text-center text-muted py-3">
            暂无相关文章
        </div>
        @endif
    </div>
</div>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 相关文章点击事件
        const relatedItems = document.querySelectorAll('.related-item');
        relatedItems.forEach((item, index) => {
            item.addEventListener('click', function() {
                // 模拟文章ID
                const articleId = index + 2; // 避免与当前文章ID冲突
                // 跳转到文章详情页
                window.location.href = `{{ route('knowledge.articles') }}/${articleId}`;
            });
        });
    });
</script>
@endsection
