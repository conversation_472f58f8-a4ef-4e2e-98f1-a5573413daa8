<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use App\Models\AssessmentQuestionnaire;
use App\Models\AssessmentQuestion;
use App\Models\AssessmentOption;

class ImportQuestionnaire extends Command
{
    /**
     * The name and signature of the console command.
     *
     * usage: php artisan questionnaire:import path/to/file.json
     *
     * @var string
     */
    protected $signature = 'questionnaire:import {file : JSON 文件路径}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '从 JSON 文件中批量导入问卷（题目＋选项）';

    public function handle()
    {
        $path = $this->argument('file');

        if (!file_exists($path) || !is_readable($path)) {
            return $this->error("文件不存在或不可读：{$path}");
        }

        $json = file_get_contents($path);
        $data = json_decode($json, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            return $this->error('JSON 解析失败：' . json_last_error_msg());
        }

        // 验证数据
        $validator = Validator::make($data, [
            'title'       => 'required|string|max:255',
            'description' => 'nullable|string',
            'domain'      => 'required|string|max:100',
            'questions'   => 'required|array|min:1',
            'questions.*.type'        => 'required|in:single,multiple,scale',
            'questions.*.content'     => 'required|string',
            'questions.*.sort_order'  => 'nullable|integer|min:0',
            'questions.*.options'     => 'required|array|min:1',
            'questions.*.options.*.content'     => 'required|string',
            'questions.*.options.*.score_value' => 'required|numeric|min:0',
        ]);

        if ($validator->fails()) {
            return $this->error('数据验证失败：' . $validator->errors()->first());
        }

        DB::beginTransaction();
        try {
            // 创建问卷
            $questionnaire = AssessmentQuestionnaire::create([
                'title'          => $data['title'],
                'description'    => $data['description'] ?? '',
                'domain'         => $data['domain'],
                'question_count' => count($data['questions']),
                'est_duration'   => max(count($data['questions']), 5),
                'is_active'      => false,
            ]);

            // 按顺序插入题目和选项
            foreach ($data['questions'] as $idx => $q) {
                $question = AssessmentQuestion::create([
                    'questionnaire_id' => $questionnaire->id,
                    'type'             => $q['type'],
                    'content'          => $q['content'],
                    'sort_order'       => $q['sort_order'] ?? ($idx + 1),
                ]);

                foreach ($q['options'] as $opt) {
                    AssessmentOption::create([
                        'question_id' => $question->id,
                        'content'     => $opt['content'],
                        'score_value' => $opt['score_value'],
                    ]);
                }
            }

            DB::commit();
            $this->info("导入成功！问卷《{$questionnaire->title}》（ID={$questionnaire->id}），共 " 
                        . count($data['questions']) . " 个题目。");
        } catch (\Exception $e) {
            DB::rollBack();
            $this->error('导入失败：' . $e->getMessage());
        }
    }
}
