<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\URL;

class ForceUrlWithPort
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next, $port = null)
    {
        // 强制使用APP_URL中的完整URL（包括端口）
        $appUrl = config('app.url');
        if ($appUrl) {
            URL::forceRootUrl($appUrl);
            URL::forceScheme(parse_url($appUrl, PHP_URL_SCHEME));
        }

        $response = $next($request);

        // 如果是重定向响应，确保URL包含端口
        if ($response instanceof \Illuminate\Http\RedirectResponse) {
            $location = $response->getTargetUrl();
            
            // 检查是否是相对URL或者缺少端口的URL
            if (strpos($location, 'http://111.34.80.160') === 0 && strpos($location, ':8004') === false) {
                $location = str_replace('http://111.34.80.160', 'http://111.34.80.160:8004', $location);
                $response->setTargetUrl($location);
            }
        }

        return $response;
    }
} 