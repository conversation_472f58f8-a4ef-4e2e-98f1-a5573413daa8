<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\AssessmentQuestionnaire;
use App\Models\AssessmentQuestion;
use App\Models\AssessmentOption;

class DiagnoseQuestionnaire extends Command
{
    protected $signature = 'questionnaire:diagnose {id? : 问卷ID，不提供则检查所有问卷}';
    protected $description = '诊断问卷数据完整性';

    public function handle()
    {
        $questionnaireId = $this->argument('id');
        
        if ($questionnaireId) {
            $this->diagnoseQuestionnaire($questionnaireId);
        } else {
            $this->diagnoseAllQuestionnaires();
        }
    }
    
    private function diagnoseQuestionnaire($id)
    {
        $questionnaire = AssessmentQuestionnaire::with(['questions.options'])->find($id);
        
        if (!$questionnaire) {
            $this->error("问卷 ID {$id} 不存在");
            return;
        }
        
        $this->info("=== 问卷诊断报告 ===");
        $this->info("问卷ID: {$questionnaire->id}");
        $this->info("问卷标题: {$questionnaire->title}");
        $this->info("记录的题目数量: {$questionnaire->question_count}");
        
        $actualQuestionCount = $questionnaire->questions->count();
        $this->info("实际题目数量: {$actualQuestionCount}");
        
        if ($questionnaire->question_count != $actualQuestionCount) {
            $this->warn("⚠️  题目数量不匹配！");
        } else {
            $this->info("✅ 题目数量正确");
        }
        
        $this->info("\n=== 题目详情 ===");
        
        foreach ($questionnaire->questions->sortBy('sort_order') as $index => $question) {
            $optionCount = $question->options->count();
            $status = $optionCount > 0 ? "✅" : "❌";
            
            $this->info("题目 {$question->sort_order}: {$status} {$question->content}");
            $this->info("  - 选项数量: {$optionCount}");
            
            if ($optionCount == 0) {
                $this->warn("  ⚠️  该题目没有选项！");
            } else {
                foreach ($question->options as $option) {
                    $this->info("    • {$option->content} (分值: {$option->score_value})");
                }
            }
            $this->info("");
        }
        
        // 检查是否有孤立的选项
        $orphanOptions = AssessmentOption::whereNotIn('question_id', 
            $questionnaire->questions->pluck('id')
        )->get();
        
        if ($orphanOptions->count() > 0) {
            $this->warn("发现 {$orphanOptions->count()} 个孤立选项（没有对应题目）");
        }
    }
    
    private function diagnoseAllQuestionnaires()
    {
        $questionnaires = AssessmentQuestionnaire::with(['questions.options'])->get();
        
        $this->info("=== 所有问卷诊断 ===");
        
        foreach ($questionnaires as $questionnaire) {
            $actualQuestionCount = $questionnaire->questions->count();
            $questionsWithoutOptions = $questionnaire->questions->filter(function($q) {
                return $q->options->count() == 0;
            })->count();
            
            $status = "✅";
            $issues = [];
            
            if ($questionnaire->question_count != $actualQuestionCount) {
                $status = "⚠️";
                $issues[] = "题目数量不匹配";
            }
            
            if ($questionsWithoutOptions > 0) {
                $status = "❌";
                $issues[] = "{$questionsWithoutOptions}个题目没有选项";
            }
            
            $issueText = empty($issues) ? "正常" : implode(", ", $issues);
            
            $this->info("{$status} [{$questionnaire->id}] {$questionnaire->title} - {$issueText}");
        }
    }
} 