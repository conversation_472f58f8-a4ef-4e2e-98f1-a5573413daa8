<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\User;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class UserController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new User(), function (Grid $grid) {
            $grid->column('id')->sortable();
            $grid->column('name');
            // $grid->column('email');
            $grid->column('phone');
            // $grid->column('phone_verified');
            // $grid->column('avatar');
            // $grid->column('email_verified_at');
            // $grid->column('password');
            // $grid->column('remember_token');
            // $grid->column('is_contact','是否为单位联络人');
            $grid->column('is_contact','是否为单位联络人')->switch('', true);
            $grid->column('enterprise','单位名称')->editable();
            $grid->column('last_login_at');
            // $grid->column('last_login_ip');
            // $grid->column('created_at');
            $grid->column('updated_at')->sortable();
            $grid->disableEditButton();
            $grid->disableBatchActions();
            $grid->disableBatchDelete();
            $grid->disableBatchDelete();
        
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');
                $filter->equal('phone');
                $filter->equal('is_contact','是否为单位联络人')->select([0 => '否',1 => '是']);
                $filter->equal('enterprise','单位名称');
                
        
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new User(), function (Show $show) {
            $show->field('id');
            $show->field('name');
            // $show->field('email');
            $show->field('phone');
            // $show->field('phone_verified');
            // $show->field('avatar');
            // $show->field('email_verified_at');
            // $show->field('password');
            // $show->field('remember_token');
            $show->field('last_login_at');
            $show->field('last_login_ip');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new User(), function (Form $form) {
            $form->display('id');
            $form->text('name');
            $form->text('email');
            $form->text('phone');
            $form->text('phone_verified');
            $form->switch('is_contact')
            ->customFormat(function ($v) {
                return $v == '是' ? 1 : 0;
            })
            ->saving(function ($v) {
                return $v ? 1 : 0;
            });
            $form->text('avatar');
            $form->text('email_verified_at');
            $form->text('password');
            $form->text('remember_token');
            $form->text('last_login_at');
            $form->text('last_login_ip');
        
            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
