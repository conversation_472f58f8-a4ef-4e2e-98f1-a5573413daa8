<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Article;
use App\Models\ArticleCategory;
use App\Models\Video;
use App\Models\VideoCategory;
use App\Models\VideoTag;
use App\Models\CourseLesson;
use App\Models\CourseLessonCategory;
use App\Models\CourseLessonTag;
use App\Models\CourseLessonProgress;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Auth;

class KnowledgeController extends Controller
{
    /**
     * 显示心理科普文章列表
     */
    public function articles(Request $request)
    {
        // 从数据库获取分类数据
        $categoriesFromDb = ArticleCategory::where('is_active', 1)->get();
        
        // 转换为前端需要的格式
        $categories = [
            ['id' => 0, 'name' => '全部'], 
        ];
        
        foreach ($categoriesFromDb as $category) {
            $categories[] = [
                'id' => $category->id, 
                'name' => $category->name
            ];
        }

        // 处理Ajax分页请求
        if ($request->ajax()) {
            $page = $request->input('page', 1);
            $perPage = 4; 
            
            $categoryId = $request->input('category_id', 0);
            $query = Article::with(['category', 'author', 'tags'])
                ->where('status', Article::STATUS_PUBLISHED);
            
            // 搜索功能
            if ($request->has('search') && !empty($request->input('search'))) {
                $search = $request->input('search');
                
                // 使用子查询进行相关性排序
                $query->where(function($q) use ($search) {
                    // 标题匹配 - 最高优先级
                    $q->where('title', 'like', "%{$search}%");
                    
                    // 摘要匹配 - 次高优先级
                    $q->orWhere('summary', 'like', "%{$search}%");
                    
                    // 内容匹配 - 中等优先级
                    $q->orWhere('content', 'like', "%{$search}%");
                    
                    // 标签匹配 - 通过关联关系查询
                    $q->orWhereHas('tags', function($tagQuery) use ($search) {
                        $tagQuery->where('name', 'like', "%{$search}%");
                    });
                });
                
                // 根据相关性字段排序（优先标题匹配，其次是发布时间）
                $query->orderByRaw("
                    CASE 
                        WHEN title LIKE ? THEN 1
                        WHEN summary LIKE ? THEN 2
                        WHEN content LIKE ? THEN 3
                        ELSE 4
                    END", 
                    ["%{$search}%", "%{$search}%", "%{$search}%"]
                );
            } else {
                // 默认排序 - 发布时间
                $query->orderBy('publish_time', 'desc');
            }
            
            // 如果选择了特定分类而不是"全部"，过滤文章
            if ($categoryId > 0) { 
                $query->where('category_id', $categoryId);
            }
            
            $total = $query->count();
            $articles = $query->skip(($page - 1) * $perPage)->take($perPage)->get();
            
            // 将文章转换为前端所需的格式
            $formattedArticles = $articles->map(function($article) {
                // 获取文章标签为数组
                $tags = $article->tags->pluck('name')->toArray();
                
                return [
                    'id' => $article->id,
                    'title' => $article->title,
                    'image' => $this->getFormattedImageUrl($article->image), 
                    'category_id' => $article->category_id, 
                    'category_name' => $article->category ? $article->category->name : '未分类',
                    'date' => $this->formatArticleDate($article),
                    'views' => $article->views,
                    'tags' => $tags, // 标签作为数组
                ];
            });
            
            // 检查是否还有更多文章
            $hasMore = $total > ($page * $perPage);
            
            return response()->json([
                'articles' => $formattedArticles,
                'hasMore' => $hasMore
            ]);
        }

        // 初始加载获取已发布文章
        $initialArticles = Article::with(['category', 'author', 'tags'])
            ->where('status', Article::STATUS_PUBLISHED)
            ->orderBy('publish_time', 'desc')
            ->take(4)
            ->get();
            
        // 将文章转换为前端所需的格式
        $articles = $initialArticles->map(function($article) {
            // 获取文章标签为数组
            $tags = $article->tags->pluck('name')->toArray();
            
            return [
                'id' => $article->id,
                'title' => $article->title,
                'image' => $this->getFormattedImageUrl($article->image), 
                'category_id' => $article->category_id, 
                'category_name' => $article->category ? $article->category->name : '未分类',
                'date' => $this->formatArticleDate($article),
                'views' => $article->views,
                'tags' => $tags, // 添加标签数组
            ];
        })->toArray();

        return view('pages.knowledge.articles', compact('categories', 'articles'));
    }

    /**
     * 格式化文章日期
     */
    private function formatArticleDate($article)
    {
        if ($article->publish_time && is_object($article->publish_time)) {
            return $article->publish_time->format('Y-m-d');
        } else if ($article->created_at && is_object($article->created_at)) {
            return $article->created_at->format('Y-m-d');
        } else if (is_string($article->publish_time) && !empty($article->publish_time)) {
            return date('Y-m-d', strtotime($article->publish_time));
        } else if (is_string($article->created_at) && !empty($article->created_at)) {
            return date('Y-m-d', strtotime($article->created_at));
        } else {
            return date('Y-m-d');
        }
    }

    /**
     * 格式化图片URL
     */
    private function getFormattedImageUrl($imagePath)
    {
        if (empty($imagePath)) {
            return asset('images/default-article.jpg'); 
        }
        
        // 如果是完整URL，直接返回
        if (filter_var($imagePath, FILTER_VALIDATE_URL)) {
            return $imagePath;
        }
        
        // 如果是相对路径，转换为完整URL
        if (strpos($imagePath, 'http') !== 0) {
            // 使用Storage来获取图片URL
            return Storage::disk(config('admin.upload.disk'))->url($imagePath);
        }
        
        return $imagePath;
    }

    /**
     * 显示文章详情
     */
    public function articleDetail($id)
    {
        // 根据ID查找文章
        $dbArticle = Article::with(['category', 'author', 'tags'])
            ->where('id', $id)
            ->where('status', Article::STATUS_PUBLISHED)
            ->firstOrFail();
            
        // 增加浏览量
        $dbArticle->increment('views');
        
        // 格式化文章数据
        $article = [
            'id' => $dbArticle->id,
            'title' => $dbArticle->title,
            'content' => $dbArticle->content,
            'image' => $this->getFormattedImageUrl($dbArticle->image),
            'category_id' => $dbArticle->category_id,
            'category_name' => $dbArticle->category ? $dbArticle->category->name : '未分类',
            'date' => $this->formatArticleDate($dbArticle),
            'views' => $dbArticle->views,
            'author' => $dbArticle->author ? $dbArticle->author->name : '系统管理员',
            'tags' => $dbArticle->tags->pluck('name')->toArray(), // 添加标签数组
        ];
        
        // 获取相关文章
        $relatedArticles = Article::with('category')
            ->where('id', '!=', $id)
            ->where('status', Article::STATUS_PUBLISHED)
            ->where(function($query) use ($dbArticle) {
                $query->where('category_id', $dbArticle->category_id)
                      ->orWhere('is_recommended', 1);
            })
            ->orderBy('publish_time', 'desc')
            ->take(3)
            ->get()
            ->map(function($related) {
                return [
                    'id' => $related->id,
                    'title' => $related->title,
                    'image' => $this->getFormattedImageUrl($related->image),
                    'date' => $this->formatArticleDate($related),
                ];
            })
            ->toArray();

        return view('pages.knowledge.article_detail', compact('article', 'relatedArticles'));
    }
    
    /**
     * 显示心理视频列表
     */
    public function videos(Request $request)
    {
        // 从数据库获取分类数据
        $categoriesFromDb = VideoCategory::where('is_active', 1)->get();
        
        // 转换为前端需要的格式，增加“全部”选项
        $categories = [
            ['id' => 0, 'name' => '全部'], 
        ];
        
        foreach ($categoriesFromDb as $category) {
            $categories[] = [
                'id' => $category->id, 
                'name' => $category->name
            ];
        }

        // 处理Ajax分页请求
        if ($request->ajax()) {
            $page = $request->input('page', 1);
            $perPage = 4; 
            
            $categoryId = $request->input('category_id', 0);
            $query = Video::with(['category', 'author', 'tags'])
                ->where('status', Video::STATUS_PUBLISHED);
            
            // 搜索功能
            if ($request->has('search') && !empty($request->input('search'))) {
                $search = $request->input('search');
                
                // 使用子查询进行相关性排序
                $query->where(function($q) use ($search) {
                    // 标题匹配 - 最高优先级
                    $q->where('title', 'like', "%{$search}%");
                    
                    // 描述匹配 - 次高优先级
                    $q->orWhere('description', 'like', "%{$search}%");
                    
                    // 内容匹配 - 中等优先级
                    $q->orWhere('content', 'like', "%{$search}%");
                    
                    // 标签匹配 - 通过关联关系查询
                    $q->orWhereHas('tags', function($tagQuery) use ($search) {
                        $tagQuery->where('name', 'like', "%{$search}%");
                    });
                });
                
                // 根据相关性字段排序（优先标题匹配，其次是发布时间）
                $query->orderByRaw("
                    CASE 
                        WHEN title LIKE ? THEN 1
                        WHEN description LIKE ? THEN 2
                        WHEN content LIKE ? THEN 3
                        ELSE 4
                    END", 
                    ["%{$search}%", "%{$search}%", "%{$search}%"]
                );
            } else {
                // 默认排序 - 发布时间
                $query->orderBy('publish_time', 'desc');
            }
            
            // 如果选择了特定分类而不是"全部"，过滤视频
            if ($categoryId > 0) { 
                $query->where('category_id', $categoryId);
            }
            
            try {
                $total = $query->count();
                $videoResults = $query->skip(($page - 1) * $perPage)->take($perPage)->get();
                
                // 将视频转换为前端所需的格式
                $formattedVideos = $videoResults->map(function($video) {
                    // 获取视频标签为数组
                    $tags = $video->tags->pluck('name')->toArray();
                    
                    return [
                        'id' => $video->id,
                        'title' => $video->title,
                        'image' => $this->getFormattedImageUrl($video->image), 
                        'video_url' => $video->video_url,
                        'video_duration' => $video->video_duration ?: '--:--',
                        'category_id' => $video->category_id, 
                        'category_name' => $video->category ? $video->category->name : '未分类',
                        'date' => $this->formatArticleDate($video),
                        'views' => $video->views,
                        'tags' => $tags,
                    ];
                });
                
                // 检查是否还有更多视频
                $hasMore = $total > ($page * $perPage);
                
                return response()->json([
                    'videos' => $formattedVideos,
                    'hasMore' => $hasMore
                ]);
            } catch (\Exception $e) {
                \Log::error('Error fetching videos for AJAX: ' . $e->getMessage());
                return response()->json([
                    'videos' => [],
                    'hasMore' => false,
                    'error' => 'Failed to load videos'
                ], 500);
            }
        }

        // 初始加载获取已发布视频
        $initialVideos = Video::with(['category', 'author', 'tags'])
            ->where('status', Video::STATUS_PUBLISHED)
            ->orderBy('publish_time', 'desc')
            ->take(4)
            ->get();
            
        // 将视频转换为前端所需的格式
        $videos = $initialVideos->map(function($video) {
            // 获取视频标签为数组
            $tags = $video->tags->pluck('name')->toArray();
            
            return [
                'id' => $video->id,
                'title' => $video->title,
                'image' => $this->getFormattedImageUrl($video->image), 
                'video_url' => $video->video_url,
                'video_duration' => $video->video_duration ?: '--:--',
                'category_id' => $video->category_id, 
                'category_name' => $video->category ? $video->category->name : '未分类',
                'date' => $this->formatArticleDate($video),
                'views' => $video->views,
                'tags' => $tags,
            ];
        })->toArray();

        return view('pages.knowledge.videos', compact('categories', 'videos'));
    }
    
    /**
     * 显示心理课堂列表
     */
    public function courses(Request $request)
    {
        // 从数据库获取分类数据
        $categoriesFromDb = CourseLessonCategory::where('is_active', 1)->get();
        
        // 转换为前端需要的格式，增加"全部"选项
        $categories = [
            ['id' => 0, 'name' => '全部'], 
        ];
        
        foreach ($categoriesFromDb as $category) {
            $categories[] = [
                'id' => $category->id, 
                'name' => $category->name
            ];
        }

        // 处理Ajax分页请求
        if ($request->ajax()) {
            $page = $request->input('page', 1);
            $perPage = 4; 
            
            $categoryId = $request->input('category_id', 0);
            $query = CourseLesson::with(['category', 'author', 'tags'])
                ->where('status', CourseLesson::STATUS_PUBLISHED);
            
            // 搜索功能
            if ($request->has('search') && !empty($request->input('search'))) {
                $search = $request->input('search');
                
                // 使用子查询进行相关性排序
                $query->where(function($q) use ($search) {
                    // 标题匹配 - 最高优先级
                    $q->where('title', 'like', "%{$search}%");
                    
                    // 描述匹配 - 次高优先级
                    $q->orWhere('description', 'like', "%{$search}%");
                    
                    // 内容匹配 - 中等优先级
                    $q->orWhere('content', 'like', "%{$search}%");
                    
                    // 标签匹配 - 通过关联关系查询
                    $q->orWhereHas('tags', function($tagQuery) use ($search) {
                        $tagQuery->where('name', 'like', "%{$search}%");
                    });
                });
                
                // 根据相关性字段排序（优先标题匹配，其次是发布时间）
                $query->orderByRaw("
                    CASE 
                        WHEN title LIKE ? THEN 1
                        WHEN description LIKE ? THEN 2
                        WHEN content LIKE ? THEN 3
                        ELSE 4
                    END", 
                    ["%{$search}%", "%{$search}%", "%{$search}%"]
                );
            } else {
                // 默认排序 - 发布时间
                $query->orderBy('publish_time', 'desc');
            }
            
            // 如果选择了特定分类而不是"全部"，过滤课程
            if ($categoryId > 0) { 
                $query->where('category_id', $categoryId);
            }
            
            try {
                $total = $query->count();
                $coursesResult = $query->skip(($page - 1) * $perPage)->take($perPage)->get();
                
                // 将课程转换为前端所需的格式
                $formattedCourses = $coursesResult->map(function($course) {
                    // 获取课程标签为数组
                    $tags = $course->tags->pluck('name')->toArray();
                    
                    return [
                        'id' => $course->id,
                        'title' => $course->title,
                        'image' => $this->getFormattedImageUrl($course->image), 
                        'video_url' => $course->video_url,
                        'video_duration' => $course->video_duration ?: '--:--',
                        'category_id' => $course->category_id, 
                        'category_name' => $course->category ? $course->category->name : '未分类',
                        'date' => $this->formatArticleDate($course),
                        'type' => $course->type,
                        'type_text' => $course->type_text,
                        'level' => $course->level,
                        'level_text' => $course->level_text,
                        'lessons_count' => $course->lessons_count,
                        'views' => $course->views,
                        'tags' => $tags,
                        'is_free' => $course->is_free,
                        'price' => $course->price
                    ];
                });
                
                // 检查是否还有更多课程
                $hasMore = $total > ($page * $perPage);
                
                return response()->json([
                    'courses' => $formattedCourses,
                    'hasMore' => $hasMore
                ]);
            } catch (\Exception $e) {
                \Log::error('Error fetching courses for AJAX: ' . $e->getMessage());
                return response()->json([
                    'courses' => [],
                    'hasMore' => false,
                    'error' => 'Failed to load courses'
                ], 500);
            }
        }

        // 初始加载获取已发布课程
        $initialCourses = CourseLesson::with(['category', 'author', 'tags'])
            ->where('status', CourseLesson::STATUS_PUBLISHED)
            ->orderBy('publish_time', 'desc')
            ->take(4)
            ->get();
            
        // 将课程转换为前端所需的格式
        $courses = $initialCourses->map(function($course) {
            // 获取课程标签为数组
            $tags = $course->tags->pluck('name')->toArray();
            
            return [
                'id' => $course->id,
                'title' => $course->title,
                'image' => $this->getFormattedImageUrl($course->image), 
                'video_url' => $course->video_url,
                'video_duration' => $course->video_duration ?: '--:--',
                'category_id' => $course->category_id, 
                'category_name' => $course->category ? $course->category->name : '未分类',
                'date' => $this->formatArticleDate($course),
                'type' => $course->type,
                'type_text' => $course->type_text,
                'level' => $course->level,
                'level_text' => $course->level_text,
                'lessons_count' => $course->lessons_count,
                'views' => $course->views,
                'tags' => $tags,
                'is_free' => $course->is_free,
                'price' => $course->price
            ];
        })->toArray();

        return view('pages.knowledge.courses', compact('categories', 'courses'));
    }

    /**
     * 显示视频详情
     */
    public function videoDetail($id)
    {
        try {
            // 根据ID查找视频，不限制状态
            $dbVideo = Video::with(['category', 'author', 'tags'])
                ->where('id', $id)
                ->firstOrFail();
                
            // 增加浏览量
            $dbVideo->increment('views');
            
            // 初始化标签数组
            $tags = [];
            if ($dbVideo->tags) {
                $tags = $dbVideo->tags->pluck('name')->toArray();
            }
            
            // 格式化视频数据
            $video = [
                'id' => $dbVideo->id,
                'title' => $dbVideo->title,
                'description' => $dbVideo->description ?: '',
                'content' => $dbVideo->content ?: '',
                'image' => $this->getFormattedImageUrl($dbVideo->image),
                'video_url' => $dbVideo->video_url ?: '',
                'video_duration' => $dbVideo->video_duration ?: '--:--',
                'category_id' => $dbVideo->category_id,
                'category_name' => $dbVideo->category ? $dbVideo->category->name : '未分类',
                'date' => $this->formatArticleDate($dbVideo),
                'views' => $dbVideo->views,
                'author' => $dbVideo->author ? $dbVideo->author->name : '系统管理员',
                'tags' => $tags,
            ];
            
            // 获取相关视频，不限制状态
            $relatedVideos = [];
            try {
                $relatedQuery = Video::with('category')
                    ->where('id', '!=', $id);
                    
                // 如果有分类，则按分类获取相关视频
                if ($dbVideo->category_id) {
                    $relatedQuery->where(function($query) use ($dbVideo) {
                        $query->where('category_id', $dbVideo->category_id)
                              ->orWhere('is_recommended', 1);
                    });
                }
                
                $relatedVideos = $relatedQuery->orderBy('id', 'desc')
                    ->take(3)
                    ->get()
                    ->map(function($related) {
                        return [
                            'id' => $related->id,
                            'title' => $related->title,
                            'image' => $this->getFormattedImageUrl($related->image),
                            'video_duration' => $related->video_duration ?: '--:--',
                            'date' => $this->formatArticleDate($related),
                            'views' => $related->views,
                        ];
                    })
                    ->toArray();
            } catch (\Exception $e) {
                \Log::error('Error fetching related videos: ' . $e->getMessage());
            }

            return view('pages.knowledge.video_detail', compact('video', 'relatedVideos'));
            
        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            \Log::error('Video not found: ' . $id);
            return redirect()->route('knowledge.videos')->with('error', '视频不存在或已被删除');
        } catch (\Exception $e) {
            \Log::error('Error displaying video: ' . $e->getMessage());
            return redirect()->route('knowledge.videos')->with('error', '获取视频详情失败，请稍后再试');
        }
    }
    
    /**
     * 显示课堂详情
     */
    public function courseDetail($id)
    {
        try {
            // 根据ID查找课程
            $dbCourse = CourseLesson::with(['category', 'author', 'tags'])
                ->where('id', $id)
                ->firstOrFail();
                
            // 增加浏览量
            $dbCourse->increment('views');
            
            // 获取用户进度信息
            $userProgress = null;
            if (Auth::check()) {
                $userProgress = $dbCourse->getUserProgress(Auth::id());
            }
            
            // 初始化标签数组
            $tags = [];
            if ($dbCourse->tags) {
                $tags = $dbCourse->tags->pluck('name')->toArray();
            }
            
            // 格式化课程数据
            $course = [
                'id' => $dbCourse->id,
                'title' => $dbCourse->title,
                'description' => $dbCourse->description ?: '',
                'content' => $dbCourse->content ?: '',
                'image' => $this->getFormattedImageUrl($dbCourse->image),
                'category_id' => $dbCourse->category_id,
                'category_name' => $dbCourse->category ? $dbCourse->category->name : '未分类',
                'date' => $this->formatArticleDate($dbCourse),
                'type' => $dbCourse->type,
                'type_text' => $dbCourse->type_text,
                'level' => $dbCourse->level,
                'level_text' => $dbCourse->level_text,
                'lessons_count' => $dbCourse->lessons_count,
                'views' => $dbCourse->views,
                'author' => $dbCourse->author ? $dbCourse->author->name : '系统管理员',
                'tags' => $tags,
                'is_free' => $dbCourse->is_free,
                'price' => $dbCourse->price,
                'resources' => $dbCourse->resources ?: [],
                'user_progress' => $userProgress ? [
                    'progress' => $userProgress->progress,
                    'current_position' => $userProgress->current_position,
                    'is_completed' => $userProgress->is_completed,
                ] : null
            ];
            
            // 如果是视频类型，添加视频相关数据
            if ($dbCourse->isVideoType()) {
                $course['video_url'] = $dbCourse->video_url;
                $course['video_duration'] = $dbCourse->video_duration ?: '--:--';
            }
            
            // 获取相关课程
            $relatedCourses = CourseLesson::with('category')
                ->where('id', '!=', $id)
                ->where('status', CourseLesson::STATUS_PUBLISHED)
                ->where(function($query) use ($dbCourse) {
                    $query->where('category_id', $dbCourse->category_id)
                          ->orWhere('is_recommended', 1);
                })
                ->orderBy('publish_time', 'desc')
                ->take(3)
                ->get()
                ->map(function($related) {
                    return [
                        'id' => $related->id,
                        'title' => $related->title,
                        'image' => $this->getFormattedImageUrl($related->image),
                        'date' => $this->formatArticleDate($related),
                        'type' => $related->type,
                        'type_text' => $related->type_text,
                    ];
                })
                ->toArray();
    
            return view('pages.knowledge.course_detail', compact('course', 'relatedCourses'));
        } catch (\Exception $e) {
            \Log::error('Error displaying course: ' . $e->getMessage());
            return redirect()->route('knowledge.courses')->with('error', '课程不存在或已被删除');
        }
    }
    
    /**
     * 更新课程学习进度
     */
    public function updateCourseProgress(Request $request, $id)
    {
        if (!Auth::check()) {
            return response()->json(['error' => '请先登录'], 401);
        }
        
        // 记录原始数据便于调试
        \Log::info('Course progress update request data:', $request->all());
        
        try {
            // 检查是否是重置请求
            $reset = (bool) $request->input('reset', false);
            
            // 如果是重置请求，强制删除并创建新记录
            if ($reset) {
                \Log::info('收到重置进度请求', ['user_id' => Auth::id(), 'course_id' => $id]);
                
                // 先删除当前用户的进度记录
                $deleted = CourseLessonProgress::where('user_id', Auth::id())
                    ->where('course_lesson_id', $id)
                    ->delete();
                
                // 创建新的进度记录，进度为0
                $progress = new CourseLessonProgress([
                    'user_id' => Auth::id(),
                    'course_lesson_id' => $id,
                    'current_position' => 0,
                    'progress' => 0,
                    'is_completed' => false,
                    'last_access_time' => now()
                ]);
                
                $progress->save();
                
                return response()->json([
                    'success' => true,
                    'message' => '进度已重置为0%',
                    'deleted_records' => $deleted,
                    'progress' => 0,
                    'current_position' => 0,
                    'is_completed' => false,
                ]);
            }
            
            // 手动处理并转换数据
            $position = (int) $request->input('position', 0);
            $progress = (int) $request->input('progress', 0);
            
            // 更可靠的完成状态判断
            $completedInput = $request->input('completed');
            // 确保0、'0'、false、'false'、null、''都转换为false
            $completed = filter_var($completedInput, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE);
            \Log::info('Completed value after conversion', ['raw' => $completedInput, 'converted' => $completed]);
            
            if ($position < 0) $position = 0;
            if ($progress < 0) $progress = 0;
            if ($progress > 100) $progress = 100;
            
            // 更新进度
            $progress = CourseLessonProgress::updateProgress(
                Auth::id(),
                $id,
                $position,
                $progress,
                $completed
            );
            
            return response()->json([
                'success' => true,
                'progress' => $progress->progress,
                'current_position' => $progress->current_position,
                'is_completed' => $progress->is_completed,
            ]);
        } catch (\Exception $e) {
            \Log::error('Error updating course progress: ' . $e->getMessage());
            return response()->json(['error' => '保存进度失败'], 500);
        }
    }
    
    /**
     * 重置课程学习进度
     */
    public function resetCourseProgress(Request $request, $id)
    {
        if (!Auth::check()) {
            return response()->json(['error' => '请先登录'], 401);
        }
        
        try {
            // 强制删除现有进度记录
            \Log::info('强制重置课程进度', ['user_id' => Auth::id(), 'course_id' => $id]);
            
            // 删除旧记录
            $deleted = CourseLessonProgress::where('user_id', Auth::id())
                ->where('course_lesson_id', $id)
                ->delete();
            
            // 创建新的进度记录
            $progress = new CourseLessonProgress([
                'user_id' => Auth::id(),
                'course_lesson_id' => $id,
                'current_position' => 0,
                'progress' => 0,
                'is_completed' => false,
                'last_access_time' => now()
            ]);
            $progress->save();
            
            return response()->json([
                'success' => true,
                'message' => '进度已重置为0%',
                'deleted_records' => $deleted,
                'progress' => $progress->progress,
                'current_position' => $progress->current_position,
                'is_completed' => $progress->is_completed,
            ]);
            
        } catch (\Exception $e) {
            \Log::error('Error resetting course progress: ' . $e->getMessage());
            return response()->json(['error' => '重置进度失败'], 500);
        }
    }
}
