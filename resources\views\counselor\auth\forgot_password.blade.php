<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>找回密码 - 心理健康平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: "SF Pro Text", "SF Pro Icons", "Helvetica Neue", Helvetica, Arial, sans-serif;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .forgot-container {
            width: 100%;
            max-width: 450px;
            padding: 30px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }
        
        .logo-container {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .form-title {
            font-size: 24px;
            font-weight: 600;
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #495057;
        }
        
        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #ced4da;
            border-radius: 8px;
            font-size: 15px;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
        }
        
        .btn-primary {
            background: #3498db;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 25px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: background 0.3s;
            width: 100%;
        }
        
        .btn-primary:hover {
            background: #2980b9;
        }
        
        .btn-back {
            display: block;
            text-align: center;
            margin-top: 15px;
            color: #6c757d;
            text-decoration: none;
        }
        
        .btn-back:hover {
            color: #3498db;
        }
        
        .alert {
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-danger {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .error-message {
            color: #dc3545;
            font-size: 14px;
            margin-top: 5px;
        }
        
        .code-container {
            display: flex;
        }
        
        .code-input {
            flex-grow: 1;
            margin-right: 10px;
        }
        
        .btn-send-code {
            background: #f1f1f1;
            color: #666;
            border: none;
            border-radius: 8px;
            padding: 12px 15px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: background 0.3s;
            white-space: nowrap;
        }
        
        .btn-send-code:hover {
            background: #e1e1e1;
        }
        
        .btn-send-code:disabled {
            background: #f1f1f1;
            color: #aaa;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="forgot-container">
        <div class="logo-container">
            <h1 style="font-size: 24px; margin-bottom: 8px;">心理健康平台</h1>
            <p style="color: #6c757d; margin-bottom: 0;">咨询师工作台</p>
        </div>
        
        <h1 class="form-title">找回密码</h1>
        
        @if(session('success'))
            <div class="alert alert-success">
                {{ session('success') }}
            </div>
        @endif
        
        @if($errors->any())
            <div class="alert alert-danger">
                <ul style="margin: 0; padding-left: 20px;">
                    @foreach($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif
        
        <form method="POST" action="{{ route('counselor.forgot_password.reset') }}" id="reset-form">
            @csrf
            
            <div class="form-group">
                <label class="form-label" for="phone">手机号</label>
                <input type="text" name="phone" id="phone" class="form-control" value="{{ old('phone') }}" required placeholder="请输入注册手机号">
                @error('phone')
                    <div class="error-message">{{ $message }}</div>
                @enderror
            </div>
            
            <div class="form-group">
                <label class="form-label" for="code">验证码</label>
                <div class="code-container">
                    <input type="text" name="code" id="code" class="form-control code-input" required placeholder="请输入验证码">
                    <button type="button" id="send-code" class="btn-send-code">获取验证码</button>
                </div>
                @error('code')
                    <div class="error-message">{{ $message }}</div>
                @enderror
            </div>
            
            <div class="form-group">
                <label class="form-label" for="password">新密码</label>
                <input type="password" name="password" id="password" class="form-control" required placeholder="请输入新密码，至少6位">
                @error('password')
                    <div class="error-message">{{ $message }}</div>
                @enderror
            </div>
            
            <div class="form-group">
                <label class="form-label" for="password_confirmation">确认新密码</label>
                <input type="password" name="password_confirmation" id="password_confirmation" class="form-control" required placeholder="请再次输入新密码">
            </div>
            
            <button type="submit" class="btn-primary">重置密码</button>
            
            <a href="{{ route('counselor.login') }}" class="btn-back">返回登录</a>
        </form>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const sendCodeBtn = document.getElementById('send-code');
            const phoneInput = document.getElementById('phone');
            
            let timer;
            let countdown = 60;
            
            // 发送验证码
            sendCodeBtn.addEventListener('click', async function() {
                const phone = phoneInput.value.trim();
                
                if (!phone) {
                    alert('请输入手机号');
                    return;
                }
                
                if (!/^1[3-9]\d{9}$/.test(phone)) {
                    alert('请输入有效的手机号');
                    return;
                }
                
                // 禁用按钮
                sendCodeBtn.disabled = true;
                
                try {
                    const response = await fetch('{{ route("counselor.forgot_password.send_code") }}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': '{{ csrf_token() }}'
                        },
                        body: JSON.stringify({ phone: phone })
                    });
                    
                    const data = await response.json();
                    
                    if (data.success) {
                        // 开始倒计时
                        startCountdown();
                        
                        // 显示验证码（测试环境）
                        if (data.debug_code) {
                            alert('验证码: ' + data.debug_code);
                        } else {
                            alert('验证码已发送，请注意查收');
                        }
                    } else {
                        alert(data.message || '发送验证码失败，请稍后重试');
                        sendCodeBtn.disabled = false;
                    }
                } catch (error) {
                    console.error('发送验证码错误:', error);
                    alert('发送验证码失败，请稍后重试');
                    sendCodeBtn.disabled = false;
                }
            });
            
            // 倒计时
            function startCountdown() {
                countdown = 60;
                sendCodeBtn.innerText = `${countdown}秒后重发`;
                
                timer = setInterval(() => {
                    countdown--;
                    sendCodeBtn.innerText = `${countdown}秒后重发`;
                    
                    if (countdown <= 0) {
                        clearInterval(timer);
                        sendCodeBtn.disabled = false;
                        sendCodeBtn.innerText = '获取验证码';
                    }
                }, 1000);
            }
        });
    </script>
</body>
</html>
