<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Dcat\Admin\Traits\HasDateTimeFormatter;

class OfflineCourse extends Model
{
    use HasFactory, SoftDeletes, HasDateTimeFormatter;

    protected $fillable = [
        'title',
        'image',
        'lecturer_id',
        'description',
        'outline',
        'location',
        'start_time',
        'end_time',
        'registration_deadline',
        'max_participants',
        'current_participants',
        'price',
        'status',
        'category',
    ];

    protected $casts = [
        'start_time' => 'datetime',
        'end_time' => 'datetime',
        'registration_deadline' => 'datetime'
    ];

    // 状态常量
    const STATUS_PENDING = 0;    // 待发布
    const STATUS_PUBLISHED = 1;  // 已发布
    const STATUS_FINISHED = 2;   // 已结束
    
     // 常量维护分类列表
    public const CATEGORIES = [
        1  => '70场职工心理健康讲座',
        2  => '55场精准专题类',
        3  => '25场沙龙类',
        4  => '积极心态',
        5  => '情绪管理',
        6  => '高效沟通',
        7  => '关爱职工',
        8  => '人际关系',
        9  => '夫妻关系',
        10  => '亲子关系',
        11 => '家庭教育',
    ];

    /**
     * 获取分类列表
     */
    public static function getCategories(): array
    {
        return self::CATEGORIES;
    }

    /**
     * 访问器：获取分类文本
     */
    public function getCategoryTextAttribute(): string
    {
        return self::CATEGORIES[$this->category] ?? '';
    }

    // 状态映射
    public static function getStatusMap()
    {
        return [
            self::STATUS_PENDING => '待发布',
            self::STATUS_PUBLISHED => '已发布',
            self::STATUS_FINISHED => '已结束',
        ];
    }

    // 获取状态文本
    public function getStatusTextAttribute()
    {
        return self::getStatusMap()[$this->status] ?? '未知';
    }

    // 与讲师（咨询师）的关联
    public function lecturer()
    {
        return $this->belongsTo(Counselor::class, 'lecturer_id');
    }
        // 与线下讲师（咨询师）的关联
    public function lecturerlx()
    {
        return $this->belongsTo(CounselorLx::class, 'lecturer_id');
    }

    // 与报名记录的关联
    public function registrations()
    {
        return $this->hasMany(CourseRegistration::class, 'course_id');
    }

    // 检查是否还有剩余名额
    public function hasAvailableSlots()
    {
        return $this->current_participants < $this->max_participants;
    }

    // 检查是否已满员
    public function isFullyBooked()
    {
        return $this->current_participants >= $this->max_participants;
    }

    // 获取剩余名额
    public function getAvailableSlotsAttribute()
    {
        return max(0, $this->max_participants - $this->current_participants);
    }

    // 检查是否可以报名
    public function isRegistrationOpen()
    {
        return $this->status == self::STATUS_PUBLISHED 
            && now()->lt($this->registration_deadline) 
            && $this->hasAvailableSlots();
    }

    // 格式化课程时间
    public function getCourseTimeAttribute()
    {
        return $this->start_time->format('Y-m-d H:i') . ' - ' . $this->end_time->format('H:i');
    }

    // 格式化课程持续时间
    public function getDurationAttribute()
    {
        return $this->start_time->diffInMinutes($this->end_time) . '分钟';
    }
}
