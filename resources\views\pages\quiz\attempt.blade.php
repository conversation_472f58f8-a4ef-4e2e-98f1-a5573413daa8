@extends('layouts.app')

@section('title', $quiz->title . ' - 答题')

@section('custom-styles')
<link rel="stylesheet" href="{{ asset('css/quiz.css') }}">
<style>
    .header-container {
        background: linear-gradient(135deg, #5b7cef, #3c67e3);
        color: white;
        padding: 15px;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 1000;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 50px;
    }
    
    .content-container {
        margin-top: 65px;
        padding: 15px;
        padding-bottom: 120px; /* 增加底部空间，预留导航栏和底部安全距离 */
    }
    
    .quiz-title {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 10px;
        color: #333;
    }
    
    .quiz-meta {
        display: flex;
        margin-bottom: 15px;
        font-size: 14px;
        color: #666;
    }
    
    .quiz-meta-item {
        display: flex;
        align-items: center;
        margin-right: 15px;
    }
    
    .quiz-meta-icon {
        margin-right: 5px;
        color: #3c67e3;
    }
    
    .question-container {
        margin-bottom: 20px;
        background-color: white;
        border-radius: 12px;
        padding: 15px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        border: 1px solid #f0f0f0;
    }
    
    .question-number {
        font-size: 14px;
        color: #3c67e3;
        font-weight: 600;
        margin-bottom: 10px;
    }
    
    .question-text {
        font-size: 16px;
        color: #333;
        line-height: 1.6;
        margin-bottom: 15px;
    }
    
    .question-type {
        font-size: 12px;
        color: #666;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
    }
    
    .question-type-icon {
        margin-right: 5px;
        color: #6c757d;
    }
    
    .options-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }
    
    .option-item {
        margin-bottom: 12px;
        position: relative;
    }
    
    .option-item:last-child {
        margin-bottom: 0;
    }
    
    .option-label {
        display: flex;
        align-items: center;
        padding: 12px 15px;
        border-radius: 8px;
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
        cursor: pointer;
        transition: all 0.2s ease;
    }
    
    .option-label:hover {
        background-color: #e9ecef;
    }
    
    .option-input {
        position: absolute;
        opacity: 0;
        cursor: pointer;
        height: 0;
        width: 0;
    }
    
    .option-input:checked + .option-label {
        background-color: #e7f3ff;
        border-color: #3c67e3;
    }
    
    .option-marker {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        background-color: white;
        border: 2px solid #ced4da;
        margin-right: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        font-weight: 500;
        color: #ced4da;
        transition: all 0.2s ease;
    }
    
    .option-input:checked + .option-label .option-marker {
        border-color: #3c67e3;
        background-color: #3c67e3;
        color: white;
    }
    
    .option-checkbox .option-marker {
        border-radius: 4px;
    }
    
    .option-text {
        flex: 1;
        font-size: 15px;
        color: #495057;
    }
    
    .quiz-navigation {
        display: flex;
        justify-content: space-between;
        margin: 20px 0;
    }
    
    .button-container {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        background-color: white;
        padding: 20px 15px;
        box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
        z-index: 100;
        display: flex;
        justify-content: space-between;
        /* 预留底部导航栏的空间 */
        padding-bottom: calc(20px + env(safe-area-inset-bottom));
        margin-bottom: 50px;
    }
    
    .nav-button {
        display: inline-block;
        padding: 10px 20px;
        border-radius: 8px;
        font-size: 15px;
        font-weight: 500;
        text-align: center;
        text-decoration: none;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .primary-button {
        background-color: #3c67e3;
        color: white;
        box-shadow: 0 3px 6px rgba(60, 103, 227, 0.2);
        border: none;
    }
    
    .primary-button:hover {
        background-color: #3559c7;
        color: white;
    }
    
    .secondary-button {
        background-color: #f0f4fe;
        color: #3c67e3;
        border: 1px solid #d8e3fc;
    }
    
    .secondary-button:hover {
        background-color: #e6ecfd;
        color: #3c67e3;
    }
    
    .disabled-button {
        background-color: #e9ecef;
        color: #868e96;
        cursor: not-allowed;
        border: 1px solid #dee2e6;
    }
    
    .timer-container {
        position: fixed;
        top: 50px;
        left: 0;
        right: 0;
        background-color: #f8f9fa;
        padding: 8px 15px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-bottom: 1px solid #e9ecef;
        font-size: 14px;
        color: #495057;
        z-index: 99;
    }
    
    .timer-icon {
        margin-right: 5px;
        color: #3c67e3;
    }
    
    .progress-container {
        margin-top: 40px;
        margin-bottom: 20px;
    }
    
    .progress-text {
        font-size: 14px;
        color: #495057;
        display: flex;
        justify-content: space-between;
        margin-bottom: 5px;
    }
    
    .progress-bar-container {
        height: 8px;
        background-color: #e9ecef;
        border-radius: 4px;
        overflow: hidden;
    }
    
    .progress-bar {
        height: 100%;
        background: linear-gradient(90deg, #3c67e3, #5b7cef);
        border-radius: 4px;
        transition: width 0.3s ease;
    }
    
    .pagination-container {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        margin-bottom: 20px;
    }
    
    .page-item {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 8px;
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
        font-size: 14px;
        font-weight: 500;
        color: #495057;
        cursor: pointer;
        transition: all 0.2s ease;
    }
    
    .page-item.active {
        background-color: #3c67e3;
        border-color: #3c67e3;
        color: white;
    }
    
    .page-item.answered {
        background-color: #e7f3ff;
        border-color: #3c67e3;
        color: #3c67e3;
    }
    
    .page-item:hover:not(.active) {
        background-color: #e9ecef;
    }
    
    .confirmation-modal {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    }
    
    .confirmation-modal.show {
        opacity: 1;
        visibility: visible;
    }
    
    .modal-content {
        background-color: white;
        border-radius: 12px;
        padding: 20px;
        width: 90%;
        max-width: 400px;
        box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        transform: translateY(-20px);
        transition: all 0.3s ease;
    }
    
    .confirmation-modal.show .modal-content {
        transform: translateY(0);
    }
    
    .modal-title {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 10px;
        color: #333;
    }
    
    .modal-text {
        font-size: 15px;
        color: #495057;
        margin-bottom: 20px;
        line-height: 1.5;
    }
    
    .modal-buttons {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
    }
</style>
@endsection

@section('content')
<div class="header-container">
    <a href="javascript:void(0)" id="quit-quiz-btn" class="back-button" style="display: flex; align-items: center; justify-content: center; width: 30px; height: 30px;">
        <div class="back-icon" style="border-top: 2px solid white; border-left: 2px solid white; width: 12px; height: 12px; transform: rotate(-45deg);"></div>
    </a>
    <h1 style="font-size: 18px; font-weight: 500; margin: 0 auto; text-align: center; flex-grow: 1; margin-left: -30px; text-shadow: 0 1px 2px rgba(0,0,0,0.1);">答题中</h1>
    <div style="width: 30px;"></div>
</div>

<div class="timer-container">
    <i class="fas fa-stopwatch timer-icon"></i>
    <span id="timer">00:00</span>
</div>

<div class="content-container">
    <form id="quiz-form" action="{{ route('quiz.submit', ['id' => $quiz->id, 'attempt_id' => $attempt->id]) }}" method="POST">
        @csrf
        
        <div class="progress-container">
            <div class="progress-text">
                <span>答题进度</span>
                <span id="progress-text">0/{{ $quiz->questions->count() }}</span>
            </div>
            <div class="progress-bar-container">
                <div class="progress-bar" id="progress-bar" style="width: 0%;"></div>
            </div>
        </div>
        
        <div class="pagination-container" id="pagination">
            @foreach($quiz->questions as $index => $question)
            <div class="page-item" data-question="{{ $index + 1 }}">{{ $index + 1 }}</div>
            @endforeach
        </div>
        
        @foreach($quiz->questions as $index => $question)
        <div class="question-container" id="question-{{ $index + 1 }}" style="{{ $index > 0 ? 'display: none;' : '' }}">
            <div class="question-number">问题 {{ $index + 1 }}/{{ $quiz->questions->count() }}</div>
            
            <div class="question-type">
                @if($question->question_type === 'single_choice')
                    <i class="far fa-circle question-type-icon"></i> 单选题
                @elseif($question->question_type === 'multiple_choice')
                    <i class="far fa-check-square question-type-icon"></i> 多选题
                @else
                    <i class="fas fa-check question-type-icon"></i> 判断题
                @endif
                
                @if($question->points > 1)
                    <span style="margin-left: 10px; color: #dc3545;">{{ $question->points }}分</span>
                @endif
            </div>
            
            <div class="question-text">{{ $question->question_text }}</div>
            
            <ul class="options-list">
                @foreach($question->options as $option)
                <li class="option-item">
                    @if($question->question_type === 'multiple_choice')
                        <!-- 多选题 -->
                        <input type="checkbox" name="answers[{{ $question->id }}][]" value="{{ $option->id }}" id="option-{{ $question->id }}-{{ $option->id }}" class="option-input option-checkbox" data-question="{{ $index + 1 }}">
                        <label for="option-{{ $question->id }}-{{ $option->id }}" class="option-label">
                            <span class="option-marker"><i class="fas fa-check" style="font-size: 12px;"></i></span>
                            <span class="option-text">{{ $option->option_text }}</span>
                        </label>
                    @elseif($question->question_type === 'true_false')
                        <!-- 判断题 -->
                        <input type="radio" name="answers[{{ $question->id }}]" value="{{ $option->id }}" id="option-{{ $question->id }}-{{ $option->id }}" class="option-input" data-question="{{ $index + 1 }}">
                        <label for="option-{{ $question->id }}-{{ $option->id }}" class="option-label">
                            <span class="option-marker">{{ in_array($option->option_text, ['是', '对', 'T', 'True', '1']) ? '✓' : '✗' }}</span>
                            <span class="option-text">{{ $option->option_text }}</span>
                        </label>
                    @else
                        <!-- 单选题 -->
                        <input type="radio" name="answers[{{ $question->id }}]" value="{{ $option->id }}" id="option-{{ $question->id }}-{{ $option->id }}" class="option-input" data-question="{{ $index + 1 }}">
                        <label for="option-{{ $question->id }}-{{ $option->id }}" class="option-label">
                            <span class="option-marker">{{ chr(65 + $loop->index) }}</span>
                            <span class="option-text">{{ $option->option_text }}</span>
                        </label>
                    @endif
                </li>
                @endforeach
            </ul>
        </div>
        @endforeach
        
        <div class="button-container">
            <button type="button" id="prev-btn" class="nav-button secondary-button" style="display: none;">上一题</button>
            <button type="button" id="next-btn" class="nav-button primary-button">下一题</button>
            <button type="button" id="submit-btn" class="nav-button primary-button" style="display: none;">提交答案</button>
        </div>
    </form>
</div>

<div class="confirmation-modal" id="quit-confirmation">
    <div class="modal-content">
        <div class="modal-title">确定要退出答题？</div>
        <div class="modal-text">退出后本次答题记录将丢失，您需要重新开始答题。</div>
        <div class="modal-buttons">
            <button class="nav-button secondary-button" id="cancel-quit">取消</button>
            <a href="{{ route('quiz.show', $quiz->id) }}" class="nav-button primary-button">确定退出</a>
        </div>
    </div>
</div>

<div class="confirmation-modal" id="submit-confirmation">
    <div class="modal-content">
        <div class="modal-title">确定要提交答案？</div>
        <div class="modal-text">
            您已回答了 <span id="answered-count">0</span>/{{ $quiz->questions->count() }} 题
            <div id="unanswered-warning" style="color: #dc3545; margin-top: 5px; display: none;">
                还有题目未回答，确定要提交吗？
            </div>
        </div>
        <div class="modal-buttons">
            <button class="nav-button secondary-button" id="cancel-submit">取消</button>
            <button class="nav-button primary-button" id="confirm-submit">确定提交</button>
        </div>
    </div>
</div>

@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const totalQuestions = {{ $quiz->questions->count() }};
        let currentQuestion = 1;
        let answeredQuestions = {};
        let startTime = new Date();
        
        // 初始化定时器
        setInterval(updateTimer, 1000);
        
        // 更新定时器显示
        function updateTimer() {
            const now = new Date();
            const diffInSeconds = Math.floor((now - startTime) / 1000);
            
            const minutes = Math.floor(diffInSeconds / 60);
            const seconds = diffInSeconds % 60;
            
            const formattedMinutes = minutes < 10 ? '0' + minutes : minutes;
            const formattedSeconds = seconds < 10 ? '0' + seconds : seconds;
            
            document.getElementById('timer').textContent = `${formattedMinutes}:${formattedSeconds}`;
        }
        
        // 选项选择事件
        document.querySelectorAll('.option-input').forEach(input => {
            input.addEventListener('change', function() {
                const questionNum = parseInt(this.getAttribute('data-question'));
                
                // 记录已回答的问题
                answeredQuestions[questionNum] = true;
                
                // 更新分页中的高亮
                updatePagination();
                
                // 更新进度
                updateProgress();
            });
        });
        
        // 分页点击事件
        document.querySelectorAll('.page-item').forEach(item => {
            item.addEventListener('click', function() {
                const questionNum = parseInt(this.getAttribute('data-question'));
                navigateToQuestion(questionNum);
            });
        });
        
        // 上一题按钮
        document.getElementById('prev-btn').addEventListener('click', function() {
            navigateToQuestion(currentQuestion - 1);
        });
        
        // 下一题按钮
        document.getElementById('next-btn').addEventListener('click', function() {
            navigateToQuestion(currentQuestion + 1);
        });
        
        // 退出按钮
        document.getElementById('quit-quiz-btn').addEventListener('click', function() {
            document.getElementById('quit-confirmation').classList.add('show');
        });
        
        // 取消退出
        document.getElementById('cancel-quit').addEventListener('click', function() {
            document.getElementById('quit-confirmation').classList.remove('show');
        });
        
        // 提交按钮
        document.getElementById('submit-btn').addEventListener('click', function() {
            const answeredCount = Object.keys(answeredQuestions).length;
            document.getElementById('answered-count').textContent = answeredCount;
            
            if (answeredCount < totalQuestions) {
                document.getElementById('unanswered-warning').style.display = 'block';
            } else {
                document.getElementById('unanswered-warning').style.display = 'none';
            }
            
            document.getElementById('submit-confirmation').classList.add('show');
        });
        
        // 取消提交
        document.getElementById('cancel-submit').addEventListener('click', function() {
            document.getElementById('submit-confirmation').classList.remove('show');
        });
        
        // 确认提交
        document.getElementById('confirm-submit').addEventListener('click', function() {
            document.getElementById('quiz-form').submit();
        });
        
        // 导航到指定问题
        function navigateToQuestion(questionNum) {
            if (questionNum < 1 || questionNum > totalQuestions) return;
            
            // 隐藏当前问题
            document.getElementById(`question-${currentQuestion}`).style.display = 'none';
            
            // 显示目标问题
            document.getElementById(`question-${questionNum}`).style.display = 'block';
            
            // 更新当前问题
            currentQuestion = questionNum;
            
            // 更新导航按钮
            updateNavButtons();
            
            // 更新分页高亮
            updatePaginationHighlight();
        }
        
        // 更新导航按钮状态
        function updateNavButtons() {
            const prevBtn = document.getElementById('prev-btn');
            const nextBtn = document.getElementById('next-btn');
            const submitBtn = document.getElementById('submit-btn');
            
            // 第一题隐藏上一题按钮
            if (currentQuestion === 1) {
                prevBtn.style.display = 'none';
            } else {
                prevBtn.style.display = 'block';
            }
            
            // 最后一题显示提交按钮，隐藏下一题按钮
            if (currentQuestion === totalQuestions) {
                nextBtn.style.display = 'none';
                submitBtn.style.display = 'block';
            } else {
                nextBtn.style.display = 'block';
                submitBtn.style.display = 'none';
            }
        }
        
        // 更新分页高亮
        function updatePaginationHighlight() {
            document.querySelectorAll('.page-item').forEach(item => {
                item.classList.remove('active');
            });
            
            document.querySelector(`.page-item[data-question="${currentQuestion}"]`).classList.add('active');
        }
        
        // 更新分页（标记已回答的问题）
        function updatePagination() {
            document.querySelectorAll('.page-item').forEach(item => {
                const questionNum = parseInt(item.getAttribute('data-question'));
                
                if (answeredQuestions[questionNum]) {
                    item.classList.add('answered');
                }
            });
        }
        
        // 更新进度条
        function updateProgress() {
            const answeredCount = Object.keys(answeredQuestions).length;
            const progressPercentage = (answeredCount / totalQuestions) * 100;
            
            document.getElementById('progress-bar').style.width = `${progressPercentage}%`;
            document.getElementById('progress-text').textContent = `${answeredCount}/${totalQuestions}`;
        }
        
        // 初始化
        updateNavButtons();
        updatePaginationHighlight();
    });
</script>
@endsection
