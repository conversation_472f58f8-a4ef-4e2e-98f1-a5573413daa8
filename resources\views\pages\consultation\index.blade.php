@extends('layouts.app')

@section('title', '留言咨询')

@section('content')
<div class="consultation-form-container">
    <!-- 页面标题 -->
    <div class="page-header">
        <div class="header-content">
            <h1>留言咨询</h1>
            <p>专业心理咨询师为您提供帮助</p>
        </div>
        <a href="{{ route('message_consultation.my_consultations') }}" class="my-messages-btn">
            我的留言
        </a>
    </div>

    <!-- 留言表单 -->
    <div class="form-card">
        <form id="consultationForm" action="{{ route('message_consultation.submit') }}" method="POST">
            @csrf
            
            <!-- 基本信息 -->
            <div class="form-section">
                <h3 class="section-title">基本信息</h3>
                
                <div class="form-group">
                    <label for="name">姓名 <span class="required">*</span></label>
                    <input type="text" id="name" name="name" required placeholder="请输入您的姓名">
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="phone">联系电话</label>
                        <input type="tel" id="phone" name="phone" placeholder="请输入手机号码">
                    </div>
                    <div class="form-group">
                        <label for="email">邮箱地址</label>
                        <input type="email" id="email" name="email" placeholder="请输入邮箱地址">
                    </div>
                </div>
            </div>

            <!-- 咨询内容 -->
            <div class="form-section">
                <h3 class="section-title">咨询内容</h3>
                
                <div class="form-group">
                    <label for="subject">咨询主题 <span class="required">*</span></label>
                    <input type="text" id="subject" name="subject" required placeholder="请简要描述您的问题">
                </div>
                
                <div class="form-group">
                    <label for="content">详细描述 <span class="required">*</span></label>
                    <textarea id="content" name="content" rows="6" required 
                              placeholder="请详细描述您遇到的心理问题或困惑，我们的专业咨询师会为您提供帮助..."></textarea>
                    <div class="char-count">
                        <span id="charCount">0</span>/500字
                    </div>
                </div>
            </div>

            <!-- 提交按钮 -->
            <div class="form-actions">
                <button type="submit" class="submit-btn">
                    <i class="fas fa-paper-plane"></i>
                    提交留言
                </button>
            </div>
        </form>
    </div>

    <!-- 温馨提示 -->
    <div class="tips-card">
        <h4><i class="fas fa-lightbulb"></i> 温馨提示</h4>
        <ul>
            <li>我们的专业心理咨询师会在24小时内回复您的留言</li>
            <li>请详细描述您的问题，以便我们提供更准确的建议</li>
            <li>所有咨询内容严格保密，请放心咨询</li>
            <li>如需紧急帮助，请拨打心理援助热线</li>
        </ul>
    </div>
</div>

<style>
.consultation-form-container {
    max-width: 100%;
    margin: 0;
    padding: 15px;
    background: #f5f5f5;
    min-height: 100vh;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.header-content h1 {
    font-size: 20px;
    color: #333;
    margin: 0 0 5px 0;
    font-weight: 600;
}

.header-content p {
    font-size: 14px;
    color: #666;
    margin: 0;
}

.my-messages-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-decoration: none;
    padding: 10px 16px;
    border-radius: 20px;
    font-size: 14px;
    white-space: nowrap;
}

.form-card {
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    padding: 20px;
    margin-bottom: 20px;
}

.form-section {
    margin-bottom: 25px;
}

.form-section:last-child {
    margin-bottom: 0;
}

.section-title {
    font-size: 16px;
    color: #333;
    margin: 0 0 15px 0;
    font-weight: 600;
    padding-bottom: 8px;
    border-bottom: 2px solid #f0f0f0;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 6px;
    color: #333;
    font-weight: 500;
    font-size: 14px;
}

.required {
    color: #e74c3c;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 14px;
    box-sizing: border-box;
    transition: border-color 0.3s, box-shadow 0.3s;
}

.form-group input:focus,
.form-group textarea:focus {
    border-color: #667eea;
    outline: none;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.char-count {
    text-align: right;
    font-size: 12px;
    color: #999;
    margin-top: 5px;
}

.char-count.warning {
    color: #f39c12;
}

.char-count.error {
    color: #e74c3c;
}

.form-actions {
    text-align: center;
    margin-top: 30px;
}

.submit-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 15px 40px;
    border-radius: 25px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: transform 0.2s, box-shadow 0.3s;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.submit-btn:active {
    transform: translateY(0);
}

.submit-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.submit-btn i {
    margin-right: 8px;
}

.tips-card {
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    padding: 20px;
    border-left: 4px solid #667eea;
}

.tips-card h4 {
    color: #333;
    margin: 0 0 15px 0;
    font-size: 16px;
    font-weight: 600;
}

.tips-card h4 i {
    color: #667eea;
    margin-right: 8px;
}

.tips-card ul {
    margin: 0;
    padding-left: 20px;
    color: #666;
}

.tips-card li {
    margin-bottom: 8px;
    line-height: 1.5;
    font-size: 14px;
}

.tips-card li:last-child {
    margin-bottom: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .consultation-form-container {
        padding: 10px;
    }
    
    .page-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .header-content {
        width: 100%;
    }
    
    .form-card {
        padding: 15px;
    }
    
    .form-row {
        grid-template-columns: 1fr;
        gap: 0;
    }
    
    .submit-btn {
        width: 100%;
        padding: 15px 20px;
    }
}

@media (max-width: 480px) {
    .section-title {
        font-size: 15px;
    }
    
    .form-group input,
    .form-group textarea {
        padding: 10px;
        font-size: 16px; /* 防止iOS缩放 */
    }
    
    .tips-card {
        padding: 15px;
    }
    
    .tips-card h4 {
        font-size: 15px;
    }
    
    .tips-card li {
        font-size: 13px;
    }
}

/* 加载状态 */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid transparent;
    border-top: 2px solid #fff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('consultationForm');
    const contentTextarea = document.getElementById('content');
    const charCount = document.getElementById('charCount');
    const submitBtn = form.querySelector('.submit-btn');
    
    // 字符计数
    function updateCharCount() {
        const length = contentTextarea.value.length;
        charCount.textContent = length;
        
        // 更新样式
        charCount.parentElement.classList.remove('warning', 'error');
        if (length > 450) {
            charCount.parentElement.classList.add('error');
        } else if (length > 400) {
            charCount.parentElement.classList.add('warning');
        }
        
        // 限制最大长度
        if (length > 500) {
            contentTextarea.value = contentTextarea.value.substring(0, 500);
            updateCharCount();
        }
    }
    
    contentTextarea.addEventListener('input', updateCharCount);
    contentTextarea.addEventListener('paste', function() {
        setTimeout(updateCharCount, 10);
    });
    
    // 表单提交
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // 验证表单
        const name = document.getElementById('name').value.trim();
        const subject = document.getElementById('subject').value.trim();
        const content = contentTextarea.value.trim();
        
        if (!name) {
            alert('请输入您的姓名');
            document.getElementById('name').focus();
            return;
        }
        
        if (!subject) {
            alert('请输入咨询主题');
            document.getElementById('subject').focus();
            return;
        }
        
        if (!content) {
            alert('请输入详细描述');
            contentTextarea.focus();
            return;
        }
        
        if (content.length < 10) {
            alert('详细描述至少需要10个字符');
            contentTextarea.focus();
            return;
        }
        
        // 显示加载状态
        submitBtn.disabled = true;
        submitBtn.classList.add('loading');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 提交中...';
        
        // 提交表单
        const formData = new FormData(form);
        
        fetch(form.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('留言提交成功！我们会尽快回复您。');
                // 跳转到我的留言页面
                window.location.href = '{{ route("message_consultation.my_consultations") }}';
            } else {
                alert(data.message || '提交失败，请重试');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('提交失败，请检查网络连接后重试');
        })
        .finally(() => {
            // 恢复按钮状态
            submitBtn.disabled = false;
            submitBtn.classList.remove('loading');
            submitBtn.innerHTML = originalText;
        });
    });
    
    // 输入验证
    const phoneInput = document.getElementById('phone');
    phoneInput.addEventListener('input', function() {
        // 只允许数字和常见符号
        this.value = this.value.replace(/[^\d\-\+\(\)\s]/g, '');
    });
    
    // 自动调整textarea高度
    contentTextarea.addEventListener('input', function() {
        this.style.height = 'auto';
        this.style.height = Math.max(120, this.scrollHeight) + 'px';
    });
});
</script>
@endsection 