<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class QuizAttempt extends Model
{
    use HasFactory, HasDateTimeFormatter;

    protected $fillable = [
        'quiz_activity_id',
        'user_id',
        'score',
        'total_points',
        'correct_count',
        'total_questions',
        'has_won_prize',
        'is_completed',
        'completed_at',
    ];

    protected $casts = [
        'has_won_prize' => 'boolean',
        'is_completed' => 'boolean',
        'completed_at' => 'datetime',
    ];

    // 关联问答活动
    public function quizActivity()
    {
        return $this->belongsTo(QuizActivity::class);
    }

    // 关联用户
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    // 关联答案
    public function answers()
    {
        return $this->hasMany(QuizAnswer::class, 'attempt_id');
    }

    // 关联奖品获奖记录
    public function prizeWinner()
    {
        return $this->hasOne(QuizPrizeWinner::class, 'attempt_id');
    }

    // 计算分数
    public function calculateScore()
    {
        $correctCount = $this->answers()->where('is_correct', true)->count();
        $totalQuestions = $this->answers()->count();
        $score = 0;
        $totalPoints = 0;

        // 计算总分和得分
        foreach ($this->answers as $answer) {
            $question = $answer->question;
            $totalPoints += $question->points;
            if ($answer->is_correct) {
                $score += $question->points;
            }
        }

        // 计算分数百分比，用于判断是否达到及格线
        $scorePercentage = $totalPoints > 0 ? ($score / $totalPoints) * 100 : 0;

        // 更新记录
        $this->update([
            'score' => $score,
            'total_points' => $totalPoints,
            'correct_count' => $correctCount,
            'total_questions' => $totalQuestions,
            'is_completed' => true,
            'completed_at' => now(),
        ]);

        // 检查是否可以获得奖品
        if ($scorePercentage >= $this->quizActivity->pass_score) {
            $this->checkAndAssignPrize();
        }

        return $this;
    }

    // 检查并分配奖品
    private function checkAndAssignPrize()
    {
        // 如果已经获得过奖品，不再分配
        if ($this->has_won_prize) {
            return;
        }

        // 查找符合分数要求且有库存的奖品
        $eligiblePrize = $this->quizActivity->prizes()
            ->where('min_score', '<=', $this->score)
            ->whereRaw('quantity > (SELECT COUNT(*) FROM quiz_prize_winners WHERE prize_id = quiz_prizes.id)')
            ->orderBy('min_score', 'desc')
            ->first();

        if ($eligiblePrize) {
            // 创建获奖记录
            QuizPrizeWinner::create([
                'quiz_activity_id' => $this->quiz_activity_id,
                'prize_id' => $eligiblePrize->id,
                'user_id' => $this->user_id,
                'attempt_id' => $this->id,
                'status' => 'pending',
            ]);

            // 更新尝试记录
            $this->update(['has_won_prize' => true]);
        }
    }
}
