<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Dcat\Admin\Traits\HasDateTimeFormatter;

/**
 * 心理测评答卷模型
 */
class AssessmentResponse extends Model
{
    use HasDateTimeFormatter;

    // 批量赋值字段
    protected $fillable = ['user_id', 'questionnaire_id', 'total_score', 'submitted_at'];

    // 类型转换
    protected $casts = [
        'submitted_at' => 'datetime',
    ];

    /**
     * 答卷所属用户
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 答卷所属问卷
     */
    public function questionnaire(): BelongsTo
    {
        return $this->belongsTo(AssessmentQuestionnaire::class, 'questionnaire_id');
    }

    /**
     * 答卷下所有答案
     */
    public function answers(): HasMany
    {
        return $this->hasMany(AssessmentAnswer::class, 'response_id');
    }

    /**
     * 答卷的 AI 分析结果
     */
    public function analysis(): HasOne
    {
        return $this->hasOne(AssessmentAnalysis::class, 'response_id');
    }
}
