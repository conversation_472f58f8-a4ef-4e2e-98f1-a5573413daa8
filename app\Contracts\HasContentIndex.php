<?php

namespace App\Contracts;

/**
 * 内容索引接口
 * 
 * 定义可被索引内容必须实现的方法
 */
interface HasContentIndex
{
    /**
     * 判断内容是否需要索引
     *
     * @return bool
     */
    public function shouldBeIndexed(): bool;

    /**
     * 获取内容类型
     *
     * @return string
     */
    public function getContentIndexType(): string;

    /**
     * 获取内容标题
     *
     * @return string
     */
    public function getContentIndexTitle(): string;

    /**
     * 获取内容摘要
     *
     * @return string|null
     */
    public function getContentIndexSummary(): ?string;

    /**
     * 获取内容URL
     *
     * @return string|null
     */
    public function getContentIndexUrl(): ?string;

    /**
     * 获取内容元数据
     *
     * @return array
     */
    public function getContentIndexMetadata(): array;

    /**
     * 获取用于向量生成的内容
     *
     * @return string
     */
    public function getContentForVector(): string;
} 