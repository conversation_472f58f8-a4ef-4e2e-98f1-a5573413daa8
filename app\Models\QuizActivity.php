<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class QuizActivity extends Model
{
    use HasFactory, SoftDeletes, HasDateTimeFormatter;

    protected $fillable = [
        'title',
        'description',
        'cover_image',
        'start_time',
        'end_time',
        'pass_score',
        'max_attempts',
        'is_active',
        'show_answers_after_submit',
    ];

    protected $casts = [
        'start_time' => 'datetime',
        'end_time' => 'datetime',
        'is_active' => 'boolean',
        'show_answers_after_submit' => 'boolean',
    ];

    // 关联问题
    public function questions()
    {
        return $this->hasMany(QuizQuestion::class)->orderBy('order');
    }

    // 关联奖品
    public function prizes()
    {
        return $this->hasMany(QuizPrize::class);
    }

    // 关联尝试记录
    public function attempts()
    {
        return $this->hasMany(QuizAttempt::class);
    }

    // 关联获奖记录
    public function prizeWinners()
    {
        return $this->hasMany(QuizPrizeWinner::class);
    }

    // 检查问答是否进行中
    public function isOngoing()
    {
        return $this->is_active &&
               now()->between($this->start_time, $this->end_time);
    }

    // 获取用户尝试次数
    public function userAttemptCount($userId)
    {
        return $this->attempts()
                    ->where('user_id', $userId)
                    ->where('is_completed', true)
                    ->count();
    }

    // 检查用户是否可以参与
    public function canUserAttempt($userId)
    {
        if (!$this->isOngoing()) {
            return false;
        }

        $attemptCount = $this->userAttemptCount($userId);
        return $attemptCount < $this->max_attempts;
    }

    // 检查是否还有奖品可以发放
    public function hasAvailablePrizes()
    {
        return $this->prizes()
                   ->where('quantity', '>', function($query) {
                       $query->selectRaw('COUNT(*)')
                             ->from('quiz_prize_winners')
                             ->whereColumn('prize_id', 'quiz_prizes.id');
                   })
                   ->exists();
    }
    
    /**
     * 分配奖品给通过测试的用户
     * 
     * @param QuizAttempt $attempt 用户的测试尝试记录
     * @param bool $passed 是否通过测试
     * @return QuizPrize|null 如果成功分配则返回奖品对象，否则返回 null
     */
    public function assignPrizeToUser($attempt, $passed = true)
    {
        // 用户必须通过测试
        if (!$passed) {
            return null;
        }
        
        // 输出调试信息
        \Log::info("Quiz prize check: user_score={$attempt->score}, required min_score for prizes");
        
        // 获取用户分数满足条件且数量还有剩余的奖品
        // 注意：min_score应该与绝对分数进行比较
        $availablePrizes = $this->prizes()
            ->where('min_score', '<=', $attempt->score) // 使用绝对分数与min_score比较
            ->where('quantity', '>', function($query) {
                $query->selectRaw('COUNT(*)')
                      ->from('quiz_prize_winners')
                      ->whereColumn('prize_id', 'quiz_prizes.id');
            })
            ->orderBy('min_score', 'desc') // 优先选择分数要求高的奖品
            ->first();
            
        return $availablePrizes;
    }
}
