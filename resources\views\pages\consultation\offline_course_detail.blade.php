@extends('layouts.app')

@section('title', $course->title . ' - 线下心理课程详情')

@section('custom-styles')
<style>
    .offline-course-detail {
        padding: 15px 15px 120px 15px; /* 为底部导航留出120px空间 */
    }
    
    .offline-course-detail-header {
        margin-bottom: 20px;
    }
    
    .offline-course-detail-banner {
        width: 100%;
        height: 200px;
        border-radius: 10px;
        overflow: hidden;
        margin-bottom: 15px;
    }
    
    .offline-course-detail-banner img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    
    .offline-course-detail-title {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 10px;
        color: #333;
    }
    
    .offline-course-detail-meta {
        margin-bottom: 15px;
    }
    
    .offline-course-detail-meta-item {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        color: #666;
        font-size: 14px;
    }
    
    .offline-course-detail-meta-label {
        min-width: 70px;
        font-weight: bold;
        color: #333;
    }
    
    .offline-course-detail-meta-value {
        flex: 1;
    }
    
    .offline-course-detail-status-badge {
        padding: 3px 10px;
        border-radius: 15px;
        font-size: 12px;
        display: inline-block;
        margin-left: 5px;
    }
    
    .offline-course-detail-status-available {
        background-color: #E8F5E9;
        color: #4CAF50;
    }
    
    .offline-course-detail-status-full {
        background-color: #FFEBEE;
        color: #F44336;
    }
    
    .offline-course-detail-section-title {
        font-size: 16px;
        font-weight: bold;
        margin: 20px 0 10px;
        padding-bottom: 5px;
        border-bottom: 1px solid #eee;
    }
    
    .offline-course-detail-section-content {
        line-height: 1.6;
        color: #333;
        margin-bottom: 15px;
    }
    
    .offline-course-detail-register {
        background-color: #f9f9f9;
        border-radius: 10px;
        padding: 15px;
        margin-top: 20px;
    }
    
    .offline-course-detail-register-title {
        font-weight: bold;
        font-size: 16px;
        margin-bottom: 10px;
    }
    
    .offline-course-detail-price-info {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
    }
    
    .offline-course-detail-price-label {
        font-weight: bold;
        margin-right: 10px;
    }
    
    .offline-course-detail-price-value {
        color: #ff5722;
        font-size: 18px;
        font-weight: bold;
    }
    
    .offline-course-detail-free-badge {
        background-color: #E3F2FD;
        color: #2196F3;
        padding: 3px 8px;
        border-radius: 5px;
        font-size: 13px;
    }
    
    .offline-course-detail-limit-info {
        font-size: 14px;
        color: #666;
        margin-bottom: 15px;
    }
    
    .offline-course-detail-register-btn {
        background-color: #4CAF50;
        color: white;
        border: none;
        padding: 12px;
        border-radius: 5px;
        width: 100%;
        font-size: 16px;
        cursor: pointer;
    }
    
    .offline-course-detail-register-btn:disabled {
        background-color: #cccccc;
        cursor: not-allowed;
    }
    
    .offline-course-detail-registered-badge {
        background-color: #E0E0E0;
        color: #757575;
        text-align: center;
        padding: 12px;
        border-radius: 5px;
        width: 100%;
        font-size: 16px;
    }
    
    .offline-course-detail-date-badge {
        background-color: #f0f0f0;
        padding: 2px 8px;
        border-radius: 10px;
        font-size: 12px;
        margin-right: 5px;
    }
    
    .offline-course-detail-registration-note {
        font-size: 12px;
        color: #666;
        margin-top: 10px;
    }
    
    .offline-course-detail-lecturer-section {
        margin-top: 20px;
        display: flex;
        align-items: flex-start;
    }
    
    .offline-course-detail-lecturer-avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        overflow: hidden;
        margin-right: 15px;
        flex-shrink: 0;
    }
    
    .offline-course-detail-lecturer-avatar img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    
    .offline-course-detail-lecturer-info {
        flex: 1;
    }
    
    .offline-course-detail-lecturer-name {
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .offline-course-detail-lecturer-title {
        color: #666;
        font-size: 13px;
        margin-bottom: 8px;
    }
    
    .offline-course-detail-lecturer-description {
        font-size: 14px;
        color: #333;
        line-height: 1.5;
    }
    
    .offline-course-detail-page-header {
        display: flex;
        align-items: center;
        padding: 15px;
        background-color: #fff;
        position: sticky;
        top: 0;
        z-index: 100;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    }
    
    .offline-course-detail-back-button {
        width: 24px;
        height: 24px;
        margin-right: 15px;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23333'%3E%3Cpath d='M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z'/%3E%3C/svg%3E");
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
        cursor: pointer;
    }


    /* 弹窗遮罩 & 弹窗内容 —— 仅此两块替换 —— */
    .modal-overlay {
        position: fixed;
        top: 0; left: 0;
        width: 100%; height: 100%;
        background: rgba(0,0,0,0.6);
        display: none;
        align-items: center;
        justify-content: center;
        z-index: 1001;
        opacity: 0;
        transition: opacity 0.25s ease-in-out;
    }
    .modal-overlay.show {
        display: flex;
        opacity: 1;
    }
    .modal-dialog {
        position: relative;
        background: #fff;
        padding: 20px;
        border-radius: 12px;
        width: 90%;
        max-width: 400px;
        max-height: 80vh;
        overflow-y: auto;
        box-shadow: 0 8px 24px rgba(0,0,0,0.2);
        transform: translateY(-20px);
        transition: transform 0.25s ease-in-out;
    }
    .modal-overlay.show .modal-dialog {
        transform: translateY(0);
    }
    .modal-dialog .modal-close {
        position: absolute;
        top: 12px; right: 16px;
        font-size: 20px;
        color: #999;
        cursor: pointer;
        transition: color 0.2s;
    }
    .modal-dialog .modal-close:hover {
        color: #333;
    }
    .modal-dialog h3 {
        margin: 0 0 12px;
        font-size: 18px;
    }
    #modalError {
        color: #e53e3e;
        margin-bottom: 12px;
        font-size: 14px;
    }
    .modal-dialog .form-group { margin-bottom: 16px; }
    .modal-dialog .form-group label { display: block; margin-bottom: 6px; font-weight: 500; }
    .modal-dialog .form-control {
        width: 100%; padding: 8px 10px;
        border: 1px solid #ccc; border-radius: 6px; box-sizing: border-box;
    }
    .modal-dialog .btn-group {
        display: flex; justify-content: flex-end; gap: 10px; margin-top: 20px;
    }
    .modal-dialog .btn-group button {
        flex: 1; padding: 10px 0; border-radius: 6px; font-size: 16px;
    }
    .modal-dialog .btn-group #cancelRegistration {
        background: #f0f0f0; color: #333;
    }
    .modal-dialog .btn-group #cancelRegistration:hover {
        background: #e0e0e0;
    }
    .modal-dialog .btn-group button[type="submit"] {
        background: #4CAF50; color: #fff;
    }
</style>
@endsection

@section('content')
<div class="offline-course-detail-page-header">
    <a href="{{ route('consultation.offline_courses') }}" class="offline-course-detail-back-button"></a>
    <h1 style="font-size: 18px; margin: 0;">课程详情</h1>
</div>

<div class="container">
    <div class="offline-course-detail">
        {{-- 课程头部信息 --}}
        <div class="offline-course-detail-header">
            <div class="offline-course-detail-banner">
                <img src="{{ $course->image ? asset('storage/'.$course->image) : asset('images/default-course.jpg') }}"
                     alt="{{ $course->title }}">
            </div>
            <h1 class="offline-course-detail-title">{{ $course->title }}</h1>
            <div class="offline-course-detail-meta">
                <div class="offline-course-detail-meta-item">
                    <div class="offline-course-detail-meta-label">课程类型</div>
                    <div class="offline-course-detail-meta-value">心理课程</div>
                </div>
                <div class="offline-course-detail-meta-item">
                    <div class="offline-course-detail-meta-label">开始时间</div>
                    <div class="offline-course-detail-meta-value">{{ date('Y-m-d H:i', strtotime($course->start_time)) }}</div>
                </div>
                <div class="offline-course-detail-meta-item">
                    <div class="offline-course-detail-meta-label">结束时间</div>
                    <div class="offline-course-detail-meta-value">{{ date('Y-m-d H:i', strtotime($course->end_time)) }}</div>
                </div>
                <div class="offline-course-detail-meta-item">
                    <div class="offline-course-detail-meta-label">课程地点</div>
                    <div class="offline-course-detail-meta-value">{{ $course->location ?: '待定' }}</div>
                </div>
                <div class="offline-course-detail-meta-item">
                    <div class="offline-course-detail-meta-label">主讲人</div>
                    <div class="offline-course-detail-meta-value">{{ $course->lecturer ? $course->lecturer->name : '待定' }}</div>
                </div>
                <div class="offline-course-detail-meta-item">
                    <div class="offline-course-detail-meta-label">报名状态</div>
                    <div class="offline-course-detail-meta-value">
                        截止时间：{{ date('Y-m-d', strtotime($course->registration_deadline)) }}
                        @if($isFullyBooked)
                            <span class="offline-course-detail-status-badge offline-course-detail-status-full">名额已满</span>
                        @else
                            <span class="offline-course-detail-status-badge offline-course-detail-status-available">可报名</span>
                        @endif
                    </div>
                </div>
            </div>
        </div>
        
        {{-- 课程简介 --}}
        <div class="offline-course-detail-section-title">课程简介</div>
        <div class="offline-course-detail-section-content">
            {!! $course->description ?: '暂无课程简介' !!}
        </div>
        
        {{-- 课程大纲 --}}
        <div class="offline-course-detail-section-title">课程大纲</div>
        <div class="offline-course-detail-section-content">
            {!! $course->outline ?: '暂无课程大纲' !!}
        </div>
        
        {{-- 讲师信息 --}}
        @if($course->lecturer)
        <div class="offline-course-detail-section-title">讲师介绍</div>
        <div class="offline-course-detail-lecturer-section">
            <div class="offline-course-detail-lecturer-avatar">
                <img src="{{ $course->lecturer->avatar ? asset('storage/'.$course->lecturer->avatar) : asset('images/default-avatar.jpg') }}" alt="{{ $course->lecturer->name }}">
            </div>
            <div class="offline-course-detail-lecturer-info">
                <div class="offline-course-detail-lecturer-name">{{ $course->lecturer->name }}</div>
                <div class="offline-course-detail-lecturer-title">{{ $course->lecturer->title ?: '心理咨询师' }}</div>
                <div class="offline-course-detail-lecturer-description">{!! $course->lecturer->introduction ?: '暂无讲师介绍' !!}</div>
            </div>
        </div>
        @endif
        
        {{-- 报名区域 --}}
        <div class="offline-course-detail-register">
            <div class="offline-course-detail-register-title">课程报名</div>
            <!--<div class="offline-course-detail-price-info">-->
            <!--    <div class="offline-course-detail-price-label">价格：</div>-->
            <!--    <div class="offline-course-detail-price-value">-->
            <!--        @if($course->price > 0)-->
            <!--            {{ '¥' . $course->price }}-->
            <!--        @else-->
            <!--            <span class="offline-course-detail-free-badge">免费</span>-->
            <!--        @endif-->
            <!--    </div>-->
            <!--</div>-->
            <div class="offline-course-detail-limit-info">
                限额：{{ $course->max_participants }} 人 (已报名：{{ $course->current_participants }} 人)
            </div>
            
            @auth
                @if($userRegistered)
                    <div class="offline-course-detail-registered-badge">已报名</div>
                    <p class="offline-course-detail-registration-note">您已成功报名该课程，请按时参加</p>
                @else
                    @if(Auth::user()->is_contact == 1)
                        <button type="button" id="openRegistrationModal" class="offline-course-detail-register-btn" {{ $isFullyBooked || now()->gt($course->registration_deadline) ? 'disabled' : '' }}>
                            {{ $isFullyBooked ? '名额已满' : ( now()->gt($course->registration_deadline) ? '报名已截止' : '立即报名') }}
                        </button>
                        @if(! $isFullyBooked && now()->lte($course->registration_deadline))
                            <p class="offline-course-detail-registration-note">点击报名即表示您同意遵守课程相关规定</p>
                        @endif
                    @else
                        <div class="offline-course-detail-registered-badge">您尚未成为联系人，无法报名</div>
                        <p class="offline-course-detail-registration-note">请联系管理员开通联系人权限。</p>
                    @endif
                @endif
            @else
                <a href="{{ route('login') }}" class="offline-course-detail-register-btn" style="display:block; text-align:center; text-decoration:none;">登录后报名</a>
                <p class="offline-course-detail-registration-note">请先登录后再进行课程报名</p>
            @endauth
        </div>
    </div>
</div>

{{-- 报名弹窗 --}}
<div class="modal-overlay" id="registrationModal">
    <div class="modal-dialog">
        <span class="modal-close" id="cancelRegistration">&times;</span>
        <h3>填写报名信息</h3>
        <div id="modalError"></div>
        <form id="registrationForm" action="{{ route('consultation.register_course') }}" method="POST">
            @csrf
            <input type="hidden" name="course_id" value="{{ $course->id }}">
            @if(Auth::user()->enterprise)
                <input type="hidden" name="department" value="{{ Auth::user()->enterprise }}">
                <div class="form-group">
                    <label for="participants">报名人数</label>
                    <input type="number" name="participants" id="participants" class="form-control" min="1" required>
                </div>
            @else
                <div class="form-group">
                    <label for="department">单位名称</label>
                    <input type="text" name="department" id="department" class="form-control" required>
                </div>
                <div class="form-group">
                    <label for="participants">报名人数</label>
                    <input type="number" name="participants" id="participants" class="form-control" min="1" required>
                </div>
            @endif
            <div class="btn-group">
                <button type="button" id="cancelRegistration" class="offline-course-detail-register-btn">取消</button>
                <button type="submit" class="offline-course-detail-register-btn">提交</button>
            </div>
        </form>
    </div>
</div>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
$(function(){
  $('#openRegistrationModal').click(function(e){
    e.preventDefault();
    $('#modalError').text('');
    $('#registrationModal').addClass('show');
  });
  $('#cancelRegistration, #registrationModal').click(function(e){
    if (e.target.id==='cancelRegistration' || e.target.id==='registrationModal') {
      $('#registrationModal').removeClass('show');
    }
  });
  $('#registrationForm').off('submit').on('submit', function(e){
    e.preventDefault();
    $('#modalError').text('');
    $.ajax({
      url: this.action,
      type: 'POST',
      data: $(this).serialize(),
      headers: {'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')}
    })
    .done(function(){
      window.location.href = "{{ route('consultation.my_courses') }}";
    })
    .fail(function(jqXHR){
      if (jqXHR.status===422 && jqXHR.responseJSON.errors) {
        var msg=''; $.each(jqXHR.responseJSON.errors, function(_,arr){ msg+=arr.join('、')+'；'; });
        $('#modalError').text(msg);
      } else if((jqXHR.status===400||jqXHR.status===500)&&jqXHR.responseJSON.message){
        $('#modalError').text(jqXHR.responseJSON.message);
      } else {
        window.location.href = "{{ route('consultation.my_courses') }}";
      }
    });
  });
});
</script>

