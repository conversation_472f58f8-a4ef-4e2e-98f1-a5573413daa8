# 心理健康平台数据库设计文档

## 数据库概述

### 基本信息
- **数据库名称**: `psy_oneself_icu`
- **数据库版本**: MySQL 5.7.44
- **字符集**: utf8mb4
- **排序规则**: utf8mb4_unicode_ci
- **表总数**: 68个
- **导出时间**: 2025-07-25 16:27:44

### 设计特点
- 采用Laravel框架的数据库迁移管理
- 支持多语言字符集（utf8mb4）
- 完整的外键约束和索引优化
- 模块化的表结构设计
- 支持软删除和时间戳记录

---

## 数据库架构图

### 1. 系统整体架构图

```mermaid
graph TB
    subgraph "前端应用层"
        WebApp[Web应用]
        AdminPanel[管理后台]
        MobileApp[移动端]
    end

    subgraph "业务逻辑层"
        UserService[用户服务]
        ContentService[内容服务]
        ConsultationService[咨询服务]
        AssessmentService[评估服务]
        AIService[AI服务]
    end

    subgraph "数据访问层"
        UserModule[用户管理模块]
        ContentModule[内容管理模块]
        ConsultationModule[咨询管理模块]
        AssessmentModule[评估测试模块]
        SystemModule[系统管理模块]
    end

    subgraph "数据存储层"
        MySQL[(MySQL数据库)]
        Redis[(Redis缓存)]
        FileStorage[(文件存储)]
    end

    WebApp --> UserService
    AdminPanel --> ContentService
    MobileApp --> ConsultationService

    UserService --> UserModule
    ContentService --> ContentModule
    ConsultationService --> ConsultationModule
    AssessmentService --> AssessmentModule
    AIService --> SystemModule

    UserModule --> MySQL
    ContentModule --> MySQL
    ConsultationModule --> MySQL
    AssessmentModule --> MySQL
    SystemModule --> MySQL

    UserModule --> Redis
    ContentModule --> FileStorage
```

### 2. 核心业务模块关系图

```mermaid
graph LR
    subgraph "用户体系"
        U1[普通用户<br/>users]
        U2[管理员<br/>admin_users]
        U3[咨询师<br/>counselors]
    end

    subgraph "权限体系"
        P1[角色<br/>admin_roles]
        P2[权限<br/>admin_permissions]
        P3[菜单<br/>admin_menu]
    end

    subgraph "内容体系"
        C1[文章<br/>articles]
        C2[视频<br/>videos]
        C3[新闻<br/>news]
        C4[课程<br/>course_lessons]
    end

    subgraph "咨询体系"
        S1[预约<br/>consultation_appointments]
        S2[消息<br/>messages]
        S3[AI咨询<br/>ai_consultation_records]
    end

    subgraph "评估体系"
        A1[问卷<br/>assessment_questionnaires]
        A2[答卷<br/>assessment_responses]
        A3[分析<br/>assessment_analyses]
    end

    subgraph "活动体系"
        Q1[答题活动<br/>quiz_activities]
        Q2[直播<br/>live_shows]
        Q3[线下课程<br/>offline_courses]
    end

    U1 -.-> S1
    U1 -.-> A2
    U1 -.-> Q1
    U3 -.-> S1
    U3 -.-> S2
    U2 -.-> P1
    P1 -.-> P2
    P2 -.-> P3

    style U1 fill:#e1f5fe
    style U2 fill:#fff3e0
    style U3 fill:#f3e5f5
    style S1 fill:#e8f5e8
    style A2 fill:#fff8e1
    style Q1 fill:#fce4ec
```

### 3. 详细实体关系图 (ERD)

```mermaid
erDiagram
    %% 用户相关实体
    users {
        bigint id PK
        varchar name
        varchar email UK
        varchar phone UK
        varchar password
        varchar avatar
        tinyint phone_verified
        timestamp email_verified_at
        varchar remember_token
        timestamp last_login_at
        varchar last_login_ip
        tinyint is_contact
        varchar enterprise
        timestamp created_at
        timestamp updated_at
    }

    counselors {
        bigint id PK
        varchar name
        varchar email UK
        varchar phone
        varchar password
        varchar avatar
        text bio
        text specialties
        text qualifications
        int experience_years
        decimal hourly_rate
        tinyint is_active
        tinyint is_verified
        timestamp created_at
        timestamp updated_at
    }

    admin_users {
        bigint id PK
        varchar username UK
        varchar password
        varchar name
        varchar avatar
        varchar remember_token
        timestamp created_at
        timestamp updated_at
    }

    %% 咨询相关实体
    consultation_appointments {
        bigint id PK
        bigint user_id FK
        bigint counselor_id FK
        date appointment_date
        time appointment_time
        int duration
        varchar consultation_type
        text description
        varchar status
        timestamp confirmed_at
        timestamp started_at
        timestamp completed_at
        timestamp cancelled_at
        text cancellation_reason
        timestamp created_at
        timestamp updated_at
    }

    messages {
        bigint id PK
        bigint user_id FK
        bigint counselor_id FK
        bigint appointment_id FK
        varchar sender_type
        text content
        tinyint is_read
        timestamp read_at
        varchar attachment
        timestamp created_at
        timestamp updated_at
    }

    %% 评估相关实体
    assessment_questionnaires {
        bigint id PK
        varchar title
        text description
        varchar domain
        int question_count
        int est_duration
        varchar cover_image
        tinyint is_active
        timestamp created_at
        timestamp updated_at
    }

    assessment_questions {
        bigint id PK
        bigint questionnaire_id FK
        enum type
        text content
        int sort_order
        timestamp created_at
        timestamp updated_at
    }

    assessment_options {
        bigint id PK
        bigint question_id FK
        text content
        int score_value
        timestamp created_at
        timestamp updated_at
    }

    assessment_responses {
        bigint id PK
        bigint user_id FK
        bigint questionnaire_id FK
        timestamp submitted_at
        int total_score
        timestamp created_at
        timestamp updated_at
    }

    assessment_answers {
        bigint id PK
        bigint response_id FK
        bigint question_id FK
        bigint option_id FK
        text custom_value
        timestamp created_at
        timestamp updated_at
    }

    assessment_analyses {
        bigint id PK
        bigint response_id FK
        int overall_score
        varchar level_name
        text description
        text suggestions
        text detailed_analysis
        longtext ai_analysis
        json detail_json
        timestamp created_at
        timestamp updated_at
    }

    %% 内容相关实体
    articles {
        bigint id PK
        varchar title
        longtext content
        varchar summary
        varchar image
        bigint category_id FK
        bigint author_id FK
        tinyint is_recommended
        int views
        enum status
        timestamp publish_time
        varchar source
        timestamp created_at
        timestamp updated_at
    }

    article_categories {
        bigint id PK
        varchar name
        varchar description
        int sort_order
        tinyint is_active
        timestamp created_at
        timestamp updated_at
    }

    %% 活动相关实体
    quiz_activities {
        bigint id PK
        varchar title
        text description
        varchar cover_image
        datetime start_time
        datetime end_time
        int pass_score
        int max_attempts
        tinyint is_active
        tinyint show_answers_after_submit
        timestamp created_at
        timestamp updated_at
        timestamp deleted_at
    }

    quiz_attempts {
        bigint id PK
        bigint quiz_activity_id FK
        bigint user_id FK
        int score
        int total_points
        int correct_count
        int total_questions
        tinyint has_won_prize
        tinyint is_completed
        datetime completed_at
        timestamp created_at
        timestamp updated_at
    }

    %% 关系定义
    users ||--o{ consultation_appointments : "用户预约咨询"
    counselors ||--o{ consultation_appointments : "咨询师接受预约"
    users ||--o{ messages : "用户发送消息"
    counselors ||--o{ messages : "咨询师发送消息"
    consultation_appointments ||--o{ messages : "预约产生消息"

    users ||--o{ assessment_responses : "用户参与评估"
    assessment_questionnaires ||--o{ assessment_questions : "问卷包含问题"
    assessment_questions ||--o{ assessment_options : "问题包含选项"
    assessment_questionnaires ||--o{ assessment_responses : "问卷产生答卷"
    assessment_responses ||--o{ assessment_answers : "答卷包含答案"
    assessment_responses ||--|| assessment_analyses : "答卷生成分析"
    assessment_questions ||--o{ assessment_answers : "问题对应答案"
    assessment_options ||--o{ assessment_answers : "选项对应答案"

    users ||--o{ articles : "用户创作文章"
    article_categories ||--o{ articles : "分类包含文章"

    users ||--o{ quiz_attempts : "用户参与答题"
    quiz_activities ||--o{ quiz_attempts : "活动产生记录"
```

---

## 表结构详细说明

### 1. 用户管理模块

#### 1.1 users - 用户表
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint(20) unsigned | - | NO | AUTO_INCREMENT | 主键ID |
| name | varchar | 191 | NO | - | 用户姓名 |
| email | varchar | 191 | NO | - | 邮箱地址 |
| phone | varchar | 20 | YES | NULL | 手机号码 |
| phone_verified | tinyint(1) | - | NO | 0 | 手机号是否验证 |
| avatar | varchar | 191 | YES | NULL | 头像URL |
| email_verified_at | timestamp | - | YES | NULL | 邮箱验证时间 |
| password | varchar | 191 | NO | - | 密码哈希 |
| remember_token | varchar | 100 | YES | NULL | 记住登录令牌 |
| last_login_at | timestamp | - | YES | NULL | 最后登录时间 |
| last_login_ip | varchar | 45 | YES | NULL | 最后登录IP |
| is_contact | tinyint(1) | - | NO | 0 | 是否是联络人 |
| enterprise | varchar | 255 | YES | NULL | 单位名称 |
| created_at | timestamp | - | YES | NULL | 创建时间 |
| updated_at | timestamp | - | YES | NULL | 更新时间 |

**索引:**
- PRIMARY KEY (`id`)
- UNIQUE KEY `users_email_unique` (`email`)
- KEY `users_phone_index` (`phone`)

#### 1.2 admin_users - 管理员用户表
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint(20) unsigned | - | NO | AUTO_INCREMENT | 主键ID |
| username | varchar | 120 | NO | - | 用户名 |
| password | varchar | 80 | NO | - | 密码哈希 |
| name | varchar | 191 | NO | - | 显示名称 |
| avatar | varchar | 191 | YES | NULL | 头像URL |
| remember_token | varchar | 100 | YES | NULL | 记住登录令牌 |
| created_at | timestamp | - | YES | NULL | 创建时间 |
| updated_at | timestamp | - | YES | NULL | 更新时间 |

**索引:**
- PRIMARY KEY (`id`)
- UNIQUE KEY `admin_users_username_unique` (`username`)

#### 1.3 sms_codes - 短信验证码表
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint(20) unsigned | - | NO | AUTO_INCREMENT | 主键ID |
| phone | varchar | 20 | NO | - | 手机号码 |
| code | varchar | 10 | NO | - | 验证码 |
| type | varchar | 20 | NO | login | 验证码类型 |
| expires_at | timestamp | - | NO | - | 过期时间 |
| used | tinyint(1) | - | NO | 0 | 是否已使用 |
| created_at | timestamp | - | YES | NULL | 创建时间 |
| updated_at | timestamp | - | YES | NULL | 更新时间 |

**索引:**
- PRIMARY KEY (`id`)
- KEY `sms_codes_phone_index` (`phone`)

### 2. 权限管理模块

#### 2.1 admin_roles - 管理员角色表
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint(20) unsigned | - | NO | AUTO_INCREMENT | 主键ID |
| name | varchar | 50 | NO | - | 角色名称 |
| slug | varchar | 50 | NO | - | 角色标识 |
| created_at | timestamp | - | YES | NULL | 创建时间 |
| updated_at | timestamp | - | YES | NULL | 更新时间 |

**索引:**
- PRIMARY KEY (`id`)
- UNIQUE KEY `admin_roles_slug_unique` (`slug`)

#### 2.2 admin_permissions - 管理员权限表
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint(20) unsigned | - | NO | AUTO_INCREMENT | 主键ID |
| name | varchar | 50 | NO | - | 权限名称 |
| slug | varchar | 50 | NO | - | 权限标识 |
| http_method | varchar | 191 | YES | NULL | HTTP方法 |
| http_path | text | - | YES | NULL | HTTP路径 |
| order | int(11) | - | NO | 0 | 排序 |
| parent_id | bigint(20) | - | NO | 0 | 父级权限ID |
| created_at | timestamp | - | YES | NULL | 创建时间 |
| updated_at | timestamp | - | YES | NULL | 更新时间 |

**索引:**
- PRIMARY KEY (`id`)
- UNIQUE KEY `admin_permissions_slug_unique` (`slug`)

#### 2.3 admin_menu - 管理员菜单表
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint(20) unsigned | - | NO | AUTO_INCREMENT | 主键ID |
| parent_id | bigint(20) | - | NO | 0 | 父级菜单ID |
| order | int(11) | - | NO | 0 | 排序 |
| title | varchar | 50 | NO | - | 菜单标题 |
| icon | varchar | 50 | YES | NULL | 菜单图标 |
| uri | varchar | 50 | YES | NULL | 菜单URI |
| extension | varchar | 50 | NO | '' | 扩展标识 |
| show | tinyint(4) | - | NO | 1 | 是否显示 |
| created_at | timestamp | - | YES | NULL | 创建时间 |
| updated_at | timestamp | - | YES | NULL | 更新时间 |

**索引:**
- PRIMARY KEY (`id`)

### 3. 内容管理模块

#### 3.1 articles - 文章表
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint(20) unsigned | - | NO | AUTO_INCREMENT | 主键ID |
| title | varchar | 191 | NO | - | 文章标题 |
| content | longtext | - | NO | - | 文章内容 |
| summary | varchar | 500 | YES | NULL | 文章摘要 |
| image | varchar | 191 | YES | NULL | 封面图片 |
| category_id | bigint(20) unsigned | - | NO | - | 分类ID |
| author_id | bigint(20) unsigned | - | YES | NULL | 作者ID |
| is_recommended | tinyint(1) | - | NO | 0 | 是否推荐 |
| views | int(11) | - | NO | 0 | 浏览次数 |
| status | enum | - | NO | draft | 状态 |
| publish_time | timestamp | - | YES | NULL | 发布时间 |
| source | varchar | 100 | YES | NULL | 文章来源 |
| created_at | timestamp | - | YES | NULL | 创建时间 |
| updated_at | timestamp | - | YES | NULL | 更新时间 |

**状态枚举值:** draft(草稿), pending(待审核), published(已发布), rejected(已拒绝)

**索引:**
- PRIMARY KEY (`id`)
- KEY `articles_category_id_foreign` (`category_id`)
- KEY `articles_author_id_foreign` (`author_id`)
- KEY `articles_status_publish_time_index` (`status`, `publish_time`)

**外键约束:**
- FOREIGN KEY (`category_id`) REFERENCES `article_categories` (`id`) ON DELETE CASCADE
- FOREIGN KEY (`author_id`) REFERENCES `users` (`id`) ON DELETE SET NULL

#### 3.2 article_categories - 文章分类表
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint(20) unsigned | - | NO | AUTO_INCREMENT | 主键ID |
| name | varchar | 50 | NO | - | 分类名称 |
| description | varchar | 255 | YES | NULL | 分类描述 |
| sort_order | int(11) | - | NO | 0 | 排序顺序 |
| is_active | tinyint(1) | - | NO | 1 | 是否启用 |
| created_at | timestamp | - | YES | NULL | 创建时间 |
| updated_at | timestamp | - | YES | NULL | 更新时间 |

**索引:**
- PRIMARY KEY (`id`)

#### 3.3 videos - 视频表
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint(20) unsigned | - | NO | AUTO_INCREMENT | 主键ID |
| title | varchar | 191 | NO | - | 视频标题 |
| description | text | - | YES | NULL | 视频简短描述 |
| content | longtext | - | YES | NULL | 视频详细内容 |
| image | varchar | 191 | YES | NULL | 视频封面图片 |
| video_url | varchar | 191 | YES | NULL | 视频URL |
| video_duration | varchar | 20 | YES | NULL | 视频时长 |
| category_id | int(10) unsigned | - | YES | NULL | 分类ID |
| author_id | int(10) unsigned | - | YES | NULL | 作者ID |
| views | int(10) unsigned | - | NO | 0 | 观看次数 |
| is_recommended | tinyint(1) | - | NO | 0 | 是否推荐 |
| status | tinyint(4) | - | NO | 0 | 状态 |
| publish_time | timestamp | - | YES | NULL | 发布时间 |
| created_at | timestamp | - | YES | NULL | 创建时间 |
| updated_at | timestamp | - | YES | NULL | 更新时间 |

**状态值:** 0=草稿，1=待审核，2=已发布，3=已拒绝

**索引:**
- PRIMARY KEY (`id`)
- KEY `videos_category_id_foreign` (`category_id`)

### 4. 咨询管理模块

#### 4.1 counselors - 咨询师表
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint(20) unsigned | - | NO | AUTO_INCREMENT | 主键ID |
| name | varchar | 100 | NO | - | 咨询师姓名 |
| email | varchar | 191 | NO | - | 邮箱地址 |
| phone | varchar | 20 | YES | NULL | 手机号码 |
| password | varchar | 191 | YES | NULL | 登录密码 |
| avatar | varchar | 191 | YES | NULL | 头像URL |
| bio | text | - | YES | NULL | 个人简介 |
| specialties | text | - | YES | NULL | 专业领域 |
| qualifications | text | - | YES | NULL | 资质证书 |
| experience_years | int(11) | - | NO | 0 | 从业年限 |
| hourly_rate | decimal(8,2) | - | NO | 0.00 | 小时费率 |
| is_active | tinyint(1) | - | NO | 1 | 是否启用 |
| is_verified | tinyint(1) | - | NO | 0 | 是否认证 |
| created_at | timestamp | - | YES | NULL | 创建时间 |
| updated_at | timestamp | - | YES | NULL | 更新时间 |

**索引:**
- PRIMARY KEY (`id`)
- UNIQUE KEY `counselors_email_unique` (`email`)

#### 4.2 consultation_appointments - 咨询预约表
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint(20) unsigned | - | NO | AUTO_INCREMENT | 主键ID |
| user_id | bigint(20) unsigned | - | NO | - | 用户ID |
| counselor_id | bigint(20) unsigned | - | NO | - | 咨询师ID |
| appointment_date | date | - | NO | - | 预约日期 |
| appointment_time | time | - | NO | - | 预约时间 |
| duration | int(11) | - | NO | 60 | 咨询时长(分钟) |
| consultation_type | varchar | 50 | NO | - | 咨询类型 |
| description | text | - | YES | NULL | 问题描述 |
| status | varchar | 20 | NO | pending | 预约状态 |
| confirmed_at | timestamp | - | YES | NULL | 确认时间 |
| started_at | timestamp | - | YES | NULL | 开始时间 |
| completed_at | timestamp | - | YES | NULL | 完成时间 |
| cancelled_at | timestamp | - | YES | NULL | 取消时间 |
| cancellation_reason | text | - | YES | NULL | 取消原因 |
| created_at | timestamp | - | YES | NULL | 创建时间 |
| updated_at | timestamp | - | YES | NULL | 更新时间 |

**状态值:** pending(待确认), confirmed(已确认), started(进行中), completed(已完成), cancelled(已取消)

**索引:**
- PRIMARY KEY (`id`)
- KEY `consultation_appointments_user_id_foreign` (`user_id`)
- KEY `consultation_appointments_counselor_id_foreign` (`counselor_id`)

**外键约束:**
- FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
- FOREIGN KEY (`counselor_id`) REFERENCES `counselors` (`id`) ON DELETE CASCADE

#### 4.3 messages - 消息表
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint(20) unsigned | - | NO | AUTO_INCREMENT | 主键ID |
| user_id | bigint(20) unsigned | - | NO | - | 用户ID |
| counselor_id | bigint(20) unsigned | - | NO | - | 咨询师ID |
| appointment_id | bigint(20) unsigned | - | YES | NULL | 预约ID |
| sender_type | varchar | 191 | NO | - | 发送者类型 |
| content | text | - | NO | - | 消息内容 |
| is_read | tinyint(1) | - | NO | 0 | 是否已读 |
| read_at | timestamp | - | YES | NULL | 阅读时间 |
| attachment | varchar | 191 | YES | NULL | 附件 |
| created_at | timestamp | - | YES | NULL | 创建时间 |
| updated_at | timestamp | - | YES | NULL | 更新时间 |

**发送者类型:** user(用户), counselor(咨询师)

**索引:**
- PRIMARY KEY (`id`)
- KEY `messages_user_id_counselor_id_index` (`user_id`, `counselor_id`)
- KEY `messages_appointment_id_index` (`appointment_id`)
- KEY `messages_sender_type_is_read_index` (`sender_type`, `is_read`)

### 5. 评估测试模块

#### 5.1 assessment_questionnaires - 评估问卷表
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint(20) unsigned | - | NO | AUTO_INCREMENT | 主键ID |
| title | varchar | 191 | NO | - | 问卷标题 |
| description | text | - | YES | NULL | 问卷描述 |
| domain | varchar | 191 | NO | - | 测评领域 |
| question_count | int(11) | - | NO | 0 | 题目数量 |
| est_duration | int(11) | - | NO | 0 | 预计时长(分钟) |
| cover_image | varchar | 191 | YES | NULL | 封面图片URL |
| is_active | tinyint(1) | - | NO | 1 | 是否激活 |
| created_at | timestamp | - | YES | NULL | 创建时间 |
| updated_at | timestamp | - | YES | NULL | 更新时间 |

**索引:**
- PRIMARY KEY (`id`)

#### 5.2 assessment_questions - 评估问题表
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint(20) unsigned | - | NO | AUTO_INCREMENT | 主键ID |
| questionnaire_id | bigint(20) unsigned | - | NO | - | 所属问卷ID |
| type | enum | - | NO | - | 题目类型 |
| content | text | - | NO | - | 题干内容 |
| sort_order | int(11) | - | NO | 0 | 排序 |
| created_at | timestamp | - | YES | NULL | 创建时间 |
| updated_at | timestamp | - | YES | NULL | 更新时间 |

**题目类型:** single(单选), multiple(多选), scale(量表)

**索引:**
- PRIMARY KEY (`id`)
- KEY `assessment_questions_questionnaire_id_foreign` (`questionnaire_id`)

**外键约束:**
- FOREIGN KEY (`questionnaire_id`) REFERENCES `assessment_questionnaires` (`id`) ON DELETE CASCADE

#### 5.3 assessment_responses - 评估答卷表
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint(20) unsigned | - | NO | AUTO_INCREMENT | 主键ID |
| user_id | bigint(20) unsigned | - | NO | - | 用户ID |
| questionnaire_id | bigint(20) unsigned | - | NO | - | 问卷ID |
| submitted_at | timestamp | - | YES | NULL | 提交时间 |
| total_score | int(11) | - | NO | 0 | 总分 |
| created_at | timestamp | - | YES | NULL | 创建时间 |
| updated_at | timestamp | - | YES | NULL | 更新时间 |

**索引:**
- PRIMARY KEY (`id`)
- KEY `assessment_responses_user_id_foreign` (`user_id`)
- KEY `assessment_responses_questionnaire_id_foreign` (`questionnaire_id`)

**外键约束:**
- FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
- FOREIGN KEY (`questionnaire_id`) REFERENCES `assessment_questionnaires` (`id`) ON DELETE CASCADE

#### 5.4 assessment_analyses - 评估分析结果表
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint(20) unsigned | - | NO | AUTO_INCREMENT | 主键ID |
| response_id | bigint(20) unsigned | - | NO | - | 所属答卷ID |
| overall_score | int(11) | - | NO | 0 | 总分 |
| level_name | varchar | 191 | YES | NULL | 等级名称 |
| description | text | - | YES | NULL | 结果描述 |
| suggestions | text | - | YES | NULL | 建议 |
| detailed_analysis | text | - | YES | NULL | 详细分析 |
| ai_analysis | longtext | - | YES | NULL | AI分析结果 |
| detail_json | json | - | NO | - | 分域得分JSON |
| created_at | timestamp | - | YES | NULL | 创建时间 |
| updated_at | timestamp | - | YES | NULL | 更新时间 |

**索引:**
- PRIMARY KEY (`id`)
- KEY `assessment_analyses_response_id_foreign` (`response_id`)

**外键约束:**
- FOREIGN KEY (`response_id`) REFERENCES `assessment_responses` (`id`) ON DELETE CASCADE

### 6. AI服务模块

#### 6.1 ai_consultation_settings - AI咨询设置表
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint(20) unsigned | - | NO | AUTO_INCREMENT | 主键ID |
| provider | varchar | 50 | NO | deepseek | AI提供商 |
| api_key | varchar | 191 | YES | NULL | API密钥 |
| api_url | varchar | 191 | YES | NULL | API地址 |
| model | varchar | 100 | YES | NULL | 模型名称 |
| max_tokens | int(11) | - | NO | 2000 | 最大token数 |
| temperature | double(8,2) | - | NO | 0.70 | 温度参数 |
| system_prompt | text | - | YES | NULL | 系统提示词 |
| is_active | tinyint(1) | - | NO | 1 | 是否启用 |
| created_at | timestamp | - | YES | NULL | 创建时间 |
| updated_at | timestamp | - | YES | NULL | 更新时间 |

**索引:**
- PRIMARY KEY (`id`)

#### 6.2 ai_consultation_records - AI咨询记录表
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint(20) unsigned | - | NO | AUTO_INCREMENT | 主键ID |
| user_id | bigint(20) unsigned | - | YES | NULL | 用户ID |
| appointment_id | bigint(20) unsigned | - | YES | NULL | 预约ID |
| user_query | text | - | NO | - | 用户问题 |
| ai_response | text | - | NO | - | AI回复 |
| recommended_resources | text | - | YES | NULL | 推荐资源JSON |
| created_at | timestamp | - | YES | NULL | 创建时间 |
| updated_at | timestamp | - | YES | NULL | 更新时间 |

**索引:**
- PRIMARY KEY (`id`)
- KEY `ai_consultation_records_user_id_foreign` (`user_id`)
- KEY `ai_consultation_records_appointment_id_foreign` (`appointment_id`)

**外键约束:**
- FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
- FOREIGN KEY (`appointment_id`) REFERENCES `consultation_appointments` (`id`) ON DELETE SET NULL

### 7. 课程管理模块

#### 7.1 course_lessons - 在线课程表
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint(20) unsigned | - | NO | AUTO_INCREMENT | 主键ID |
| title | varchar | 191 | NO | - | 课程标题 |
| description | text | - | YES | NULL | 课程描述 |
| content | longtext | - | YES | NULL | 课程内容 |
| cover_image | varchar | 191 | YES | NULL | 封面图片 |
| video_url | varchar | 191 | YES | NULL | 视频URL |
| duration | int(11) | - | NO | 0 | 课程时长(分钟) |
| category_id | bigint(20) unsigned | - | YES | NULL | 分类ID |
| instructor_id | bigint(20) unsigned | - | YES | NULL | 讲师ID |
| difficulty_level | tinyint(4) | - | NO | 1 | 难度等级 |
| is_free | tinyint(1) | - | NO | 1 | 是否免费 |
| price | decimal(8,2) | - | NO | 0.00 | 课程价格 |
| is_published | tinyint(1) | - | NO | 0 | 是否发布 |
| sort_order | int(11) | - | NO | 0 | 排序 |
| view_count | int(11) | - | NO | 0 | 观看次数 |
| created_at | timestamp | - | YES | NULL | 创建时间 |
| updated_at | timestamp | - | YES | NULL | 更新时间 |

**索引:**
- PRIMARY KEY (`id`)
- KEY `course_lessons_category_id_foreign` (`category_id`)

#### 7.2 offline_courses - 线下课程表
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint(20) unsigned | - | NO | AUTO_INCREMENT | 主键ID |
| title | varchar | 100 | NO | - | 课程标题 |
| image | varchar | 191 | YES | NULL | 课程图片 |
| lecturer_id | bigint(20) | - | YES | NULL | 讲师ID |
| description | text | - | YES | NULL | 课程描述 |
| outline | text | - | YES | NULL | 课程大纲 |
| location | varchar | 200 | YES | NULL | 上课地点 |
| start_time | datetime | - | NO | - | 开始时间 |
| end_time | datetime | - | NO | - | 结束时间 |
| registration_deadline | datetime | - | NO | - | 报名截止时间 |
| max_participants | int(11) | - | NO | 30 | 最大参与人数 |
| current_participants | int(11) | - | NO | 0 | 当前参与人数 |
| price | decimal(10,2) | - | NO | 0.00 | 课程价格 |
| status | tinyint(4) | - | NO | 1 | 状态 |
| created_at | timestamp | - | YES | NULL | 创建时间 |
| updated_at | timestamp | - | YES | NULL | 更新时间 |

**状态值:** 0=待发布，1=已发布，2=已结束

**索引:**
- PRIMARY KEY (`id`)

### 8. 活动管理模块

#### 8.1 quiz_activities - 答题活动表
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint(20) unsigned | - | NO | AUTO_INCREMENT | 主键ID |
| title | varchar | 100 | NO | - | 活动标题 |
| description | text | - | YES | NULL | 活动描述 |
| cover_image | varchar | 191 | YES | NULL | 封面图片 |
| start_time | datetime | - | NO | - | 开始时间 |
| end_time | datetime | - | NO | - | 结束时间 |
| pass_score | int(11) | - | NO | 60 | 及格分数 |
| max_attempts | int(11) | - | NO | 1 | 最大尝试次数 |
| is_active | tinyint(1) | - | NO | 1 | 是否启用 |
| show_answers_after_submit | tinyint(1) | - | NO | 0 | 提交后显示答案 |
| created_at | timestamp | - | YES | NULL | 创建时间 |
| updated_at | timestamp | - | YES | NULL | 更新时间 |
| deleted_at | timestamp | - | YES | NULL | 删除时间 |

**索引:**
- PRIMARY KEY (`id`)

#### 8.2 quiz_attempts - 答题记录表
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint(20) unsigned | - | NO | AUTO_INCREMENT | 主键ID |
| quiz_activity_id | bigint(20) unsigned | - | NO | - | 活动ID |
| user_id | bigint(20) unsigned | - | NO | - | 用户ID |
| score | int(11) | - | NO | 0 | 得分 |
| total_points | int(11) | - | NO | 0 | 总分 |
| correct_count | int(11) | - | NO | 0 | 正确题数 |
| total_questions | int(11) | - | NO | 0 | 总题数 |
| has_won_prize | tinyint(1) | - | NO | 0 | 是否中奖 |
| is_completed | tinyint(1) | - | NO | 0 | 是否完成 |
| completed_at | datetime | - | YES | NULL | 完成时间 |
| created_at | timestamp | - | YES | NULL | 创建时间 |
| updated_at | timestamp | - | YES | NULL | 更新时间 |

**索引:**
- PRIMARY KEY (`id`)
- KEY `quiz_attempts_quiz_activity_id_foreign` (`quiz_activity_id`)
- KEY `quiz_attempts_user_id_foreign` (`user_id`)

**外键约束:**
- FOREIGN KEY (`quiz_activity_id`) REFERENCES `quiz_activities` (`id`) ON DELETE CASCADE
- FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE

#### 8.3 live_shows - 直播表
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint(20) unsigned | - | NO | AUTO_INCREMENT | 主键ID |
| title | varchar | 191 | NO | - | 直播标题 |
| description | text | - | YES | NULL | 直播描述 |
| cover_image | varchar | 500 | YES | NULL | 封面图片 |
| live_url | varchar | 1000 | YES | NULL | 直播链接 |
| qr_code_image | varchar | 500 | YES | NULL | 预约二维码 |
| counselor_id | bigint(20) unsigned | - | YES | NULL | 主播咨询师ID |
| scheduled_at | timestamp | - | YES | NULL | 预定直播时间 |
| ended_at | datetime | - | YES | NULL | 结束时间 |
| status | enum | - | NO | upcoming | 状态 |
| is_featured | tinyint(1) | - | NO | 0 | 是否首页推荐 |
| is_active | tinyint(1) | - | NO | 1 | 是否启用 |
| sort_order | int(11) | - | NO | 0 | 排序权重 |
| notice | text | - | YES | NULL | 直播公告 |
| created_at | timestamp | - | YES | NULL | 创建时间 |
| updated_at | timestamp | - | YES | NULL | 更新时间 |

**状态枚举值:** upcoming(即将开始), live(直播中), ended(已结束)

**索引:**
- PRIMARY KEY (`id`)
- KEY `live_shows_counselor_id_foreign` (`counselor_id`)

### 9. 系统管理模块

#### 9.1 system_settings - 系统设置表
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint(20) unsigned | - | NO | AUTO_INCREMENT | 主键ID |
| key | varchar | 191 | NO | - | 配置键 |
| value | text | - | YES | NULL | 配置值 |
| label | varchar | 255 | YES | NULL | 配置项说明 |
| created_at | timestamp | - | YES | NULL | 创建时间 |
| updated_at | timestamp | - | YES | NULL | 更新时间 |

**索引:**
- PRIMARY KEY (`id`)
- UNIQUE KEY `uniq_system_settings_key` (`key`)

#### 9.2 hotlines - 热线表
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint(20) unsigned | - | NO | AUTO_INCREMENT | 主键ID |
| name | varchar | 100 | NO | - | 热线名称 |
| phone | varchar | 20 | NO | - | 热线电话 |
| description | text | - | YES | NULL | 热线描述 |
| service_hours | varchar | 100 | YES | NULL | 服务时间 |
| is_active | tinyint(1) | - | NO | 1 | 是否启用 |
| sort_order | int(11) | - | NO | 0 | 排序 |
| created_at | timestamp | - | YES | NULL | 创建时间 |
| updated_at | timestamp | - | YES | NULL | 更新时间 |

**索引:**
- PRIMARY KEY (`id`)

---

### 4. 数据流向图

```mermaid
flowchart TD
    subgraph "用户端数据流"
        A1[用户注册/登录] --> A2[身份验证]
        A2 --> A3[用户信息存储]
        A3 --> A4[权限验证]
    end

    subgraph "咨询业务流"
        B1[选择咨询师] --> B2[创建预约]
        B2 --> B3[咨询师确认]
        B3 --> B4[开始咨询]
        B4 --> B5[消息交互]
        B5 --> B6[完成咨询]
        B6 --> B7[生成记录]
    end

    subgraph "评估业务流"
        C1[选择问卷] --> C2[开始答题]
        C2 --> C3[提交答案]
        C3 --> C4[计算分数]
        C4 --> C5[AI分析]
        C5 --> C6[生成报告]
    end

    subgraph "内容管理流"
        D1[内容创建] --> D2[内容审核]
        D2 --> D3[内容发布]
        D3 --> D4[用户浏览]
        D4 --> D5[数据统计]
    end

    A4 --> B1
    A4 --> C1
    A4 --> D4

    style A1 fill:#e3f2fd
    style B1 fill:#e8f5e8
    style C1 fill:#fff3e0
    style D1 fill:#fce4ec
```

### 5. 权限管理架构图

```mermaid
graph TB
    subgraph "权限层级结构"
        SuperAdmin[超级管理员<br/>所有权限]
        Admin[管理员<br/>业务管理权限]
        Editor[编辑员<br/>内容管理权限]
        Counselor[咨询师<br/>咨询相关权限]
        User[普通用户<br/>基础功能权限]
    end

    subgraph "权限控制机制"
        Role[角色管理<br/>admin_roles]
        Permission[权限定义<br/>admin_permissions]
        Menu[菜单控制<br/>admin_menu]
        RoleUser[用户角色关联<br/>admin_role_users]
        RolePermission[角色权限关联<br/>admin_role_permissions]
        PermissionMenu[权限菜单关联<br/>admin_permission_menu]
    end

    subgraph "功能模块权限"
        UserMgmt[用户管理]
        ContentMgmt[内容管理]
        ConsultationMgmt[咨询管理]
        AssessmentMgmt[评估管理]
        SystemMgmt[系统管理]
    end

    SuperAdmin --> Admin
    Admin --> Editor
    Admin --> Counselor
    Editor --> User
    Counselor --> User

    Role --> RoleUser
    Role --> RolePermission
    Permission --> RolePermission
    Permission --> PermissionMenu
    Menu --> PermissionMenu

    RolePermission --> UserMgmt
    RolePermission --> ContentMgmt
    RolePermission --> ConsultationMgmt
    RolePermission --> AssessmentMgmt
    RolePermission --> SystemMgmt

    style SuperAdmin fill:#ff5722,color:#fff
    style Admin fill:#ff9800,color:#fff
    style Editor fill:#ffc107
    style Counselor fill:#4caf50,color:#fff
    style User fill:#2196f3,color:#fff
```

### 6. AI服务集成架构图

```mermaid
graph LR
    subgraph "AI服务配置"
        AIConfig[AI配置管理<br/>ai_consultation_settings<br/>assessment_ai_settings]
        APIKey[API密钥管理]
        ModelConfig[模型参数配置]
    end

    subgraph "AI服务调用"
        ConsultationAI[咨询AI服务]
        AssessmentAI[评估AI服务]
        ContentAI[内容AI服务]
    end

    subgraph "数据处理"
        UserQuery[用户查询]
        DataPreprocess[数据预处理]
        AIAnalysis[AI分析处理]
        ResultGenerate[结果生成]
    end

    subgraph "结果存储"
        ConsultationRecord[咨询记录<br/>ai_consultation_records]
        AssessmentAnalysis[评估分析<br/>assessment_analyses]
        ContentIndex[内容索引<br/>content_indices]
    end

    AIConfig --> ConsultationAI
    AIConfig --> AssessmentAI
    AIConfig --> ContentAI

    UserQuery --> DataPreprocess
    DataPreprocess --> AIAnalysis
    AIAnalysis --> ResultGenerate

    ConsultationAI --> ConsultationRecord
    AssessmentAI --> AssessmentAnalysis
    ContentAI --> ContentIndex

    style AIConfig fill:#e1f5fe
    style ConsultationAI fill:#e8f5e8
    style AssessmentAI fill:#fff3e0
    style ContentAI fill:#fce4ec
```

### 7. 数据库分层架构图

```mermaid
graph TB
    subgraph "应用层 Application Layer"
        WebController[Web控制器]
        APIController[API控制器]
        AdminController[管理后台控制器]
    end

    subgraph "业务逻辑层 Business Logic Layer"
        UserService[用户服务]
        ConsultationService[咨询服务]
        AssessmentService[评估服务]
        ContentService[内容服务]
        AIService[AI服务]
    end

    subgraph "数据访问层 Data Access Layer"
        UserRepository[用户仓库]
        ConsultationRepository[咨询仓库]
        AssessmentRepository[评估仓库]
        ContentRepository[内容仓库]
        SystemRepository[系统仓库]
    end

    subgraph "数据模型层 Data Model Layer"
        UserModels[用户模型<br/>users, admin_users, counselors]
        ConsultationModels[咨询模型<br/>consultation_appointments, messages]
        AssessmentModels[评估模型<br/>assessment_*, quiz_*]
        ContentModels[内容模型<br/>articles, videos, news, courses]
        SystemModels[系统模型<br/>system_settings, hotlines]
    end

    subgraph "数据存储层 Data Storage Layer"
        PrimaryDB[(主数据库<br/>MySQL 5.7)]
        CacheDB[(缓存数据库<br/>Redis)]
        FileStorage[(文件存储<br/>本地/云存储)]
        SearchEngine[(搜索引擎<br/>Elasticsearch)]
    end

    WebController --> UserService
    APIController --> ConsultationService
    AdminController --> ContentService

    UserService --> UserRepository
    ConsultationService --> ConsultationRepository
    AssessmentService --> AssessmentRepository
    ContentService --> ContentRepository
    AIService --> SystemRepository

    UserRepository --> UserModels
    ConsultationRepository --> ConsultationModels
    AssessmentRepository --> AssessmentModels
    ContentRepository --> ContentModels
    SystemRepository --> SystemModels

    UserModels --> PrimaryDB
    ConsultationModels --> PrimaryDB
    AssessmentModels --> PrimaryDB
    ContentModels --> PrimaryDB
    SystemModels --> PrimaryDB

    UserModels --> CacheDB
    ContentModels --> FileStorage
    ContentModels --> SearchEngine

    style PrimaryDB fill:#4caf50,color:#fff
    style CacheDB fill:#ff9800,color:#fff
    style FileStorage fill:#2196f3,color:#fff
    style SearchEngine fill:#9c27b0,color:#fff
```

## 数据库关系图

### 核心实体关系总览
```mermaid
erDiagram
    %% 核心用户关系
    users ||--o{ consultation_appointments : "creates"
    counselors ||--o{ consultation_appointments : "accepts"
    users ||--o{ assessment_responses : "takes"
    users ||--o{ quiz_attempts : "participates"
    users ||--o{ messages : "sends"
    counselors ||--o{ messages : "replies"

    %% 评估系统关系
    assessment_questionnaires ||--o{ assessment_questions : "contains"
    assessment_questions ||--o{ assessment_options : "has"
    assessment_responses ||--o{ assessment_answers : "includes"
    assessment_responses ||--|| assessment_analyses : "generates"

    %% 内容管理关系
    article_categories ||--o{ articles : "categorizes"
    video_categories ||--o{ videos : "categorizes"
    news_categories ||--o{ news : "categorizes"

    %% 活动管理关系
    quiz_activities ||--o{ quiz_questions : "contains"
    quiz_activities ||--o{ quiz_attempts : "tracks"
    quiz_activities ||--o{ quiz_prizes : "offers"

    %% 权限管理关系
    admin_roles ||--o{ admin_role_users : "assigns"
    admin_permissions ||--o{ admin_role_permissions : "grants"
    admin_menu ||--o{ admin_permission_menu : "controls"
```

### 8. 数据库性能优化架构图

```mermaid
graph TB
    subgraph "查询优化层"
        QueryCache[查询缓存]
        IndexOptimization[索引优化]
        QueryRewrite[查询重写]
    end

    subgraph "连接池管理"
        ConnectionPool[连接池]
        LoadBalancer[负载均衡]
        ReadWriteSplit[读写分离]
    end

    subgraph "缓存策略"
        L1Cache[一级缓存<br/>应用缓存]
        L2Cache[二级缓存<br/>Redis]
        L3Cache[三级缓存<br/>CDN]
    end

    subgraph "数据分片"
        UserShard[用户数据分片]
        ContentShard[内容数据分片]
        LogShard[日志数据分片]
    end

    subgraph "监控告警"
        PerformanceMonitor[性能监控]
        SlowQueryLog[慢查询日志]
        AlertSystem[告警系统]
    end

    QueryCache --> IndexOptimization
    IndexOptimization --> QueryRewrite

    ConnectionPool --> LoadBalancer
    LoadBalancer --> ReadWriteSplit

    L1Cache --> L2Cache
    L2Cache --> L3Cache

    UserShard --> ContentShard
    ContentShard --> LogShard

    PerformanceMonitor --> SlowQueryLog
    SlowQueryLog --> AlertSystem

    style QueryCache fill:#e3f2fd
    style L1Cache fill:#e8f5e8
    style UserShard fill:#fff3e0
    style PerformanceMonitor fill:#fce4ec
```

### 9. 数据安全架构图

```mermaid
graph LR
    subgraph "访问控制"
        Authentication[身份认证]
        Authorization[权限授权]
        SessionMgmt[会话管理]
    end

    subgraph "数据加密"
        DataEncryption[数据加密]
        PasswordHash[密码哈希]
        TokenEncryption[令牌加密]
    end

    subgraph "审计日志"
        AccessLog[访问日志]
        OperationLog[操作日志]
        SecurityLog[安全日志]
    end

    subgraph "备份恢复"
        DataBackup[数据备份]
        DisasterRecovery[灾难恢复]
        VersionControl[版本控制]
    end

    subgraph "安全防护"
        SQLInjectionPrevention[SQL注入防护]
        XSSPrevention[XSS防护]
        CSRFPrevention[CSRF防护]
        RateLimiting[频率限制]
    end

    Authentication --> Authorization
    Authorization --> SessionMgmt

    DataEncryption --> PasswordHash
    PasswordHash --> TokenEncryption

    AccessLog --> OperationLog
    OperationLog --> SecurityLog

    DataBackup --> DisasterRecovery
    DisasterRecovery --> VersionControl

    SQLInjectionPrevention --> XSSPrevention
    XSSPrevention --> CSRFPrevention
    CSRFPrevention --> RateLimiting

    style Authentication fill:#f44336,color:#fff
    style DataEncryption fill:#ff9800,color:#fff
    style AccessLog fill:#4caf50,color:#fff
    style DataBackup fill:#2196f3,color:#fff
    style SQLInjectionPrevention fill:#9c27b0,color:#fff
```

### 10. 数据库扩展性架构图

```mermaid
graph TB
    subgraph "水平扩展"
        ShardingStrategy[分片策略]
        DistributedDB[分布式数据库]
        DataMigration[数据迁移]
    end

    subgraph "垂直扩展"
        HardwareUpgrade[硬件升级]
        MemoryOptimization[内存优化]
        StorageOptimization[存储优化]
    end

    subgraph "微服务架构"
        ServiceDecomposition[服务拆分]
        DatabasePerService[服务独立数据库]
        EventSourcing[事件溯源]
    end

    subgraph "云原生架构"
        ContainerDeployment[容器化部署]
        KubernetesOrchestration[K8s编排]
        AutoScaling[自动扩缩容]
    end

    subgraph "数据同步"
        MasterSlave[主从复制]
        MasterMaster[主主复制]
        CrossRegionSync[跨区域同步]
    end

    ShardingStrategy --> DistributedDB
    DistributedDB --> DataMigration

    HardwareUpgrade --> MemoryOptimization
    MemoryOptimization --> StorageOptimization

    ServiceDecomposition --> DatabasePerService
    DatabasePerService --> EventSourcing

    ContainerDeployment --> KubernetesOrchestration
    KubernetesOrchestration --> AutoScaling

    MasterSlave --> MasterMaster
    MasterMaster --> CrossRegionSync

    style ShardingStrategy fill:#e3f2fd
    style HardwareUpgrade fill:#e8f5e8
    style ServiceDecomposition fill:#fff3e0
    style ContainerDeployment fill:#fce4ec
    style MasterSlave fill:#f3e5f5
```

---

## 数据库设计模式总结

### 1. 设计模式应用

| 设计模式 | 应用场景 | 具体实现 |
|----------|----------|----------|
| **单表继承** | 用户类型管理 | users, admin_users, counselors |
| **多对多关联** | 标签系统 | article_tag, video_tag, news_tag |
| **一对一关联** | 分析结果 | assessment_responses ↔ assessment_analyses |
| **聚合根模式** | 评估系统 | assessment_questionnaires 作为聚合根 |
| **事件溯源** | 审计日志 | article_reviews, video_audit_logs |
| **CQRS模式** | 读写分离 | 查询优化视图 + 写入规范化表 |

### 2. 数据一致性保证

```mermaid
graph LR
    subgraph "事务管理"
        ACID[ACID特性]
        Isolation[隔离级别]
        Deadlock[死锁检测]
    end

    subgraph "约束机制"
        ForeignKey[外键约束]
        UniqueConstraint[唯一约束]
        CheckConstraint[检查约束]
    end

    subgraph "业务规则"
        BusinessLogic[业务逻辑验证]
        DataValidation[数据验证]
        StateTransition[状态转换]
    end

    ACID --> ForeignKey
    Isolation --> UniqueConstraint
    Deadlock --> CheckConstraint

    ForeignKey --> BusinessLogic
    UniqueConstraint --> DataValidation
    CheckConstraint --> StateTransition

    style ACID fill:#4caf50,color:#fff
    style ForeignKey fill:#2196f3,color:#fff
    style BusinessLogic fill:#ff9800,color:#fff
```

### 3. 性能优化策略

| 优化类型 | 策略 | 实现方式 |
|----------|------|----------|
| **索引优化** | 复合索引 | (user_id, created_at), (status, publish_time) |
| **查询优化** | 分页查询 | LIMIT + OFFSET 优化 |
| **缓存策略** | 多级缓存 | Redis + 应用缓存 + CDN |
| **分区表** | 时间分区 | 按月分区大表数据 |
| **读写分离** | 主从架构 | 写主库，读从库 |
| **连接池** | 连接复用 | 数据库连接池管理 |

---

## 总结

本数据库设计文档提供了心理健康平台的完整数据库架构，包含：

### ✅ **专业特色**
- **多层次架构图**: 从系统整体到具体实体的完整视图
- **详细ERD图**: 包含所有字段和关系的专业实体关系图
- **业务流程图**: 展示数据在各业务场景中的流转
- **技术架构图**: 涵盖性能、安全、扩展性等技术层面

### 🎯 **设计亮点**
- **模块化设计**: 清晰的功能模块划分
- **扩展性考虑**: 支持水平和垂直扩展
- **性能优化**: 完整的索引和缓存策略
- **安全保障**: 多层次的安全防护机制
- **数据一致性**: 完善的约束和事务管理

### 📊 **图表类型**
1. 系统整体架构图
2. 核心业务模块关系图
3. 详细实体关系图 (ERD)
4. 数据流向图
5. 权限管理架构图
6. AI服务集成架构图
7. 数据库分层架构图
8. 数据库性能优化架构图
9. 数据安全架构图
10. 数据库扩展性架构图

这些专业的数据库结构图为系统开发、维护和优化提供了全面的技术指导。

---

## 索引优化策略

### 1. 主要查询索引
- **用户相关查询**: `users.email`, `users.phone`
- **时间范围查询**: `created_at`, `updated_at`, `scheduled_at`
- **状态筛选**: `status`, `is_active`, `is_published`
- **关联查询**: 所有外键字段都建立了索引

### 2. 复合索引
- `messages(user_id, counselor_id)` - 消息会话查询
- `messages(sender_type, is_read)` - 未读消息统计
- `articles(status, publish_time)` - 已发布文章按时间排序
- `assessment_answers(response_id, question_id)` - 答卷答案查询

### 3. 唯一索引
- 用户邮箱、管理员用户名等关键字段
- 多对多关联表的组合唯一索引

---

## 数据库设计特色

### 1. 模块化设计
- 按功能模块划分表结构
- 清晰的命名规范
- 完整的外键约束关系

### 2. 扩展性设计
- JSON字段存储灵活数据
- 预留扩展字段
- 软删除支持

### 3. 性能优化
- 合理的索引设计
- 适当的字段类型选择
- 查询优化考虑

### 4. 数据完整性
- 外键约束保证数据一致性
- 枚举类型限制数据范围
- 非空约束保证数据质量

---

## 开发团队

- **数据库设计**: 刘博涛
- **前端对接**: 王岩
- **UI优化**: 李永盛
- **系统配置**: 曹健鹏

---

*本文档基于 psy_oneself_icu_2025-07-25_16-27-44_mysql_data_2hAI7.sql 文件生成，涵盖了心理健康平台的完整数据库设计。*
