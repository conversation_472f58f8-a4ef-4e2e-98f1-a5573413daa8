<?php

namespace App\Admin\Controllers;

use App\Models\CourseLesson;
use App\Models\CourseLessonCategory;
use App\Models\CourseLessonTag;
use App\Models\AdminUser;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Admin;

class CourseLessonController extends AdminController
{
    /**
     * 设置标题
     *
     * @return string
     */
    protected $title = '心理课堂';

    /**
     * 列表页
     *
     * @param Grid $grid
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(CourseLesson::with(['category', 'author', 'tags']), function (Grid $grid) {
            $grid->column('id')->sortable();
            $grid->column('image', '封面图')->image('', 100, 60);
            $grid->column('title', '标题');
            $grid->column('category.name', '分类');
            $grid->column('author.name', '作者');
            $grid->column('views', '观看次数');
            $grid->column('type', '类型')->using(CourseLesson::getTypeMap())->label();
            $grid->column('level', '难度级别')->using(CourseLesson::getLevelMap())->label();
            $grid->column('lessons_count', '课时数量');
            $grid->column('is_free', '是否免费')->using([1 => '是', 0 => '否']);
            $grid->column('price', '价格');
            $grid->column('status', '状态')->using(CourseLesson::getStatusMap())->label();
            $grid->column('publish_time', '发布时间')->sortable();
            $grid->column('is_recommended', '是否推荐')->switch();
            $grid->column('updated_at', '更新时间')->sortable();

            // 过滤器
            $grid->filter(function (Grid\Filter $filter) {
                $filter->panel();

                $filter->equal('id');
                $filter->like('title', '标题');
                $filter->equal('category_id', '分类')->select(CourseLessonCategory::pluck('name', 'id'));
                $filter->equal('author_id', '作者')->select(AdminUser::pluck('name', 'id'));
                $filter->equal('type', '类型')->select(CourseLesson::getTypeMap());
                $filter->equal('level', '难度级别')->select(CourseLesson::getLevelMap());
                $filter->equal('status', '状态')->select(CourseLesson::getStatusMap());
                $filter->between('publish_time', '发布时间')->datetime();
                $filter->equal('is_recommended', '是否推荐')->radio([
                    1 => '是',
                    0 => '否',
                ]);
                $filter->whereBetween('created_at', function ($query) {
                    $start = $this->input['start'] ?? null;
                    $end = $this->input['end'] ?? null;

                    if ($start && $end) {
                        $query->whereBetween('created_at', [$start, $end]);
                    }
                })->datetime();
            });

            // 工具栏
            $grid->toolsWithOutline(false);

            // 快捷搜索
            $grid->quickSearch('title', 'content');

            // 行操作
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableView();
            });

            // 批量操作
            $grid->batchActions(function (Grid\Tools\BatchActions $batch) {
                $batch->add(new \App\Admin\Actions\BatchRecommend);
                $batch->add(new \App\Admin\Actions\BatchCancelRecommend);
            });

            $grid->export();
        });
    }

    /**
     * 详情页
     *
     * @param mixed $id
     * @param Content $content
     * @return Content
     */
    public function show($id, Content $content)
    {
        return $content
            ->title($this->title())
            ->description('详情')
            ->body(Show::make($id, new CourseLesson(), function (Show $show) {
                $show->field('id');
                $show->field('title', '标题');
                $show->field('slug', 'Slug');
                $show->field('image', '封面图')->image();
                $show->field('description', '描述');
                $show->field('content', '内容')->unescape();
                $show->field('video_url', '视频URL');
                $show->field('video_duration', '视频时长');
                $show->field('resources', '课程资源')->json();
                $show->field('category.name', '分类');
                $show->field('author.name', '作者');
                $show->field('views', '浏览量');
                $show->field('likes', '点赞数');
                $show->field('lessons_count', '课时数量');
                $show->field('publish_time', '发布时间');
                $show->field('type', '类型')->using(CourseLesson::getTypeMap());
                $show->field('level', '难度级别')->using(CourseLesson::getLevelMap());
                $show->field('status', '状态')->using(CourseLesson::getStatusMap());
                $show->field('is_recommended', '是否推荐')->as(function ($value) {
                    return $value ? '是' : '否';
                });
                $show->field('is_free', '是否免费')->as(function ($value) {
                    return $value ? '是' : '否';
                });
                $show->field('price', '价格');
                $show->field('created_at', '创建时间');
                $show->field('updated_at', '更新时间');

                $show->field('tags', '标签')->as(function ($tags) {
                    return $tags->pluck('name');
                })->label();
            }));
    }

    /**
     * 表单页
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(CourseLesson::with('tags'), function (Form $form) {
            $form->display('id');

            $form->tab('基本信息', function (Form $form) {
                $form->text('title', '标题')->required();
                $form->text('slug', 'Slug')->help('用于URL的别名，可选');
                $form->image('image', '封面图')
                    ->autoUpload()
                    ->uniqueName()
                    ->required()
                    ->help('课程封面图片，建议尺寸 1200x675');
                $form->select('category_id', '分类')
                    ->options(CourseLessonCategory::pluck('name', 'id'))
                    ->required();
                $form->select('author_id', '作者')
                    ->options(AdminUser::pluck('name', 'id'))
                    ->required();
                $form->textarea('description', '描述')
                    ->rows(3)
                    ->required();
                $form->select('type', '类型')
                    ->options(CourseLesson::getTypeMap())
                    ->default(CourseLesson::TYPE_ARTICLE)
                    ->required();
                $form->select('level', '难度级别')
                    ->options(CourseLesson::getLevelMap())
                    ->default(CourseLesson::LEVEL_BEGINNER)
                    ->required();
                $form->number('lessons_count', '课时数量')
                    ->min(1)
                    ->default(1)
                    ->required();


                $form->multipleSelect('tags', '标签')
                    ->options(CourseLessonTag::where('is_active', 1)->pluck('name', 'id'))
                    ->customFormat(function ($v) {
                        if (!$v) return [];
                        
                        // 这一步非常重要，需要把数据库中查出来的二维数组转化成一维数组
                        return array_column($v, 'id');
                    });

                $form->radio('is_free', '是否免费')
                    ->options([1 => '是', 0 => '否'])
                    ->default(1)
                    ->required();
                $form->currency('price', '价格')
                    ->symbol('￥')
                    ->default(0.00)
                    ->help('如果不是免费课程，请设置价格');
            })->tab('课程内容', function (Form $form) {
                $form->editor('content', '内容')
                    ->disk(config('admin.upload.disk'));

                $form->divider('视频配置（视频类型需要填写）');

                $form->url('video_url', '视频URL')
                    ->help('输入视频链接，支持MP4格式');
                    // 添加视频上传按钮，上传后自动填充视频链接字段
            $form->divider('或者上传本地视频');
            
            // 添加一个自定义按钮来处理视频上传
            $form->html('<div class="btn-group pull-left" style="margin-top: 10px;">
                <button type="button" id="upload-video-btn" class="btn btn-primary"><i class="feather icon-upload"></i> 上传视频</button>
            </div>
            <input type="file" id="video-upload-input" style="display:none;" accept=".mp4">
            <div id="video-upload-progress" class="progress" style="display:none; margin-top: 10px;">
                <div class="progress-bar" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
            </div>
            <div id="video-preview-container"></div>');
            
            // 添加脚本实现视频上传与链接字段的联动
            Admin::script(<<<JS
            $(function () {
                // 点击上传按钮时触发文件选择
                $('#upload-video-btn').on('click', function() {
                    $('#video-upload-input').click();
                });
                
                // 当选择文件后开始上传
                $('#video-upload-input').on('change', function() {
                    var file = this.files[0];
                    if (!file) return;
                    
                    // 检查文件类型
                    if (file.type !== 'video/mp4' && !file.name.toLowerCase().endsWith('.mp4')) {
                        Dcat.error('只支持上传MP4格式的视频文件');
                        return;
                    }
                    
                    // 显示进度条
                    $('#video-upload-progress').show();
                    $('.progress-bar').css('width', '0%').attr('aria-valuenow', 0).text('0%');
                    
                    // 创建FormData对象
                    var formData = new FormData();
                    formData.append('file', file);
                    formData.append('_token', $('meta[name="csrf-token"]').attr('content'));
                    
                    // 使用AJAX上传文件
                    $.ajax({
                        url: '/admin/upload/video',
                        type: 'POST',
                        data: formData,
                        processData: false,
                        contentType: false,
                        xhr: function() {
                            var xhr = new window.XMLHttpRequest();
                            xhr.upload.addEventListener("progress", function(evt) {
                                if (evt.lengthComputable) {
                                    var percentComplete = Math.round((evt.loaded / evt.total) * 100);
                                    $('.progress-bar').css('width', percentComplete + '%')
                                        .attr('aria-valuenow', percentComplete)
                                        .text(percentComplete + '%');
                                }
                            }, false);
                            return xhr;
                        },
                        success: function(response) {
                            console.log('Upload success:', response);
                            
                            // 获取视频URL
                            var videoUrl = '';
                            if (response.url) {
                                videoUrl = response.url;
                            } else if (response.location) {
                                videoUrl = response.location;
                            }
                            
                            if (videoUrl) {
                                // 将URL填充到视频链接字段
                                $('input[name="video_url"]').val(videoUrl);
                                
                                // 显示成功消息
                                Dcat.success('视频上传成功，链接已自动填充');
                                
                                // 显示视频预览
                                var previewHtml = '<div class="video-preview mt-2"><video src="' + videoUrl + '" controls style="max-width:100%;max-height:200px;"></video></div>';
                                $('#video-preview-container').html(previewHtml);
                            }
                        },
                        error: function(xhr, status, error) {
                            console.error('Upload error:', error);
                            Dcat.error('视频上传失败: ' + (xhr.responseJSON ? xhr.responseJSON.message : error));
                        },
                        complete: function() {
                            // 重置文件输入框
                            $('#video-upload-input').val('');
                            // 隐藏进度条
                            setTimeout(function() {
                                $('#video-upload-progress').hide();
                            }, 1000);
                        }
                    });
                });
            });
            JS
            );
                

                $form->text('video_duration', '视频时长')
                    ->help('格式如: 12:34');

                $form->divider('课程资源');

                $form->table('resources', '资源列表', function (Form\NestedForm $table) {
                    $table->text('title', '资源标题')->required();
                    $table->textarea('description', '资源描述');
                    $table->url('url', '资源链接')->required();
                })
                ->help('添加可下载的课程资源、讲义等');
            })->tab('发布设置', function (Form $form) {
                $form->radio('status', '状态')
                    ->options(CourseLesson::getStatusMap())
                    ->default(CourseLesson::STATUS_DRAFT)
                    ->required();

                $form->switch('is_recommended', '是否推荐')
                    ->default(false);

                $form->datetime('publish_time', '发布时间')
                    ->default(date('Y-m-d H:i:s'));
            });

            $form->display('created_at', '创建时间');
            $form->display('updated_at', '更新时间');

            // 表单保存前的处理
            $form->saving(function (Form $form) {
                if ($form->isCreating()) {
                    $form->publish_time = $form->publish_time ?: now();
                }
                
                // 如果是免费课程，确保价格为0
                if ($form->is_free == 1 || $form->is_free === '1') {
                    $form->price = 0;
                } 
                // 如果课程不是免费的，验证价格必填
                elseif (($form->is_free === 0 || $form->is_free === '0') && ($form->price === null || $form->price <= 0)) {
                    return $form->response()->error('课程不是免费的，请设置价格');
                }
            });
            
            // 删除标签关联的手动处理，使用自动多对多关联
        });
    }
}
