<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Dcat\Admin\Traits\HasDateTimeFormatter;

class ConsultationAppointment extends Model
{
    use HasFactory, SoftDeletes, HasDateTimeFormatter;

    protected $fillable = [
        'user_id',
        'counselor_id',
        'schedule_id',
        'consultation_type',
        'appointment_time',
        'status',
        'issue_description',
        'counselor_notes',
        'meeting_url',
        'meeting_password',
        'duration',
        'price',
        'payment_status'
    ];

    protected $casts = [
        'appointment_time' => 'datetime',
    ];

    // 咨询方式常量
    const TYPE_TEXT = 1;    // 文字咨询
    const TYPE_VOICE = 2;   // 语音咨询
    const TYPE_VIDEO = 3;   // 视频咨询

    // 状态常量
    const STATUS_CANCELLED = 0;  // 已取消
    const STATUS_PENDING = 1;    // 待确认
    const STATUS_CONFIRMED = 2;  // 已确认
    const STATUS_ONGOING = 3;    // 进行中
    const STATUS_COMPLETED = 4;  // 已完成

    // 支付状态常量
    const PAYMENT_UNPAID = 0;    // 未支付
    const PAYMENT_PAID = 1;      // 已支付
    const PAYMENT_REFUNDED = 2;  // 已退款

    // 咨询方式映射
    public static function getConsultationTypeMap()
    {
        return [
            self::TYPE_TEXT => '文字咨询',
            self::TYPE_VOICE => '语音咨询',
            self::TYPE_VIDEO => '视频咨询',
        ];
    }

    // 状态映射
    public static function getStatusMap()
    {
        return [
            self::STATUS_CANCELLED => '已取消',
            self::STATUS_PENDING => '待确认',
            self::STATUS_CONFIRMED => '已确认',
            self::STATUS_ONGOING => '进行中',
            self::STATUS_COMPLETED => '已完成',
        ];
    }

    // 支付状态映射
    public static function getPaymentStatusMap()
    {
        return [
            self::PAYMENT_UNPAID => '未支付',
            self::PAYMENT_PAID => '已支付',
            self::PAYMENT_REFUNDED => '已退款',
        ];
    }

    // 获取咨询方式文本
    public function getConsultationTypeTextAttribute()
    {
        return self::getConsultationTypeMap()[$this->consultation_type] ?? '未知';
    }

    // 获取状态文本
    public function getStatusTextAttribute()
    {
        return self::getStatusMap()[$this->status] ?? '未知';
    }

    // 获取支付状态文本
    public function getPaymentStatusTextAttribute()
    {
        return self::getPaymentStatusMap()[$this->payment_status] ?? '未知';
    }

    // 与用户的关联
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    // 与咨询师的关联
    public function counselor()
    {
        return $this->belongsTo(Counselor::class);
    }

    // 与排班的关联
    public function schedule()
    {
        return $this->belongsTo(CounselorSchedule::class, 'schedule_id');
    }

    // 与AI咨询记录的关联
    public function aiRecords()
    {
        return $this->hasMany(AiConsultationRecord::class, 'appointment_id');
    }

    // 检查是否可以取消
    public function canCancel()
    {
        return in_array($this->status, [self::STATUS_PENDING, self::STATUS_CONFIRMED]) && 
               $this->appointment_time->gt(now()->addHours(2));
    }

    // 检查是否可以开始咨询
    public function canStart()
    {
        return $this->status == self::STATUS_CONFIRMED && 
               $this->appointment_time->lt(now()->addMinutes(15)) &&
               $this->appointment_time->gt(now()->subMinutes(15));
    }
}
