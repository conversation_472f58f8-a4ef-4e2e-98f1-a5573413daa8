<?php

namespace App\Admin\Controllers;

use App\Models\Hotline;
use App\Models\HotlineRecord;
use App\Models\User;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class HotlineRecordController extends AdminController
{
    /**
     * 设置页面标题
     */
    protected $title = '热线使用记录';

    /**
     * 列表页面
     */
    protected function grid()
    {
        return Grid::make(new HotlineRecord(), function (Grid $grid) {
            // 默认按时间倒序
            $grid->model()->with(['hotline', 'user'])->orderBy('created_at', 'desc');
            
            // 获取URL参数
            $hotlineId = request()->get('hotline_id');
            
            // 根据URL参数筛选数据
            if ($hotlineId) {
                $grid->model()->where('hotline_id', $hotlineId);
                $hotline = Hotline::find($hotlineId);
                if ($hotline) {
                    $grid->tools(function (Grid\Tools $tools) use ($hotline) {
                        $tools->append('<div class="alert alert-info">当前显示: ' . $hotline->name . ' 的使用记录</div>');
                    });
                }
            }
            
            $grid->column('id')->sortable();
            $grid->column('hotline.name', '热线名称');
            $grid->column('user.name', '用户')->display(function($name) {
                return $name ?? ($this->user ? $this->user->id : '未登录用户');
            });
            
            $grid->column('action', '类型')->using(HotlineRecord::getActionMap())->label([
                'call' => 'primary',
                'chat' => 'success',
            ]);
            
            $grid->column('ip_address', 'IP地址');
            $grid->column('user_agent', '设备信息')->limit(30);
            
            $grid->column('created_at', '操作时间')->sortable();
            
            // 禁用创建按钮
            $grid->disableCreateButton();
            
            // 禁用编辑和删除按钮
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableEdit();
                $actions->disableDelete();
            });
            
            // 筛选器
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');
                $filter->equal('hotline_id', '热线')->select(function () {
                    return Hotline::all()->pluck('name', 'id');
                });
                $filter->equal('user_id', '用户')->select(function () {
                    return User::all()->pluck('name', 'id');
                });
                $filter->equal('action', '操作类型')->select(HotlineRecord::getActionMap());
                $filter->like('ip_address', 'IP地址');
                $filter->between('created_at', '操作时间')->datetime()->width(6, 4)->default();
            });
            
            // 添加导出按钮
            $grid->export();
        });
    }

    /**
     * 详情页面
     */
    protected function detail($id)
    {
        return Show::make($id, new HotlineRecord(['hotline', 'user']), function (Show $show) {
            $show->field('id');
            $show->field('hotline.name', '热线名称');
            $show->field('user.name', '用户名称')->as(function() {
                return $this->user ? ($this->user->name ?? 'ID:'.$this->user->id) : '未登录用户';
            });
            $show->field('action', '操作类型')->using(HotlineRecord::getActionMap());
            $show->field('ip_address', 'IP地址');
            $show->field('user_agent', '设备信息');
            $show->field('created_at', '操作时间');
            $show->field('updated_at', '更新时间');
        });
    }

    /**
     * 表单页面 (此处只用于查看，不提供编辑功能)
     */
    protected function form()
    {
        return Form::make(new HotlineRecord(), function (Form $form) {
            $form->display('id');
            $form->display('hotline.name', '热线名称');
            $form->display('user.name', '用户名称');
            $form->display('action', '操作类型')->with(function ($value) {
                return HotlineRecord::getActionMap()[$value] ?? '未知';
            });
            $form->display('ip_address', 'IP地址');
            $form->display('user_agent', '设备信息');
            $form->display('created_at', '操作时间');
            $form->display('updated_at', '更新时间');
            
            // 禁用提交按钮
            $form->disableSubmitButton();
            // 禁用重置按钮
            $form->disableResetButton();
        });
    }
}
