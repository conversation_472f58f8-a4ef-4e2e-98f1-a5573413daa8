@extends('layouts.app')

@section('title', '我的学习 - 心理健康平台')

@section('head')
<!-- 引入高级动效库 -->
<link href="https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.css" rel="stylesheet">
<script src="https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.js"></script>
<!-- 引入GSAP高级动画库 -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.11.4/gsap.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.11.4/ScrollTrigger.min.js"></script>
@endsection

@section('custom-styles')
<style>
    /* 为学习卡片添加3D效果 */
    :root {
        --card-perspective: 1000px;
    }
    
    html, body {
        background-color: #f5f7fa;
        overflow-x: hidden;
    }
    
    .container {
        perspective: var(--card-perspective);
    }
    .study-header {
        background-color: #fff;
        padding: 15px;
        margin-bottom: 15px;
        border-radius: 8px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    }
    
    .study-tabs {
        display: flex;
        border-bottom: 1px solid #f0f0f0;
    }
    
    .study-tab {
        padding: 10px 15px;
        font-size: 14px;
        color: #666;
        position: relative;
        cursor: pointer;
    }
    
    .study-tab.active {
        color: #1890ff;
        font-weight: bold;
    }
    
    .study-tab.active::after {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 50%;
        transform: translateX(-50%);
        width: 20px;
        height: 2px;
        background-color: #1890ff;
    }
    
    .study-card {
        background-color: #fff;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        margin-bottom: 15px;
        transform: translateY(0);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        will-change: transform, box-shadow;
    }
    
    .study-card:hover {
        transform: translateY(-5px) scale(1.01);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.08);
    }
    
    .study-card-header {
        display: flex;
        padding: 15px;
        border-bottom: 1px solid #f0f0f0;
    }
    
    .study-card-image {
        width: 80px;
        height: 60px;
        border-radius: 4px;
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        margin-right: 10px;
        flex-shrink: 0;
        position: relative;
        z-index: 2;
        overflow: hidden;
    }
    
    .study-card-info {
        flex: 1;
    }
    
    .study-card-title {
        font-size: 14px;
        font-weight: bold;
        margin-bottom: 5px;
        color: #333;
    }
    
    .study-card-meta {
        font-size: 12px;
        color: #999;
    }
    
    .study-card-progress {
        padding: 15px;
    }
    
    .progress-info {
        display: flex;
        justify-content: space-between;
        margin-bottom: 5px;
    }
    
    .progress-text {
        font-size: 12px;
        color: #666;
    }
    
    .progress-bar {
        height: 6px;
        background-color: #f0f0f0;
        border-radius: 3px;
        overflow: hidden;
        position: relative;
    }
    
    .progress-value {
        height: 100%;
        background: linear-gradient(90deg, #3c67e3, #4FACFE);
        border-radius: 3px;
        position: relative;
        transition: width 1s cubic-bezier(0.34, 1.56, 0.64, 1);
        box-shadow: 0 0 10px rgba(60, 103, 227, 0.5);
    }
    
    .progress-value::after {
        content: '';
        position: absolute;
        right: 0;
        top: 0;
        height: 100%;
        width: 6px;
        background: #fff;
        border-radius: 50%;
        box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
        opacity: 0.8;
    }
    
    .study-card-actions {
        display: flex;
        padding: 10px 15px;
        border-top: 1px solid #f0f0f0;
    }
    
    .study-card-action {
        flex: 1;
        text-align: center;
        font-size: 12px;
        color: #666;
    }
    
    .study-card-action i {
        font-size: 16px;
        margin-bottom: 3px;
        display: block;
    }
    
    .empty-state {
        padding: 40px 0;
        text-align: center;
        color: #999;
    }
    
    .empty-state i {
        font-size: 48px;
        margin-bottom: 10px;
        color: #d9d9d9;
    }
    
    .empty-state-text {
        font-size: 14px;
        margin-bottom: 15px;
    }
    
    .empty-state-button {
        display: inline-block;
        padding: 8px 20px;
        background-color: #1890ff;
        color: #fff;
        border-radius: 4px;
        font-size: 14px;
        text-decoration: none;
    }
</style>
@endsection

@section('content')
@php
    // 需要登录屏蔽
    $needLogin = !Auth::check();
@endphp

@if($needLogin)
<div class="container">
    <div class="empty-state">
        <i class="fas fa-lock"></i>
        <div class="empty-state-text">请登录后查看您的学习进度</div>
        <a href="{{ route('login') }}" class="empty-state-button">立即登录</a>
    </div>
</div>
@else
<div class="container">
    <div class="study-header" data-aos="fade-down" data-aos-duration="800">
        <div class="study-tabs">
            <div class="study-tab active" data-tab="in-progress" data-aos="fade-right" data-aos-delay="100">在学</div>
            <div class="study-tab" data-tab="completed" data-aos="fade-right" data-aos-delay="200">已学完</div>
        </div>
    </div>
    
    <!-- 正在学习的课程列表 -->
    <div class="tab-content active" id="in-progress-content" data-aos="fade-up" data-aos-delay="300">
        @if($inProgressCourses->count() > 0)
            @foreach($inProgressCourses as $course)
                @php
                    $progress = $course->progress->first();
                    $currentPosition = $progress ? $progress->current_position : 0;
                    $totalLength = $course->lessons_count;
                    
                    // 使用数据库中保存的实际进度值，而不是重新计算
                    // 这样可以确保与课程详情页的进度显示相一致
                    $percent = $progress ? intval($progress->progress) : 0;
                    
                    // 如果总课时为0，则设置为1，避免除零错误
                    if ($totalLength <= 0) {
                        $totalLength = 1;
                    }
                    
                    // 根据进度百分比计算出实际课时位置，而不是直接使用位置
                    // 例如：29%的进度应该对应于第1课时，而不是第2课时
                    $calculatedPosition = ceil(($percent / 100) * $totalLength);
                    // 使用计算出的位置，确保不超过总课时数
                    $currentPosition = min(max(1, $calculatedPosition), $totalLength);
                    // 如果进度为0，则位置也应为0
                    if ($percent == 0) {
                        $currentPosition = 0;
                    }
                @endphp
                <div class="study-card gsap-card" data-aos="flip-up" data-aos-delay="{{ $loop->index * 100 }}" data-tilt data-tilt-max="5">
                    <div class="study-card-header">
                        <div class="study-card-image" style="background-image: url('/storage/{{ $course->image }}');"></div>
                        <div class="study-card-info">
                            <div class="study-card-title">{{ $course->title }}</div>
                            <div class="study-card-meta">已学习 {{ min($currentPosition, $totalLength) }}/{{ $totalLength }} 课时</div>
                        </div>
                    </div>
                    <div class="study-card-progress">
                        <div class="progress-info">
                            <div class="progress-text">学习进度</div>
                            <div class="progress-text">{{ $percent }}%</div>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-value" style="width: {{ $percent }}%"></div>
                        </div>
                    </div>
                    <div class="study-card-actions">
                        <a href="{{ route('knowledge.course_detail', $course->id) }}" class="study-card-action">
                            <i class="fas fa-play-circle"></i>
                            <span>继续学习</span>
                        </a>
                        <a href="{{ route('knowledge.course_detail', $course->id) }}" class="study-card-action">
                            <i class="fas fa-book"></i>
                            <span>课程详情</span>
                        </a>
                    </div>
                </div>
            @endforeach
        @else
            <div class="empty-state">
                <i class="fas fa-book-open"></i>
                <div class="empty-state-text">您还没有学习任何课程</div>
                <a href="{{ route('knowledge.courses') }}" class="empty-state-button">去心理课堂选课</a>
            </div>
        @endif
    </div>
    
    <!-- 已完成的课程列表 -->
    <div class="tab-content" id="completed-content" style="display: none;">
        @if($completedCourses->count() > 0)
            @foreach($completedCourses as $course)
                <div class="study-card gsap-card" data-aos="flip-up" data-aos-delay="{{ $loop->index * 100 }}" data-tilt data-tilt-max="5">
                    <div class="study-card-header">
                        <div class="study-card-image" style="background-image: url('/storage/{{ $course->image }}');"></div>
                        <div class="study-card-info">
                            <div class="study-card-title">{{ $course->title }}</div>
                            <div class="study-card-meta">已完成 {{ $course->lessons_count }}/{{ $course->lessons_count }} 课时</div>
                        </div>
                    </div>
                    <div class="study-card-progress">
                        <div class="progress-info">
                            <div class="progress-text">学习进度</div>
                            <div class="progress-text">100%</div>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-value" style="width: 100%"></div>
                        </div>
                    </div>
                    <div class="study-card-actions">
                        <a href="#" class="study-card-action reset-course" data-course-id="{{ $course->id }}">
                            <i class="fas fa-redo"></i>
                            <span>重新学习</span>
                        </a>
                        <a href="{{ route('knowledge.course_detail', $course->id) }}" class="study-card-action">
                            <i class="fas fa-book"></i>
                            <span>课程详情</span>
                        </a>
                    </div>
                </div>
            @endforeach
        @else
            <div class="empty-state">
                <i class="fas fa-graduation-cap"></i>
                <div class="empty-state-text">您还没有完成任何课程</div>
                <a href="{{ route('knowledge.courses') }}" class="empty-state-button">去心理课堂选课</a>
            </div>
        @endif
    </div>
    
    <!-- 收藏的课程列表 -->
    <div class="tab-content" id="favorite-content" style="display: none;">
        <div class="empty-state">
            <i class="fas fa-heart"></i>
            <div class="empty-state-text">您还没有收藏任何课程</div>
            <a href="{{ route('knowledge.courses') }}" class="empty-state-button">去心理课堂选课</a>
        </div>
    </div>
</div>
@endif
@endsection

@section('scripts')
<!-- 引入vanilla-tilt.js库实现3D惯性效果 -->
<script src="https://cdn.jsdelivr.net/npm/vanilla-tilt@1.7.2/dist/vanilla-tilt.min.js"></script>
<script>
    // 初始化AOS动画库
    AOS.init({
        duration: 800,
        easing: 'ease-out-cubic',
        once: false,
        mirror: true,
        offset: 50
    });
    
    // 初始化GSAP动画
    document.addEventListener('DOMContentLoaded', function() {
        // 注册GSAP插件
        gsap.registerPlugin(ScrollTrigger);
        
        // 为卡片添加高级漂浮效果
        gsap.utils.toArray('.gsap-card').forEach(card => {
            gsap.to(card, {
                scrollTrigger: {
                    trigger: card,
                    start: 'top bottom-=100',
                    toggleActions: 'play none none reverse'
                },
                y: 0,
                opacity: 1,
                duration: 0.8,
                ease: 'power3.out'
            });
        });
        
        // 初始升3D效果
        VanillaTilt.init(document.querySelectorAll('.gsap-card'), {
            max: 5,
            speed: 400,
            glare: true,
            'max-glare': 0.1,
            gyroscope: true
        });
        
        // 初始化进度条动画
        gsap.utils.toArray('.progress-value').forEach(progressBar => {
            const width = progressBar.style.width;
            progressBar.style.width = '0%';
            
            gsap.to(progressBar, {
                scrollTrigger: {
                    trigger: progressBar.parentElement,
                    start: 'top bottom-=50'
                },
                width: width,
                duration: 1.5,
                ease: 'elastic.out(1, 0.3)'
            });
        });
        
        // 选项卡功能
        // 处理重新学习按钮点击事件
        document.querySelectorAll('.reset-course').forEach(function(btn) {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                
                if (!confirm('确认要重新学习该课程吗？当前进度将被重置为0%')) {
                    return false;
                }
                
                var courseId = this.getAttribute('data-course-id');
                
                // 发送重置进度请求
                $.ajax({
                    url: '/knowledge/course_progress/' + courseId + '/reset',
                    type: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                    },
                    success: function(response) {
                        alert('进度已重置为0%');
                        // 刷新页面显示更新后的状态
                        location.reload(true);
                    },
                    error: function(xhr) {
                        alert('重置进度失败，请稍后再试');
                    }
                });
            });
        });
        
        // 选项卡切换功能
        var tabs = document.querySelectorAll('.study-tab');
        tabs.forEach(function(tab) {
            tab.addEventListener('click', function() {
                // 移除所有选项卡的活动状态
                tabs.forEach(function(t) { t.classList.remove('active'); });
                
                // 设置当前选项卡为活动状态
                this.classList.add('active');
                
                // 隐藏所有内容区域
                var contents = document.querySelectorAll('.tab-content');
                contents.forEach(function(content) { content.style.display = 'none'; });
                
                // 显示对应的内容区域
                var tabId = this.getAttribute('data-tab');
                document.getElementById(tabId + '-content').style.display = 'block';
            });
        });
    });
</script>
@endsection
