@extends('layouts.app')

@section('title', $news->title)

@section('custom-styles')
<style>
    body {
        background-color: #f5f5f7;
        min-height: 100vh;
        padding-bottom: 80px;
    }

    .news-header {
        background: linear-gradient(135deg, #5b7cef 0%, #3c67e3 100%);
        padding: 20px;
        color: white;
        border-radius: 0 0 15px 15px;
        box-shadow: 0 4px 12px rgba(91, 124, 239, 0.2);
        margin-bottom: 20px;
        position: relative;
    }

    .back-button {
        color: white;
        font-size: 14px;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        position: absolute;
        left: 20px;
        top: 50%;
        transform: translateY(-50%);
    }

    .back-button i {
        margin-right: 5px;
    }

    .news-header h1 {
        text-align: center;
        font-size: 18px;
        font-weight: 600;
        margin: 0;
        padding: 0 50px;
    }

    .news-container {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        padding: 20px;
        margin-bottom: 20px;
    }

    .news-meta {
        margin-bottom: 20px;
        color: #888;
        font-size: 14px;
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
    }

    .news-cover {
        width: 100%;
        border-radius: 8px;
        margin-bottom: 20px;
    }

    .news-content {
        font-size: 15px;
        line-height: 1.8;
        color: #333;
        overflow-wrap: break-word;
        word-break: break-word;
    }

    .news-content img {
        max-width: 100%;
        height: auto;
        border-radius: 8px;
        margin: 15px 0;
    }

    .news-content h2, .news-content h3 {
        margin-top: 25px;
        margin-bottom: 15px;
        font-weight: 600;
    }

    .news-content p {
        margin-bottom: 15px;
    }

    .news-tags {
        margin-top: 30px;
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
    }

    .tag-badge {
        padding: 5px 10px;
        background: #f5f7fa;
        border-radius: 20px;
        color: #666;
        font-size: 13px;
        text-decoration: none;
    }

    .news-meta-box {
        margin: 25px 0;
        padding: 15px 20px;
        background-color: #f9f9f9;
        border-radius: 10px;
        border-left: 3px solid #3c67e3;
    }

    .news-meta-table {
        width: 100%;
    }

    .news-meta-table tr {
        line-height: 1.8;
    }

    .meta-label {
        font-weight: 600;
        color: #333;
        width: 60px;
        vertical-align: top;
    }

    .meta-content {
        color: #444;
    }

    .related-news-title {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 15px;
        margin-top: 30px;
    }

    .related-news-card {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        overflow: hidden;
        margin-bottom: 20px;
        transition: transform 0.2s, box-shadow 0.2s;
    }

    .related-news-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .related-news-image {
        height: 120px;
        background-size: cover;
        background-position: center;
    }

    .related-news-content {
        padding: 12px;
    }

    .related-news-title-text {
        font-size: 14px;
        font-weight: 600;
        color: #333;
        margin-bottom: 5px;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        height: 42px;
    }

    .sidebar {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        padding: 20px;
        margin-bottom: 20px;
        transition: transform 0.2s, box-shadow 0.2s;
    }

    .sidebar-title {
        font-size: 17px;
        font-weight: 600;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid #eee;
        color: #3c67e3;
        letter-spacing: 0.02em;
    }

    .sidebar-item {
        padding: 10px 0;
        border-bottom: 1px solid #f5f5f5;
        transition: transform 0.2s;
        display: flex;
        align-items: center;
    }

    .sidebar-item:hover {
        transform: translateX(5px);
    }

    .sidebar-item:last-child {
        border-bottom: none;
    }

    .sidebar-item a {
        color: #333;
        display: flex;
        width: 100%;
        text-decoration: none;
        justify-content: space-between;
        align-items: center;
    }

    .sidebar-item img {
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        transition: transform 0.2s;
    }

    .sidebar-item:hover img {
        transform: scale(1.05);
    }

    .tag-cloud a {
        display: inline-block;
        padding: 6px 12px;
        background: #f0f4ff;
        border-radius: 20px;
        color: #3c67e3;
        font-size: 13px;
        margin: 0 5px 8px 0;
        text-decoration: none;
        transition: all 0.2s ease;
    }

    .tag-cloud a:hover {
        background: #3c67e3;
        color: #fff;
        transform: translateY(-2px);
        box-shadow: 0 3px 8px rgba(60, 103, 227, 0.2);
    }

    .sidebar .list-unstyled {
        margin: 0;
        padding: 0;
    }

    .sidebar .list-unstyled li {
        border-bottom: 1px solid rgba(0,0,0,0.05);
        transition: background-color 0.2s;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 5px;
    }

    .sidebar .list-unstyled li:last-child {
        border-bottom: none;
    }

    .sidebar .list-unstyled li:hover {
        background-color: #f8f9ff;
    }

    .sidebar .badge {
        background-color: #f0f4ff;
        color: #3c67e3;
        font-size: 12px;
        padding: 3px 10px;
        border-radius: 12px;
        font-weight: 500;
        min-width: 28px;
        text-align: center;
    }

    .sidebar .list-unstyled li:hover .badge {
        background-color: #3c67e3;
        color: white;
    }
</style>
@endsection

@section('content')
<!-- 页面头部 -->
<div class="news-header">
    <a href="{{ route('news.index') }}" class="back-button">
        <i class="fas fa-chevron-left"></i> 返回
    </a>
    <h1>新闻详情</h1>
</div>

<div class="container">
    <div class="row">
        <!-- 主内容区 -->
        <div class="col-lg-8">
            <!-- 新闻详情 -->
            <div class="news-container">
                <h1 style="font-size: 22px; font-weight: 600; margin-bottom: 15px;">{{ $news->title }}</h1>
                
                <div class="news-meta">
                    <span><i class="far fa-folder"></i> {{ $news->category->name }}</span>
                    <span><i class="far fa-calendar-alt"></i> {{ $news->published_at->format('Y-m-d') }}</span>
                    <span><i class="far fa-eye"></i> {{ $news->view_count }}次阅读</span>
                    @if($news->author)<span><i class="far fa-user"></i> {{ $news->author }}</span>@endif
                    @if($news->source)<span><i class="far fa-bookmark"></i> {{ $news->source }}</span>@endif
                </div>
                
                @if($news->cover_image)
                <img src="{{ asset('storage/'.$news->cover_image) }}" alt="{{ $news->title }}" class="news-cover">
                @endif
                
                @if($news->summary)
                <div class="p-3 mb-4" style="background: #f8f9fa; border-left: 4px solid #5b7cef; border-radius: 4px;">
                    <p class="mb-0" style="font-size: 15px; color: #666;">{{ $news->summary }}</p>
                </div>
                @endif
                
                <div class="news-content">
                    {!! $news->content !!}
                </div>
                
                <!-- 标签 -->
                @if($news->tags->count() > 0)
                <div class="news-tags">
                    @foreach($news->tags as $tag)
                    <a href="{{ route('news.index', ['tag' => $tag->slug]) }}" class="tag-badge">
                        # {{ $tag->name }}
                    </a>
                    @endforeach
                </div>
                @endif
                
                <!-- 来源和作者信息 -->
                @if($news->author || $news->source)
                <div class="news-meta-box">
                    <table class="news-meta-table">
                        @if($news->source)
                        <tr>
                            <td class="meta-label">来源：</td>
                            <td class="meta-content">{{ $news->source }}</td>
                        </tr>
                        @endif
                        @if($news->author)
                        <tr>
                            <td class="meta-label">作者：</td>
                            <td class="meta-content">{{ $news->author }}</td>
                        </tr>
                        @endif
                    </table>
                </div>
                @endif
            </div>
            
            <!-- 相关新闻 -->
            @if($relatedNews->count() > 0)
            <h3 class="related-news-title">相关阅读</h3>
            <div class="row">
                @foreach($relatedNews as $item)
                <div class="col-md-6">
                    <div class="related-news-card">
                        <div class="related-news-image" style="background-image: url('{{ $item->cover_image ? asset('storage/'.$item->cover_image) : asset('images/default-news.jpg') }}');"></div>
                        <div class="related-news-content">
                            <h3 class="related-news-title-text">{{ $item->title }}</h3>
                            <div class="d-flex justify-content-between" style="color: #888; font-size: 12px;">
                                <span>{{ $item->category->name }}</span>
                                <span>{{ $item->published_at->format('Y-m-d') }}</span>
                            </div>
                        </div>
                        <a href="{{ route('news.show', $item->slug) }}" class="stretched-link"></a>
                    </div>
                </div>
                @endforeach
            </div>
            @endif
        </div>
        
        <!-- 侧边栏 -->
        <div class="col-lg-4">
            <!-- 热门新闻 -->
            <div class="sidebar">
                <h4 class="sidebar-title">热门阅读</h4>
                @foreach($popularNews as $item)
                    <div class="sidebar-item">
                        <a href="{{ route('news.show', $item->slug) }}">
                            <div class="d-flex">
                                <div class="flex-shrink-0">
                                    <img src="{{ $item->cover_image ? asset('storage/'.$item->cover_image) : asset('images/default-news.jpg') }}" alt="{{ $item->title }}" style="width: 70px; height: 50px; object-fit: cover; border-radius: 4px;">
                                </div>
                                <div class="flex-grow-1 ms-3">
                                    <p class="mb-1" style="font-size: 14px; font-weight: 500;">{{ $item->title }}</p>
                                    <small class="text-muted">{{ $item->view_count }}次阅读</small>
                                </div>
                            </div>
                        </a>
                    </div>
                @endforeach
            </div>
            
            <!-- 分类列表 -->
            <div class="sidebar">
                <h4 class="sidebar-title">新闻分类</h4>
                <ul class="list-unstyled mb-0">
                    @foreach($categories as $category)
                    <li>
                        <a href="{{ route('news.index', ['category' => $category->slug]) }}">
                            <span>{{ $category->name }}</span>
                            <span class="badge">{{ $category->news_count }}</span>
                        </a>
                    </li>
                    @endforeach
                </ul>
            </div>
            
            <!-- 热门标签 -->
            <div class="sidebar">
                <h4 class="sidebar-title">热门标签</h4>
                <div class="tag-cloud">
                    @foreach($popularTags as $tag)
                        <a href="{{ route('news.index', ['tag' => $tag->slug]) }}">
                            <span style="margin-right: 2px;">#</span>{{ $tag->name }} <span style="opacity: 0.7;">({{ $tag->news_count }})</span>
                        </a>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('custom-scripts')
<script>
    // 自定义脚本可以在这里添加
</script>
@endsection
