<?php

namespace App\Admin\Controllers;

use App\Models\Hotline;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Content;

class HotlineController extends AdminController
{
    /**
     * 设置页面标题
     */
    protected $title = '心理援助热线';

    /**
     * 列表页面
     */
    protected function grid()
    {
        return Grid::make(new Hotline(), function (Grid $grid) {
            // 默认按排序字段排序
            $grid->model()->orderBy('sort_order', 'asc')->orderBy('id', 'desc');
            
            $grid->column('id')->sortable();
            $grid->column('name', '热线名称');
            $grid->column('phone_number', '热线电话');
            
            $grid->column('status', '状态')->using(Hotline::getStatusMap())->dot([
                'active' => 'success',
                'busy' => 'warning',
                'closed' => 'danger',
            ]);
            
            $grid->column('service_hours', '服务时间');
            $grid->column('sort_order', '排序')->sortable();
            $grid->column('is_enabled', '启用状态')->switch();
            
            $grid->column('created_at', '创建时间')->sortable();
            $grid->column('updated_at', '更新时间');
            
            // 筛选器
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');
                $filter->like('name', '热线名称');
                $filter->like('phone_number', '热线电话');
                $filter->equal('status', '状态')->select(Hotline::getStatusMap());
                $filter->equal('is_enabled', '启用状态')->select([0 => '禁用', 1 => '启用']);
            });
            
            // 添加批量启用和禁用操作
            $grid->batchActions(function (Grid\Tools\BatchActions $batch) {
                $batch->add(new \App\Admin\Actions\BatchEnable());
                $batch->add(new \App\Admin\Actions\BatchDisable());
            });
            
            // 添加导出按钮
            $grid->export();
            
            // 添加操作按钮
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                // 添加查看使用记录按钮
                $actions->append('<a href="'.admin_url('hotline-records?hotline_id='.$actions->row->id).'" class="btn btn-sm btn-primary"><i class="feather icon-eye"></i> 查看记录</a>');
            });
        });
    }

    /**
     * 详情页面
     */
    protected function detail($id)
    {
        return Show::make($id, new Hotline(), function (Show $show) {
            $show->field('id');
            $show->field('name', '热线名称');
            $show->field('phone_number', '热线电话');
            $show->field('description', '热线描述');
            $show->field('status', '状态')->using(Hotline::getStatusMap());
            $show->field('service_hours', '服务时间');
            $show->field('sort_order', '排序');
            $show->field('is_enabled', '是否启用')->as(function ($is_enabled) {
                return $is_enabled ? '是' : '否';
            });
            $show->field('created_at', '创建时间');
            $show->field('updated_at', '更新时间');
            
            // 添加使用记录卡片
            $show->relation('records', '使用记录', function ($grid) {
                $grid->model()->orderBy('created_at', 'desc');
                $grid->column('id');
                $grid->column('user.name', '用户');
                $grid->column('action', '操作类型')->using(\App\Models\HotlineRecord::getActionMap());
                $grid->column('ip_address', 'IP地址');
                $grid->column('created_at', '操作时间');
            });
        });
    }

    /**
     * 表单页面
     */
    protected function form()
    {
        return Form::make(new Hotline(), function (Form $form) {
            $form->display('id');
            
            $form->text('name', '热线名称')
                ->required()
                ->help('输入热线名称，如"专业心理咨询热线"');
            
            $form->text('phone_number', '热线电话')
                ->required()
                ->help('输入热线电话号码，如"************"');
            
            $form->textarea('description', '热线描述')
                ->rows(3)
                ->help('简要描述该热线的服务内容');
            
            $form->select('status', '状态')
                ->options(Hotline::getStatusMap())
                ->default('active')
                ->required();
            
            $form->text('service_hours', '服务时间')
                ->default('24小时')
                ->help('服务时间，如"24小时"、"工作日9:00-18:00"等');
            
            $form->number('sort_order', '排序')
                ->default(0)
                ->help('数字越小越靠前显示');
            
            $form->switch('is_enabled', '是否启用')
                ->default(true);
            
            $form->display('created_at', '创建时间');
            $form->display('updated_at', '更新时间');
        });
    }
}
