<?php

use App\Admin\Controllers\AiConsultationSettingController;
use App\Admin\Controllers\AssessmentAiSettingsController;
use App\Admin\Controllers\AssessmentAnalysisController;
use App\Admin\Controllers\AssessmentOptionController;
use App\Admin\Controllers\AssessmentQuestionController;
use App\Admin\Controllers\AssessmentQuestionnaireController;
use App\Admin\Controllers\AssessmentResponseController;
use App\Admin\Controllers\ConsultationAppointmentController;
use App\Admin\Controllers\CounselorController;
use App\Admin\Controllers\CounselorScheduleController;
use App\Admin\Controllers\CourseLessonController;
use App\Admin\Controllers\CourseLessonCategoryController;
use App\Admin\Controllers\CourseLessonTagController;
use App\Admin\Controllers\CourseRegistrationController;
use App\Admin\Controllers\HotlineController;
use App\Admin\Controllers\HotlineRecordController;
use App\Admin\Controllers\OfflineCourseController;
use App\Admin\Controllers\QuizActivityController;
use App\Admin\Controllers\QuizPrizeController;
use App\Admin\Controllers\QuizPrizeWinnerController;
use App\Admin\Controllers\QuizQuestionController;
use App\Admin\Controllers\QuestionnaireTemplateController;
use App\Admin\Controllers\QuestionnaireBatchController;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;
use Dcat\Admin\Admin;
use App\Admin\Controllers\ArticleCategoryController;
use App\Admin\Controllers\ArticleTagController;
use App\Admin\Controllers\ArticleController;
use App\Admin\Controllers\VideoCategoryController;
use App\Admin\Controllers\VideoTagController;
use App\Admin\Controllers\VideoController;
use App\Admin\Controllers\NewsCategoryController;
use App\Admin\Controllers\NewsTagController;
use App\Admin\Controllers\NewsController;
use App\Admin\Controllers\ConsultationController;
use App\Admin\Controllers\UserController;
use App\Admin\Controllers\LiveShowController as AdminLiveShowController;
use App\Admin\Controllers\ViewBrushController;
use App\Http\Middleware\ForceUrlWithPort;

Admin::routes();



Route::group([
    'prefix'     => config('admin.route.prefix'),
    'namespace'  => config('admin.route.namespace'),
    'middleware' => config('admin.route.middleware'),
], function (Router $router) {
    $router->any('auth/extensions/{any?}', fn() => abort(404))->where('any', '.*');

    $router->get('/', 'HomeController@index');
    
    //系统配置管理
    $router->resource('system-settings', SystemConfigController::class);
    
    //用户管理
    $router->resource('users', UserController::class);

    // 心理科普文章管理
    $router->resource('article-categories', ArticleCategoryController::class);
    $router->resource('article-tags', ArticleTagController::class);
    $router->resource('articles', ArticleController::class);
    //浏览量
    $router->resource('view-brush', ViewBrushController::class);

    // 文章审核相关路由
    $router->get('articles/{id}/review', 'ArticleController@review');
    $router->post('articles/{id}/handle-review', 'ArticleController@handleReview');
    $router->post('articles/{id}/submit-review', 'ArticleController@submitReview');

    // 文章批量操作路由
    $router->post('articles/batch-recommend', 'ArticleController@batchRecommend');
    $router->post('articles/batch-unrecommend', 'ArticleController@batchUnrecommend');

    // 心理科普视频管理
    $router->resource('video-categories', VideoCategoryController::class);
    $router->resource('video-tags', VideoTagController::class);
    $router->resource('videos', VideoController::class);

    // 视频审核相关路由
    $router->get('videos/pending-review', 'VideoController@pendingReview');
    $router->get('videos/{id}/review', 'VideoController@review');
    $router->post('videos/{id}/handle-review', 'VideoController@handleReview');
    $router->post('videos/{id}/submit-review', 'VideoController@submitReview');

    // 文件上传路由
    $router->post('upload/video', 'UploadController@video');
    $router->post('upload/video/delete', 'UploadController@deleteVideo');

    // 心理咨询模块管理
    $router->resource('counselors', CounselorController::class);
    //线下咨询师
    $router->resource('counselors-lx', CounselorLxController::class);
    $router->resource('counselor_schedules', CounselorScheduleController::class);
    $router->resource('offline-courses', OfflineCourseController::class);
    $router->resource('consultation-appointments', ConsultationAppointmentController::class);
    $router->resource('course-registrations', CourseRegistrationController::class);
    $router->resource('ai-consultation-settings', AiConsultationSettingController::class);

    // 咨询预约处理相关路由
    $router->get('consultation-appointments/pending', 'ConsultationAppointmentController@pendingAppointments');
    $router->post('consultation-appointments/{id}/confirm', 'ConsultationAppointmentController@confirmAppointment');
    $router->post('consultation-appointments/{id}/reject', 'ConsultationAppointmentController@rejectAppointment');
    $router->post('consultation-appointments/{id}/complete', 'ConsultationAppointmentController@completeAppointment');

    // 咨询师排班批量操作路由
    $router->post('counselor_schedules/mark-available', 'CounselorScheduleController@markAsAvailable');
    $router->post('counselor_schedules/mark-unavailable', 'CounselorScheduleController@markAsUnavailable');

    // 咨询师排班开关状态更新路由
    $router->post('counselor_schedules/switch-availability', 'CounselorScheduleController@switchAvailability');

    // 咨询预约批量操作路由
    $router->post('consultation-appointments/batch-confirm', 'ConsultationAppointmentController@batchConfirm');
    $router->post('consultation-appointments/batch-complete', 'ConsultationAppointmentController@batchComplete');

    // AI咨询通过Action类实现测试连接功能，不需要路由

    // 心理援助热线管理
    $router->resource('hotlines', HotlineController::class);
    $router->resource('hotline-records', HotlineRecordController::class);

    // 新闻资讯管理
    $router->resource('news-categories', NewsCategoryController::class);
    $router->resource('news-tags', NewsTagController::class);

    // 新闻仪表盘 - 注意：必须在资源路由之前定义
    $router->get('news/dashboard', 'NewsController@dashboard')->name('news.dashboard');

    // 新闻资源路由
    $router->resource('news', NewsController::class);

    // 心理课堂管理
    $router->resource('course-lesson-categories', CourseLessonCategoryController::class);
    $router->resource('course-lesson-tags', CourseLessonTagController::class);
    $router->resource('course-lessons', CourseLessonController::class);

    // 课程批量操作路由
    $router->post('course-lessons/batch-recommend', 'CourseLessonController@batchRecommend');
    $router->post('course-lessons/batch-unrecommend', 'CourseLessonController@batchUnrecommend');

    // 心理测评模块管理
    $router->get('questionnaire-templates', 'QuestionnaireTemplateController@index')->name('questionnaire-templates');
    $router->get('questionnaire-templates/create-from-template', 'QuestionnaireTemplateController@createFromTemplate')->name('questionnaire-templates.create-from-template');
    $router->get('questionnaire-batch', 'QuestionnaireBatchController@index')->name('questionnaire-batch');
    $router->post('questionnaire-batch/import', 'QuestionnaireBatchController@import')->name('questionnaire-batch.import')->withoutMiddleware([ForceUrlWithPort::class]);
    $router->get('questionnaire-batch/export/{id}', 'QuestionnaireBatchController@export')->name('questionnaire-batch.export');
    
    // 问卷额外功能路由
    $router->post('assessment-questionnaires/{id}/duplicate', 'AssessmentQuestionnaireController@duplicate')->name('assessment-questionnaires.duplicate');
    $router->post('assessment-questionnaires/{id}/toggle-status', 'AssessmentQuestionnaireController@toggleStatus')->name('assessment-questionnaires.toggle-status');
    $router->get('assessment-questionnaires/{id}/preview', 'AssessmentQuestionnaireController@preview')->name('assessment-questionnaires.preview');
    $router->get('assessment-questionnaires/{id}/stats', 'AssessmentQuestionnaireController@stats')->name('assessment-questionnaires.stats');
    
    $router->resource('assessment-questionnaires', AssessmentQuestionnaireController::class);
    $router->resource('assessment-questions', AssessmentQuestionController::class);
    $router->resource('assessment-options', AssessmentOptionController::class);
    $router->resource('assessment-responses', AssessmentResponseController::class);
    $router->resource('assessment-analyses', AssessmentAnalysisController::class);
    $router->resource('assessment-ai-settings', AssessmentAiSettingsController::class);
    // 测试 AI 设置连接
    $router->post('assessment-ai-settings/{id}/test-connection', 'AssessmentAiSettingsController@testConnection');

    // 有奖问答模块管理
    $router->resource('quiz-activities', QuizActivityController::class);
    $router->resource('quiz-questions', QuizQuestionController::class);
    $router->resource('quiz-prizes', QuizPrizeController::class);
    $router->resource('quiz-prize-winners', QuizPrizeWinnerController::class);

    // 奖品管理相关路由
    $router->post('quiz-prize-winners/{id}/claim', 'QuizPrizeWinnerController@claimPrize');
    $router->post('quiz-prize-winners/{id}/ship', 'QuizPrizeWinnerController@shipPrize');
    $router->post('quiz-prize-winners/{id}/deliver', 'QuizPrizeWinnerController@deliverPrize');
    $router->post('quiz-prize-winners/{id}/cancel', 'QuizPrizeWinnerController@cancelPrize');
    
    //留言咨询模块
    $router->resource('consultations', ConsultationController::class);
    $router->get('consultations/{id}/detail', 'ConsultationController@detail');
    $router->post('consultations/{id}/reply', 'ConsultationController@addReply');
    $router->post('consultations/{id}/status', 'ConsultationController@updateStatus');

    // 直播管理模块
    $router->resource('live-shows', AdminLiveShowController::class);

});
