<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class News extends Model
{
    use HasFactory, SoftDeletes, HasDateTimeFormatter;

    /**
     * 表名
     *
     * @var string
     */
    protected $table = 'news';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'title', 'slug', 'summary', 'content', 'cover_image',
        'source', 'author', 'category_id', 'is_featured',
        'is_published', 'published_at', 'view_count', 'share_count'
    ];

    /**
     * 类型转换
     *
     * @var array
     */
    protected $casts = [
        'is_featured' => 'boolean',
        'is_published' => 'boolean',
        'published_at' => 'datetime',
    ];

    /**
     * 获取关联的分类
     */
    public function category()
    {
        return $this->belongsTo(NewsCategory::class, 'category_id');
    }

    /**
     * 获取关联的标签
     */
    public function tags()
    {
        return $this->belongsToMany(NewsTag::class, 'news_tag', 'news_id', 'news_tag_id')
            ->withTimestamps();
    }

    /**
     * 获取新闻浏览记录
     */
    public function views()
    {
        return $this->hasMany(NewsView::class);
    }

    /**
     * 本地作用域：已发布
     */
    public function scopePublished($query)
    {
        return $query->where('is_published', true)
                    ->where('published_at', '<=', now());
    }

    /**
     * 本地作用域：推荐
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * 本地作用域：按分类筛选
     */
    public function scopeInCategory($query, $categoryId)
    {
        return $query->where('category_id', $categoryId);
    }

    /**
     * 本地作用域：按标签筛选
     */
    public function scopeWithTag($query, $tagId)
    {
        return $query->whereHas('tags', function ($q) use ($tagId) {
            $q->where('news_tags.id', $tagId);
        });
    }

    /**
     * 本地作用域：搜索
     */
    public function scopeSearch($query, $keyword)
    {
        if ($keyword) {
            return $query->where(function ($q) use ($keyword) {
                $q->where('title', 'like', "%{$keyword}%")
                  ->orWhere('summary', 'like', "%{$keyword}%")
                  ->orWhere('content', 'like', "%{$keyword}%");
            });
        }

        return $query;
    }

    /**
     * 记录浏览
     */
    public function recordView($userId = null)
    {
        $this->increment('view_count');

        // 记录详细浏览信息
        return $this->views()->create([
            'user_id' => $userId,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent()
        ]);
    }

    /**
     * 记录分享
     */
    public function recordShare()
    {
        return $this->increment('share_count');
    }
}
