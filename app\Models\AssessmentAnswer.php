<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Dcat\Admin\Traits\HasDateTimeFormatter;

/**
 * 心理测评答案模型
 */
class AssessmentAnswer extends Model
{
    use HasDateTimeFormatter;

    // 批量赋值字段
    protected $fillable = ['response_id','question_id','option_id','custom_value'];

    /**
     * 答案所属答卷
     */
    public function response(): BelongsTo
    {
        return $this->belongsTo(AssessmentResponse::class);
    }

    /**
     * 答案对应题目
     */
    public function question(): BelongsTo
    {
        return $this->belongsTo(AssessmentQuestion::class);
    }

    /**
     * 答案对应选项（若有）
     */
    public function option(): BelongsTo
    {
        return $this->belongsTo(AssessmentOption::class);
    }
}
