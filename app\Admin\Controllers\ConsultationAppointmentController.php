<?php

namespace App\Admin\Controllers;

use App\Admin\Actions\BatchConfirmAppointment;
use App\Admin\Actions\BatchCompleteAppointment;
use App\Models\Counselor;
use App\Models\ConsultationAppointment;
use App\Models\User;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Carbon\Carbon;

class ConsultationAppointmentController extends AdminController
{
    /**
     * 设置标题
     */
    protected $title = '咨询预约';

    /**
     * 列表页面
     */
    protected function grid()
    {
        return Grid::make(new ConsultationAppointment(), function (Grid $grid) {
            // 添加排序
            $grid->model()->orderBy('created_at', 'desc');
            
            // 确保与用户和咨询师关联加载
            $grid->model()->with(['user', 'counselor']);
            
            // 获取URL参数
            $counselorId = request()->get('counselor_id');
            $userId = request()->get('user_id');
            
            // 根据URL参数筛选数据
            if ($counselorId) {
                $grid->model()->where('counselor_id', $counselorId);
                $counselor = Counselor::find($counselorId);
                if ($counselor) {
                    $grid->tools(function (Grid\Tools $tools) use ($counselor) {
                        $tools->append('<div class="alert alert-info">当前显示: ' . $counselor->name . ' 的预约</div>');
                    });
                }
            }
            
            if ($userId) {
                $grid->model()->where('user_id', $userId);
                $user = User::find($userId);
                if ($user) {
                    $grid->tools(function (Grid\Tools $tools) use ($user) {
                        $tools->append('<div class="alert alert-info">当前显示: ' . ($user->name ?? $user->nickname ?? 'ID:'.$user->id) . ' 的预约</div>');
                    });
                }
            }

            // 设置表格字段
            $grid->column('id')->sortable();
            
            // 用户信息
            $grid->column('user_id', '用户ID');
            $grid->column('user.name', '用户名称')->display(function($name) {
                return $name ?? ($this->user->nickname ?? ($this->user ? 'ID:'.$this->user->id : '未知用户'));
            });            
            
            // 咨询师信息
            $grid->column('counselor_id', '咨询师ID');
            $grid->column('counselor.name', '咨询师名称')->display(function($name) {
                return $name ?? ($this->counselor ? $this->counselor->id : '未知咨询师');
            });
            
            // 时间信息
            $grid->column('appointment_time', '预约时间')->display(function($time) {
                return $time ? Carbon::parse($time)->format('Y-m-d H:i') : '未设置';
            })->sortable();
            
            // 状态显示，使用模型定义的常量
            $grid->column('status', '状态')->using(ConsultationAppointment::getStatusMap())->dot([
                ConsultationAppointment::STATUS_CANCELLED => 'danger',
                ConsultationAppointment::STATUS_PENDING => 'warning',
                ConsultationAppointment::STATUS_CONFIRMED => 'primary',
                ConsultationAppointment::STATUS_ONGOING => 'info',
                ConsultationAppointment::STATUS_COMPLETED => 'success',
            ]);
            
            // 支付状态
            $grid->column('payment_status', '支付状态')->using(ConsultationAppointment::getPaymentStatusMap())->dot([
                ConsultationAppointment::PAYMENT_UNPAID => 'danger',
                ConsultationAppointment::PAYMENT_PAID => 'success',
                ConsultationAppointment::PAYMENT_REFUNDED => 'info',
            ]);
            
            $grid->column('price', '支付金额')->display(function ($price) {
                return '￥' . number_format($price/100, 2);
            });
            
            $grid->column('created_at', '创建时间')->sortable();
            
            // 筛选功能
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');
                $filter->equal('user_id', '用户')->select(function () {
                    return User::all()->pluck('nickname', 'id');
                });
                $filter->equal('counselor_id', '咨询师')->select(function () {
                    return Counselor::all()->pluck('name', 'id');
                });
                $filter->where('date', function ($query) {
                    $query->whereHas('schedule', function ($q) {
                        $q->whereDate('date', $this->input);
                    });
                }, '预约日期')->date();
                
                $filter->equal('status', '状态')->select([
                    0 => '待确认',
                    1 => '已确认', 
                    2 => '已完成',
                    3 => '已取消'
                ]);
                $filter->equal('payment_status', '支付状态')->select([
                    0 => '未支付',
                    1 => '已支付', 
                    2 => '已退款'
                ]);
            });
            
            // 默认按创建时间排序
            $grid->model()->orderBy('created_at', 'desc');
            
            // 批量操作
            $grid->batchActions(function (Grid\Tools\BatchActions $batch) {
                $batch->add(new BatchConfirmAppointment());
                $batch->add(new BatchCompleteAppointment());
            });
        });
    }

    /**
     * 详情页面
     */
    protected function detail($id)
    {
        return Show::make($id, new ConsultationAppointment(['user', 'counselor']), function (Show $show) {
            $show->field('id');
            
            // 用户信息
            $show->field('user_id', '用户ID');
            $show->field('user.name', '用户名称')->as(function() {
                return $this->user ? ($this->user->name ?? $this->user->nickname ?? 'ID:'.$this->user->id) : '未知用户';
            });
            
            // 咨询师信息
            $show->field('counselor_id', '咨询师ID');
            $show->field('counselor.name', '咨询师名称')->as(function() {
                return $this->counselor ? $this->counselor->name : '未知咨询师';
            });
            
            // 预约时间
            $show->field('appointment_time', '预约时间')->as(function($time) {
                return $time ? Carbon::parse($time)->format('Y-m-d H:i') : '未设置';
            });
            
            // 状态显示
            $show->field('status', '状态')->using(ConsultationAppointment::getStatusMap());
            $show->field('payment_status', '支付状态')->using(ConsultationAppointment::getPaymentStatusMap());
            
            $show->field('price', '支付金额')->as(function ($price) {
                return '￥' . number_format($price/100, 2);
            });
            
            $show->field('comment', '备注');
            $show->field('review', '评价');
            $show->field('rating', '评分');
            
            $show->field('created_at', '创建时间');
            $show->field('updated_at', '更新时间');
        });
    }

    /**
     * 表单页面
     */
    protected function form()
    {
        return Form::make(new ConsultationAppointment(), function (Form $form) {
            $form->display('id');
            
            // 用户选择改进，使用名称或ID作为显示
            $form->select('user_id', '用户')
                ->options(function () {
                    $users = User::all();
                    $options = [];
                    foreach ($users as $user) {
                        $displayName = $user->name ?? $user->nickname ?? ('ID:' . $user->id);
                        $options[$user->id] = $displayName;
                    }
                    return $options;
                })
                ->required()
                ->help('选择预约的用户');
            
            // 咨询师选择改进
            $form->select('counselor_id', '咨询师')
                ->options(function () {
                    $counselors = Counselor::all();
                    $options = [];
                    foreach ($counselors as $counselor) {
                        $options[$counselor->id] = $counselor->name ?? ('ID:' . $counselor->id);
                    }
                    return $options;
                })
                ->required()
                ->help('选择咨询师');
                
            // 添加预约时间选择
            $form->datetime('appointment_time', '预约时间')
                ->required()
                ->help('选择预约时间');
            
            $form->select('status', '状态')
                ->options(ConsultationAppointment::getStatusMap())
                ->default(ConsultationAppointment::STATUS_PENDING);
            
            $form->select('payment_status', '支付状态')
                ->options(ConsultationAppointment::getPaymentStatusMap())
                ->default(ConsultationAppointment::PAYMENT_UNPAID);
            
            $form->currency('price', '支付金额')
                ->symbol('￥');
                
            $form->textarea('comment', '备注');
            $form->textarea('review', '评价');
            $form->rate('rating', '评分');
            
            // 表单验证
            $form->saving(function (Form $form) {
                // 验证必填字段
                if (!$form->user_id) {
                    return $form->response()->error('请选择用户');
                }
                if (!$form->counselor_id) {
                    return $form->response()->error('请选择咨询师');
                }
            });
            
            $form->display('created_at', '创建时间');
            $form->display('updated_at', '更新时间');
        });
    }
    
    /**
     * 批量确认预约
     */
    public function batchConfirm($ids)
    {
        ConsultationAppointment::whereIn('id', $ids)->update(['status' => ConsultationAppointment::STATUS_CONFIRMED]);
        return $this->response()->success('成功确认'.count($ids).'条预约')->refresh();
    }
    
    /**
     * 批量完成预约
     */
    public function batchComplete($ids)
    {
        ConsultationAppointment::whereIn('id', $ids)->update(['status' => ConsultationAppointment::STATUS_COMPLETED]);
        return $this->response()->success('成功完成'.count($ids).'条预约')->refresh();
    }
}
