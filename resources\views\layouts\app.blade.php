<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>@yield('title', '心理咨询平台')</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    <link rel="preload" href="https://fonts.googleapis.com/icon?family=Material+Icons" as="style">
    <link rel="stylesheet" href="{{ asset('css/app.css') }}">
    <style>
        /* 防止Material Icons加载时显示文本 */
        .material-icons {
            font-family: 'Material Icons';
            visibility: hidden;
        }
        .material-icons.icon-loaded {
            visibility: visible;
        }
    </style>
    <style>
        /* 覆盖容器最大宽度 */
        .container-fluid {
            width: 100%;
            max-width: 100%;
            padding: 0;
            margin: 0;
        }
        
        .content-wrapper {
            width: 100%;
            max-width: 100%;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
        }
    </style>
    @yield('head')
    @yield('custom-styles')
    
    <script>
        // Material Icons 加载完成后解除隐藏
        document.addEventListener('DOMContentLoaded', function() {
            // 创建测试元素检查字体是否加载完成
            let fontTest = document.createElement('span');
            fontTest.style.fontFamily = 'Material Icons';
            fontTest.style.visibility = 'hidden';
            fontTest.textContent = 'event'; // 任意Material Icon名称
            document.body.appendChild(fontTest);
            
            // 5ms延迟确保CSS已应用
            setTimeout(function checkFontLoaded() {
                // 当字体加载完成后显示所有图标
                let icons = document.querySelectorAll('.material-icons');
                icons.forEach(function(icon) {
                    icon.classList.add('icon-loaded');
                });
                // 移除测试元素
                document.body.removeChild(fontTest);
            }, 200);
        });
    </script>
</head>
<body>
    <div class="content-wrapper">
        @yield('content')
    </div>
    
    @include('components.navbar')
    
    <!-- jQuery库 -->
    <script src="{{ asset("js/jquery-3.6.0.min.js") }}"></script>
    
    <!-- 手机预览模式脚本 -->
    <script src="{{ asset('js/mobile-preview.js') }}"></script>
    
    @yield('scripts')
</body>
</html>