@extends('layouts.app')

@section('title', '正念冥想 - 心理减压')

@section('content')
<div class="meditation-container">
    <!-- 页面头部 - 自然融合设计 -->
    <div class="page-header">
        <div class="container">
            <div class="header-content">
                <div class="breadcrumb">
                    <a href="{{ route('stress_relief.index') }}">心理减压</a>
                    <span class="divider">/</span>
                    <span class="current">正念冥想</span>
                </div>
                <h1 class="page-title">正念冥想</h1>
                <p class="page-subtitle">正念内观，清净入耳，宁静入心</p>
            </div>
        </div>
    </div>

    <div class="meditation-programs">
        <div class="container">
            <div class="programs-grid">
                <div class="program-card" onclick="startMeditation('basic')">
                    <div class="program-icon">
                        <i class="fas fa-lotus-position"></i>
                    </div>
                    <h3 class="program-title">基础冥想</h3>
                    <p class="program-desc">初学者友好的基础冥想练习</p>
                    <div class="program-duration">开始冥想</div>
                </div>

                <div class="program-card" onclick="startMeditation('body')">
                    <div class="program-icon">
                        <i class="fas fa-hand-paper"></i>
                    </div>
                    <h3 class="program-title">身体扫描</h3>
                    <p class="program-desc">觉察身体各部分的感受</p>
                    <div class="program-duration">开始冥想</div>
                </div>

                <div class="program-card" onclick="startMeditation('loving')">
                    <div class="program-icon">
                        <i class="fas fa-heart"></i>
                    </div>
                    <h3 class="program-title">慈悲冥想</h3>
                    <p class="program-desc">培养对自己和他人的慈悲心</p>
                    <div class="program-duration">开始冥想</div>
                </div>

                <div class="program-card" onclick="startMeditation('mindful')">
                    <div class="program-icon">
                        <i class="fas fa-eye"></i>
                    </div>
                    <h3 class="program-title">觉察冥想</h3>
                    <p class="program-desc">提升当下觉察力的练习</p>
                    <div class="program-duration">开始冥想</div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="meditation-guide" class="meditation-guide" style="display: none;">
    <div class="guide-overlay"></div>
    <div class="guide-content">
        <div class="guide-header">
            <h3 id="meditation-title">正念冥想</h3>
            <button class="close-btn" onclick="closeMeditationGuide()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="guide-body">
            <div class="meditation-visual">
                <div class="mandala">
                    <div class="circle outer"></div>
                    <div class="circle middle"></div>
                    <div class="circle inner"></div>
                    <div class="center-dot"></div>
                </div>
            </div>
            <div class="guide-text">
                <p id="meditation-instruction">找一个舒适的坐姿，轻轻闭上眼睛...</p>
            </div>
            <div class="meditation-controls">
                <button class="control-btn primary" onclick="toggleMeditation()">
                    <i class="fas fa-play" id="meditation-play-icon"></i>
                    <span id="meditation-btn-text">开始冥想</span>
                </button>
            </div>
            <div class="meditation-timer">
                <span id="meditation-time">00:00</span>
            </div>
        </div>
    </div>
</div>

<style>
/* (CSS 保持原样，不做任何修改) */
.meditation-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #a855f7 0%, #8b5cf6 100%);
    padding-top: calc(env(safe-area-inset-top, 0px) + 20px);
    padding-bottom: calc(env(safe-area-inset-bottom, 0px) + 80px);
}
.status-bar-spacer { height: env(safe-area-inset-top, 0px); background: transparent; }
.navbar-spacer { height: 50px; background: transparent; }
.meditation-container .container { max-width: none; width: 100%; padding: 0 15px; background-color: transparent !important; box-sizing: border-box; }
.page-header { padding: 6rem 0 4rem; color: white; position: relative; background: transparent; }
.page-header::before { content: ''; position: absolute; top: 0; left: 0; right: 0; height: 100%; background: radial-gradient(ellipse at top, rgba(255,255,255,0.1) 0%, transparent 50%); pointer-events: none; }
.header-content { position: relative; z-index: 1; text-align: center; }
.breadcrumb { margin-bottom: 1rem; font-size: 0.9rem; }
.breadcrumb a { color: rgba(255,255,255,0.8); text-decoration: none; }
.breadcrumb a:hover { color: white; }
.divider { margin: 0 0.5rem; opacity: 0.6; }
.current { color: white; }
.page-title { font-size: 3rem; font-weight: 700; margin-bottom: 1rem; text-shadow: 0 2px 10px rgba(0,0,0,0.3); }
.page-subtitle { font-size: 1.2rem; opacity: 0.9; }
.meditation-programs { padding: 3rem 0; width: 100%; box-sizing: border-box; }
.programs-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 2rem; margin-top: 2rem; max-width: 1200px; margin-left: auto; margin-right: auto; box-sizing: border-box; width: 100%; }
.program-card { background: white; border-radius: 20px; padding: 2rem 2rem 4rem; box-shadow: 0 10px 30px rgba(0,0,0,0.1); transition: transform 0.3s ease; cursor: pointer; text-align: center; position: relative; }
.program-card:hover { transform: translateY(-5px); }
.program-icon { width: 70px; height: 70px; border-radius: 50%; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); display: flex; align-items: center; justify-content: center; margin: 0 auto 1.5rem; font-size: 1.8rem; color: white; }
.program-title { font-size: 1.3rem; font-weight: 600; color: #2c3e50; margin-bottom: 1rem; }
.program-desc { color: #5a6c7d; line-height: 1.6; margin-bottom: 2rem; }
.program-duration { position: absolute; bottom: 1.5rem; left: 50%; transform: translateX(-50%); background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 0.5rem 1rem; border-radius: 15px; font-size: 0.9rem; font-weight: 500; }
.meditation-guide { position: fixed; top: 0; left: 0; width: 100%; height: 100%; z-index: 1000; display: flex; align-items: center; justify-content: center; }
.guide-overlay { position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.9); backdrop-filter: blur(10px); }
.guide-content { position: relative; background: linear-gradient(135deg, #8360c3 0%, #2ebf91 100%); border-radius: 20px; width: 90%; max-width: 500px; overflow: hidden; color: white; }
.guide-header { padding: 1.5rem; display: flex; justify-content: space-between; align-items: center; border-bottom: 1px solid rgba(255,255,255,0.1); }
.guide-header h3 { margin: 0; font-size: 1.3rem; }
.close-btn { background: none; border: none; color: white; font-size: 1.5rem; cursor: pointer; width: 40px; height: 40px; border-radius: 50%; display: flex; align-items: center; justify-content: center; transition: background 0.3s ease; }
.close-btn:hover { background: rgba(255,255,255,0.2); }
.guide-body { padding: 2rem; text-align: center; }
.meditation-visual { margin-bottom: 2rem; }
.mandala { width: 200px; height: 200px; margin: 0 auto; position: relative; }
.circle { position: absolute; border: 2px solid rgba(255,255,255,0.3); border-radius: 50%; animation: rotate 20s linear infinite; }
.circle.outer { width: 200px; height: 200px; top: 0; left: 0; }
.circle.middle { width: 140px; height: 140px; top: 30px; left: 30px; animation-duration: 15s; animation-direction: reverse; }
.circle.inner { width: 80px; height: 80px; top: 60px; left: 60px; animation-duration: 10s; }
.center-dot { position: absolute; top: 90px; left: 90px; width: 20px; height: 20px; background: white; border-radius: 50%; animation: pulse 3s infinite; }
@keyframes rotate { from { transform: rotate(0deg); } to { transform: rotate(360deg); } }
@keyframes pulse { 0%,100% { opacity: 1; transform: scale(1); } 50% { opacity: 0.5; transform: scale(1.2); } }
.meditation-instruction { margin-bottom: 2rem; }
#meditation-instruction { font-size: 1rem; line-height: 1.6; opacity: 0.9; }
.meditation-controls { display: flex; justify-content: center; gap: 1rem; margin-bottom: 2rem; }
.control-btn { display: flex; align-items: center; gap: 0.5rem; padding: 1rem 2rem; border: none; border-radius: 10px; font-size: 1rem; font-weight: 600; cursor: pointer; transition: all 0.3s ease; }
.control-btn.primary { background: rgba(255,255,255,0.2); color: white; }
.control-btn.primary:hover { background: rgba(255,255,255,0.3); transform: translateY(-2px); }
.control-btn:not(.primary) { background: rgba(255,255,255,0.1); color: white; }
.control-btn:not(.primary):hover { background: rgba(255,255,255,0.2); }
.meditation-timer { font-size: 1.5rem; font-weight: 600; }
@media (max-width: 768px) {
    .mandala { width: 150px; height: 150px; }
    .circle.outer { width: 150px; height: 150px; }
    .circle.middle { width: 105px; height: 105px; top: 22.5px; left: 22.5px; }
    .circle.inner { width: 60px; height: 60px; top: 45px; left: 45px; }
    .center-dot { top: 67.5px; left: 67.5px; }
}
@media (max-width: 500px) {
    .programs-grid { grid-template-columns: repeat(2,1fr); gap:15px; padding:0!important; margin:0!important; width:100%!important; max-width:100%!important; box-sizing:border-box!important; }
}
</style>

<script>
let meditationActive = false;
let meditationTimer = null;
let sessionTimeout = null;
let meditationStartTime = null;
let speechIndex = 0;

const meditationPrograms = {
    basic: {
        title: '基础冥想',
        durationSeconds: 10 * 60,
        script: [
            { text: '欢迎来到基础冥想。请找到一个舒适的坐姿，双脚平放，双手轻轻放在膝盖上。', pause: 5 },
            { text: '闭上眼睛，感受身体与椅子的接触。让全身慢慢放松。', pause: 5 },
            { text: '将注意力轻柔地放在呼吸上，感受空气穿过鼻腔的温度。', pause: 6 },
            { text: '吸气时心中默念“吸”，呼气时默念“呼”，保持节奏。', pause: 6 },
            { text: '如果注意力飘散，再次把它带回到呼吸上。温柔地接纳每一次分心。', pause: 7 },
            { text: '现在，将注意力扩展到身体，感受身体的自然律动。', pause: 5 },
            { text: '最后，慢慢睁开眼睛，带着平静意识，回到当下。', pause: 5 }
        ]
    },
    body: {
        title: '身体扫描',
        durationSeconds: 15 * 60,
        script: [
            { text: '欢迎来到身体扫描练习。请躺下或坐好，双腿伸直，双臂自然放置。', pause: 5 },
            { text: '闭上眼睛，先把注意力带到脚趾，感受它们的轻微触感。', pause: 6 },
            { text: '慢慢将注意力移动到脚背、脚踝、小腿，感受每一处的感知。', pause: 7 },
            { text: '接着，将意识带到膝盖、大腿，感受肌肉的松弛与重量。', pause: 6 },
            { text: '再将注意力移动到臀部和下背部，感受脊柱的自然曲线。', pause: 6 },
            { text: '呼吸进入胸腔，感受胸部的起伏。然后带到肩膀、手臂，直到指尖。', pause: 7 },
            { text: '最后，将注意力移到颈部、下巴、面部，放松眉间和颞部。', pause: 7 },
            { text: '整体扫描一遍，感受全身的统一与平静。', pause: 5 }
        ]
    },
    loving: {
        title: '慈悲冥想',
        durationSeconds: 12 * 60,
        script: [
            { text: '欢迎来到慈悲冥想。请舒适地坐好，闭上眼睛。', pause: 5 },
            { text: '将双手放在胸前，感受心脏区域的温暖。', pause: 6 },
            { text: '默念：愿我安全，愿我健康，愿我快乐，愿我自在。', pause: 8 },
            { text: '现在，将这份善意扩展到身边的人，默念：愿你安全，愿你健康，愿你快乐，愿你自在。', pause: 8 },
            { text: '如果有难以接纳的人，也在心中送出慈悲：愿你能够平静，获得幸福。', pause: 8 },
            { text: '最后，将关注带回自己，感受慈悲带来的温柔流动。', pause: 6 }
        ]
    },
    mindful: {
        title: '觉察冥想',
        durationSeconds: 20 * 60,
        script: [
            { text: '欢迎来到觉察冥想。请找到一个稳定的坐姿，双手自然放置。', pause: 5 },
            { text: '闭上眼睛，将注意力轻放在呼吸上。', pause: 6 },
            { text: '当有想法或情绪出现时，先观察它们，然后轻轻放手，让它们如云般消散。', pause: 8 },
            { text: '保持开放的觉察，不刻意追随任何思绪或感受。', pause: 7 },
            { text: '如果情绪起伏，接纳它的存在，就像接纳天空中的一朵云。', pause: 7 },
            { text: '让当下的每一刻都成为你的练习场，宁静地观察。', pause: 6 },
            { text: '最后，深吸一口气，慢慢睁开眼睛，带着觉察回到日常。', pause: 6 }
        ]
    }
};

function startMeditation(type) {
    const program = meditationPrograms[type];
    document.getElementById('meditation-title').textContent = program.title;
    document.getElementById('meditation-instruction').textContent = program.script[0].text;
    document.getElementById('meditation-time').textContent = '00:00';
    document.getElementById('meditation-play-icon').className = 'fas fa-play';
    document.getElementById('meditation-btn-text').textContent = '开始冥想';
    document.getElementById('meditation-guide').style.display = 'flex';
    stopMeditationSession();
}

function closeMeditationGuide() {
    document.getElementById('meditation-guide').style.display = 'none';
    stopMeditationSession();
}

function toggleMeditation() {
    if (meditationActive) {
        stopMeditationSession();
    } else {
        startMeditationSession();
    }
}

function startMeditationSession() {
    const title = document.getElementById('meditation-title').textContent;
    const program = Object.values(meditationPrograms).find(p => p.title === title);
    if (!program) return;

    meditationActive = true;
    meditationStartTime = Date.now();
    speechIndex = 0;

    document.getElementById('meditation-play-icon').className = 'fas fa-pause';
    document.getElementById('meditation-btn-text').textContent = '暂停冥想';

    // 开始朗读脚本
    speakLine(program);

    // 更新时间显示
    meditationTimer = setInterval(updateMeditationTimer, 1000);

    // 自动结束
    sessionTimeout = setTimeout(() => {
        completeMeditationSession(program.title);
    }, program.durationSeconds * 1000);
}

function speakLine(program) {
    if (speechIndex >= program.script.length) return;
    const { text, pause } = program.script[speechIndex];
    document.getElementById('meditation-instruction').textContent = text;

    const utter = new SpeechSynthesisUtterance(text);
    utter.lang = 'zh-CN';
    utter.rate = 0.9;
    utter.pitch = 1.1;
    utter.volume = 1.0;
    utter.onend = () => {
        setTimeout(() => {
            speechIndex++;
            speakLine(program);
        }, pause * 1000);
    };
    window.speechSynthesis.speak(utter);
}

function stopMeditationSession() {
    meditationActive = false;
    clearInterval(meditationTimer);
    clearTimeout(sessionTimeout);
    meditationTimer = null;
    sessionTimeout = null;
    window.speechSynthesis.cancel();
    document.getElementById('meditation-play-icon').className = 'fas fa-play';
    document.getElementById('meditation-btn-text').textContent = '开始冥想';
}

function updateMeditationTimer() {
    if (!meditationStartTime) return;
    const elapsed = Math.floor((Date.now() - meditationStartTime) / 1000);
    const m = Math.floor(elapsed / 60).toString().padStart(2, '0');
    const s = (elapsed % 60).toString().padStart(2, '0');
    document.getElementById('meditation-time').textContent = `${m}:${s}`;
}

function completeMeditationSession(title) {
    stopMeditationSession();
    document.getElementById('meditation-instruction').textContent = `恭喜完成“${title}”练习，愿您平静安宁。`;
    const utter = new SpeechSynthesisUtterance(`恭喜您完成${title}练习。愿您平静安宁。`);
    utter.lang = 'zh-CN';
    utter.rate = 0.9;
    utter.pitch = 1.1;
    window.speechSynthesis.speak(utter);
}
</script>
@endsection
