@extends('layouts.app')

@section('title', '线下心理课程 - 心理咨询平台')

@section('custom-styles')
<style>
    .offline-courses-list {
        padding: 15px;
    }
    
    .offline-courses-item {
        background-color: #fff;
        border-radius: 10px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        overflow: hidden;
    }
    
    .offline-courses-banner {
        height: 150px;
        overflow: hidden;
        position: relative;
    }
    
    .offline-courses-banner img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    
    .offline-courses-category {
        position: absolute;
        top: 10px;
        left: 10px;
        background-color: rgba(0, 0, 0, 0.6);
        color: #fff;
        padding: 3px 8px;
        border-radius: 3px;
        font-size: 12px;
    }
    
    .offline-courses-content {
        padding: 15px;
    }
    
    .offline-courses-title {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 8px;
        color: #333;
    }
    
    .offline-courses-meta {
        color: #666;
        font-size: 13px;
        margin-bottom: 8px;
        display: flex;
        flex-wrap: wrap;
    }
    
    .offline-courses-meta-item {
        margin-right: 15px;
        display: flex;
        align-items: center;
        margin-bottom: 5px;
    }
    
    .offline-courses-meta-icon {
        margin-right: 5px;
        width: 16px;
        height: 16px;
        background-size: contain;
        background-repeat: no-repeat;
    }
    
    .offline-courses-time-icon {
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23666'%3E%3Cpath d='M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8zm.5-13H11v6l5.25 3.15.75-1.23-4.5-2.67z'/%3E%3C/svg%3E");
    }
    
    .offline-courses-location-icon {
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23666'%3E%3Cpath d='M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z'/%3E%3C/svg%3E");
    }
    
    .offline-courses-lecturer-icon {
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23666'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E");
    }
    
    .offline-courses-description {
        color: #555;
        font-size: 14px;
        line-height: 1.5;
        margin-bottom: 15px;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    
    .offline-courses-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 10px;
        border-top: 1px solid #eee;
    }
    
    .offline-courses-status {
        display: flex;
        align-items: flex-start;
        flex-direction: column;
    }
    
    .offline-courses-status-badge {
        padding: 2px 8px;
        border-radius: 10px;
        font-size: 12px;
        margin-bottom: 5px;
    }
    
    .offline-courses-status-available {
        background-color: #E8F5E9;
        color: #4CAF50;
    }
    
    .offline-courses-status-full {
        background-color: #FFEBEE;
        color: #F44336;
    }
    
    .offline-courses-price {
        color: #ff5722;
        font-weight: bold;
        margin-top: 5px;
    }
    
    .offline-courses-free-badge {
        background-color: #E3F2FD;
        color: #2196F3;
        padding: 2px 6px;
        border-radius: 3px;
        font-size: 12px;
    }
    
    .offline-courses-view-detail-btn {
        background-color: #4CAF50;
        color: white;
        border: none;
        padding: 6px 12px;
        border-radius: 4px;
        font-size: 13px;
        text-decoration: none;
    }
    
    .offline-courses-filter-bar {
        background-color: #f5f5f5;
        padding: 10px 15px;
        margin-bottom: 15px;
        border-radius: 5px;
    }
    
    .offline-courses-filter-title {
        font-weight: bold;
        margin-bottom: 8px;
    }
    
    .offline-courses-filter-options {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
    }
    
    .offline-courses-filter-option {
        background-color: #e0e0e0;
        border-radius: 15px;
        padding: 4px 12px;
        font-size: 12px;
        color: #333;
        text-decoration: none;
    }
    
    .offline-courses-filter-option.active {
        background-color: #4CAF50;
        color: white;
    }
    
    .offline-courses-no-courses {
        text-align: center;
        padding: 30px;
        color: #666;
    }
    
    .offline-courses-page-header {
        display: flex;
        align-items: center;
        padding: 15px;
        background-color: #fff;
        position: sticky;
        top: 0;
        z-index: 100;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    }
    
    .offline-courses-back-button {
        width: 24px;
        height: 24px;
        margin-right: 15px;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23333'%3E%3Cpath d='M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z'/%3E%3C/svg%3E");
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
        cursor: pointer;
    }
</style>
@endsection

@section('content')
<div class="offline-courses-page-header">
    <a href="javascript:goBack()" class="offline-courses-back-button"></a>
    <h1 style="font-size: 18px; margin: 0;">线下心理课程</h1>
</div>

<div class="container">
    <!-- 分类筛选 -->
    <div class="offline-courses-filter-bar">
        <div class="offline-courses-filter-title">分类筛选：</div>
        <div class="offline-courses-filter-options">
            <a href="{{ route('consultation.offline_courses') }}"
               class="offline-courses-filter-option {{ empty($selected) ? 'active' : '' }}">
                全部
            </a>
            @foreach($categories as $key => $label)
                <a href="{{ route('consultation.offline_courses', ['category' => $key]) }}"
                   class="offline-courses-filter-option {{ $selected == $key ? 'active' : '' }}">
                    {{ $label }}
                </a>
            @endforeach
        </div>
    </div>

    <!-- 课程列表 -->
    <div class="offline-courses-list">
        @if($courses->count() > 0)
            @foreach($courses as $course)
                <div class="offline-courses-item">
                    <div class="offline-courses-banner">
                        <img src="{{ $course->image ? asset('storage/'.$course->image) : asset('images/default-course.jpg') }}"
                             alt="{{ $course->title }}">
                        <div class="offline-courses-category">{{ $course->category_text }}</div>
                    </div>
                    <div class="offline-courses-content">
                        <h3 class="offline-courses-title">{{ $course->title }}</h3>
                        <div class="offline-courses-meta">
                            <div class="offline-courses-meta-item">
                                <div class="offline-courses-meta-icon offline-courses-time-icon"></div>
                                {{ date('Y-m-d H:i', strtotime($course->start_time)) }}
                            </div>
                            <div class="offline-courses-meta-item">
                                <div class="offline-courses-meta-icon offline-courses-location-icon"></div>
                                {{ $course->location ?: '待定' }}
                            </div>
                            <div class="offline-courses-meta-item">
                                <div class="offline-courses-meta-icon offline-courses-lecturer-icon"></div>
                                {{ $course->lecturer ? $course->lecturer->name : '待定' }}
                            </div>
                        </div>
                        <div class="offline-courses-description">
                            {{ Str::limit(strip_tags($course->description), 100) ?: '暂无课程简介' }}
                        </div>
                        <div class="offline-courses-footer">
                            <div class="offline-courses-status">
                                @if($course->isFullyBooked())
                                    <span class="offline-courses-status-badge offline-courses-status-full">名额已满</span>
                                @else
                                    <span class="offline-courses-status-badge offline-courses-status-available">可报名</span>
                                @endif
                                <div style="font-size:12px;color:#666;margin-left:8px;">
                                    限额：{{ $course->max_participants }}人 (已报名：{{ $course->current_participants }}人)
                                </div>
                                <div class="offline-courses-price">
                                    @if($course->price > 0)
                                        {{ '¥' . $course->price }}
                                    @else
                                        <span class="offline-courses-free-badge">免费</span>
                                    @endif
                                </div>
                            </div>
                            <a href="{{ route('consultation.offline_course_detail', ['id' => $course->id]) }}"
                               class="offline-courses-view-detail-btn">查看详情</a>
                        </div>
                    </div>
                </div>
            @endforeach

            <!-- 分页 -->
            <div class="pagination-container">
                {{ $courses->appends(request()->query())->links() }}
            </div>
        @else
            <div class="offline-courses-no-courses">
                <p>暂无符合条件的课程</p>
            </div>
        @endif
    </div>
</div>
@endsection

<script>
function goBack() {
    if (document.referrer && document.referrer !== location.href) {
        window.history.back();
    } else {
        window.location.href = "{{ route('consultation.counselors') }}";
    }
}
</script>
