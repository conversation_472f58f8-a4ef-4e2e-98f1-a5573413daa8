<?php

namespace App\Http\Controllers;

use App\Models\Hotline;
use App\Models\HotlineRecord;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class HotlineController extends Controller
{
    /**
     * 显示心理援助热线列表
     */
    public function index()
    {
        $hotlines = Hotline::enabled()->ordered()->paginate(10);
        return view('pages.hotline', compact('hotlines'));
    }
    
    /**
     * 记录热线使用情况
     */
    public function recordUsage(Request $request)
    {
        $request->validate([
            'hotline_id' => 'required|exists:hotlines,id',
            'action' => 'required|in:call,chat',
        ]);
        
        $record = new HotlineRecord([
            'hotline_id' => $request->hotline_id,
            'user_id' => Auth::id(),
            'action' => $request->action,
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
        ]);
        
        $record->save();
        
        return response()->json([
            'success' => true,
            'message' => '记录成功'
        ]);
    }
}
