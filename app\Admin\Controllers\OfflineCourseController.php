<?php

namespace App\Admin\Controllers;

use App\Models\CounselorLx;
use App\Models\OfflineCourse;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Carbon\Carbon;

class OfflineCourseController extends AdminController
{
    /**
     * 设置标题
     */
    protected $title = '线下课程管理';

    /**
     * 列表页面
     */
    protected function grid()
    {
        return Grid::make(new OfflineCourse(), function (Grid $grid) {
            $grid->model()->with('lecturerlx');
            
            $grid->column('id')->sortable();
            $grid->column('image', '课程图片')->image('', 50, 50);
            $grid->column('title', '课程名称');
            $grid->column('lecturerlx.name', '讲师');
            // 新增分类列
            $grid->column('category', '分类')->using(OfflineCourse::getCategories());
            $grid->column('start_time', '开始时间');
            $grid->column('end_time', '结束时间');
            $grid->column('location', '上课地点');
            $grid->column('registration_deadline', '报名截止时间');
            
            $grid->column('participants', '报名情况')->display(function () {
                return "{$this->current_participants}/{$this->max_participants}";
            });
            
            $grid->column('price', '价格')->display(function ($price) {
                return $price > 0 ? '¥' . $price : '免费';
            });
            
            $grid->column('status', '状态')->display(function ($status) {
                if ($status == OfflineCourse::STATUS_PENDING) {
                    return '<span class="badge badge-warning">待发布</span>';
                } elseif ($status == OfflineCourse::STATUS_PUBLISHED) {
                    if (Carbon::now()->gt($this->end_time)) {
                        return '<span class="badge badge-info">已结束</span>';
                    } elseif (Carbon::now()->gt($this->registration_deadline)) {
                        return '<span class="badge badge-secondary">报名截止</span>';
                    } else {
                        return '<span class="badge badge-success">报名中</span>';
                    }
                } elseif ($status == OfflineCourse::STATUS_FINISHED) {
                    return '<span class="badge badge-info">已结束</span>';
                }
                return '<span class="badge badge-dark">未知</span>';
            });
            
            $grid->column('created_at', '创建时间')->sortable();
            
            // 筛选功能
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');
                $filter->like('title', '课程名称');
                $filter->equal('lecturer_id', '讲师')->select(CounselorLx::all()->pluck('name', 'id'));
                // 新增分类筛选
                $filter->equal('category', '分类')->select(OfflineCourse::getCategories());
                $filter->equal('status', '状态')->select(OfflineCourse::getStatusMap());
                $filter->like('location', '地点');
                $filter->date('start_time', '开始日期');
            });
            
            // 默认按开课时间排序
            $grid->model()->orderBy('start_time', 'desc');
            
            // 添加查看报名记录按钮
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->append('<a href="' . admin_url('course_registrations?course_id=' . $actions->row->id) . '" class="btn btn-sm btn-primary">查看报名</a>');
            });
        });
    }

    /**
     * 详情页面
     */
    protected function detail($id)
    {
        return Show::make($id, new OfflineCourse(), function (Show $show) {
            $show->field('id');
            $show->field('image', '课程图片')->image();
            $show->field('title', '课程名称');
            $show->field('lecturer.name', '讲师');
            // 显示分类
            $show->field('category', '分类')->using(OfflineCourse::getCategories());
            $show->field('description', '课程描述')->unescape()->as(function ($description) {
                return $description ? "<div style='max-width:600px'>$description</div>" : '';
            });
            $show->field('outline', '课程大纲')->unescape()->as(function ($outline) {
                return $outline ? "<div style='max-width:600px'>$outline</div>" : '';
            });
            $show->field('location', '上课地点');
            $show->field('start_time', '开始时间');
            $show->field('end_time', '结束时间');
            $show->field('duration', '课程时长');
            $show->field('registration_deadline', '报名截止时间');
            $show->field('max_participants', '最大参与人数');
            $show->field('current_participants', '当前参与人数');
            $show->field('price', '课程价格');
            $show->field('status', '状态')->using(OfflineCourse::getStatusMap());
            $show->field('created_at', '创建时间');
            $show->field('updated_at', '更新时间');
        });
    }

    /**
     * 表单页面
     */
    protected function form()
    {
        return Form::make(new OfflineCourse(), function (Form $form) {
            $form->display('id');
            $form->text('title', '课程名称')->required();
            $form->image('image', '课程图片')
                ->uniqueName()
                ->autoUpload()
                ->required();
            $form->select('lecturer_id', '讲师')
                ->options(CounselorLx::all()->pluck('name', 'id'))
                ->required();

            // 新增分类下拉
            $form->select('category', '分类')
                ->options(OfflineCourse::getCategories())
                ->required();

            $form->editor('description', '课程描述')->required();
            $form->editor('outline', '课程大纲')->help('填写课程大纲，可以使用列表形式');
            $form->text('location', '上课地点')->required();
            $form->datetime('start_time', '开始时间')->required();
            $form->datetime('end_time', '结束时间')->required();
            $form->datetime('registration_deadline', '报名截止时间')->required();
            $form->number('max_participants', '最大参与人数')->min(1)->default(30)->required();

            if (!$form->isCreating()) {
                $form->display('current_participants', '当前参与人数');
            }

            $form->currency('price', '课程价格')->symbol('￥')->default(0);
            $form->radio('status', '状态')->options(OfflineCourse::getStatusMap())->default(OfflineCourse::STATUS_PENDING);

            // 时间逻辑验证
            $form->saving(function (Form $form) {
                if ($form->start_time && $form->end_time && $form->registration_deadline) {
                    $startTime = Carbon::parse($form->start_time);
                    $endTime = Carbon::parse($form->end_time);
                    $deadline = Carbon::parse($form->registration_deadline);

                    if ($endTime->lte($startTime)) {
                        return $form->response()->error('结束时间必须晚于开始时间');
                    }
                    if ($deadline->gt($startTime)) {
                        return $form->response()->error('报名截止时间必须早于课程开始时间');
                    }
                }
                if ($form->end_time && Carbon::parse($form->end_time)->lt(Carbon::now())) {
                    $form->status = OfflineCourse::STATUS_FINISHED;
                }
            });

            $form->display('created_at', '创建时间');
            $form->display('updated_at', '更新时间');
        });
    }
}
