@extends('layouts.app')

@section('title', '课程 - 在线教育平台')

@section('custom-styles')
<style>
    .courses-header {
        background-color: #fff;
        padding: 15px;
        margin-bottom: 15px;
        border-radius: 8px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    }
    
    .courses-tabs {
        display: flex;
        border-bottom: 1px solid #f0f0f0;
        margin-bottom: 15px;
    }
    
    .courses-tab {
        padding: 10px 15px;
        font-size: 14px;
        color: #666;
        position: relative;
        cursor: pointer;
    }
    
    .courses-tab.active {
        color: #1890ff;
        font-weight: bold;
    }
    
    .courses-tab.active::after {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 50%;
        transform: translateX(-50%);
        width: 20px;
        height: 2px;
        background-color: #1890ff;
    }
    
    .filter-options {
        display: flex;
        justify-content: space-between;
        padding: 10px 0;
    }
    
    .filter-option {
        font-size: 12px;
        color: #666;
        display: flex;
        align-items: center;
    }
    
    .filter-option i {
        margin-left: 3px;
        font-size: 10px;
    }
    
    .course-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
        padding: 15px;
    }
    
    .course-card {
        background-color: #fff;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    }
    
    .course-image {
        width: 100%;
        height: 100px;
        background-size: cover;
        background-position: center;
    }
    
    .course-info {
        padding: 10px;
    }
    
    .course-title {
        font-size: 14px;
        font-weight: bold;
        margin-bottom: 5px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
    }
    
    .course-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 12px;
        color: #999;
    }
    
    .course-price {
        color: #ff6b6b;
        font-weight: bold;
    }
    
    .course-free {
        color: #52c41a;
        font-weight: bold;
    }
    
    .search-bar {
        display: flex;
        align-items: center;
        background-color: #fff;
        padding: 10px 15px;
        position: sticky;
        top: 0;
        z-index: 100;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        margin-bottom: 15px;
    }
    
    .search-input {
        flex: 1;
        height: 36px;
        border-radius: 18px;
        border: none;
        background-color: #f5f5f5;
        padding: 0 15px;
        font-size: 14px;
        color: #333;
    }
    
    .search-input::placeholder {
        color: #999;
    }
</style>
@endsection

@section('content')
<div class="search-bar">
    <input type="text" class="search-input" placeholder="搜索课程、讲师">
</div>

<div class="container">
    <div class="courses-header">
        <div class="courses-tabs">
            <div class="courses-tab active">全部课程</div>
            <div class="courses-tab">精品课程</div>
            <div class="courses-tab">免费课程</div>
            <div class="courses-tab">直播课程</div>
        </div>
        
        <div class="filter-options">
            <div class="filter-option">
                综合排序 <i class="fas fa-chevron-down"></i>
            </div>
            <div class="filter-option">
                最新 <i class="fas fa-chevron-down"></i>
            </div>
            <div class="filter-option">
                价格 <i class="fas fa-chevron-down"></i>
            </div>
        </div>
    </div>
    
    <div class="course-grid">
        <div class="course-card">
            <div class="course-image" style="background-image: url('https://via.placeholder.com/300x200/1890ff/ffffff?text=Course+1')"></div>
            <div class="course-info">
                <div class="course-title">Web前端开发进阶实战课程</div>
                <div class="course-meta">
                    <span>1.2万人学习</span>
                    <span class="course-price">¥299</span>
                </div>
            </div>
        </div>
        <div class="course-card">
            <div class="course-image" style="background-image: url('https://via.placeholder.com/300x200/ff6b6b/ffffff?text=Course+2')"></div>
            <div class="course-info">
                <div class="course-title">Python数据分析与可视化</div>
                <div class="course-meta">
                    <span>8.5千人学习</span>
                    <span class="course-price">¥199</span>
                </div>
            </div>
        </div>
        <div class="course-card">
            <div class="course-image" style="background-image: url('https://via.placeholder.com/300x200/52c41a/ffffff?text=Course+3')"></div>
            <div class="course-info">
                <div class="course-title">UI设计入门到精通</div>
                <div class="course-meta">
                    <span>5.3千人学习</span>
                    <span class="course-price">¥399</span>
                </div>
            </div>
        </div>
        <div class="course-card">
            <div class="course-image" style="background-image: url('https://via.placeholder.com/300x200/faad14/ffffff?text=Course+4')"></div>
            <div class="course-info">
                <div class="course-title">零基础学英语口语</div>
                <div class="course-meta">
                    <span>3.7千人学习</span>
                    <span class="course-free">免费</span>
                </div>
            </div>
        </div>
        <div class="course-card">
            <div class="course-image" style="background-image: url('https://via.placeholder.com/300x200/722ed1/ffffff?text=Course+5')"></div>
            <div class="course-info">
                <div class="course-title">Flutter跨平台应用开发</div>
                <div class="course-meta">
                    <span>2.1千人学习</span>
                    <span class="course-price">¥499</span>
                </div>
            </div>
        </div>
        <div class="course-card">
            <div class="course-image" style="background-image: url('https://via.placeholder.com/300x200/eb2f96/ffffff?text=Course+6')"></div>
            <div class="course-info">
                <div class="course-title">产品经理实战训练营</div>
                <div class="course-meta">
                    <span>1.8千人学习</span>
                    <span class="course-price">¥599</span>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
