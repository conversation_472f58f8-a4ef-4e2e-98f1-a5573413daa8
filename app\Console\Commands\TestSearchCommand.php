<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\ContentIndex;
use App\Models\Article;
use App\Models\Video;
use App\Models\CourseLesson;
use App\Services\ContentSearchService;
use App\Services\AlibabaVectorService;
use App\Services\ContentIndexService;
use Exception;

class TestSearchCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'search:test {--query=孩子教育 : 测试搜索的关键词} {--detailed : 显示详细信息}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '一站式搜索功能测试命令';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔍 开始搜索功能全面测试...');
        $this->newLine();

        // 1. 基础数据检查
        $this->checkBasicData();
        
        // 2. 向量服务检查
        $this->checkVectorService();
        
        // 3. DashVector 连接检查
        $this->checkDashVectorConnection();
        
        // 4. 内容索引状态检查
        $this->checkContentIndexStatus();
        
        // 5. 搜索功能测试
        $this->testSearchFunction();
        
        // 6. 相似内容测试
        $this->testSimilarContent();
        
        // 7. 问题诊断和建议
        $this->provideDiagnosisAndSuggestions();

        $this->newLine();
        $this->info('✅ 测试完成！');
    }

    private function checkBasicData()
    {
        $this->info('📊 1. 基础数据检查');
        $this->line('─────────────────────');

        $totalContent = ContentIndex::count();
        $withVectors = ContentIndex::where('vector_generated', true)->count();
        $activeContent = ContentIndex::where('is_active', true)->count();

        $this->line("总内容索引数量: {$totalContent}");
        $this->line("已生成向量数量: {$withVectors}");
        $this->line("激活状态数量: {$activeContent}");

        if ($totalContent === 0) {
            $this->error('❌ 没有任何内容索引！请先运行: php artisan content:index');
            return;
        }

        if ($withVectors === 0) {
            $this->error('❌ 没有生成任何向量！请运行: php artisan content:index --vector');
            return;
        }

        // 按类型统计
        $byType = ContentIndex::selectRaw('type, count(*) as total, sum(case when vector_generated then 1 else 0 end) as with_vectors')
            ->groupBy('type')
            ->get();

        $this->line("\n按类型统计:");
        foreach ($byType as $stat) {
            $coverage = $stat->total > 0 ? round(($stat->with_vectors / $stat->total) * 100, 1) : 0;
            $this->line("  {$stat->type}: {$stat->with_vectors}/{$stat->total} ({$coverage}%)");
        }

        $this->newLine();
    }

    private function checkVectorService()
    {
        $this->info('🔧 2. 向量服务检查');
        $this->line('─────────────────────');

        try {
            $vectorService = app(AlibabaVectorService::class);
            
            // 健康检查
            $health = $vectorService->healthCheck();
            $this->line("服务健康状态: " . ($health ? '✅ 正常' : '❌ 异常'));

            // 测试向量生成
            $testText = '这是一个测试文本';
            $testVector = $vectorService->generateTextVector($testText);
            
            if (!empty($testVector)) {
                $this->line("向量生成测试: ✅ 成功 (维度: " . count($testVector) . ")");
            } else {
                $this->error("向量生成测试: ❌ 失败");
            }

        } catch (Exception $e) {
            $this->error("向量服务错误: " . $e->getMessage());
        }

        $this->newLine();
    }

    private function checkDashVectorConnection()
    {
        $this->info('🌐 3. DashVector 连接检查');
        $this->line('─────────────────────');

        try {
            $vectorService = app(AlibabaVectorService::class);
            $stats = $vectorService->getCollectionStats();
            
            $this->line("DashVector 连接: ✅ 正常");
            
            if ($this->option('detailed')) {
                $this->line("Collection 统计:");
                if (is_array($stats)) {
                    foreach ($stats as $key => $value) {
                        if (is_array($value) || is_object($value)) {
                            $this->line("  {$key}: " . json_encode($value, JSON_UNESCAPED_UNICODE));
                        } else {
                            $this->line("  {$key}: {$value}");
                        }
                    }
                } else {
                    $this->line("  统计信息: " . json_encode($stats, JSON_UNESCAPED_UNICODE));
                }
            }

            // 检查 Collection 中是否有数据
            $this->checkDashVectorData($vectorService);

        } catch (Exception $e) {
            $this->error("DashVector 连接错误: " . $e->getMessage());
            $this->line("请检查 .env 中的 DashVector 配置:");
            $this->line("  DASHVECTOR_API_KEY");
            $this->line("  DASHVECTOR_ENDPOINT");
            $this->line("  DASHVECTOR_COLLECTION_NAME");
        }

        $this->newLine();
    }

    private function checkDashVectorData($vectorService)
    {
        $this->line("\n检查 DashVector 中的数据:");
        
        try {
            // 尝试查询一些数据来验证是否有向量存储
            $testVector = array_fill(0, 1536, 0.1); // 创建一个测试向量
            $results = $vectorService->queryDocs($testVector, 5, [], true);
            
            if (empty($results)) {
                $this->warn("  ❌ DashVector 中没有找到任何数据");
                $this->line("  建议运行: php artisan content:sync-vectors");
            } else {
                $this->line("  ✅ DashVector 中有 " . count($results) . " 条数据");
                if ($this->option('detailed')) {
                    $this->line("  前几条数据:");
                    foreach (array_slice($results, 0, 3) as $i => $result) {
                        $title = $result['fields']['title'] ?? 'N/A';
                        $score = isset($result['score']) ? round($result['score'], 3) : 'N/A';
                        $this->line("    " . ($i+1) . ". {$title} (score: {$score})");
                    }
                }
            }
        } catch (Exception $e) {
            $this->warn("  ❌ 无法查询 DashVector 数据: " . $e->getMessage());
        }
    }

    private function checkContentIndexStatus()
    {
        $this->info('📝 4. 内容索引状态检查');
        $this->line('─────────────────────');

        $query = $this->option('query');
        
        // 查找包含关键词的内容
        $matchingContent = ContentIndex::where(function($q) use ($query) {
            $q->where('title', 'like', "%{$query}%")
              ->orWhere('summary', 'like', "%{$query}%");
        })->get();

        $this->line("包含关键词 '{$query}' 的内容:");
        
        if ($matchingContent->isEmpty()) {
            $this->warn("❌ 没有找到包含关键词的内容");
            
            // 显示一些示例内容
            $sampleContent = ContentIndex::take(5)->get(['id', 'title', 'type', 'vector_generated']);
            $this->line("\n现有内容示例:");
            foreach ($sampleContent as $content) {
                $vectorStatus = $content->vector_generated ? '✅' : '❌';
                $this->line("  [{$content->type}] {$content->title} {$vectorStatus}");
            }
        } else {
            foreach ($matchingContent as $content) {
                $vectorStatus = $content->vector_generated ? '✅' : '❌';
                $activeStatus = $content->is_active ? '✅' : '❌';
                $this->line("  ID:{$content->id} [{$content->type}] {$content->title}");
                $this->line("    向量: {$vectorStatus} | 激活: {$activeStatus}");
            }
        }

        $this->newLine();
    }

    private function testSearchFunction()
    {
        $this->info('🔍 5. 搜索功能测试');
        $this->line('─────────────────────');

        try {
            $searchService = app(ContentSearchService::class);
            $query = $this->option('query');

            // 测试不同的搜索参数
            $testCases = [
                ['query' => $query, 'options' => [], 'name' => '默认搜索'],
                ['query' => $query, 'options' => ['similarity_threshold' => 0.5], 'name' => '降低阈值搜索'],
                ['query' => $query, 'options' => ['types' => ['article']], 'name' => '仅搜索文章'],
                ['query' => $query, 'options' => ['limit' => 20], 'name' => '增加结果数量'],
            ];

            foreach ($testCases as $test) {
                $this->line("\n{$test['name']}:");
                
                $results = $searchService->search($test['query'], $test['options']);
                $this->line("  结果数量: " . count($results));
                
                if (!empty($results)) {
                    $this->line("  前3个结果:");
                    foreach (array_slice($results, 0, 3) as $i => $result) {
                        $similarity = isset($result['similarity']) ? round($result['similarity'], 3) : 'N/A';
                        $this->line("    " . ($i+1) . ". {$result['title']} (相似度: {$similarity})");
                    }
                } else {
                    $this->warn("    ❌ 无结果");
                }
            }

        } catch (Exception $e) {
            $this->error("搜索测试失败: " . $e->getMessage());
        }

        $this->newLine();
    }

    private function testSimilarContent()
    {
        $this->info('🎯 6. 相似内容测试');
        $this->line('─────────────────────');

        try {
            $searchService = app(ContentSearchService::class);
            
            // 找一个有向量的内容进行相似度测试
            $contentWithVector = ContentIndex::where('vector_generated', true)
                ->where('is_active', true)
                ->first();

            if (!$contentWithVector) {
                $this->warn("❌ 没有找到有向量的内容进行相似度测试");
                return;
            }

            $this->line("测试内容: {$contentWithVector->title}");
            
            // 获取模型实例
            $model = $contentWithVector->indexable;
            if (!$model) {
                $this->warn("❌ 无法获取关联模型");
                return;
            }

            $similar = $searchService->recommendSimilar(
                $model->id, 
                $contentWithVector->type,
                ['limit' => 5, 'similarity_threshold' => 0.3]
            );

            $this->line("相似内容数量: " . count($similar));
            
            if (!empty($similar)) {
                $this->line("相似内容列表:");
                foreach ($similar as $i => $item) {
                    $similarity = isset($item['similarity']) ? round($item['similarity'], 3) : 'N/A';
                    $this->line("  " . ($i+1) . ". {$item['title']} (相似度: {$similarity})");
                }
            }

        } catch (Exception $e) {
            $this->error("相似内容测试失败: " . $e->getMessage());
        }

        $this->newLine();
    }

    private function provideDiagnosisAndSuggestions()
    {
        $this->info('💡 7. 问题诊断和建议');
        $this->line('─────────────────────');

        $totalContent = ContentIndex::count();
        $withVectors = ContentIndex::where('vector_generated', true)->count();
        $activeContent = ContentIndex::where('is_active', true)->count();

        $suggestions = [];

        if ($totalContent === 0) {
            $suggestions[] = "运行索引命令: php artisan content:index";
        }

        if ($withVectors === 0) {
            $suggestions[] = "生成向量数据: php artisan content:index --vector";
        }

        if ($withVectors < $totalContent) {
            $suggestions[] = "补充向量生成: php artisan content:index --vector --force";
        }

        if ($activeContent < $totalContent) {
            $suggestions[] = "检查内容激活状态: 确保 is_active = true";
        }

        // 检查配置
        $vectorEnabled = config('content_search.vector.enabled', false);
        if (!$vectorEnabled) {
            $suggestions[] = "启用向量功能: 在 .env 中设置 CONTENT_VECTOR_ENABLED=true";
        }

        if (empty($suggestions)) {
            $this->line("✅ 系统状态良好，如果搜索仍无结果，可能是:");
            $this->line("  1. 相似度阈值过高 (默认 0.7)");
            $this->line("  2. 查询词与内容语义差异较大");
            $this->line("  3. DashVector 数据同步延迟");
            
            $this->line("\n尝试以下命令:");
            $this->line("  php artisan search:test --query='其他关键词'");
        } else {
            $this->line("建议执行以下操作:");
            foreach ($suggestions as $i => $suggestion) {
                $this->line("  " . ($i+1) . ". {$suggestion}");
            }
        }

        // 显示有用的调试命令
        $this->line("\n🛠️  调试命令:");
        $this->line("  php artisan content:index --dry-run  # 查看待索引内容");
        $this->line("  php artisan search:test --detailed   # 详细测试信息");
        $this->line("  php artisan tinker                   # 手动调试");

        $this->newLine();
    }
} 