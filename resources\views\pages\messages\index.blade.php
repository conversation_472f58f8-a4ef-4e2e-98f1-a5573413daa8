@extends('layouts.app')

@section('title', '消息中心 - 心理咨询平台')

@section('custom-styles')
<style>
    body {
        background-color: #f8f9fa;
    }
    
    .container {
        max-width: 900px;
        margin: 0 auto;
        padding-bottom: 80px !important; /* 为底部菜单栏预留空间 */
    }
    
    .page-header {
        display: flex;
        align-items: center;
        background: linear-gradient(135deg, #5b7cef 0%, #3c67e3 100%);
        color: white;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        border-radius: 0 0 10px 10px;
        padding: 12px 15px;
        margin-bottom: 15px;
    }
    
    .page-header h1 {
        font-size: 18px;
        font-weight: 500;
        margin: 0 auto;
        text-align: center;
        flex-grow: 1;
        text-shadow: 0 1px 2px rgba(0,0,0,0.1);
    }
    
    .chat-list {
        padding: 15px;
    }
    
    .chat-item {
        display: flex;
        align-items: center;
        background-color: #fff;
        border-radius: 12px;
        padding: 15px;
        margin-bottom: 12px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        position: relative;
        transition: transform 0.2s ease;
        border: 1px solid rgba(0, 0, 0, 0.03);
    }
    
    .chat-item:active {
        transform: scale(0.98);
        background-color: #f9f9f9;
    }
    
    .avatar {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        overflow: hidden;
        margin-right: 15px;
        flex-shrink: 0;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        border: 2px solid #fff;
    }
    
    .avatar img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    
    .chat-info {
        flex-grow: 1;
        overflow: hidden;
    }
    
    .chat-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 5px;
    }
    
    .counselor-name {
        font-weight: 600;
        font-size: 16px;
        color: #333;
    }
    
    .chat-time {
        font-size: 12px;
        color: #999;
    }
    
    .last-message {
        font-size: 14px;
        color: #666;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 100%;
    }
    
    .unread-badge {
        background-color: #ff3b30;
        color: white;
        font-size: 12px;
        width: 20px;
        height: 20px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 10px;
        font-weight: bold;
    }
    
    .appointment-info {
        font-size: 12px;
        color: #3c67e3;
        margin-top: 3px;
    }
    
    .no-messages {
        text-align: center;
        padding: 40px 30px;
        color: #666;
        background-color: #fff;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        margin-top: 20px;
    }
    
    .empty-message-icon {
        font-size: 60px;
        color: #ccc;
        margin-bottom: 15px;
    }
    
    .start-btn {
        background-color: #3c67e3;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 8px;
        font-size: 14px;
        text-decoration: none;
        display: inline-block;
        margin-top: 15px;
        font-weight: 500;
        box-shadow: 0 2px 4px rgba(60, 103, 227, 0.2);
    }
</style>
@endsection

@section('content')
<div class="page-header">
    <h1>消息中心</h1>
</div>

<div class="container">
    <div class="chat-list">
        @if(count($counselors) > 0)
            @foreach($counselors as $counselor)
                <a href="{{ route('messages.show', ['counselorId' => $counselor->id]) }}" style="text-decoration: none; color: inherit;">
                    <div class="chat-item">
                        <div class="avatar">
                            <img src="{{ $counselor->avatar ? asset('storage/'.$counselor->avatar) : asset('images/default-avatar.jpg') }}" alt="{{ $counselor->name }}">
                        </div>
                        <div class="chat-info">
                            <div class="chat-header">
                                <div class="counselor-name">{{ $counselor->name }}</div>
                                @if(isset($lastMessages[$counselor->id]))
                                    <div class="chat-time">{{ \Carbon\Carbon::parse($lastMessages[$counselor->id]->created_at)->format('H:i') }}</div>
                                @endif
                            </div>
                            <div class="last-message">
                                @if(isset($lastMessages[$counselor->id]))
                                    {{ \Illuminate\Support\Str::limit($lastMessages[$counselor->id]->content, 30) }}
                                @else
                                    <span style="color: #999;">暂无消息</span>
                                @endif
                            </div>
                            
                            @php
                                $appointment = $appointments->where('counselor_id', $counselor->id)->first();
                            @endphp
                            
                            @if($appointment)
                                <div class="appointment-info">
                                    预约时间: {{ \Carbon\Carbon::parse($appointment->appointment_time)->format('m-d H:i') }}
                                </div>
                            @endif
                        </div>
                        @if(isset($unreadCounts[$counselor->id]) && $unreadCounts[$counselor->id] > 0)
                            <div class="unread-badge">{{ $unreadCounts[$counselor->id] > 99 ? '99+' : $unreadCounts[$counselor->id] }}</div>
                        @endif
                    </div>
                </a>
            @endforeach
        @else
            <div class="no-messages">
                <div class="empty-message-icon">💬</div>
                <p>暂无消息记录</p>
                <a href="{{ route('consultation.counselors') }}" class="start-btn">去找咨询师</a>
            </div>
        @endif
    </div>
</div>
@endsection
