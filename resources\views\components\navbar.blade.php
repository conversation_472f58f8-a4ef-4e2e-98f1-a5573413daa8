<!-- 底部浮动导航栏 -->
<div class="tabbar">
    <div class="tabbar-item {{ request()->is('/') ? 'active' : '' }}">
        <a href="{{ route('home') }}">
            <i class="tabbar-icon icon-home"></i>
            <span class="tabbar-text">首页</span>
        </a>
    </div>
    <div class="tabbar-item {{ request()->is('knowledge/courses*') ? 'active' : '' }}">
        <a href="{{ route('knowledge.courses') }}">
            <i class="tabbar-icon icon-courses"></i>
            <span class="tabbar-text">心理课堂</span>
        </a>
    </div>
    
    <!-- 突出的消息中心按钮 -->
    <!-- 突出的消息中心按钮 -->
    <div class="tabbar-item tabbar-item-message {{ request()->is('messages*') ? 'active' : '' }}">
      <a href="{{ route('messages.index') }}" class="message-btn">
        <div 
          class="message-btn-inner"
          style="
            width:44px !important;
            height:44px !important;
            border-radius:9999px !important;
            overflow:hidden !important;
            background: linear-gradient(135deg, #4a8fff 0%, #3567df 100%) !important;
            display:flex !important;
            align-items:center !important;
            justify-content:center !important;
            box-shadow:0 2px 6px rgba(60,103,227,0.2) !important;
            border:2px solid rgba(255,255,255,0.9) !important;
            margin-bottom:2px;
          "
        >
          <i class="tabbar-icon icon-message" style="width:22px;height:22px;margin:0;"></i>
          @if(session('unread_messages') && session('unread_messages') > 0)
            <span class="message-badge" style="
              position:absolute;top:0;right:0;
              min-width:16px;height:16px;border-radius:8px;
              background:#ff3b30;color:#fff;
              font-size:9px;font-weight:500;
              display:flex;align-items:center;justify-content:center;
              padding:0 4px;box-shadow:0 0 1px rgba(0,0,0,0.15);
              border:1px solid rgba(255,255,255,0.8);
              transform:scale(0.9);
            ">
              {{ session('unread_messages')>99?'99+':session('unread_messages') }}
            </span>
          @endif
        </div>
        <span class="tabbar-text">消息</span>
      </a>
    </div>
    
    <div class="tabbar-item {{ request()->is('study*') ? 'active' : '' }}">
        <a href="{{ route('study') }}">
            <i class="tabbar-icon icon-study"></i>
            <span class="tabbar-text">学习</span>
        </a>
    </div>
    <div class="tabbar-item {{ request()->is('my*') ? 'active' : '' }}">
        <a href="{{ route('my') }}">
            <i class="tabbar-icon icon-my"></i>
            <span class="tabbar-text">我的</span>
        </a>
    </div>
</div>

<style>
    .tabbar {
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 50px;
        background-color: #fff;
        display: flex;
        justify-content: space-around;
        align-items: center;
        box-shadow: 0 -1px 0 0 rgba(0,0,0,.05);
        z-index: 999;
        box-sizing: border-box;
        padding: 0;
    }
    
    .tabbar-item {
        flex: 1;
        text-align: center;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        max-width: 25%;
    }
    
    .tabbar-item a {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-decoration: none;
        color: #999;
        font-size: 10px;
        width: 100%;
        height: 100%;
        justify-content: center;
        padding: 5px 0;
        box-sizing: border-box;
    }
    
    .tabbar-item.active a {
        color: #4e9cff;
    }
    
    .tabbar-icon {
        width: 24px;
        height: 24px;
        margin-bottom: 2px;
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
    }
    
    .icon-home {
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999'%3E%3Cpath d='M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z'/%3E%3C/svg%3E");
    }
    
    .tabbar-item.active .icon-home {
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234e9cff'%3E%3Cpath d='M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z'/%3E%3C/svg%3E");
    }
    
    .icon-courses {
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999'%3E%3Cpath d='M21 5c-1.11-.35-2.33-.5-3.5-.5-1.95 0-4.05.4-5.5 1.5-1.45-1.1-3.55-1.5-5.5-1.5S2.45 4.9 1 6v14.65c0 .25.25.5.5.5.1 0 .15-.05.25-.05C3.1 20.45 5.05 20 6.5 20c1.95 0 4.05.4 5.5 1.5 1.35-.85 3.8-1.5 5.5-1.5 1.65 0 3.35.3 4.75 1.05.1.05.15.05.25.05.25 0 .5-.25.5-.5V6c-.6-.45-1.25-.75-2-1zm0 13.5c-1.1-.35-2.3-.5-3.5-.5-1.7 0-4.15.65-5.5 1.5V8c1.35-.85 3.8-1.5 5.5-1.5 1.2 0 2.4.15 3.5.5v11.5z'/%3E%3C/svg%3E");
    }
    
    .tabbar-item.active .icon-courses {
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234e9cff'%3E%3Cpath d='M21 5c-1.11-.35-2.33-.5-3.5-.5-1.95 0-4.05.4-5.5 1.5-1.45-1.1-3.55-1.5-5.5-1.5S2.45 4.9 1 6v14.65c0 .25.25.5.5.5.1 0 .15-.05.25-.05C3.1 20.45 5.05 20 6.5 20c1.95 0 4.05.4 5.5 1.5 1.35-.85 3.8-1.5 5.5-1.5 1.65 0 3.35.3 4.75 1.05.1.05.15.05.25.05.25 0 .5-.25.5-.5V6c-.6-.45-1.25-.75-2-1zm0 13.5c-1.1-.35-2.3-.5-3.5-.5-1.7 0-4.15.65-5.5 1.5V8c1.35-.85 3.8-1.5 5.5-1.5 1.2 0 2.4.15 3.5.5v11.5z'/%3E%3C/svg%3E");
    }
    
    .icon-study {
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999'%3E%3Cpath d='M5 13.18v4L12 21l7-3.82v-4L12 17l-7-3.82zM12 3L1 9l11 6 9-4.91V17h2V9L12 3z'/%3E%3C/svg%3E");
    }
    
    .tabbar-item.active .icon-study {
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234e9cff'%3E%3Cpath d='M5 13.18v4L12 21l7-3.82v-4L12 17l-7-3.82zM12 3L1 9l11 6 9-4.91V17h2V9L12 3z'/%3E%3C/svg%3E");
    }
    
    .icon-my {
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E");
    }
    
    .tabbar-item.active .icon-my {
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234e9cff'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E");
    }
    
    /* 消息按钮样式 - 苹果风格优化 */
    .tabbar-item-message {
        position: relative;
        margin-top: -20px; /* 减少突出高度 */
    }
    
    .message-btn-inner {
        width: 44px; /* 减小圆圈大小 */
        height: 44px;
        border-radius: 50%;
        background: linear-gradient(135deg, #4a8fff 0%, #3567df 100%); /* 柔和的蓝色渐变 */
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 2px;
        box-shadow: 0 2px 6px rgba(60, 103, 227, 0.2); /* 减少阴影强度 */
        position: relative;
        transition: all 0.2s ease;
        border: 2px solid rgba(255, 255, 255, 0.9); /* 添加白色边框增强融合感 */
    }
    
    .tabbar-item-message .tabbar-icon {
        width: 22px; /* 缩小图标尺寸 */
        height: 22px;
        margin-bottom: 0;
        opacity: 0.95; /* 轻微降低透明度，增加柔和感 */
    }
    
    .tabbar-item-message .tabbar-text {
        margin-top: 4px; /* 增加文字间距 */
        font-weight: 400; /* 降低字重，增加美观性 */
        font-size: 10px;
    }
    
    .message-badge {
        position: absolute;
        top: 0px;
        right: 0px;
        min-width: 16px; /* 缩小徒幅 */
        height: 16px;
        border-radius: 8px;
        background-color: #ff3b30; /* 苹果系统红色 */
        color: white;
        font-size: 9px; /* 减小字号 */
        font-weight: 500; /* 降低字重 */
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 4px;
        box-sizing: border-box;
        box-shadow: 0 0 1px rgba(0, 0, 0, 0.15); /* 极轻的阴影 */
        border: 1px solid rgba(255, 255, 255, 0.8);
        transform: scale(0.9); /* 缩小整体大小，增加精致感 */
    }
    
    .icon-message {
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M20 2H4c-1.1 0-1.99.9-1.99 2L2 22l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z'/%3E%3C/svg%3E");
    }
    
    /* 响应式调整 */
    @media (max-width: 320px) {
        .tabbar-text {
            font-size: 9px;
        }
        
        .tabbar-icon {
            width: 20px;
            height: 20px;
        }
    }
    
    @media (min-width: 768px) {
        .tabbar {
            max-width: 750px;
            left: 50%;
            transform: translateX(-50%);
        }
    }
    
    /* 适配iPhone X及以上机型的底部安全区域 */
    @supports (padding-bottom: env(safe-area-inset-bottom)) {
        .tabbar {
            padding-bottom: env(safe-area-inset-bottom);
            height: calc(50px + env(safe-area-inset-bottom));
        }
    }
    
.tabbar-item-message > a.message-btn > .message-btn-inner {
  width: 44px !important;
  height: 44px !important;
  flex-shrink: 0 !important;
  border-radius: 50% !important;
  overflow: hidden !important;
}
    
</style>
