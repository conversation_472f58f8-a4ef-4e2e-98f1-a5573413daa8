@extends('layouts.app')

@section('title', '音乐疗愈 - 心理减压')

@section('content')
<div class="music-container">
    <!-- 页面头部 - 自然融合设计 -->
    <div class="page-header">
        <div class="container">
            <div class="header-content">
                <div class="breadcrumb">
                    <a href="{{ route('stress_relief.index') }}">心理减压</a>
                    <span class="divider">/</span>
                    <span class="current">音乐疗愈</span>
                </div>
                <h1 class="page-title">音乐疗愈</h1>
                <p class="page-subtitle">精选治愈音乐，助力身心健康</p>
            </div>
        </div>
    </div>

    <div class="music-categories">
        <div class="container">
            <div class="categories-grid">
                <div class="category-card" onclick="playMusicCategory('nature')">
                    <div class="category-icon">
                        <i class="fas fa-leaf"></i>
                    </div>
                    <h3 class="category-title">自然音效</h3>
                    <p class="category-desc">雨声、海浪、鸟鸣等大自然的声音</p>
                </div>

                <div class="category-card" onclick="playMusicCategory('classical')">
                    <div class="category-icon">
                        <i class="fas fa-music"></i>
                    </div>
                    <h3 class="category-title">古典音乐</h3>
                    <p class="category-desc">优美的古典音乐，舒缓心情</p>
                </div>

                <div class="category-card" onclick="playMusicCategory('meditation')">
                    <div class="category-icon">
                        <i class="fas fa-om"></i>
                    </div>
                    <h3 class="category-title">冥想音乐</h3>
                    <p class="category-desc">专为冥想设计的宁静音乐</p>
                </div>

                <div class="category-card" onclick="playMusicCategory('ambient')">
                    <div class="category-icon">
                        <i class="fas fa-cloud"></i>
                    </div>
                    <h3 class="category-title">环境音乐</h3>
                    <p class="category-desc">柔和的环境音乐，营造舒适氛围</p>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="music-player" class="music-player" style="display: none;">
    <div class="player-overlay"></div>
    <div class="player-content">
        <div class="player-header">
            <h3 id="music-title">音乐疗愈</h3>
            <button class="close-btn" onclick="closeMusicPlayer()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="player-body">
            <div class="music-visual">
                <div class="equalizer">
                    <div class="bar"></div>
                    <div class="bar"></div>
                    <div class="bar"></div>
                    <div class="bar"></div>
                    <div class="bar"></div>
                </div>
            </div>
            <div class="player-controls">
                <button class="control-btn" onclick="toggleMusicPlay()">
                    <i class="fas fa-play" id="play-icon"></i>
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.music-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    /* 自然处理安全区域 */
    padding-top: calc(env(safe-area-inset-top, 0px) + 20px);
    padding-bottom: calc(env(safe-area-inset-bottom, 0px) + 80px);
}

/* 状态栏和导航栏占位 */
.status-bar-spacer {
    height: env(safe-area-inset-top, 0px);
    background: transparent;
}

.navbar-spacer {
    height: 50px;
    background: transparent;
}

/* 覆盖全局容器样式 */
.music-container .container {
    max-width: none;
    width: 100%;
    padding: 0 15px;
    background-color: transparent !important;
    box-sizing: border-box;
}

/* 页面头部 - 自然设计，确保足够空间给卡片悬浮效果 */
.page-header {
    padding: 6rem 0 4rem;
    color: white;
    position: relative;
    background: transparent;
}

/* 装饰性背景 */
.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 100%;
    background: radial-gradient(ellipse at top, rgba(255,255,255,0.1) 0%, transparent 50%);
    pointer-events: none;
}

.header-content {
    position: relative;
    z-index: 1;
    text-align: center;
}

.breadcrumb {
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.breadcrumb a {
    color: rgba(255,255,255,0.8);
    text-decoration: none;
}

.divider {
    margin: 0 0.5rem;
    opacity: 0.6;
}

.page-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 0 2px 10px rgba(0,0,0,0.3);
}

.page-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
}

.music-categories {
    padding: 3rem 0 2rem;
    width: 100%;
    box-sizing: border-box;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
    box-sizing: border-box;
    width: 100%;
}

.category-card {
    background: white;
    border-radius: 20px;
    padding: 2.5rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    text-align: center;
}

.category-card:hover {
    transform: translateY(-8px);
}

.category-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: white;
}

.category-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.category-desc {
    color: #7f8c8d;
    line-height: 1.6;
}

.music-player {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.player-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.9);
    backdrop-filter: blur(10px);
}

.player-content {
    position: relative;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    width: 90%;
    max-width: 500px;
    overflow: hidden;
    color: white;
}

.player-header {
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.player-header h3 {
    margin: 0;
    font-size: 1.3rem;
}

.close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.3s ease;
}

.close-btn:hover {
    background: rgba(255,255,255,0.2);
}

.player-body {
    padding: 2rem;
    text-align: center;
}

.equalizer {
    display: flex;
    justify-content: center;
    align-items: flex-end;
    height: 100px;
    gap: 5px;
    margin-bottom: 2rem;
}

.bar {
    width: 8px;
    background: linear-gradient(to top, #4facfe, #00f2fe);
    border-radius: 4px;
    animation: equalizer 1s ease-in-out infinite;
}

.bar:nth-child(1) { animation-delay: 0s; }
.bar:nth-child(2) { animation-delay: 0.2s; }
.bar:nth-child(3) { animation-delay: 0.4s; }
.bar:nth-child(4) { animation-delay: 0.6s; }
.bar:nth-child(5) { animation-delay: 0.8s; }

@keyframes equalizer {
    0%, 100% { height: 20px; }
    50% { height: 80px; }
}

.control-btn {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: white;
    border: none;
    font-size: 2rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.control-btn:hover {
    transform: scale(1.1);
}

@media (max-width: 768px) {
    .music-container {
        padding-top: calc(env(safe-area-inset-top, 0px) + 15px);
        padding-bottom: calc(env(safe-area-inset-bottom, 0px) + 80px);
    }
    
    .categories-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        padding: 0 10px;
    }
}

/* 移动端适配 */
@media (max-width: 500px) {
    .music-container {
        padding-top: calc(env(safe-area-inset-top, 0px) + 10px);
        padding-bottom: calc(env(safe-area-inset-bottom, 0px) + 80px);
        width: 100%;
        overflow-x: hidden;
    }
    
    /* 针对移动端框架的容器修正 */
    .music-container .container {
        max-width: 100% !important;
        width: 100% !important;
        padding: 0 10px !important;
        margin: 0 !important;
        background-color: transparent !important;
        box-sizing: border-box !important;
    }
    
    .page-header {
        padding: 1rem 0 1.5rem;
        width: 100%;
        box-sizing: border-box;
    }
    
    .page-title {
        font-size: 2rem;
    }
    
    .page-subtitle {
        font-size: 1rem;
    }
    
    .music-categories {
        padding: 1.5rem 0 2rem;
        width: 100%;
        box-sizing: border-box;
    }
    
    .categories-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
        padding: 0 !important;
        margin: 0 !important;
        width: 100% !important;
        max-width: 100% !important;
        box-sizing: border-box !important;
    }
    
    .category-card {
        padding: 1.5rem 1rem;
        transition: transform 0.2s ease;
        width: 100%;
        box-sizing: border-box;
    }
    
    .category-card:hover {
        transform: translateY(-3px);
    }
    
    .category-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
        margin-bottom: 1rem;
    }
    
    .category-title {
        font-size: 1rem;
        margin-bottom: 0.5rem;
    }
    
    .category-desc {
        font-size: 0.75rem;
        line-height: 1.4;
    }
    
    .player-content {
        width: 95%;
        margin: 1rem;
    }
    
    .player-body {
        padding: 1.5rem;
    }
    
    .equalizer {
        height: 80px;
        margin-bottom: 1.5rem;
    }
    
    .control-btn {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
}

/* 超小屏幕适配 */
@media (max-width: 360px) {
    .categories-grid {
        gap: 10px;
    }
    
    .category-card {
        padding: 1rem 0.8rem;
    }
    
    .category-title {
        font-size: 0.9rem;
    }
    
    .category-desc {
        font-size: 0.7rem;
    }
}
</style>

<script>
let musicPlaying = false;

const musicCategories = {
    nature: '自然音效',
    classical: '古典音乐', 
    meditation: '冥想音乐',
    ambient: '环境音乐'
};

function playMusicCategory(category) {
    document.getElementById('music-title').textContent = musicCategories[category];
    document.getElementById('music-player').style.display = 'flex';
}

function closeMusicPlayer() {
    document.getElementById('music-player').style.display = 'none';
    musicPlaying = false;
    document.getElementById('play-icon').className = 'fas fa-play';
}

function toggleMusicPlay() {
    musicPlaying = !musicPlaying;
    const icon = document.getElementById('play-icon');
    icon.className = musicPlaying ? 'fas fa-pause' : 'fas fa-play';
}
</script>
@endsection 