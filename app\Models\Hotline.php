<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Dcat\Admin\Traits\HasDateTimeFormatter;

class Hotline extends Model
{
    use HasFactory, SoftDeletes, HasDateTimeFormatter;

    protected $fillable = [
        'name',
        'phone_number',
        'description',
        'status',
        'service_hours',
        'sort_order',
        'is_enabled',
    ];

    protected $casts = [
        'is_enabled' => 'boolean',
    ];

    /**
     * 获取状态映射
     */
    public static function getStatusMap()
    {
        return [
            'active' => '在线',
            'busy' => '繁忙',
            'closed' => '关闭',
        ];
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttribute()
    {
        return self::getStatusMap()[$this->status] ?? '未知';
    }

    /**
     * 热线使用记录关联
     */
    public function records()
    {
        return $this->hasMany(HotlineRecord::class);
    }

    /**
     * 筛选启用的热线
     */
    public function scopeEnabled($query)
    {
        return $query->where('is_enabled', true);
    }

    /**
     * 按排序顺序排列
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order', 'asc')->orderBy('id', 'desc');
    }
}
