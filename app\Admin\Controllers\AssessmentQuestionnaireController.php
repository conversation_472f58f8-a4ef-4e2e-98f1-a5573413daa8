<?php

namespace App\Admin\Controllers;

use App\Models\AssessmentQuestionnaire;
use App\Models\AssessmentQuestion;
use App\Models\AssessmentOption;
use App\Models\AssessmentResultConfig;
use App\Services\QuestionnaireService;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Content;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AssessmentQuestionnaireController extends AdminController
{
    protected $title = '心理测评问卷';
    protected $questionnaireService;

    public function __construct(QuestionnaireService $questionnaireService)
    {
        $this->questionnaireService = $questionnaireService;
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new AssessmentQuestionnaire(), function (Grid $grid) {
            $grid->column('id')->sortable();
            $grid->column('title', '问卷标题');
            $grid->column('domain', '测评领域');
            $grid->column('question_count', '题目数量');
            $grid->column('est_duration', '预计时长(分钟)');
            $grid->column('is_active', '状态')->display(function ($value) {
                return $value ? '<span class="badge badge-success">启用</span>' : '<span class="badge badge-secondary">停用</span>';
            });
            $grid->column('created_at', '创建时间');
            $grid->column('updated_at', '更新时间');

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');
                $filter->like('title', '问卷标题');
                $filter->equal('domain', '测评领域')->select([
                    '焦虑测评' => '焦虑测评',
                    '抑郁测评' => '抑郁测评',
                    '人格测评' => '人格测评',
                    '压力测评' => '压力测评',
                    '其他' => '其他'
                ]);
                $filter->equal('is_active', '状态')->select([
                    1 => '启用',
                    0 => '停用'
                ]);
            });

            // 自定义操作列
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $questionnaire = $actions->row;
                
                // 复制按钮 - 添加图标
                $actions->append('
                    <a href="javascript:void(0)" 
                       class="grid-row-action duplicate-questionnaire" 
                       data-id="' . $questionnaire->id . '" 
                       title="复制问卷">
                        <i class="fa fa-copy"></i>&nbsp;复制
                    </a>
                ');

                // 预览按钮 - 添加图标
                $actions->append('
                    <a href="' . admin_url('assessment-questionnaires/' . $questionnaire->id . '/preview') . '" 
                       class="grid-row-action" title="预览问卷" target="_blank">
                        <i class="fa fa-eye"></i>&nbsp;预览
                    </a>
                ');

                // 状态切换按钮 - 改为Ajax方式
                if ($questionnaire->is_active) {
                    $actions->append('
                        <a href="javascript:void(0)" 
                           class="grid-row-action toggle-status" 
                           data-id="' . $questionnaire->id . '" 
                           data-action="disable"
                           title="停用问卷">
                            <i class="fa fa-toggle-on text-success"></i>&nbsp;停用
                        </a>
                    ');
                } else {
                    $actions->append('
                        <a href="javascript:void(0)" 
                           class="grid-row-action toggle-status" 
                           data-id="' . $questionnaire->id . '" 
                           data-action="enable"
                           title="启用问卷">
                            <i class="fa fa-toggle-off text-muted"></i>&nbsp;启用
                        </a>
                    ');
                }
            });

            // 添加JavaScript处理Ajax状态切换
            $grid->tools(function (Grid\Tools $tools) {
                $tools->append('
                    <div class="btn-group" role="group">
                        <a href="' . admin_url('questionnaire-templates') . '" class="btn btn-primary">
                            <i class="fa fa-magic"></i> 模板创建
                        </a>
                        <a href="' . admin_url('questionnaire-batch') . '" class="btn btn-info">
                            <i class="fa fa-upload"></i> 批量导入
                        </a>
                        <a href="' . admin_url('assessment-questionnaires/create') . '" class="btn btn-success">
                            <i class="fa fa-plus"></i> 手动创建
                        </a>
                    </div>
                    <script>
                    $(document).ready(function() {
                        // 状态切换处理
                        $(document).on("click", ".toggle-status", function(e) {
                            e.preventDefault();
                            var $this = $(this);
                            var id = $this.data("id");
                            var action = $this.data("action");
                            var actionText = action === "enable" ? "启用" : "停用";
                            
                            if (!confirm("确定要" + actionText + "这个问卷吗？")) {
                                return;
                            }
                            
                            $.ajax({
                                url: "' . admin_url('assessment-questionnaires') . '/" + id + "/toggle-status",
                                type: "POST",
                                data: {
                                    _token: "' . csrf_token() . '"
                                },
                                success: function(response) {
                                    if (response.status) {
                                        Dcat.success(response.message);
                                        // 刷新页面
                                        setTimeout(function() {
                                            location.reload();
                                        }, 1000);
                                    } else {
                                        Dcat.error(response.message || "操作失败");
                                    }
                                },
                                error: function() {
                                    Dcat.error("操作失败，请重试");
                                }
                            });
                        });
                        
                        // 复制问卷处理
                        $(document).on("click", ".duplicate-questionnaire", function(e) {
                            e.preventDefault();
                            var $this = $(this);
                            var id = $this.data("id");
                            
                            if (!confirm("确定要复制这个问卷吗？")) {
                                return;
                            }
                            
                            $.ajax({
                                url: "' . admin_url('assessment-questionnaires') . '/" + id + "/duplicate",
                                type: "POST",
                                data: {
                                    _token: "' . csrf_token() . '"
                                },
                                success: function(response) {
                                    if (response.status) {
                                        Dcat.success(response.message);
                                        // 跳转到编辑页面
                                        setTimeout(function() {
                                            window.location.href = response.redirect_url;
                                        }, 1000);
                                    } else {
                                        Dcat.error(response.message || "复制失败");
                                    }
                                },
                                error: function() {
                                    Dcat.error("复制失败，请重试");
                                }
                            });
                        });
                    });
                    </script>
                ');
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new AssessmentQuestionnaire(), function (Show $show) {
            $show->field('id');
            $show->field('title', '问卷标题');
            $show->field('description', '问卷描述');
            $show->field('domain', '测评领域');
            $show->field('question_count', '题目数量');
            $show->field('est_duration', '预计时长(分钟)');
            $show->field('cover_image', '封面图片')->image();
            $show->field('is_active', '状态')->using([1 => '启用', 0 => '停用']);
            $show->field('created_at', '创建时间');
            $show->field('updated_at', '更新时间');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        $controller = $this; // 保存控制器实例
        
        return Form::make(AssessmentQuestionnaire::with(['questions' => function($query) {
            $query->with('options')->orderBy('sort_order', 'asc')->orderBy('id', 'asc');
        }, 'resultConfigs']), function (Form $form) use ($controller) {
            // Tab 1: 基本信息
            $form->tab('基本信息', function (Form $form) {
                $form->display('id', 'ID');
                $form->text('title', '问卷标题')->required();
                $form->textarea('description', '问卷描述')->rows(3);
                $form->select('domain', '测评领域')->options([
                    'anxiety' => '焦虑评估',
                    'depression' => '抑郁评估', 
                    'stress' => '压力评估',
                    'personality' => '人格测试',
                    'emotion' => '情绪评估',
                    'social' => '社交能力',
                    'cognitive' => '认知能力',
                    'snappy' => '趣味测评',
                    'other' => '其他'
                ])->required();
                $form->number('est_duration', '预计用时(分钟)')->min(1)->default(10);
                $form->image('cover_image', '封面图片')->autoUpload();
                $form->switch('is_active', '是否启用')->default(1);
                $form->display('created_at', '创建时间');
                $form->display('updated_at', '更新时间');
            });

            // Tab 2: 问卷试题和选项
            $form->tab('试题设置', function (Form $form) {
                $form->hasMany('questions', '问卷题目', function (Form\NestedForm $form) {
                    $form->text('content', '题目内容')->required();
                    $form->select('type', '题目类型')->options([
                        // 'single' => '单选题',
                        // 'multiple' => '多选题', 
                        'scale' => '量表题'
                    ])->default('single')->required();
                    $form->number('sort_order', '排序')->default(1);

                    // 选项文本输入框
                    $form->textarea('options_text', '选项内容')
                        ->help('每行一个选项，格式：选项内容|分值（如：非常同意|5）')
                        ->placeholder("非常同意|5\n同意|4\n一般|3\n不同意|2\n非常不同意|1");
                });
            });

            // Tab 3: 答案分析配置
            $form->tab('结果分析', function (Form $form) {
                $form->hasMany('resultConfigs', '结果配置', function (Form\NestedForm $form) {
                    $form->text('dimension_name', '维度名称')
                        ->required()
                        ->help('如：焦虑程度、抑郁程度、压力水平等');
                    
                    $form->number('min_score', '最低分数')->required()->min(0);
                    $form->number('max_score', '最高分数')->required()->min(0);
                    
                    $form->text('level_name', '等级名称')
                        ->required()
                        ->help('如：正常、轻度、中度、重度');
                    
                    $form->textarea('description', '结果描述')
                        ->help('对该分数段的详细描述');
                    
                    $form->textarea('suggestion', '建议内容')
                        ->help('针对该结果的建议和指导');
                    
                    $form->color('color', '显示颜色')->default('#28a745')->required();
                    
                    $form->number('sort_order', '排序')->default(0);
                });
            });

            // 保存前处理选项文本
            $form->saving(function (Form $form) {
                // 直接从request获取完整数据
                $requestData = request()->all();
                \Log::info('保存前获取的完整数据', $requestData);
                
                // 存储到session供saved回调使用
                if (isset($requestData['questions'])) {
                    session(['questionnaire_form_data' => $requestData['questions']]);
                }
            });

            // 保存后处理选项文本和更新统计
            $form->saved(function (Form $form, $result) use ($controller) {
                \Log::info('saved回调被触发', ['result' => $result, 'model_id' => $form->model()->id]);
                
                // 正确获取问卷ID
                $questionnaireId = $form->model()->id;
                
                if ($questionnaireId) {
                    // 从session获取表单数据
                    $formQuestionsData = session('questionnaire_form_data', []);
                    
                    if (!empty($formQuestionsData)) {
                        $controller->processQuestionnaireOptions($questionnaireId, $formQuestionsData);
                    }
                    
                    // 清理session数据
                    session()->forget('questionnaire_form_data');
                } else {
                    \Log::error('无法获取问卷ID', ['result' => $result, 'model_id' => $form->model()->id ?? 'null']);
                }
            });
        });
    }

    /**
     * 复制问卷
     */
    public function duplicate(Request $request, $id)
    {
        try {
            $newTitle = $request->input('title', '');
            $newQuestionnaire = $this->questionnaireService->duplicate($id, $newTitle);
            
            return response()->json([
                'status' => true,
                'message' => "问卷复制成功！新问卷ID：{$newQuestionnaire->id}",
                'redirect_url' => admin_url('assessment-questionnaires/' . $newQuestionnaire->id . '/edit')
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => '复制失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 切换问卷状态
     */
    public function toggleStatus($id)
    {
        try {
            $questionnaire = $this->questionnaireService->toggleQuestionnaireStatus($id);
            
            $status = $questionnaire->is_active ? '启用' : '停用';
            
            return response()->json([
                'status' => true,
                'message' => "问卷已成功{$status}！"
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => '操作失败：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 预览问卷
     */
    public function preview($id)
    {
        try {
            $preview = $this->questionnaireService->getQuestionnairePreview($id);
            
            return view('admin.questionnaire-preview', compact('preview'));
        } catch (\Exception $e) {
            admin_error('预览失败', '预览失败：' . $e->getMessage());
            return redirect()->back();
        }
    }

    /**
     * 问卷统计
     */
    public function stats($id)
    {
        try {
            $stats = $this->questionnaireService->getQuestionnaireStats($id);
            $questionnaire = AssessmentQuestionnaire::findOrFail($id);
            
            return view('admin.questionnaire.stats', compact('stats', 'questionnaire'));
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->with('error', '获取统计信息失败：' . $e->getMessage());
        }
    }

    /**
     * 处理问卷选项
     */
    private function processQuestionnaireOptions($questionnaireId, $formQuestionsData)
    {
        \Log::info('开始处理问卷选项', [
            'questionnaire_id' => $questionnaireId,
            'questionnaire_id_type' => gettype($questionnaireId),
            'form_questions_count' => count($formQuestionsData),
            'form_questions_keys' => array_keys($formQuestionsData)
        ]);
        
        if (empty($formQuestionsData)) {
            \Log::warning('没有找到表单问题数据');
            return;
        }
        
        // 确保问卷ID是数字
        $questionnaireId = (int)$questionnaireId;
        if ($questionnaireId <= 0) {
            \Log::error('无效的问卷ID', ['questionnaire_id' => $questionnaireId]);
            return;
        }
        
        // 获取数据库中已保存的问题
        $dbQuestions = AssessmentQuestion::where('questionnaire_id', $questionnaireId)
            ->orderBy('sort_order', 'asc')
            ->orderBy('id', 'asc')
            ->get();
        
        \Log::info('数据库问题信息', [
            'questionnaire_id' => $questionnaireId,
            'db_questions_count' => $dbQuestions->count(),
            'db_questions' => $dbQuestions->map(function($q) {
                return ['id' => $q->id, 'content' => substr($q->content, 0, 30)];
            })->toArray()
        ]);
        
        // 处理每个表单问题的选项
        foreach ($formQuestionsData as $questionId => $formQuestion) {
            // 跳过标记为删除的问题
            if (isset($formQuestion['_remove_']) && $formQuestion['_remove_']) {
                \Log::info('跳过删除的问题', ['question_id' => $questionId]);
                continue;
            }
            
            $optionsText = $formQuestion['options_text'] ?? '';
            if (empty(trim($optionsText))) {
                \Log::info('跳过空选项文本的问题', ['question_id' => $questionId]);
                continue;
            }
            
            // 查找对应的数据库问题
            $dbQuestion = null;
            
            // 方法1: 通过ID匹配（表单中的key就是问题ID）
            if (is_numeric($questionId)) {
                $dbQuestion = $dbQuestions->where('id', $questionId)->first();
                \Log::info('尝试通过ID匹配', ['question_id' => $questionId, 'found' => $dbQuestion ? 'yes' : 'no']);
            }
            
            // 方法2: 通过表单中的id字段匹配
            if (!$dbQuestion && isset($formQuestion['id']) && $formQuestion['id']) {
                $dbQuestion = $dbQuestions->where('id', $formQuestion['id'])->first();
                \Log::info('尝试通过表单ID匹配', ['form_id' => $formQuestion['id'], 'found' => $dbQuestion ? 'yes' : 'no']);
            }
            
            // 方法3: 通过内容匹配
            if (!$dbQuestion && isset($formQuestion['content'])) {
                $dbQuestion = $dbQuestions->where('content', $formQuestion['content'])->first();
                \Log::info('尝试通过内容匹配', ['content' => substr($formQuestion['content'], 0, 30), 'found' => $dbQuestion ? 'yes' : 'no']);
            }
            
            if (!$dbQuestion) {
                \Log::warning('无法找到匹配的问题', [
                    'question_key' => $questionId,
                    'form_question' => $formQuestion,
                    'available_db_questions' => $dbQuestions->pluck('id')->toArray()
                ]);
                continue;
            }
            
            \Log::info('处理问题选项', [
                'question_id' => $dbQuestion->id,
                'options_text_length' => strlen($optionsText),
                'options_text_preview' => substr($optionsText, 0, 100)
            ]);
            
            // 删除该问题的所有现有选项
            $deletedCount = AssessmentOption::where('question_id', $dbQuestion->id)->count();
            AssessmentOption::where('question_id', $dbQuestion->id)->delete();
            
            // 解析选项文本并创建新选项
            $lines = array_filter(array_map('trim', explode("\n", $optionsText)));
            $createdCount = 0;
            
            foreach ($lines as $line) {
                if (empty($line)) continue;
                
                $parts = explode('|', $line, 2);
                $content = trim($parts[0]);
                $score = isset($parts[1]) ? (float)trim($parts[1]) : 1;
                
                if (!empty($content)) {
                    try {
                        AssessmentOption::create([
                            'question_id' => $dbQuestion->id,
                            'content' => $content,
                            'score_value' => $score
                        ]);
                        $createdCount++;
                        \Log::info('创建选项成功', [
                            'question_id' => $dbQuestion->id,
                            'content' => $content,
                            'score' => $score
                        ]);
                    } catch (\Exception $e) {
                        \Log::error('创建选项失败', [
                            'question_id' => $dbQuestion->id,
                            'content' => $content,
                            'score' => $score,
                            'error' => $e->getMessage()
                        ]);
                    }
                }
            }
            
            \Log::info('选项处理完成', [
                'question_id' => $dbQuestion->id,
                'deleted_count' => $deletedCount,
                'created_count' => $createdCount
            ]);
        }
        
        // 更新问题数量统计
        $questionnaire = AssessmentQuestionnaire::find($questionnaireId);
        if ($questionnaire) {
            $actualQuestionCount = AssessmentQuestion::where('questionnaire_id', $questionnaireId)->count();
            $questionnaire->update(['question_count' => $actualQuestionCount]);
            
            \Log::info('问卷处理完成', [
                'questionnaire_id' => $questionnaireId,
                'final_question_count' => $actualQuestionCount
            ]);
        }
    }
}