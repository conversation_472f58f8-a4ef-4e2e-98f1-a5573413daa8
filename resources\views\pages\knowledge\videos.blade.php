@extends('layouts.app')

@section('title', '心理科普 - 视频列表')

@section('custom-styles')
<style>
    /* 使用特定前缀避免全局CSS冲突 */
    .kp-page {
        width: 100%;
        max-width: 100%;
        padding: 0;
        margin: 0;
        background-color: #f5f5f5;
        overflow-x: hidden;
        min-height: 100vh;
    }

    .kp-search-bar {
        padding: 10px 15px;
        background-color: #fff;
        display: flex;
        align-items: center;
        position: sticky;
        top: 0;
        z-index: 100;
        border-bottom: 1px solid #eee;
        width: 100%;
        box-sizing: border-box;
    }
    
    .kp-search-input-wrap {
        flex: 1;
        background-color: #f5f5f5;
        border-radius: 20px;
        display: flex;
        align-items: center;
        padding: 5px 15px;
    }
    
    .kp-search-icon {
        width: 20px;
        height: 20px;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999'%3E%3Cpath d='M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z'/%3E%3C/svg%3E");
        background-size: contain;
        background-repeat: no-repeat;
        margin-right: 5px;
    }
    
    .kp-search-input {
        flex: 1;
        border: none;
        background: transparent;
        padding: 5px 0;
        font-size: 14px;
    }
    
    .kp-search-input:focus {
        outline: none;
    }
    
    .kp-message-icon {
        width: 24px;
        height: 24px;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23666'%3E%3Cpath d='M20 2H4c-1.1 0-1.99.9-1.99 2L2 22l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z'/%3E%3C/svg%3E");
        background-size: contain;
        background-repeat: no-repeat;
    }
    
    /* 分类导航区域 */
    .kp-category-section {
        background-color: #fff;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        margin-bottom: 10px;
        width: 100%;
        box-sizing: border-box;
        overflow: hidden;
    }
    
    .kp-category-tabs-container {
        position: relative;
        width: 100%;
    }
    
    .kp-category-tabs {
        display: flex;
        overflow-x: auto;
        padding: 12px 15px;
        scrollbar-width: none; 
        -ms-overflow-style: none;
        position: relative;
        width: 100%;
        -webkit-overflow-scrolling: touch; /* 增加惯性滚动效果 */
    }
    
    .kp-category-tabs::-webkit-scrollbar {
        display: none;
    }
    
    .kp-category-tab {
        padding: 8px 15px;
        margin: 0 4px;
        white-space: nowrap;
        font-size: 14px;
        color: #666;
        border-radius: 16px;
        transition: all 0.2s ease;
        cursor: pointer;
        flex-shrink: 0; /* 防止Tab被压缩 */
    }
    
    .kp-category-tab.active {
        background-color: #4e9cff;
        color: #fff;
        box-shadow: 0 2px 6px rgba(78, 156, 255, 0.3);
    }
    
    .kp-category-expand {
        text-align: center;
        padding: 10px;
        border-top: 1px solid #f0f0f0;
        color: #666;
        font-size: 13px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;
    }
    
    .kp-category-expand-icon {
        width: 16px;
        height: 16px;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23666'%3E%3Cpath d='M16.59 8.59L12 13.17 7.41 8.59 6 10l6 6 6-6z'/%3E%3C/svg%3E");
        background-size: contain;
        background-repeat: no-repeat;
        margin-left: 5px;
        transition: transform 0.3s ease;
    }
    
    .kp-category-expand.open .kp-category-expand-icon {
        transform: rotate(180deg);
    }
    
    .kp-category-grid {
        display: none;
        grid-template-columns: repeat(4, 1fr);
        padding: 15px;
        gap: 10px;
        border-top: 1px solid #f0f0f0;
        width: 100%;
        box-sizing: border-box;
    }
    
    .kp-category-grid.visible {
        display: grid;
    }
    
    .kp-category-grid-item {
        padding: 8px 0;
        text-align: center;
        border-radius: 4px;
        font-size: 13px;
        color: #666;
        background-color: #f9f9f9;
        transition: all 0.2s ease;
    }
    
    .kp-category-grid-item.active {
        background-color: #e6f2ff;
        color: #4e9cff;
    }
    
    /* 指示器箭头 */
    .kp-category-scroll-indicator {
        position: absolute;
        right: 0;
        top: 0;
        height: 100%;
        width: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(to right, rgba(255,255,255,0), rgba(255,255,255,1) 70%);
        pointer-events: none;
    }
    
    .kp-scroll-indicator-arrow {
        width: 16px;
        height: 16px;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23666'%3E%3Cpath d='M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z'/%3E%3C/svg%3E");
        background-size: contain;
        background-repeat: no-repeat;
    }
    
    /* 视频列表区域 */
    .kp-section-title {
        font-size: 18px;
        font-weight: 600;
        color: #333;
        margin: 0;
        padding: 15px;
        display: flex;
        align-items: center;
    }
    
    .kp-video-wrapper {
        background-color: #fff;
        margin-bottom: 80px; /* 增加底部空间，避免与底部导航栏重叠 */
    }
    
    .kp-video-list {
        padding: 0 15px 15px;
    }
    
    .kp-video-item {
        display: flex;
        padding: 15px 0;
        border-bottom: 1px solid #f0f0f0;
        text-decoration: none;
        color: inherit;
    }
    
    .kp-video-image {
        width: 160px;
        height: 90px;
        background-size: cover;
        background-position: center;
        position: relative;
        flex-shrink: 0;
        border-radius: 6px;
        overflow: hidden;
    }
    
    .kp-video-duration {
        position: absolute;
        right: 5px;
        bottom: 5px;
        background-color: rgba(0, 0, 0, 0.7);
        color: #fff;
        font-size: 12px;
        padding: 2px 5px;
        border-radius: 2px;
    }
    
    .kp-play-icon {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        width: 40px;
        height: 40px;
        background-color: rgba(0, 0, 0, 0.5);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .kp-play-icon:before {
        content: '';
        width: 0;
        height: 0;
        border-style: solid;
        border-width: 8px 0 8px 12px;
        border-color: transparent transparent transparent #ffffff;
        margin-left: 3px;
    }
    
    .kp-video-content {
        flex: 1;
        padding: 0 15px;
        display: flex;
        flex-direction: column;
    }
    
    .kp-video-title {
        font-size: 15px;
        font-weight: 500;
        margin-bottom: 8px;
        color: #333;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        line-height: 1.4;
    }
    
    .kp-video-info {
        display: flex;
        justify-content: space-between;
        margin-top: auto;
        font-size: 12px;
        color: #999;
    }
    
    .kp-video-category {
        background-color: #4e9cff;
        color: #ffffff;
        padding: 2px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;
        display: inline-flex;
        align-items: center;
        margin-right: 8px;
    }
    
    .kp-video-date {
        color: #999;
    }
    
    .kp-video-views {
        display: flex;
        align-items: center;
        color: #999;
    }
    
    .kp-views-icon {
        width: 14px;
        height: 14px;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999'%3E%3Cpath d='M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5z'/%3E%3C/svg%3E");
        background-size: contain;
        background-repeat: no-repeat;
        margin-right: 3px;
    }
    
    /* 视频标签样式 */
    .kp-video-tags {
        display: flex;
        flex-wrap: wrap;
        margin-top: 5px;
        gap: 5px;
    }
    
    .kp-video-tag {
        background-color: #f0f7ff;
        color: #6a8caf;
        font-size: 10px;
        padding: 2px 6px;
        border-radius: 8px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 100px;
        border: 1px solid #e0eeff;
        display: inline-flex;
        align-items: center;
    }
    
    .kp-video-tag:before {
        content: '';
        display: inline-block;
        width: 8px;
        height: 8px;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%236a8caf'%3E%3Cpath d='M21.41 11.58l-9-9C12.05 2.22 11.55 2 11 2H4c-1.1 0-2 .9-2 2v7c0 .55.22 1.05.59 1.42l9 9c.*********** ********** 0 1.05-.22 1.41-.59l7-7c.37-.36.59-.86.59-1.41 0-.55-.23-1.06-.59-1.42zM5.5 7C4.67 7 4 6.33 4 5.5S4.67 4 5.5 4 7 4.67 7 5.5 6.33 7 5.5 7z'/%3E%3C/svg%3E");
        background-size: contain;
        background-repeat: no-repeat;
        margin-right: 3px;
    }
    
    /* 加载状态 */
    .kp-loading {
        text-align: center;
        padding: 15px;
        display: none;
    }
    
    .kp-loading-spinner {
        display: inline-block;
        width: 24px;
        height: 24px;
        border: 2px solid rgba(78, 156, 255, 0.2);
        border-radius: 50%;
        border-top-color: #4e9cff;
        animation: kp-spin 0.8s linear infinite;
    }
    
    @keyframes kp-spin {
        to {
            transform: rotate(360deg);
        }
    }
    
    /* 加载更多区域 */
    .kp-load-more {
        text-align: center;
        padding: 15px;
        color: #666;
        font-size: 14px;
        display: none;
    }
    
    @media (min-width: 768px) {
        .kp-video-list {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
        }
        
        .kp-video-item {
            width: calc(50% - 8px);
            flex-direction: column;
        }
        
        .kp-video-image {
            width: 100%;
            height: 120px;
        }
        
        .kp-category-grid {
            grid-template-columns: repeat(6, 1fr);
        }
    }
    
    @media (min-width: 992px) {
        .kp-video-item {
            width: calc(33.333% - 10px);
        }
        
        .kp-category-grid {
            grid-template-columns: repeat(8, 1fr);
        }
    }
</style>
@endsection

@section('content')
<div class="container-fluid kp-page">
    <div class="kp-search-bar">
        <div class="kp-search-input-wrap">
            <div class="kp-search-icon"></div>
            <input type="text" class="kp-search-input" placeholder="搜索视频标题、内容或标签">
        </div>
        <div class="kp-message-icon"></div>
    </div>
    
    <div class="kp-category-section">
        <div class="kp-category-tabs-container">
            <div class="kp-category-tabs">
                @if(isset($categories) && count($categories) > 0)
                    @foreach($categories as $index => $category)
                        <div class="kp-category-tab {{ $index === 0 ? 'active' : '' }}" data-id="{{ $category['id'] }}">{{ $category['name'] }}</div>
                    @endforeach
                @else
                    <div class="kp-category-tab" data-id="1">情绪管理</div>
                    <div class="kp-category-tab" data-id="2">人际关系</div>
                    <div class="kp-category-tab" data-id="3">心理健康</div>
                    <div class="kp-category-tab" data-id="4">学习成长</div>
                    <div class="kp-category-tab" data-id="5">职场压力</div>
                    <div class="kp-category-tab" data-id="6">亲子教育</div>
                    <div class="kp-category-tab" data-id="7">婚恋情感</div>
                @endif
            </div>
            <div class="kp-category-scroll-indicator">
                <div class="kp-scroll-indicator-arrow"></div>
            </div>
        </div>
        <div class="kp-category-expand">
            展开更多分类 <div class="kp-category-expand-icon"></div>
        </div>
        <div class="kp-category-grid">
            @if(isset($categories) && count($categories) > 0)
                @foreach($categories as $index => $category)
                    <div class="kp-category-grid-item {{ $index === 0 ? 'active' : '' }}" data-id="{{ $category['id'] }}">{{ $category['name'] }}</div>
                @endforeach
            @else
                <div class="kp-category-grid-item" data-id="1">情绪管理</div>
                <div class="kp-category-grid-item" data-id="2">人际关系</div>
                <div class="kp-category-grid-item" data-id="3">心理健康</div>
                <div class="kp-category-grid-item" data-id="4">学习成长</div>
                <div class="kp-category-grid-item" data-id="5">职场压力</div>
                <div class="kp-category-grid-item" data-id="6">亲子教育</div>
                <div class="kp-category-grid-item" data-id="7">婚恋情感</div>
            @endif
        </div>
    </div>
    
    <div class="kp-video-wrapper">
        <h2 class="kp-section-title">全部视频</h2>
        <div class="kp-video-list" id="videoList">
            @if(isset($videos) && count($videos) > 0)
                @foreach($videos as $video)
                <a href="{{ route('knowledge.video_detail', ['id' => $video['id']]) }}" class="kp-video-item">
                    <div class="kp-video-image" style="background-image: url('{{ $video['image'] }}')">
                        <div class="kp-play-icon"></div>
                        <div class="kp-video-duration">{{ $video['video_duration'] }}</div>
                    </div>
                    <div class="kp-video-content">
                        <div class="kp-video-title">{{ $video['title'] }}</div>
                        <div class="kp-video-info">
                            <span class="kp-video-category">{{ $video['category_name'] }}</span>
                            <span class="kp-video-date">{{ $video['date'] }}</span>
                            <span class="kp-video-views">
                                <div class="kp-views-icon"></div>
                                {{ $video['views'] }}
                            </span>
                        </div>
                        <div class="kp-video-tags">
                            @if(isset($video['tags']) && is_array($video['tags']))
                                @foreach($video['tags'] as $tag)
                                    <div class="kp-video-tag">{{ $tag }}</div>
                                @endforeach
                            @endif
                        </div>
                    </div>
                </a>
                @endforeach
            @else
                <!-- 没有视频数据时显示提示 -->
                <div class="kp-no-videos">
                    <p>暂无视频数据</p>
                </div>
            @endif

        </div>
        
        <!-- 加载更多区域 -->
        <div class="kp-loading" id="loadingIndicator">
            <div class="kp-loading-spinner"></div>
        </div>
        
        <div class="kp-load-more" id="loadMoreEnd">
            没有更多视频了
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        let currentPage = 1;
        let isLoading = false;
        let hasMoreVideos = true;
        let currentCategoryId = 0; // 默认为"全部"分类
        let currentSearch = '';
        const videoList = document.getElementById('videoList');
        const loadingIndicator = document.getElementById('loadingIndicator');
        const loadMoreEnd = document.getElementById('loadMoreEnd');
        
        // 加载更多视频的函数
        const loadMoreVideos = () => {
            if (isLoading || !hasMoreVideos) return;
            
            isLoading = true;
            currentPage++;
            loadingIndicator.style.display = 'block';
            
            // 构建请求URL
            let url = `{{ route('knowledge.videos') }}?page=${currentPage}`;
            
            // 添加分类参数
            if (currentCategoryId > 0) {
                url += `&category_id=${currentCategoryId}`;
            }
            
            // 添加搜索参数
            if (currentSearch) {
                url += `&search=${encodeURIComponent(currentSearch)}`;
            }
            
            // 发送AJAX请求
            fetch(url, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                loadingIndicator.style.display = 'none';
                
                if (data.videos && data.videos.length > 0) {
                    // 添加视频到列表
                    data.videos.forEach(video => {
                        const videoItem = createVideoItem(video);
                        videoList.appendChild(videoItem);
                    });
                    
                    // 检查是否还有更多视频
                    hasMoreVideos = data.hasMore;
                    if (!hasMoreVideos) {
                        loadMoreEnd.style.display = 'block';
                    }
                } else {
                    hasMoreVideos = false;
                    loadMoreEnd.style.display = 'block';
                }
                
                isLoading = false;
            })
            .catch(error => {
                console.error('加载视频失败:', error);
                loadingIndicator.style.display = 'none';
                isLoading = false;
            });
        };
        
        // 创建视频项的HTML
        const createVideoItem = (video) => {
            const videoItem = document.createElement('a');
            videoItem.href = `/knowledge/videos/${video.id}`;
            videoItem.className = 'kp-video-item';
            
            // 标签HTML
            let tagsHtml = '';
            if (video.tags && video.tags.length > 0) {
                video.tags.forEach(tag => {
                    tagsHtml += `<div class="kp-video-tag">${tag}</div>`;
                });
            }
            
            videoItem.innerHTML = `
                <div class="kp-video-image" style="background-image: url('${video.image}')">
                    <div class="kp-play-icon"></div>
                    <div class="kp-video-duration">${video.video_duration}</div>
                </div>
                <div class="kp-video-content">
                    <div class="kp-video-title">${video.title}</div>
                    <div class="kp-video-info">
                        <span class="kp-video-category">${video.category_name}</span>
                        <span class="kp-video-date">${video.date}</span>
                        <span class="kp-video-views">
                            <div class="kp-views-icon"></div>
                            ${video.views}
                        </span>
                    </div>
                    <div class="kp-video-tags">${tagsHtml}</div>
                </div>
            `;
            
            return videoItem;
        };
        
        // 滚动到底部时加载更多视频
        window.addEventListener('scroll', () => {
            if (isLoading || !hasMoreVideos) return;
            
            const scrollY = window.scrollY || window.pageYOffset;
            const windowHeight = window.innerHeight;
            const documentHeight = document.documentElement.scrollHeight;
            
            // 当滚动到离底部200px距离时加载更多
            if (scrollY + windowHeight > documentHeight - 200) {
                loadMoreVideos();
            }
        });
        
        // 分类标签点击事件
        const bindCategoryEvents = (selector) => {
            const items = document.querySelectorAll(selector);
            items.forEach(item => {
                item.addEventListener('click', function() {
                    // 移除所有标签的active类
                    document.querySelectorAll('.kp-category-tab, .kp-category-grid-item').forEach(t => {
                        t.classList.remove('active');
                    });
                    
                    // 为当前点击的标签和对应的网格项添加active类
                    const categoryId = this.getAttribute('data-id');
                    document.querySelectorAll(`.kp-category-tab[data-id="${categoryId}"], .kp-category-grid-item[data-id="${categoryId}"]`).forEach(el => {
                        el.classList.add('active');
                    });
                    
                    // 如果是从网格视图点击的，关闭网格视图
                    const grid = document.querySelector('.kp-category-grid');
                    if (grid.classList.contains('visible')) {
                        grid.classList.remove('visible');
                        document.querySelector('.kp-category-expand').classList.remove('open');
                    }
                    
                    // 如果点击的是选项卡，滚动到视图中央
                    if (selector === '.kp-category-tab') {
                        const tabsContainer = document.querySelector('.kp-category-tabs');
                        const tabRect = this.getBoundingClientRect();
                        const containerRect = tabsContainer.getBoundingClientRect();
                        
                        const scrollLeft = tabsContainer.scrollLeft + tabRect.left - containerRect.left - (containerRect.width / 2) + (tabRect.width / 2);
                        tabsContainer.scrollTo({
                            left: scrollLeft,
                            behavior: 'smooth'
                        });
                    }
                    
                    // 更新标题显示当前分类
                    const categoryName = this.textContent.trim();
                    document.querySelector('.kp-section-title').textContent = categoryName === '全部' ? '全部视频' : categoryName + '视频';
                    
                    // 切换分类，重置视频列表并加载新分类的视频
                    currentCategoryId = parseInt(categoryId, 10);
                    currentPage = 1;
                    hasMoreVideos = true;
                    loadMoreEnd.style.display = 'none';
                    
                    // 清空当前列表
                    videoList.innerHTML = '';
                    
                    // 加载新分类的视频
                    fetchVideos();
                });
            });
        };
        
        // 获取视频数据的函数
        const fetchVideos = () => {
            isLoading = true;
            loadingIndicator.style.display = 'block';
            
            // 构建请求URL
            let url = `{{ route('knowledge.videos') }}?page=1`;
            
            // 添加分类参数
            if (currentCategoryId > 0) {
                url += `&category_id=${currentCategoryId}`;
            }
            
            // 添加搜索参数
            if (currentSearch) {
                url += `&search=${encodeURIComponent(currentSearch)}`;
            }
            
            // 发送AJAX请求
            fetch(url, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                loadingIndicator.style.display = 'none';
                
                if (data.videos && data.videos.length > 0) {
                    // 添加视频到列表
                    data.videos.forEach(video => {
                        const videoItem = createVideoItem(video);
                        videoList.appendChild(videoItem);
                    });
                    
                    // 检查是否还有更多视频
                    hasMoreVideos = data.hasMore;
                    if (!hasMoreVideos) {
                        loadMoreEnd.style.display = 'block';
                    }
                } else {
                    // 没有视频数据
                    videoList.innerHTML = `
                        <div class="kp-no-videos">
                            <p>没有找到相关视频</p>
                        </div>
                    `;
                    loadMoreEnd.style.display = 'none';
                }
                
                isLoading = false;
            })
            .catch(error => {
                console.error('加载视频失败:', error);
                loadingIndicator.style.display = 'none';
                isLoading = false;
            });
        };
        
        // 绑定分类项点击事件
        bindCategoryEvents('.kp-category-tab');
        bindCategoryEvents('.kp-category-grid-item');
        
        // 展开/收起分类网格
        document.querySelector('.kp-category-expand').addEventListener('click', function() {
            const grid = document.querySelector('.kp-category-grid');
            this.classList.toggle('open');
            grid.classList.toggle('visible');
        });
        
        // 处理横向滚动指示器
        const tabsContainer = document.querySelector('.kp-category-tabs');
        const handleScrollIndicator = () => {
            const indicator = document.querySelector('.kp-category-scroll-indicator');
            // 如果已经滚动到最右侧，隐藏指示器
            if (tabsContainer.scrollLeft + tabsContainer.offsetWidth >= tabsContainer.scrollWidth - 10) {
                indicator.style.opacity = '0';
            } else {
                indicator.style.opacity = '1';
            }
        };
        
        tabsContainer.addEventListener('scroll', handleScrollIndicator);
        handleScrollIndicator(); // 初始检查
        
        // 点击滚动指示器，向右滚动
        document.querySelector('.kp-category-scroll-indicator').addEventListener('click', function() {
            tabsContainer.scrollBy({
                left: 100,
                behavior: 'smooth'
            });
        });
        
        // 搜索功能
        const searchInput = document.querySelector('.kp-search-input');
        let searchTimeout;
        
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            
            searchTimeout = setTimeout(() => {
                currentSearch = this.value.trim();
                currentPage = 1;
                hasMoreVideos = true;
                loadMoreEnd.style.display = 'none';
                
                // 清空当前列表
                videoList.innerHTML = '';
                
                // 加载搜索结果
                fetchVideos();
            }, 500); // 输入停止500ms后才执行搜索，避免频繁请求
        });
        
        // 初始检查指示器状态
        handleScrollIndicator();
    });
</script>
@endsection
