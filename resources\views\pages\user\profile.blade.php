@extends('layouts.app')

@section('title', '个人资料 - 心理咨询平台')

@section('custom-styles')
<style>
    body {
        background-color: #f8f9fa;
    }
    
    .container {
        max-width: 900px;
        margin: 0 auto;
        padding-bottom: 80px !important; /* 为底部菜单栏预留空间 */
    }
    
    .page-header {
        display: flex;
        align-items: center;
        background: linear-gradient(135deg, #5b7cef 0%, #3c67e3 100%);
        color: white;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        border-radius: 0 0 10px 10px;
        padding: 12px 15px;
        margin-bottom: 15px;
    }
    
    .back-button {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 30px;
        height: 30px;
    }
    
    .back-icon {
        border-top: 2px solid white;
        border-left: 2px solid white;
        width: 12px;
        height: 12px;
        transform: rotate(-45deg);
    }
    
    .page-header h1 {
        font-size: 18px;
        font-weight: 500;
        margin: 0 auto;
        text-align: center;
        flex-grow: 1;
        margin-left: -30px;
        text-shadow: 0 1px 2px rgba(0,0,0,0.1);
    }
    
    .profile-card {
        background-color: #fff;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        margin: 15px;
        padding: 20px;
        border: 1px solid rgba(0, 0, 0, 0.03);
    }
    
    .profile-avatar {
        text-align: center;
        margin-bottom: 20px;
    }
    
    .profile-avatar-container {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        overflow: hidden;
        margin: 0 auto;
        border: 3px solid #f0f0f0;
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        position: relative;
    }
    
    .profile-avatar-container img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    
    .profile-avatar-edit {
        position: absolute;
        bottom: 0;
        right: 0;
        background: rgba(0, 0, 0, 0.5);
        color: white;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
    }
    
    .profile-avatar .default-avatar {
        background-color: #4a6fff; 
        display: flex; 
        align-items: center; 
        justify-content: center; 
        width: 100%; 
        height: 100%; 
        border-radius: 50%; 
        color: white; 
        font-size: 36px; 
        font-weight: bold;
    }
    
    .profile-form-group {
        margin-bottom: 20px;
    }
    
    .profile-form-label {
        display: block;
        margin-bottom: 8px;
        font-weight: 600;
        font-size: 14px;
        color: #444;
    }
    
    .profile-form-input {
        width: 100%;
        padding: 12px 15px;
        border: 1px solid #ddd;
        border-radius: 8px;
        font-size: 15px;
        transition: all 0.3s ease;
    }
    
    .profile-form-input:focus {
        border-color: #3c67e3;
        box-shadow: 0 0 0 2px rgba(60, 103, 227, 0.1);
        outline: none;
    }
    
    .btn-primary {
        display: block;
        width: 100%;
        padding: 14px;
        background: linear-gradient(135deg, #2c7ffc, #7b58ff);
        color: white;
        text-align: center;
        border: none;
        border-radius: 8px;
        font-size: 16px;
        font-weight: 600;
        text-decoration: none;
        box-shadow: 0 4px 10px rgba(44, 127, 252, 0.2);
        transition: all 0.3s ease;
        cursor: pointer;
        margin-top: 10px;
    }
    
    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 15px rgba(44, 127, 252, 0.25);
    }
    
    .alert {
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 20px;
    }
    
    .alert-success {
        background-color: #e3fcef;
        color: #0d915c;
        border: 1px solid #c3f0da;
    }
    
    .alert-danger {
        background-color: #fee7e6;
        color: #d6292c;
        border: 1px solid #fcd0cf;
    }
</style>
@endsection

@section('content')
<div class="page-header">
    <a href="{{ route('my') }}" class="back-button">
        <div class="back-icon"></div>
    </a>
    <h1>个人资料</h1>
    <div style="width: 30px;"></div>
</div>

<div class="container">
    @if(session('success'))
    <div class="alert alert-success">
        {{ session('success') }}
    </div>
    @endif
    
    @if($errors->any())
    <div class="alert alert-danger">
        <ul style="margin: 0; padding-left: 15px;">
            @foreach($errors->all() as $error)
            <li>{{ $error }}</li>
            @endforeach
        </ul>
    </div>
    @endif
    
    <div class="profile-card">
        <div class="profile-avatar">
            <form id="avatar-form" action="{{ route('user.profile.update') }}" method="POST" enctype="multipart/form-data">
                @csrf
                <input type="file" name="avatar" id="avatar-input" style="display: none;" accept="image/*">
                <div class="profile-avatar-container" onclick="document.getElementById('avatar-input').click()" style="cursor: pointer;">
                    @if(Auth::user()->avatar)
                        <img src="{{ asset('storage/'.Auth::user()->avatar) }}" alt="{{ Auth::user()->name }}">
                    @else
                        <div class="default-avatar">
                            {{ strtoupper(substr(Auth::user()->name, 0, 1)) }}
                        </div>
                    @endif
                </div>
            </form>
            <p style="margin-top: 8px; font-size: 14px; color: #666;">点击头像更换</p>
        </div>
        
        <form action="{{ route('user.profile.update') }}" method="POST">
            @csrf
            <div class="profile-form-group">
                <label for="name" class="profile-form-label">用户名</label>
                <input type="text" id="name" name="name" class="profile-form-input" value="{{ Auth::user()->name }}" required>
            </div>
            
            <div class="profile-form-group">
                <label for="phone" class="profile-form-label">手机号码</label>
                <input type="tel" id="phone" name="phone" class="profile-form-input" value="{{ Auth::user()->phone }}">
            </div>
            
            <div class="profile-form-group">
                <label for="email" class="profile-form-label">邮箱</label>
                <input type="email" id="email" name="email" class="profile-form-input" value="{{ Auth::user()->email }}">
            </div>
            
            <button type="submit" class="btn-primary">保存修改</button>
        </form>
    </div>
</div>
@endsection

@section('scripts')
<script>
    // 头像上传逻辑
    document.getElementById('avatar-input').addEventListener('change', function() {
        if (this.files && this.files[0]) {
            // 自动提交表单
            document.getElementById('avatar-form').submit();
        }
    });
</script>
@endsection
