@extends('layouts.app')

@php
use Illuminate\Support\Facades\DB;
use App\Models\AssessmentResponse;
@endphp

@section('title', '我的 - 心理咨询平台')

@section('custom-styles')
<link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
<style>
    body {
        background-color: #f8f9fd;
    }
    
    /* 2025年现代UI设计 - 玻璃态和粘土态混合设计 */
    .user-header {
        background: linear-gradient(135deg, #2c7ffc, #7b58ff);
        padding: 20px 15px 30px;
        color: #fff;
        position: relative;
        border-radius: 0 0 30px 30px;
        margin-bottom: 20px;
        overflow: hidden;
    }
    
    .user-header-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiPjxkZWZzPjxwYXR0ZXJuIGlkPSJwYXR0ZXJuIiB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHBhdHRlcm5Vbml0cz0idXNlclNwYWNlT25Vc2UiIHBhdHRlcm5UcmFuc2Zvcm09InJvdGF0ZSgxMzUpIj48cmVjdCBpZD0icGF0dGVybi1iZyIgd2lkdGg9IjQwMCUiIGhlaWdodD0iNDAwJSIgZmlsbD0icmdiYSgyNTUsMjU1LDI1NSwwLjEpIj48L3JlY3Q+PHBhdGggZmlsbD0icmdiYSgyNTUsMjU1LDI1NSwwLjA1KSIgZD0iTTAgMGg0MHY0MEgweiI+PC9wYXRoPjwvcGF0dGVybj48L2RlZnM+PHJlY3QgZmlsbD0idXJsKCNwYXR0ZXJuKSIgaGVpZ2h0PSIxMDAlIiB3aWR0aD0iMTAwJSI+PC9yZWN0Pjwvc3ZnPg==');
        opacity: 0.15;
        z-index: 0;
    }
    
    .user-info {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        position: relative;
        z-index: 1;
        text-align: center;
        width: 100%;
        padding: 20px 0;
    }
    
    .user-avatar {
        width: 80px;
        height: 80px;
        border-radius: 24px;
        background-color: rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 15px;
        overflow: hidden;
        border: 3px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 8px 32px rgba(31, 38, 135, 0.2);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
    
    .user-avatar:active {
        transform: scale(0.95);
        box-shadow: 0 4px 16px rgba(31, 38, 135, 0.1);
    }
    
    .user-avatar img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    
    .user-name {
        font-size: 22px;
        font-weight: 700;
        margin-bottom: 5px;
        letter-spacing: -0.5px;
    }
    
    .user-id {
        font-size: 14px;
        opacity: 0.8;
        font-weight: 400;
    }
    
    .user-stats {
        display: flex;
        justify-content: space-around;
        margin: 5px 15px 0;
        background-color: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border-radius: 20px;
        padding: 15px 0;
        border: 1px solid rgba(255, 255, 255, 0.18);
        box-shadow: 0 8px 32px rgba(31, 38, 135, 0.1);
        transform: translateY(25px);
    }
    
    .user-stat {
        text-align: center;
        padding: 0 15px;
    }
    
    .user-stat-value {
        font-size: 24px;
        font-weight: 700;
        margin-bottom: 5px;
        background: linear-gradient(135deg, #ffffff, rgba(255, 255, 255, 0.7));
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
    }
    
    .user-stat-label {
        font-size: 12px;
        opacity: 0.9;
        font-weight: 500;
    }
    
    .menu-section {
        margin: 35px 15px 20px;
    }
    
    .menu-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 10px;
        margin: 0 15px;
    }
    
    .menu-section-title {
        font-size: 17px;
        font-weight: 700;
        margin-bottom: 15px;
        color: #333;
        padding-left: 5px;
        position: relative;
    }
    
    .menu-section-title::before {
        content: '';
        position: absolute;
        left: -5px;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 18px;
        background: linear-gradient(to bottom, #2c7ffc, #7b58ff);
        border-radius: 2px;
    }
    
    .menu-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 16px 5px;
        background-color: #fff;
        border-radius: 16px;
        position: relative;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.03);
        transition: all 0.3s ease;
        border: 1px solid rgba(240, 240, 240, 0.8);
        overflow: hidden;
        text-decoration: none;
        color: inherit;
    }
    
    .menu-item::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        width: 5px;
        height: 100%;
        background: linear-gradient(to bottom, #2c7ffc, #7b58ff);
        opacity: 0;
        transition: opacity 0.3s ease;
    }
    
    .menu-item:active {
        transform: scale(0.98);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.01);
    }
    
    .menu-item:hover::before {
        opacity: 1;
    }
    
    .menu-item-icon {
        width: 46px;
        height: 46px;
        margin-bottom: 8px;
        background: rgba(240, 242, 255, 0.8);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
        color: #4a6fff;
    }
    
    .menu-item-icon .material-icons {
        font-size: 24px;
        display: block;
        line-height: 1;
        text-align: center;
        color: #4a6fff;
    }
    
    .menu-item-text {
        font-size: 14px;
        font-weight: 500;
        color: #333;
        text-align: center;
        width: 100%;
        margin-top: 4px;
    }
    
    .menu-item-arrow {
        display: none;
    }
    
    .btn-primary {
        display: block;
        width: 100%;
        padding: 16px;
        background: linear-gradient(135deg, #2c7ffc, #7b58ff);
        color: white;
        text-align: center;
        border: none;
        border-radius: 14px;
        font-size: 16px;
        font-weight: 600;
        text-decoration: none;
        box-shadow: 0 8px 16px rgba(44, 127, 252, 0.2);
        transition: all 0.3s ease;
        margin-top: 20px;
    }
    
    .btn-primary:active {
        transform: scale(0.98);
        box-shadow: 0 4px 8px rgba(44, 127, 252, 0.1);
    }
    
    .logout-button {
        display: block;
        margin: 20px 15px;
        padding: 12px 0;
        text-align: center;
        background-color: #fff;
        color: #ff4d4f;
        border-radius: 14px;
        font-size: 16px;
        font-weight: 600;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.03);
        border: 1px solid rgba(240, 240, 240, 0.8);
    }
</style>
@endsection

@section('content')
<div style="display: none !important;">
    <span class="material-icons" style="font-size: 0;">event</span>
    <span class="material-icons" style="font-size: 0;">assessment</span>
    <span class="material-icons" style="font-size: 0;">menu_book</span>
    <span class="material-icons" style="font-size: 0;">person</span>
    <span class="material-icons" style="font-size: 0;">security</span>
    <span class="material-icons" style="font-size: 0;">settings</span>
    <span class="material-icons" style="font-size: 0;">help</span>
    <span class="material-icons" style="font-size: 0;">email</span>
    <span class="material-icons" style="font-size: 0;">info</span>
</div>
<div class="user-header">
    <div class="user-header-bg"></div>
    <div class="user-info">
       @auth
            <a href="{{ route('user.profile') }}">
                <div class="user-avatar">
                    @if(Auth::user()->avatar)
                        <img src="{{ asset('storage/'.Auth::user()->avatar) }}" alt="{{ Auth::user()->name }}">
                    @else
                        <div class="default-avatar">
                            {{ strtoupper(substr(Auth::user()->name, 0, 1)) }}
                        </div>
                    @endif
                </div>
            </a>
            <div class="user-name">{{ Auth::user()->name }}</div>
            <div class="user-id">用户ID: {{ Auth::user()->id }}</div>
        @else
            <a href="{{ route('login') }}" class="user-avatar-link">
                <div class="user-avatar"></div>
                <div class="user-name">未登录</div>
                <div class="user-id">点击登录账号</div>
            </a>
        @endauth

    </div>
    <div class="user-stats">
        <div class="user-stat">
            <div class="user-stat-value animate__animated animate__fadeIn">{{ Auth::check() ? Auth::user()->consultationAppointments()->count() : 0 }}</div>
            <div class="user-stat-label">咨询次数</div>
        </div>
        <div class="user-stat">
            <div class="user-stat-value animate__animated animate__fadeIn animate__delay-1s">{{ Auth::check() ? Auth::user()->assessments()->count() : 0 }}</div>
            <div class="user-stat-label">测评次数</div>
        </div>
        <div class="user-stat">
            <div class="user-stat-value animate__animated animate__fadeIn animate__delay-2s">{{ Auth::check() ? DB::table('course_lesson_progress')->where('user_id', Auth::id())->distinct('course_lesson_id')->count('course_lesson_id') : 0 }}</div>
            <div class="user-stat-label">课程数量</div>
        </div>
    </div>
</div>



@auth
@php
$recentAssessments = AssessmentResponse::with(['questionnaire', 'analysis'])
    ->where('user_id', Auth::id())
    ->whereNotNull('submitted_at')
    ->orderBy('created_at', 'desc')
    ->limit(1)
    ->get();
@endphp


@auth
<div class="menu-section">
    <div class="menu-section-title">最近讲座预约</div>
    @php
        $recentLectures = Auth::user()
            ->offlineCourses()
            ->wherePivot('status', \App\Models\CourseRegistration::STATUS_REGISTERED)
            ->where('start_time', '>=', now())
            ->orderBy('created_at', 'desc')
            ->limit(1)
            ->get();
    @endphp

    @if($recentLectures->isEmpty())
        <div style="padding:15px;background:#fff;border-radius:16px;margin:0 15px 20px;text-align:center;color:#666;">
            暂无新的讲座预约
        </div>
    @else
        <div class="lecture-list" style="margin:0 15px 20px;">
            @foreach($recentLectures as $course)
                <div class="lecture-item"
                     style="
                       display: flex;
                       justify-content: space-between;
                       align-items: flex-start !important;
                       background: #fff;
                       border-radius: 16px;
                       padding: 12px 15px;
                       margin-bottom: 12px;
                       box-shadow: 0 4px 12px rgba(0,0,0,0.03);
                       border: 1px solid rgba(240,240,240,0.8);
                     ">
                    <div class="lecture-info">
                        <h4 style="font-size:15px;font-weight:600;color:#333;margin:0 0 5px;line-height:1.3;">
                            {{ Str::limit($course->title, 30) }}
                        </h4>
                        <p style="font-size:12px;color:#999;margin:0;">
                            {{ $course->start_time->format('m月d日 H:i') }} · {{ $course->location ?: '待定' }}
                        </p>
                    </div>
                    <a href="{{ route('consultation.offline_course_detail', $course->id) }}"
                       style="
                         align-self: flex-start !important;
                         background: linear-gradient(135deg,#2c7ffc,#7b58ff);
                         color: #fff;
                         padding: 4px 10px;
                         border-radius: 12px;
                         font-size: 12px;
                         font-weight: 600;
                         text-decoration: none;
                       ">
                       查看详情
                    </a>
                </div>

            @endforeach
            <a href="{{ route('consultation.my_courses') }}" style="display:block;text-align:center;padding:12px;background:rgba(44,127,252,0.05);color:#2c7ffc;border-radius:12px;text-decoration:none;font-size:14px;font-weight:500;">
                查看全部讲座预约 →
            </a>
        </div>
    @endif
</div>
@endauth

@if($recentAssessments->count() > 0)
<div class="menu-section">
    <div class="menu-section-title">最近测评</div>
    <div style="margin: 0 15px;">
        @foreach($recentAssessments as $assessment)
        <div style="background: #fff; border-radius: 16px; padding: 15px; margin-bottom: 12px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.03); border: 1px solid rgba(240, 240, 240, 0.8);">
            <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 8px;">
                <div style="flex: 1;">
                    <h4 style="font-size: 15px; font-weight: 600; color: #333; margin: 0 0 5px 0; line-height: 1.3;">
                        {{ Str::limit($assessment->questionnaire->title, 30) }}
                    </h4>
                    <div style="font-size: 12px; color: #999;">
                        {{ $assessment->created_at->format('m月d日 H:i') }}
                    </div>
                </div>
                <div style="text-align: right;">
                    <div style="background: linear-gradient(135deg, #2c7ffc, #7b58ff); color: white; padding: 4px 10px; border-radius: 12px; font-size: 12px; font-weight: 600; margin-bottom: 5px;">
                        {{ $assessment->total_score ?? 0 }}分
                    </div>
                    @if($assessment->analysis && $assessment->analysis->level_name)
                    <div style="font-size: 11px; color: #666;">
                        {{ $assessment->analysis->level_name }}
                    </div>
                    @endif
                </div>
            </div>
            <div style="display: flex; gap: 8px;">
                <a href="{{ route('assessment.result', $assessment->id) }}" style="flex: 1; background: rgba(44, 127, 252, 0.1); color: #2c7ffc; padding: 8px 12px; border-radius: 10px; text-decoration: none; font-size: 12px; font-weight: 500; text-align: center;">
                    查看详情
                </a>
                <a href="{{ route('assessment.show', $assessment->questionnaire_id) }}" style="flex: 1; background: rgba(123, 88, 255, 0.1); color: #7b58ff; padding: 8px 12px; border-radius: 10px; text-decoration: none; font-size: 12px; font-weight: 500; text-align: center;">
                    重新测评
                </a>
            </div>
        </div>
        @endforeach
        
        <a href="{{ route('assessment.my-records') }}" style="display: block; text-align: center; padding: 12px; background: rgba(44, 127, 252, 0.05); color: #2c7ffc; border-radius: 12px; text-decoration: none; font-size: 14px; font-weight: 500; margin-top: 8px;">
            查看全部测评记录 →
        </a>
    </div>
</div>
@endif
@endauth

<div class="menu-section">
    <div class="menu-section-title">学习管理</div>
    <div class="menu-grid">
        <a href="{{ route('consultation.my_appointments') }}" class="menu-item">
            <div class="menu-item-icon">
                <span class="material-icons">event</span>
            </div>
            <div class="menu-item-text">我的预约</div>
        </a>
        <a href="{{ route('assessment.my-records') }}" class="menu-item">
            <div class="menu-item-icon">
                <span class="material-icons">assessment</span>
            </div>
            <div class="menu-item-text">我的测评</div>
        </a>
        <a href="{{ route('study') }}" class="menu-item">
            <div class="menu-item-icon">
                <span class="material-icons">menu_book</span>
            </div>
            <div class="menu-item-text">我的课程</div>
        </a>
    </div>
</div>

<div class="menu-section">
    <div class="menu-section-title">账户管理</div>
    <div class="menu-grid">
        <a href="{{ route('user.profile') }}" class="menu-item">
            <div class="menu-item-icon">
                <span class="material-icons">person</span>
            </div>
            <div class="menu-item-text">个人资料</div>
        </a>
        <a href="{{ route('user.account') }}" class="menu-item">
            <div class="menu-item-icon">
                <span class="material-icons">security</span>
            </div>
            <div class="menu-item-text">账号管理</div>
        </a>
        <a href="{{ route('user.settings') }}" class="menu-item">
            <div class="menu-item-icon">
                <span class="material-icons">settings</span>
            </div>
            <div class="menu-item-text">设置</div>
        </a>
    </div>
</div>

<div class="menu-section">
    <div class="menu-section-title">其他</div>
    <div class="menu-grid">
        <a href="{{ route('help') }}" class="menu-item">
            <div class="menu-item-icon">
                <span class="material-icons">help</span>
            </div>
            <div class="menu-item-text">帮助中心</div>
        </a>
        <a href="{{ route('contact') }}" class="menu-item">
            <div class="menu-item-icon">
                <span class="material-icons">email</span>
            </div>
            <div class="menu-item-text">联系我们</div>
        </a>
        <a href="{{ route('about') }}" class="menu-item">
            <div class="menu-item-icon">
                <span class="material-icons">info</span>
            </div>
            <div class="menu-item-text">关于我们</div>
        </a>
    </div>
</div>

<style>
/* 添加重要的悬停效果和动画 */
.hvr-sweep-to-right {
    display: inline-block;
    vertical-align: middle;
    -webkit-transform: perspective(1px) translateZ(0);
    transform: perspective(1px) translateZ(0);
    box-shadow: 0 0 1px rgba(0, 0, 0, 0);
    position: relative;
    -webkit-transition-property: color;
    transition-property: color;
    -webkit-transition-duration: 0.3s;
    transition-duration: 0.3s;
}

.hvr-sweep-to-right:before {
    content: "";
    position: absolute;
    z-index: -1;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(78, 156, 255, 0.08);
    -webkit-transform: scaleX(0);
    transform: scaleX(0);
    -webkit-transform-origin: 0 50%;
    transform-origin: 0 50%;
    -webkit-transition-property: transform;
    transition-property: transform;
    -webkit-transition-duration: 0.3s;
    transition-duration: 0.3s;
    -webkit-transition-timing-function: ease-out;
    transition-timing-function: ease-out;
    border-radius: 8px;
}

.hvr-sweep-to-right:hover:before, .hvr-sweep-to-right:focus:before, .hvr-sweep-to-right:active:before {
    -webkit-transform: scaleX(1);
    transform: scaleX(1);
}

/* 添加用于动画的CSS类 */
.animate__animated {
    -webkit-animation-duration: 1s;
    animation-duration: 1s;
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
}

.animate__fadeIn {
    -webkit-animation-name: fadeIn;
    animation-name: fadeIn;
}

.animate__delay-1s {
    -webkit-animation-delay: 0.2s;
    animation-delay: 0.2s;
}

.animate__delay-2s {
    -webkit-animation-delay: 0.4s;
    animation-delay: 0.4s;
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}
</style>

@auth
<form action="{{ route('logout') }}" method="POST" style="margin-bottom: 80px;">
    @csrf
    <button type="submit" class="logout-button" style="width: calc(100% - 30px); border: none; margin: 0 15px;">退出登录</button>
</form>
@else
<div style="margin-bottom: 50px;"></div>
@endauth
@endsection
