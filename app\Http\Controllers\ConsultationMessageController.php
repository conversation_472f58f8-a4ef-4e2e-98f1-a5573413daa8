<?php

namespace App\Http\Controllers;

use App\Models\Consultation;
use App\Models\ConsultationReply;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class ConsultationMessageController extends Controller
{
    /**
     * 显示留言咨询页面
     */
    public function index()
    {
        return view('pages.consultation.index');
    }

    /**
     * 提交留言
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'phone' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255',
            'subject' => 'required|string|max:255',
            'content' => 'required|string|max:2000',
        ], [
            'name.required' => '请输入您的姓名',
            'subject.required' => '请输入留言主题',
            'content.required' => '请输入留言内容',
            'content.max' => '留言内容不能超过2000字',
            'email.email' => '请输入正确的邮箱格式',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first()
            ]);
        }

        $consultation = Consultation::create([
            'user_id' => Auth::id(),
            'name' => $request->name,
            'phone' => $request->phone,
            'email' => $request->email,
            'subject' => $request->subject,
            'content' => $request->content,
            'status' => Consultation::STATUS_PENDING,
        ]);

        return response()->json([
            'success' => true,
            'message' => '留言提交成功，我们会尽快回复您！',
            'consultation_id' => $consultation->id
        ]);
    }

    /**
     * 我的留言列表
     */
    public function myConsultations()
    {
        if (!Auth::check()) {
            return redirect()->route('login')->with('message', '请先登录');
        }

        $consultations = Consultation::where('user_id', Auth::id())
            ->with(['replies' => function($query) {
                $query->orderBy('created_at', 'asc');
            }])
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('pages.consultation.my_consultations', compact('consultations'));
    }

    /**
     * 留言详情
     */
    public function show($id)
    {
        $consultation = Consultation::with(['replies.user', 'replies.admin'])
            ->findOrFail($id);

        // 检查权限：只有留言者本人或管理员可以查看
        if (Auth::id() != $consultation->user_id && !Auth::user()?->is_admin) {
            abort(403, '无权访问此留言');
        }

        return view('pages.consultation.detail', compact('consultation'));
    }

    /**
     * 添加回复
     */
    public function reply(Request $request, $id)
    {
        if (!Auth::check()) {
            return response()->json([
                'success' => false,
                'message' => '请先登录'
            ]);
        }

        $validator = Validator::make($request->all(), [
            'content' => 'required|string|max:1000',
        ], [
            'content.required' => '请输入回复内容',
            'content.max' => '回复内容不能超过1000字',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first()
            ]);
        }

        $consultation = Consultation::findOrFail($id);

        // 检查权限
        if (Auth::id() != $consultation->user_id && !Auth::user()->is_admin) {
            return response()->json([
                'success' => false,
                'message' => '无权回复此留言'
            ]);
        }

        $isAdminReply = Auth::user()->is_admin ?? false;

        $reply = ConsultationReply::create([
            'consultation_id' => $consultation->id,
            'user_id' => $isAdminReply ? null : Auth::id(),
            'admin_id' => $isAdminReply ? Auth::id() : null,
            'content' => $request->content,
            'is_admin_reply' => $isAdminReply,
        ]);

        // 更新留言状态
        if ($isAdminReply) {
            // 如果是管理员回复，更新为已回复
            $consultation->update([
                'status' => Consultation::STATUS_REPLIED,
                'replied_at' => now(),
                'replied_by' => Auth::id(),
            ]);
        } else {
            // 如果是用户回复，更新为待回复（需要管理员回复）
            $consultation->update([
                'status' => Consultation::STATUS_PENDING,
                'replied_at' => null,
                'replied_by' => null,
            ]);
        }

        return response()->json([
            'success' => true,
            'message' => '回复成功',
            'reply' => [
                'id' => $reply->id,
                'content' => $reply->content,
                'is_admin_reply' => $reply->is_admin_reply,
                'created_at' => $reply->created_at->format('Y-m-d H:i:s'),
                'author_name' => $isAdminReply ? '管理员' : Auth::user()->name,
            ]
        ]);
    }
} 