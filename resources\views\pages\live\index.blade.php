@extends('layouts.app')

@section('custom-styles')
<style>
/* 直播页面专用样式 */
.live-page {
    min-height: 100vh;
    background-color: #f8f9fa;
}

.live-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0;
}

.live-header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
}

.back-btn {
    display: flex;
    align-items: center;
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: background-color 0.2s;
}

.back-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.back-icon {
    width: 24px;
    height: 24px;
    stroke: currentColor;
    fill: none;
    stroke-width: 2;
    stroke-linecap: round;
    stroke-linejoin: round;
}

.live-title {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
}

.live-tabs {
    background: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.live-tabs-content {
    display: flex;
}

.live-tab {
    flex: 1;
    padding: 16px;
    text-align: center;
    font-size: 14px;
    font-weight: 500;
    color: #6b7280;
    background: none;
    border: none;
    border-bottom: 2px solid transparent;
    cursor: pointer;
    transition: all 0.2s;
}

.live-tab.active {
    color: #667eea;
    border-bottom-color: #667eea;
}

.live-tab:hover {
    color: #667eea;
}

.tab-count {
    font-size: 12px;
    opacity: 0.7;
    margin-left: 4px;
}

.live-cards-container {
    padding: 16px;
}

.live-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border: 1px solid #e5e7eb;
    overflow: hidden;
    margin-bottom: 16px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.live-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.live-card-image-container {
    position: relative;
}

.live-card-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.live-status-badge {
    position: absolute;
    top: 12px;
    left: 12px;
    padding: 4px 8px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 4px;
}

.live-status-badge.live {
    background-color: #ef4444;
    color: white;
}

.live-status-badge.upcoming {
    background-color: #f97316;
    color: white;
}

.live-status-badge.ended {
    background-color: #6b7280;
    color: white;
}

.status-dot {
    width: 8px;
    height: 8px;
    background-color: white;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.featured-badge {
    position: absolute;
    top: 12px;
    right: 12px;
    background-color: #eab308;
    color: white;
    padding: 4px 8px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
}

.live-card-overlay {
    position: absolute;
    inset: 0;
    background-color: rgba(0,0,0,0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.2s;
}

.live-card:hover .live-card-overlay {
    opacity: 1;
}

.play-button {
    background-color: rgba(255,255,255,0.9);
    border-radius: 50%;
    padding: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.play-icon {
    width: 32px;
    height: 32px;
    color: #667eea;
    fill: currentColor;
}

.live-card-content {
    padding: 16px;
}

.live-card-title {
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 8px;
    line-height: 1.4;
}

.live-card-description {
    color: #6b7280;
    font-size: 14px;
    margin-bottom: 12px;
    line-height: 1.5;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.counselor-info {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
}

.counselor-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    margin-right: 8px;
    object-fit: cover;
}

.counselor-name {
    font-size: 14px;
    color: #374151;
}

.live-time-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
}

.live-time {
    font-size: 14px;
    color: #667eea;
}

.live-created-time {
    font-size: 12px;
    color: #9ca3af;
}

.live-notice {
    background-color: #dbeafe;
    border: 1px solid #93c5fd;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 12px;
}

.live-notice-text {
    font-size: 14px;
    color: #1e40af;
}

.live-card-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.live-status-info {
    display: flex;
    align-items: center;
    font-size: 14px;
}

.live-status-info.available {
    color: #059669;
}

.live-status-info.unavailable {
    color: #6b7280;
}

.status-info-icon {
    width: 16px;
    height: 16px;
    margin-right: 4px;
    fill: currentColor;
}

.detail-btn {
    font-size: 14px;
    color: #667eea;
    text-decoration: none;
    transition: color 0.2s;
}

.detail-btn:hover {
    color: #4c51bf;
}

.empty-state {
    text-align: center;
    padding: 80px 20px;
}

.empty-icon {
    font-size: 60px;
    margin-bottom: 16px;
    display: block;
}

.empty-title {
    color: #6b7280;
    font-size: 18px;
    margin-bottom: 8px;
}

.empty-subtitle {
    color: #9ca3af;
    font-size: 14px;
}

.pagination-container {
    padding: 16px;
}

/* 响应式调整 */
@media (max-width: 480px) {
    .live-cards-container {
        padding: 12px;
    }
    
    .live-card {
        margin-bottom: 12px;
    }
    
    .live-card-content {
        padding: 12px;
    }
    
    .live-card-title {
        font-size: 16px;
    }
}
</style>
@endsection
@php
    // 保证变量存在
    $status       = $status       ?? request()->query('status', 'live');
    $statusCounts = $statusCounts ?? ['live'=>0,'upcoming'=>0,'ended'=>0];
    // 如果控制器没传 liveShows，就用一个空数组，让 forelse 不出错
    $liveShows    = $liveShows    ?? collect(); 
@endphp
@section('content')
<div class="live-page">
    <!-- 头部 -->
    <div class="live-header">
        <div class="live-header-content">
            <button class="back-btn" onclick="history.back()">
                <svg class="back-icon" viewBox="0 0 24 24">
                    <path d="M15 19l-7-7 7-7"></path>
                </svg>
            </button>
            <h1 class="live-title">直播间</h1>
            <div style="width: 40px;"></div>
        </div>
    </div>

    @isset($status)
    <!-- 导航标签 -->
    <div class="live-tabs">
        <div class="live-tabs-content">
            <button class="live-tab {{ $status === 'live' ? 'active' : '' }}" 
                    onclick="changeStatusFilter('live')">
                直播中
                @if($statusCounts['live'] > 0)
                <span class="tab-count">({{ $statusCounts['live'] }})</span>
                @endif
            </button>
            <button class="live-tab {{ $status === 'upcoming' ? 'active' : '' }}" 
                    onclick="changeStatusFilter('upcoming')">
                即将开始
                @if($statusCounts['upcoming'] > 0)
                <span class="tab-count">({{ $statusCounts['upcoming'] }})</span>
                @endif
            </button>
            <button class="live-tab {{ $status === 'ended' ? 'active' : '' }}" 
                    onclick="changeStatusFilter('ended')">
                已结束
                @if($statusCounts['ended'] > 0)
                <span class="tab-count">({{ $statusCounts['ended'] }})</span>
                @endif
            </button>
        </div>
    </div>
    @endisset


    <!-- 直播卡片列表 -->
    <div class="live-cards-container">
        @forelse($liveShows as $liveShow)
        <div class="live-card" 
            onclick="showLiveDetail({{ $liveShow->id }}); event.stopPropagation(); return false;" >
            <div class="live-card-image-container">
                <!-- 封面图片 -->
                <img src="{{ $liveShow->cover_image ? asset('storage/' . $liveShow->cover_image) : asset('images/default-live-cover.jpg') }}" 
                     alt="{{ $liveShow->title }}" 
                     class="live-card-image">
                
                <!-- 直播状态标签 -->
                <div class="live-status-badge {{ $liveShow->status }}">
                    @if($liveShow->status === 'live')
                        <span class="status-dot"></span>
                        <span>直播中</span>
                    @elseif($liveShow->status === 'upcoming')
                        <span>即将开始</span>
                    @else
                        <span>已结束</span>
                    @endif
                </div>

                <!-- 精选标签 -->
                @if($liveShow->is_featured)
                <div class="featured-badge">
                    精选
                </div>
                @endif

                <!-- 点击提示 -->
                @if($liveShow->live_url && $liveShow->status !== 'ended')
                <div class="live-card-overlay">
                    <div class="play-button">
                        <svg class="play-icon" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                </div>
                @endif
            </div>

            <!-- 内容信息 -->
            <div class="live-card-content">
                <h3 class="live-card-title">{{ $liveShow->title }}</h3>
                
                @if($liveShow->description)
                <p class="live-card-description">{{ $liveShow->description }}</p>
                @endif

                <!-- 咨询师信息 -->
                @if($liveShow->counselor)
                <div class="counselor-info">
                    <img src="{{ $liveShow->counselor->avatar ? asset('storage/' . $liveShow->counselor->avatar) : asset('images/default-avatar.jpg') }}" 
                         alt="{{ $liveShow->counselor->name }}" 
                         class="counselor-avatar">
                    <span class="counselor-name">{{ $liveShow->counselor->name }}</span>
                </div>
                @endif

                <!-- 时间信息 -->
                <div class="live-time-info">
                    <span class="live-time">
                        {{ $liveShow->formatted_scheduled_at }}
                    </span>
                    <span class="live-created-time">
                        {{ $liveShow->created_at->diffForHumans() }}
                    </span>
                </div>

                <!-- 通知信息 -->
                @if($liveShow->notice)
                <div class="live-notice">
                    <p class="live-notice-text">{{ $liveShow->notice }}</p>
                </div>
                @endif

                <!-- 状态提示 -->
                <div class="live-card-footer">
                    @if($liveShow->live_url && $liveShow->status !== 'ended')
                    <div class="live-status-info available">
                        <svg class="status-info-icon" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        点击即可{{ $liveShow->status === 'live' ? '进入直播' : '预约观看' }}
                    </div>
                    @elseif(!$liveShow->live_url)
                    <div class="live-status-info unavailable">
                        <svg class="status-info-icon" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                        </svg>
                        链接暂未设置
                    </div>
                    @else
                    <div class="live-status-info unavailable">
                        <svg class="status-info-icon" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                        </svg>
                        直播已结束
                    </div>
                    @endif

                    <a href="#" onclick="showLiveDetail({{ $liveShow->id }}); event.stopPropagation(); return false;" 
                       class="detail-btn">
                        查看详情 →
                    </a>
                </div>
            </div>
        </div>
        @empty
        <div class="empty-state">
            <span class="empty-icon">��</span>
            @if($status === 'live')
                <p class="empty-title">暂无正在直播</p>
                <p class="empty-subtitle">目前没有进行中的直播，请关注即将开始的精彩内容</p>
            @elseif($status === 'upcoming')
                <p class="empty-title">暂无预告直播</p>
                <p class="empty-subtitle">目前没有即将开始的直播，敬请期待更多精彩内容</p>
            @else
                <p class="empty-title">暂无历史直播</p>
                <p class="empty-subtitle">还没有已结束的直播记录</p>
            @endif
        </div>
        @endforelse
    </div>

    <!-- 分页 -->
    @if(isset($liveShows) && method_exists($liveShows, 'hasPages') && $liveShows->hasPages())
    <div class="pagination-container">
        {{ $liveShows->links() }}
    </div>
    @endif
</div>

<script>
// 状态筛选切换
function changeStatusFilter(status) {
    const currentUrl = new URL(window.location);
    currentUrl.searchParams.set('status', status);
    currentUrl.searchParams.delete('page'); // 重置分页
    window.location.href = currentUrl.toString();
}

// 处理直播卡片点击
function handleLiveCardClick(id, status, hasUrl) {
    if (!hasUrl) {
        alert('直播链接暂未设置，请稍后再试');
        return;
    }
    
    if (status === 'ended') {
        showLiveDetail(id);
        return;
    }
    
    // 显示加载提示
    const loadingToast = showLoading();
    
    // 直接跳转到直播链接
    window.location.href = `/live/${id}/redirect`;
}

function showLiveDetail(id) {
    window.location.href = `/live/${id}`;
}

function showLoading() {
    // 创建简单的加载提示
    const toast = document.createElement('div');
    toast.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;  
        transform: translate(-50%, -50%);
        background: rgba(0,0,0,0.75);
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        z-index: 9999;
        font-size: 14px;
    `;
    toast.innerHTML = '正在跳转到微信视频号...';
    document.body.appendChild(toast);
    
    // 3秒后自动移除
    setTimeout(() => {
        if (document.body.contains(toast)) {
            document.body.removeChild(toast);
        }
    }, 3000);
    
    return toast;
}
</script>
@endsection 