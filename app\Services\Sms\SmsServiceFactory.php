<?php

namespace App\Services\Sms;

/**
 * 短信服务工厂类
 * 用于根据环境和配置创建短信服务实例
 */
class SmsServiceFactory
{
    /**
     * 创建短信服务实例
     *
     * @param string|null $provider 指定提供商，为空则使用配置中的默认提供商
     * @return SmsServiceInterface
     */
    public static function create(?string $provider = null): SmsServiceInterface
    {
        // 未指定提供商则使用配置
        if (is_null($provider)) {
            $provider = config('sms.default_provider', 'test');
        }
        
        // 如果环境是测试环境且未强制指定提供商，则使用测试服务
        if (app()->environment('local', 'testing') && !config('sms.force_provider')) {
            return new TestSmsService();
        }
        
        // 根据提供商名称创建对应的服务实例
        switch ($provider) {
            case 'aliyun':
                return new AliyunSmsService();
            
            case 'jinan':
                return new JinanSmsService();
            
            // 可以在此添加其他短信提供商的实现
            case 'tencent':
                // return new TencentSmsService();
                
            case 'yunpian':
                // return new YunpianSmsService();
                
            // 默认返回测试服务
            case 'test':
            default:
                return new TestSmsService();
        }
    }
}
