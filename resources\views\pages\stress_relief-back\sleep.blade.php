@extends('layouts.app')

@section('title', '睡眠调整 - 心理减压')

@section('content')
<div class="sleep-container">
    <!-- 页面头部 - 自然融合状态栏区域 -->
    <div class="page-header">
        <div class="container">
            <div class="header-content">
                <div class="breadcrumb">
                    <a href="{{ route('stress_relief.index') }}">心理减压</a>
                    <span class="divider">/</span>
                    <span class="current">睡眠调整</span>
                </div>
                <h1 class="page-title">睡眠调整</h1>
                <p class="page-subtitle">专业睡眠管理，与您一起入睡</p>
            </div>
        </div>
    </div>

    <!-- 睡眠项目 -->
    <div class="sleep-programs">
        <div class="container">
            <div class="programs-grid">
                <!-- 催眠音乐 -->
                <div class="program-card">
                    <div class="program-header">
                        <div class="program-icon">
                            <i class="fas fa-music"></i>
                        </div>
                        <h3 class="program-title">催眠音乐</h3>
                        <p class="program-duration">30-60分钟</p>
                    </div>
                    <div class="program-content">
                        <p class="program-desc">轻柔舒缓的音乐，帮助您快速进入睡眠状态</p>
                        <button class="start-btn" onclick="startSleepProgram('music')">
                            <i class="fas fa-play"></i>
                            开始聆听
                        </button>
                    </div>
                </div>

                <!-- 白噪音 -->
                <div class="program-card">
                    <div class="program-header">
                        <div class="program-icon">
                            <i class="fas fa-water"></i>
                        </div>
                        <h3 class="program-title">白噪音</h3>
                        <p class="program-duration">持续播放</p>
                    </div>
                    <div class="program-content">
                        <p class="program-desc">雨声、海浪声等自然音效，营造宁静睡眠环境</p>
                        <button class="start-btn" onclick="startSleepProgram('whitenoise')">
                            <i class="fas fa-play"></i>
                            开始聆听
                        </button>
                    </div>
                </div>

                <!-- 睡前冥想 -->
                <div class="program-card">
                    <div class="program-header">
                        <div class="program-icon">
                            <i class="fas fa-moon"></i>
                        </div>
                        <h3 class="program-title">睡前冥想</h3>
                        <p class="program-duration">15-20分钟</p>
                    </div>
                    <div class="program-content">
                        <p class="program-desc">引导式睡前冥想，放松身心，准备深度睡眠</p>
                        <button class="start-btn" onclick="startSleepProgram('meditation')">
                            <i class="fas fa-play"></i>
                            开始冥想
                        </button>
                    </div>
                </div>

                <!-- 渐进式放松 -->
                <div class="program-card">
                    <div class="program-header">
                        <div class="program-icon">
                            <i class="fas fa-user-circle"></i>
                        </div>
                        <h3 class="program-title">渐进式放松</h3>
                        <p class="program-duration">20-25分钟</p>
                    </div>
                    <div class="program-content">
                        <p class="program-desc">逐步放松全身肌肉，释放一天的疲劳和紧张</p>
                        <button class="start-btn" onclick="startSleepProgram('progressive')">
                            <i class="fas fa-play"></i>
                            开始放松
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 睡眠小贴士 -->
    <div class="sleep-tips">
        <div class="container">
            <h2 class="tips-title">睡眠小贴士</h2>
            <div class="tips-grid">
                <div class="tip-item">
                    <div class="tip-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h4>规律作息</h4>
                    <p>每天固定时间上床睡觉和起床</p>
                </div>
                <div class="tip-item">
                    <div class="tip-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h4>远离屏幕</h4>
                    <p>睡前1小时避免使用电子设备</p>
                </div>
                <div class="tip-item">
                    <div class="tip-icon">
                        <i class="fas fa-thermometer-half"></i>
                    </div>
                    <h4>适宜温度</h4>
                    <p>保持卧室温度在18-22℃之间</p>
                </div>
                <div class="tip-item">
                    <div class="tip-icon">
                        <i class="fas fa-coffee"></i>
                    </div>
                    <h4>避免咖啡因</h4>
                    <p>睡前6小时内避免摄入咖啡因</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 睡眠播放器 -->
<div id="sleep-player" class="sleep-player" style="display: none;">
    <div class="player-overlay"></div>
    <div class="player-content">
        <div class="player-header">
            <h3 id="sleep-player-title">睡眠调整</h3>
            <button class="close-btn" onclick="closeSleepPlayer()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="player-body">
            <div class="sleep-visual">
                <div class="sleep-animation" id="sleep-animation">
                    <div class="moon"></div>
                    <div class="stars">
                        <div class="star"></div>
                        <div class="star"></div>
                        <div class="star"></div>
                        <div class="star"></div>
                        <div class="star"></div>
                    </div>
                </div>
            </div>
            <div class="sleep-instruction">
                <p id="sleep-instruction-text">轻松躺下，闭上眼睛，让我们开始睡眠之旅...</p>
            </div>
            <div class="player-controls">
                <button id="sleep-play-pause-btn" class="control-btn" onclick="toggleSleepPlayPause()">
                    <i class="fas fa-play"></i>
                </button>
                <button class="control-btn" onclick="closeSleepPlayer()">
                    <i class="fas fa-stop"></i>
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.sleep-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    /* 自然处理安全区域 */
    padding-top: calc(env(safe-area-inset-top, 0px) + 20px);
    padding-bottom: calc(env(safe-area-inset-bottom, 0px) + 80px);
}

/* 覆盖全局容器样式 */
.sleep-container .container {
    max-width: none;
    width: 100%;
    padding: 0 15px;
    background-color: transparent !important;
    box-sizing: border-box;
}

/* 页面头部 - 自然设计，确保足够空间给卡片悬浮效果 */
.page-header {
    padding: 6rem 0 4rem;
    color: white;
    position: relative;
    background: transparent;
}

/* 装饰性背景 */
.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 100%;
    background: radial-gradient(ellipse at top, rgba(255,255,255,0.1) 0%, transparent 50%);
    pointer-events: none;
}

.header-content {
    position: relative;
    z-index: 1;
    text-align: center;
}

.breadcrumb {
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.breadcrumb a {
    color: rgba(255,255,255,0.8);
    text-decoration: none;
}

.breadcrumb a:hover {
    color: white;
}

.divider {
    margin: 0 0.5rem;
    opacity: 0.6;
}

.current {
    color: white;
}

.page-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 0 2px 10px rgba(0,0,0,0.3);
}

.page-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
}

/* 睡眠项目 */
.sleep-programs {
    padding: 3rem 0;
    width: 100%;
    box-sizing: border-box;
}

.programs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
    box-sizing: border-box;
    width: 100%;
}

.program-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
    text-align: center;
}

.program-card:hover {
    transform: translateY(-5px);
}

.program-icon {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 1.8rem;
    color: white;
}

.program-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.program-duration {
    color: #7f8c8d;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.program-desc {
    color: #5a6c7d;
    line-height: 1.6;
    margin-bottom: 2rem;
}

.start-btn {
    width: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.start-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

.start-btn i {
    margin-right: 0.5rem;
}

/* 睡眠小贴士 */
.sleep-tips {
    padding: 4rem 0;
    background: rgba(255,255,255,0.1);
    backdrop-filter: blur(10px);
}

.tips-title {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    color: white;
    margin-bottom: 3rem;
    text-shadow: 0 2px 10px rgba(0,0,0,0.3);
}

.tips-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.tip-item {
    text-align: center;
    color: white;
}

.tip-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: rgba(255,255,255,0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 1.5rem;
}

.tip-item h4 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.tip-item p {
    opacity: 0.9;
    line-height: 1.6;
}

/* 睡眠播放器 */
.sleep-player {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.player-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.9);
    backdrop-filter: blur(10px);
}

.player-content {
    position: relative;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    border-radius: 20px;
    width: 90%;
    max-width: 500px;
    overflow: hidden;
    color: white;
}

.player-header {
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.player-header h3 {
    margin: 0;
    font-size: 1.3rem;
}

.close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.3s ease;
}

.close-btn:hover {
    background: rgba(255,255,255,0.2);
}

.player-body {
    padding: 2rem;
    text-align: center;
}

.sleep-visual {
    margin-bottom: 2rem;
}

.sleep-animation {
    position: relative;
    width: 200px;
    height: 200px;
    margin: 0 auto;
    border-radius: 50%;
    background: radial-gradient(circle, #1a237e 0%, #0d47a1 100%);
    overflow: hidden;
}

.moon {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #ffd54f;
    box-shadow: 0 0 20px rgba(255, 213, 79, 0.5);
}

.stars {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.star {
    position: absolute;
    width: 4px;
    height: 4px;
    background: white;
    border-radius: 50%;
    animation: twinkle 2s infinite;
}

.star:nth-child(1) { top: 30%; left: 20%; animation-delay: 0s; }
.star:nth-child(2) { top: 50%; left: 70%; animation-delay: 0.5s; }
.star:nth-child(3) { top: 70%; left: 30%; animation-delay: 1s; }
.star:nth-child(4) { top: 40%; left: 80%; animation-delay: 1.5s; }
.star:nth-child(5) { top: 80%; left: 60%; animation-delay: 2s; }

@keyframes twinkle {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.3; }
}

.sleep-instruction {
    margin-bottom: 2rem;
}

#sleep-instruction-text {
    font-size: 1rem;
    line-height: 1.6;
    opacity: 0.9;
}

.player-controls {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

.control-btn {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: rgba(255,255,255,0.2);
    color: white;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.control-btn:hover {
    background: rgba(255,255,255,0.3);
    transform: scale(1.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .page-title {
        font-size: 2.5rem;
    }
    
    .programs-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .tips-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .player-content {
        width: 95%;
        margin: 1rem;
    }
}

@media (max-width: 480px) {
    .tips-grid {
        grid-template-columns: 1fr;
    }
    
    .sleep-animation {
        width: 150px;
        height: 150px;
    }
}

/* 移动端适配 - 覆盖桌面端样式 */
@media (max-width: 500px) {
    .sleep-container {
        padding-top: calc(env(safe-area-inset-top, 0px) + 10px);
        padding-bottom: calc(env(safe-area-inset-bottom, 0px) + 80px);
        width: 100%;
        overflow-x: hidden;
    }
    
    /* 针对移动端框架的容器修正 */
    .sleep-container .container {
        max-width: 100% !important;
        width: 100% !important;
        padding: 0 10px !important;
        margin: 0 !important;
        background-color: transparent !important;
        box-sizing: border-box !important;
    }
    
    .page-header {
        padding: 1rem 0 1.5rem;
        width: 100%;
        box-sizing: border-box;
    }
    
    .page-title {
        font-size: 2rem;
    }
    
    .page-subtitle {
        font-size: 1rem;
    }
    
    .sleep-programs {
        padding: 1.5rem 0 2rem;
        width: 100%;
        box-sizing: border-box;
    }
    
    .programs-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
        padding: 0 !important;
        margin: 0 !important;
        width: 100% !important;
        max-width: 100% !important;
        box-sizing: border-box !important;
    }
    
    .program-card {
        padding: 1.5rem 1rem;
        transition: transform 0.2s ease;
        width: 100%;
        box-sizing: border-box;
    }
    
    .program-card:hover {
        transform: translateY(-3px);
    }
    
    .program-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
        margin-bottom: 1rem;
    }
    
    .program-title {
        font-size: 1rem;
        margin-bottom: 0.5rem;
    }
    
    .program-duration {
        font-size: 0.8rem;
        margin-bottom: 0.8rem;
    }
    
    .program-desc {
        font-size: 0.75rem;
        margin-bottom: 1.5rem;
        line-height: 1.4;
    }
    
    .start-btn {
        padding: 0.8rem 1rem;
        font-size: 0.85rem;
        width: 100%;
        box-sizing: border-box;
    }
    
    .sleep-tips {
        padding: 2rem 0;
        width: 100%;
        box-sizing: border-box;
    }
    
    .tips-title {
        font-size: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .tips-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
        padding: 0 !important;
        margin: 0 !important;
        width: 100% !important;
        max-width: 100% !important;
        box-sizing: border-box !important;
    }
    
    .tip-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
        margin-bottom: 0.8rem;
    }
    
    .tip-item h4 {
        font-size: 1rem;
        margin-bottom: 0.3rem;
    }
    
    .tip-item p {
        font-size: 0.75rem;
        line-height: 1.4;
    }
}

/* 超小屏幕适配 */
@media (max-width: 360px) {
    .programs-grid,
    .tips-grid {
        gap: 10px;
    }
    
    .program-card {
        padding: 1rem 0.8rem;
    }
    
    .program-title {
        font-size: 0.9rem;
    }
    
    .program-desc {
        font-size: 0.7rem;
    }
    
    .start-btn {
        font-size: 0.8rem;
        padding: 0.7rem 0.8rem;
    }
    
    .tip-item h4 {
        font-size: 0.9rem;
    }
    
    .tip-item p {
        font-size: 0.7rem;
    }
}
</style>

<script>
let sleepAudio;
let isSleepPlaying = false;

const sleepPrograms = {
    music: {
        title: '催眠音乐',
        instruction: '轻松躺下，闭上眼睛，让舒缓的音乐带您进入甜美的梦乡...'
    },
    whitenoise: {
        title: '白噪音',
        instruction: '倾听大自然的声音，让雨声或海浪声为您营造宁静的睡眠环境...'
    },
    meditation: {
        title: '睡前冥想',
        instruction: '跟随引导进行睡前冥想，放松身心，准备进入深度睡眠...'
    },
    progressive: {
        title: '渐进式放松',
        instruction: '从头到脚逐步放松每一处肌肉，释放一天的疲劳和紧张...'
    }
};

function startSleepProgram(type) {
    const program = sleepPrograms[type];
    
    document.getElementById('sleep-player-title').textContent = program.title;
    document.getElementById('sleep-instruction-text').textContent = program.instruction;
    
    document.getElementById('sleep-player').style.display = 'flex';
}

function closeSleepPlayer() {
    document.getElementById('sleep-player').style.display = 'none';
    if (sleepAudio) {
        sleepAudio.pause();
    }
    isSleepPlaying = false;
    updateSleepPlayPauseButton();
}

function toggleSleepPlayPause() {
    if (isSleepPlaying) {
        if (sleepAudio) {
            sleepAudio.pause();
        }
        isSleepPlaying = false;
    } else {
        // 这里可以添加实际的音频播放逻辑
        isSleepPlaying = true;
    }
    updateSleepPlayPauseButton();
}

function updateSleepPlayPauseButton() {
    const btn = document.getElementById('sleep-play-pause-btn');
    const icon = btn.querySelector('i');
    if (isSleepPlaying) {
        icon.className = 'fas fa-pause';
    } else {
        icon.className = 'fas fa-play';
    }
}
</script>
@endsection 