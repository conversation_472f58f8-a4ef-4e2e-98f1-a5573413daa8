<?php

namespace App\Console\Commands;

use App\Models\LiveShow;
use Illuminate\Console\Command;

class UpdateLiveShowStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'live:update-status {--interval=60 : 轮询间隔(秒)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '更新直播状态';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $interval = (int) $this->option('interval');
        
        while (true) {
            $updatedCount = LiveShow::updateAllStatuses();
            
            if ($updatedCount > 0) {
                $this->info(date('Y-m-d H:i:s') . " Updated {$updatedCount} live shows");
            } else {
                $this->info(date('Y-m-d H:i:s') . " No updates needed");
            }

            sleep($interval);
        }

        return 0;
    }
} 