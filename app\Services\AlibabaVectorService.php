<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Exception;

/**
 * 阿里云DashVector向量检索服务
 * 
 * 基于阿里云DashVector API实现向量存储和检索
 */
class AlibabaVectorService
{
    /**
     * API配置
     */
    private string $apiKey;
    private string $endpoint;
    private string $collectionName;
    private int $dimension;
    private int $timeout;
    private int $maxRetries;

    /**
     * 构造函数
     */
    public function __construct()
    {
        // DashVector配置
        $dashvectorConfig = config('services.dashvector');
        $this->endpoint = $dashvectorConfig['endpoint'];
        $this->apiKey = $dashvectorConfig['api_key'];
        $this->collectionName = $dashvectorConfig['collection_name'];
        $this->dimension = $dashvectorConfig['dimension'];
        $this->timeout = $dashvectorConfig['timeout'];
        $this->maxRetries = $dashvectorConfig['max_retries'];
        
        if (empty($this->endpoint) || empty($this->apiKey)) {
            throw new Exception('DashVector配置不完整，请检查DASHVECTOR_ENDPOINT和DASHVECTOR_API_KEY环境变量');
        }
    }

    /**
     * 初始化Collection（如果不存在则创建）
     *
     * @return bool
     * @throws Exception
     */
    public function initializeCollection(): bool
    {
        try {
            // 检查Collection是否存在
            $response = $this->makeApiRequest('GET', "/v1/collections/{$this->collectionName}");
            
            if ($response) {
                Log::info('DashVector Collection已存在', [
                    'collection' => $this->collectionName,
                ]);
                return true;
            }
        } catch (Exception $e) {
            // Collection不存在，需要创建
            Log::info('Collection不存在，准备创建', [
                'collection' => $this->collectionName,
                'error' => $e->getMessage(),
            ]);
        }

        // 创建Collection
        $createData = [
            'name' => $this->collectionName,
            'dimension' => $this->dimension,
            'metric' => 'cosine', // 使用余弦相似度
            'dtype' => 'float',
        ];

        $response = $this->makeApiRequest('POST', '/v1/collections', $createData);
        
        Log::info('DashVector Collection创建成功', [
            'collection' => $this->collectionName,
            'dimension' => $this->dimension,
        ]);

        return true;
    }

    /**
     * 插入向量文档
     *
     * @param string $id 文档ID
     * @param array $vector 向量数据
     * @param array $fields 附加字段
     * @return bool
     * @throws Exception
     */
    public function insertDoc(string $id, array $vector, array $fields = []): bool
    {
        $retries = 0;
        $lastException = null;

        while ($retries < $this->maxRetries) {
            try {
                $doc = [
                    'id' => $id,
                    'vector' => $vector,
                ];

                if (!empty($fields)) {
                    $doc['fields'] = $fields;
                }

                $data = ['docs' => [$doc]];
                
                $response = $this->makeApiRequest(
                    'POST', 
                    "/v1/collections/{$this->collectionName}/docs", 
                    $data
                );

                Log::info('向量文档插入成功', [
                    'id' => $id,
                    'vector_dimension' => count($vector),
                    'fields_count' => count($fields),
                    'retries' => $retries,
                ]);

                return true;

            } catch (Exception $e) {
                $lastException = $e;
                $retries++;
                
                Log::warning('向量文档插入失败，准备重试', [
                    'id' => $id,
                    'retry' => $retries,
                    'max_retries' => $this->maxRetries,
                    'error' => $e->getMessage(),
                ]);

                if ($retries < $this->maxRetries) {
                    sleep(pow(2, $retries)); // 指数退避
                }
            }
        }

        Log::error('向量文档插入最终失败', [
            'id' => $id,
            'retries' => $retries,
            'error' => $lastException?->getMessage(),
        ]);

        throw new Exception('向量文档插入失败: ' . $lastException?->getMessage());
    }

    /**
     * 批量插入向量文档
     *
     * @param array $docs 文档数组，每个文档包含id、vector和fields
     * @return bool
     * @throws Exception
     */
    public function insertDocsBatch(array $docs): bool
    {
        $batchSize = 100; // DashVector建议的批次大小
        $batches = array_chunk($docs, $batchSize);
        
        foreach ($batches as $batchIndex => $batch) {
            try {
                $data = ['docs' => $batch];
                
                $response = $this->makeApiRequest(
                    'POST', 
                    "/v1/collections/{$this->collectionName}/docs", 
                    $data
                );
                
                Log::info('批量向量文档插入进度', [
                    'batch' => $batchIndex + 1,
                    'total_batches' => count($batches),
                    'batch_size' => count($batch),
                ]);
                
                // 批次间延迟，避免API限流
                if ($batchIndex < count($batches) - 1) {
                    sleep(1);
                }
                
            } catch (Exception $e) {
                Log::error('批量向量文档插入失败', [
                    'batch_index' => $batchIndex,
                    'batch_size' => count($batch),
                    'error' => $e->getMessage(),
                ]);
                throw $e;
            }
        }
        
        return true;
    }

    /**
     * 向量相似性查询
     *
     * @param array $queryVector 查询向量
     * @param int $topk 返回结果数量
     * @param array $filter 过滤条件
     * @param bool $includeFields 是否包含字段信息
     * @return array
     * @throws Exception
     */
    public function queryDocs(
        array $queryVector, 
        int $topk = 10, 
        array $filter = [], 
        bool $includeFields = true
    ): array {
        $retries = 0;
        $lastException = null;

        while ($retries < $this->maxRetries) {
            try {
                $queryData = [
                    'vector' => $queryVector,
                    'topk' => $topk,
                    'include_fields' => $includeFields,
                ];

                // 转换 filter 格式为 DashVector 支持的字符串格式
                if (!empty($filter)) {
                    $queryData['filter'] = $this->buildFilterString($filter);
                }

                $response = $this->makeApiRequest(
                    'POST', 
                    "/v1/collections/{$this->collectionName}/query", 
                    $queryData
                );

                $results = $response['output'] ?? [];

                Log::info('向量查询成功', [
                    'query_dimension' => count($queryVector),
                    'topk' => $topk,
                    'results_count' => count($results),
                    'retries' => $retries,
                ]);

                return $results;

            } catch (Exception $e) {
                $lastException = $e;
                $retries++;
                
                Log::warning('向量查询失败，准备重试', [
                    'retry' => $retries,
                    'max_retries' => $this->maxRetries,
                    'error' => $e->getMessage(),
                ]);

                if ($retries < $this->maxRetries) {
                    sleep(pow(2, $retries));
                }
            }
        }

        Log::error('向量查询最终失败', [
            'retries' => $retries,
            'error' => $lastException->getMessage(),
        ]);

        throw $lastException;
    }

    /**
     * 构建 DashVector 过滤字符串
     *
     * @param array $filter 过滤条件数组
     * @return string 过滤字符串
     */
    private function buildFilterString(array $filter): string
    {
        $conditions = [];

        foreach ($filter as $field => $value) {
            if (is_bool($value)) {
                // 布尔值直接比较
                $conditions[] = "{$field} = " . ($value ? 'true' : 'false');
            } elseif (is_array($value)) {
                // 处理数组条件，如 ['$in' => ['article', 'video']]
                if (isset($value['$in']) && is_array($value['$in'])) {
                    $values = array_map(function($v) {
                        return is_string($v) ? "'{$v}'" : $v;
                    }, $value['$in']);
                    $conditions[] = "{$field} in (" . implode(', ', $values) . ")";
                } else {
                    // 其他数组条件暂时跳过
                    continue;
                }
            } elseif (is_string($value)) {
                // 字符串值
                $conditions[] = "{$field} = '{$value}'";
            } elseif (is_numeric($value)) {
                // 数值
                $conditions[] = "{$field} = {$value}";
            }
        }

        return implode(' AND ', $conditions);
    }

    /**
     * 删除向量文档
     *
     * @param array $ids 文档ID数组
     * @return bool
     * @throws Exception
     */
    public function deleteDocs(array $ids): bool
    {
        try {
            $data = ['ids' => $ids];
            
            $response = $this->makeApiRequest(
                'DELETE', 
                "/v1/collections/{$this->collectionName}/docs", 
                $data
            );

            Log::info('向量文档删除成功', [
                'ids' => $ids,
                'count' => count($ids),
            ]);

            return true;

        } catch (Exception $e) {
            Log::error('向量文档删除失败', [
                'ids' => $ids,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * 获取Collection统计信息
     *
     * @return array
     * @throws Exception
     */
    public function getCollectionStats(): array
    {
        try {
            $response = $this->makeApiRequest(
                'GET', 
                "/v1/collections/{$this->collectionName}/stats"
            );

            return $response;

        } catch (Exception $e) {
            Log::error('获取Collection统计信息失败', [
                'collection' => $this->collectionName,
                'error' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * 发起API请求
     *
     * @param string $method HTTP方法
     * @param string $path API路径
     * @param array|null $data 请求数据
     * @return array
     * @throws Exception
     */
    private function makeApiRequest(string $method, string $path, ?array $data = null): array
    {
        $url = $this->endpoint . $path;
        
        $headers = [
            'dashvector-auth-token' => $this->apiKey,
            'Content-Type' => 'application/json',
        ];

        $httpClient = Http::timeout($this->timeout)->withHeaders($headers);

        $response = match (strtoupper($method)) {
            'GET' => $httpClient->get($url),
            'POST' => $httpClient->post($url, $data),
            'DELETE' => $httpClient->delete($url, $data),
            default => throw new Exception("不支持的HTTP方法: {$method}"),
        };

        if (!$response->successful()) {
            $errorBody = $response->body();
            throw new Exception("DashVector API请求失败: HTTP {$response->status()}, {$errorBody}");
        }

        $responseData = $response->json();
        
        // 检查API响应中的错误
        if (isset($responseData['code']) && $responseData['code'] !== 0) {
            $message = $responseData['message'] ?? '未知错误';
            throw new Exception("DashVector API错误: {$message}");
        }

        return $responseData;
    }

    /**
     * 检查服务健康状态
     *
     * @return bool
     */
    public function healthCheck(): bool
    {
        try {
            $response = Http::timeout(10)
                ->withHeaders(['dashvector-auth-token' => $this->apiKey])
                ->get($this->endpoint . "/v1/collections/{$this->collectionName}");
            
            return $response->successful();
        } catch (Exception $e) {
            Log::error('DashVector服务健康检查失败', [
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * 生成文本向量
     * 
     * 使用配置的文本向量化服务（阿里云灵积或OpenAI）将文本转换为向量
     *
     * @param string $text 要转换的文本
     * @return array 向量数组
     * @throws Exception
     */
    public function generateTextVector(string $text): array
    {
        $config = config('services.text_embedding');
        $provider = $config['provider'];

        Log::info('开始生成文本向量', [
            'provider' => $provider,
            'text_length' => mb_strlen($text),
        ]);

        switch ($provider) {
            case 'openai':
                return $this->generateVectorWithOpenAI($text, $config);
            case 'dashscope':
                return $this->generateVectorWithDashScope($text, $config);
            default:
                throw new Exception("不支持的文本向量化服务: {$provider}，请使用 'dashscope' 或 'openai'");
        }
    }

    /**
     * 使用OpenAI生成向量
     */
    private function generateVectorWithOpenAI(string $text, array $config): array
    {
        $response = Http::timeout($config['timeout'])
            ->withHeaders([
                'Authorization' => 'Bearer ' . $config['api_key'],
                'Content-Type' => 'application/json',
            ])
            ->post($config['openai_endpoint'], [
                'input' => $text,
                'model' => $config['openai_model'],
            ]);

        if (!$response->successful()) {
            throw new Exception("OpenAI API请求失败: " . $response->body());
        }

        $data = $response->json();
        $vector = $data['data'][0]['embedding'] ?? [];
        
        Log::info('OpenAI向量生成成功', [
            'vector_dimension' => count($vector),
        ]);
        
        return $vector;
    }

    /**
     * 使用阿里云灵积生成向量
     */
    private function generateVectorWithDashScope(string $text, array $config): array
    {
        $response = Http::timeout($config['timeout'])
            ->withHeaders([
                'Authorization' => 'Bearer ' . $config['api_key'],
                'Content-Type' => 'application/json',
            ])
            ->post($config['dashscope_endpoint'], [
                'model' => $config['model'],
                'input' => [
                    'texts' => [$text]
                ],
                'parameters' => [
                    'text_type' => 'document'
                ]
            ]);

        if (!$response->successful()) {
            throw new Exception("阿里云灵积API请求失败: " . $response->body());
        }

        $data = $response->json();
        $vector = $data['output']['embeddings'][0]['embedding'] ?? [];
        
        Log::info('阿里云灵积向量生成成功', [
            'vector_dimension' => count($vector),
        ]);
        
        return $vector;
    }
} 