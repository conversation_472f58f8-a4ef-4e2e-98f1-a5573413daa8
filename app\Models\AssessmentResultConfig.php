<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Dcat\Admin\Traits\HasDateTimeFormatter;

/**
 * 心理测评结果配置模型
 */
class AssessmentResultConfig extends Model
{
    use HasDateTimeFormatter;

    // 批量赋值字段
    protected $fillable = [
        'questionnaire_id', 
        'dimension_name', 
        'min_score', 
        'max_score', 
        'level_name', 
        'description', 
        'suggestion', 
        'color', 
        'sort_order'
    ];

    /**
     * 该配置所属的问卷
     */
    public function questionnaire(): BelongsTo
    {
        return $this->belongsTo(AssessmentQuestionnaire::class, 'questionnaire_id', 'id');
    }
} 