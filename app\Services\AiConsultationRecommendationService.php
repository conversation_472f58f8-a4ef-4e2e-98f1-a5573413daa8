<?php

namespace App\Services;

use App\Services\AlibabaVectorService;
use App\Models\Article;
use App\Models\Video;
use App\Models\CourseLesson;
use Illuminate\Support\Facades\Log;
use Exception;

/**
 * AI咨询推荐服务
 * 
 * 基于用户咨询内容，使用向量搜索推荐相关的心理文章、视频和课程
 */
class AiConsultationRecommendationService
{
    private AlibabaVectorService $vectorService;

    public function __construct(AlibabaVectorService $vectorService)
    {
        $this->vectorService = $vectorService;
    }

    /**
     * 根据用户咨询内容获取推荐资源
     *
     * @param string $userQuery 用户咨询内容
     * @return array 推荐资源数组
     */
    public function getRecommendations(string $userQuery): array
    {
        try {
            // 提取用户咨询的关键词
            $keywords = $this->extractKeywords($userQuery);
            
            // 生成查询向量
            $queryVector = $this->vectorService->generateTextVector($keywords);
            
            // 执行向量搜索
            $searchResults = $this->vectorService->queryDocs(
                $queryVector,
                20, // 获取更多结果以便筛选
                [], // 不使用过滤条件
                true // 包含字段信息
            );
            
            // 按类型分组并筛选推荐内容
            $recommendations = $this->processSearchResults($searchResults);
            
            Log::info('AI咨询推荐生成成功', [
                'user_query' => $userQuery,
                'keywords' => $keywords,
                'results_count' => count($searchResults),
                'recommendations' => array_map(fn($r) => count($r), $recommendations)
            ]);
            
            return $recommendations;
            
        } catch (Exception $e) {
            Log::error('AI咨询推荐生成失败', [
                'user_query' => $userQuery,
                'error' => $e->getMessage()
            ]);
            
            // 返回空推荐
            return [
                'article' => null,
                'video' => null,
                'course' => null
            ];
        }
    }

    /**
     * 提取用户咨询的关键词
     *
     * @param string $userQuery 用户咨询内容
     * @return string 处理后的关键词
     */
    private function extractKeywords(string $userQuery): string
    {
        // 移除常见的无意义词汇
        $stopWords = [
            '我', '你', '他', '她', '它', '我们', '你们', '他们', '她们', '它们',
            '的', '了', '在', '是', '有', '和', '与', '或', '但', '而', '因为', '所以',
            '这', '那', '这个', '那个', '这些', '那些', '什么', '怎么', '为什么',
            '今天', '昨天', '明天', '现在', '刚才', '一直', '总是', '经常', '有时',
            '很', '非常', '特别', '比较', '更', '最', '太', '挺', '还', '就', '都',
            '吗', '呢', '吧', '啊', '哦', '嗯', '好', '行', '可以', '能', '会'
        ];
        
        // 心理相关关键词映射
        $psychologyKeywords = [
            '压力大' => '压力 焦虑 紧张',
            '焦虑' => '焦虑 担心 紧张',
            '抑郁' => '抑郁 情绪低落 悲伤',
            '失眠' => '失眠 睡眠 休息',
            '情绪' => '情绪管理 心理调节',
            '人际关系' => '人际交往 社交 沟通',
            '工作' => '职场 工作压力 职业发展',
            '学习' => '学习压力 考试焦虑 学业',
            '恋爱' => '恋爱关系 情感 爱情',
            '家庭' => '家庭关系 亲子 夫妻',
            '自信' => '自信心 自尊 自我价值',
            '孤独' => '孤独感 社交恐惧 内向',
            '愤怒' => '愤怒管理 情绪控制',
            '恐惧' => '恐惧症 害怕 担心',
            '强迫' => '强迫症 完美主义',
            '创伤' => '心理创伤 PTSD 痛苦经历'
        ];
        
        $processedQuery = $userQuery;
        
        // 替换心理关键词
        foreach ($psychologyKeywords as $keyword => $replacement) {
            if (strpos($processedQuery, $keyword) !== false) {
                $processedQuery = str_replace($keyword, $replacement, $processedQuery);
            }
        }
        
        // 移除停用词
        foreach ($stopWords as $stopWord) {
            $processedQuery = str_replace($stopWord, ' ', $processedQuery);
        }
        
        // 清理多余空格
        $processedQuery = preg_replace('/\s+/', ' ', trim($processedQuery));
        
        // 如果处理后的内容太短，使用原始查询
        if (mb_strlen($processedQuery) < 3) {
            $processedQuery = $userQuery;
        }
        
        return $processedQuery;
    }

    /**
     * 处理搜索结果，按类型分组并选择最佳推荐
     *
     * @param array $searchResults 向量搜索结果
     * @return array 分类后的推荐内容
     */
    private function processSearchResults(array $searchResults): array
    {
        $recommendations = [
            'article' => null,
            'video' => null,
            'course' => null
        ];
        
        foreach ($searchResults as $result) {
            $fields = $result['fields'] ?? [];
            $type = $fields['type'] ?? '';
            $score = $result['score'] ?? 0;
            
            // 降低相似度阈值，因为向量搜索的分数可能较低
            if ($score < 0.5) {
                continue;
            }
            
            try {
                switch ($type) {
                    case 'article':
                        if (!$recommendations['article']) {
                            $article = $this->getArticleById($fields['indexable_id'] ?? null);
                            if ($article) {
                                $recommendations['article'] = $this->formatArticleRecommendation($article);
                            }
                        }
                        break;
                        
                    case 'video':
                        if (!$recommendations['video']) {
                            $video = $this->getVideoById($fields['indexable_id'] ?? null);
                            if ($video) {
                                $recommendations['video'] = $this->formatVideoRecommendation($video);
                            }
                        }
                        break;
                        
                    case 'course_lesson':
                        if (!$recommendations['course']) {
                            $course = $this->getCourseById($fields['indexable_id'] ?? null);
                            if ($course) {
                                $recommendations['course'] = $this->formatCourseRecommendation($course);
                            }
                        }
                        break;
                }
                
                // 如果三种类型都找到了，提前退出
                if ($recommendations['article'] && $recommendations['video'] && $recommendations['course']) {
                    break;
                }
                
            } catch (Exception $e) {
                Log::warning('处理推荐结果时出错', [
                    'type' => $type,
                    'fields' => $fields,
                    'error' => $e->getMessage()
                ]);
                continue;
            }
        }
        
        // 如果向量搜索没有找到足够的结果，使用备用推荐
        if (!$recommendations['article'] || !$recommendations['video'] || !$recommendations['course']) {
            $fallbackRecommendations = $this->getFallbackRecommendations();
            
            if (!$recommendations['article'] && $fallbackRecommendations['article']) {
                $recommendations['article'] = $fallbackRecommendations['article'];
            }
            if (!$recommendations['video'] && $fallbackRecommendations['video']) {
                $recommendations['video'] = $fallbackRecommendations['video'];
            }
            if (!$recommendations['course'] && $fallbackRecommendations['course']) {
                $recommendations['course'] = $fallbackRecommendations['course'];
            }
        }
        
        return $recommendations;
    }

    /**
     * 获取备用推荐内容（当向量搜索结果不足时使用）
     */
    private function getFallbackRecommendations(): array
    {
        $recommendations = [
            'article' => null,
            'video' => null,
            'course' => null
        ];
        
        try {
            // 获取推荐的文章
            $article = Article::with('category')
                ->where('status', Article::STATUS_PUBLISHED)
                ->where('is_recommended', 1)
                ->orderBy('views', 'desc')
                ->first();
            
            if ($article) {
                $recommendations['article'] = $this->formatArticleRecommendation($article);
            }
            
            // 获取推荐的视频
            $video = Video::with('category')
                ->where('status', Video::STATUS_PUBLISHED)
                ->where('is_recommended', 1)
                ->orderBy('views', 'desc')
                ->first();
            
            if ($video) {
                $recommendations['video'] = $this->formatVideoRecommendation($video);
            }
            
            // 获取推荐的课程
            $course = CourseLesson::with('category')
                ->where('status', CourseLesson::STATUS_PUBLISHED)
                ->where('is_recommended', 1)
                ->orderBy('views', 'desc')
                ->first();
            
            if ($course) {
                $recommendations['course'] = $this->formatCourseRecommendation($course);
            }
            
        } catch (Exception $e) {
            Log::warning('获取备用推荐失败', [
                'error' => $e->getMessage()
            ]);
        }
        
        return $recommendations;
    }

    /**
     * 根据ID获取文章
     */
    private function getArticleById($id): ?Article
    {
        if (!$id) return null;
        
        return Article::with('category')
            ->where('id', $id)
            ->where('status', Article::STATUS_PUBLISHED)
            ->first();
    }

    /**
     * 根据ID获取视频
     */
    private function getVideoById($id): ?Video
    {
        if (!$id) return null;
        
        return Video::with('category')
            ->where('id', $id)
            ->where('status', Video::STATUS_PUBLISHED)
            ->first();
    }

    /**
     * 根据ID获取课程
     */
    private function getCourseById($id): ?CourseLesson
    {
        if (!$id) return null;
        
        return CourseLesson::with('category')
            ->where('id', $id)
            ->where('status', CourseLesson::STATUS_PUBLISHED)
            ->first();
    }

    /**
     * 格式化文章推荐
     */
    private function formatArticleRecommendation(Article $article): array
    {
        return [
            'id' => $article->id,
            'title' => $article->title,
            'category' => $article->category?->name ?? '未分类',
            'url' => route('knowledge.article_detail', $article->id)
        ];
    }

    /**
     * 格式化视频推荐
     */
    private function formatVideoRecommendation(Video $video): array
    {
        return [
            'id' => $video->id,
            'title' => $video->title,
            'category' => $video->category?->name ?? '未分类',
            'url' => route('knowledge.video_detail', $video->id)
        ];
    }

    /**
     * 格式化课程推荐
     */
    private function formatCourseRecommendation(CourseLesson $course): array
    {
        return [
            'id' => $course->id,
            'title' => $course->title,
            'category' => $course->category?->name ?? '未分类',
            'url' => route('knowledge.course_detail', $course->id)
        ];
    }

    /**
     * 格式化推荐内容为文本
     *
     * @param array $recommendations 推荐内容
     * @return string 格式化后的推荐文本
     */
    public function formatRecommendationsText(array $recommendations): string
    {
        $text = "\n\n**本站相关内容推荐：**\n\n";
        
        if ($recommendations['article']) {
            $article = $recommendations['article'];
            $text .= "📖 **心理文章**：[{$article['title']}]({$article['url']}) \n\n";
        }
        
        if ($recommendations['course']) {
            $course = $recommendations['course'];
            $text .= "🎓 **心理课堂**：[{$course['title']}]({$course['url']}) \n\n";
        }
        
        if ($recommendations['video']) {
            $video = $recommendations['video'];
            $text .= "🎬 **心理视频**：[{$video['title']}]({$video['url']}) \n\n";
        }
        
        return $text;
    }
} 