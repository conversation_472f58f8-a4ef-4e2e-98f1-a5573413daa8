<?php

namespace App\Http\Controllers\Counselor;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\ConsultationAppointment;
use Carbon\Carbon;

class AppointmentController extends Controller
{
    /**
     * 构造函数 - 确保用户已登录
     */
    public function __construct()
    {
        $this->middleware('counselor.auth');
    }
    
    /**
     * 显示预约列表页面
     */
    public function index(Request $request)
    {
        $counselorId = $request->session()->get('counselor_id');
        
        // 获取所有预约
        $query = ConsultationAppointment::with('user')
            ->where('counselor_id', $counselorId);
        
        // 根据状态筛选
        if ($request->has('status')) {
            $status = $request->get('status');
            $query->where('status', $status);
        }
        
        $appointments = $query->orderBy('created_at', 'desc')
            ->paginate(10)
            ->withQueryString(); // 保留查询参数
        
        // 统计各状态数量
        $pendingCount = ConsultationAppointment::where('counselor_id', $counselorId)
            ->where('status', ConsultationAppointment::STATUS_PENDING)
            ->count();
            
        $confirmedCount = ConsultationAppointment::where('counselor_id', $counselorId)
            ->where('status', ConsultationAppointment::STATUS_CONFIRMED)
            ->count();
            
        $ongoingCount = ConsultationAppointment::where('counselor_id', $counselorId)
            ->where('status', ConsultationAppointment::STATUS_ONGOING)
            ->count();
            
        $completedCount = ConsultationAppointment::where('counselor_id', $counselorId)
            ->where('status', ConsultationAppointment::STATUS_COMPLETED)
            ->count();
            
        $cancelledCount = ConsultationAppointment::where('counselor_id', $counselorId)
            ->where('status', ConsultationAppointment::STATUS_CANCELLED)
            ->count();
        
        return view('counselor.appointments', [
            'appointments' => $appointments,
            'pendingCount' => $pendingCount,
            'confirmedCount' => $confirmedCount,
            'ongoingCount' => $ongoingCount,
            'completedCount' => $completedCount,
            'cancelledCount' => $cancelledCount
        ]);
    }
    
    /**
     * 确认预约
     */
    public function confirm(Request $request, $id)
    {
        $counselorId = $request->session()->get('counselor_id');
        
        $appointment = ConsultationAppointment::where('id', $id)
            ->where('counselor_id', $counselorId)
            ->where('status', ConsultationAppointment::STATUS_PENDING)
            ->firstOrFail();
            
        $appointment->status = ConsultationAppointment::STATUS_CONFIRMED;
        $appointment->save();
        
        return redirect()->back()->with('success', '预约已确认');
    }
    
    /**
     * 开始咨询
     */
    public function start(Request $request, $id)
    {
        $counselorId = $request->session()->get('counselor_id');
        
        $appointment = ConsultationAppointment::where('id', $id)
            ->where('counselor_id', $counselorId)
            ->where('status', ConsultationAppointment::STATUS_CONFIRMED)
            ->firstOrFail();
            
        $appointment->status = ConsultationAppointment::STATUS_ONGOING;
        $appointment->save();
        
        // 跳转到聊天页面
        return redirect()->route('counselor.messages.show', $appointment->user_id)
            ->with('success', '咨询已开始');
    }
    
    /**
     * 完成咨询
     */
    public function complete(Request $request, $id)
    {
        $counselorId = $request->session()->get('counselor_id');
        
        $appointment = ConsultationAppointment::where('id', $id)
            ->where('counselor_id', $counselorId)
            ->where('status', ConsultationAppointment::STATUS_ONGOING)
            ->firstOrFail();
            
        $appointment->status = ConsultationAppointment::STATUS_COMPLETED;
        $appointment->completed_at = Carbon::now();
        $appointment->save();
        
        // 跳转回聊天页面
        return redirect()->route('counselor.messages.show', $appointment->user_id)
            ->with('success', '咨询已标记为完成');
    }
    
    /**
     * 拒绝预约
     */
    public function reject(Request $request, $id)
    {
        $counselorId = $request->session()->get('counselor_id');
        
        $appointment = ConsultationAppointment::where('id', $id)
            ->where('counselor_id', $counselorId)
            ->where('status', ConsultationAppointment::STATUS_PENDING)
            ->firstOrFail();
            
        $appointment->status = ConsultationAppointment::STATUS_CANCELLED;
        $appointment->save();
        
        return redirect()->back()->with('success', '预约已拒绝');
    }
    
    /**
     * 取消预约
     */
    public function cancel(Request $request, $id)
    {
        $counselorId = $request->session()->get('counselor_id');
        
        $appointment = ConsultationAppointment::where('id', $id)
            ->where('counselor_id', $counselorId)
            ->whereIn('status', [
                ConsultationAppointment::STATUS_PENDING,
                ConsultationAppointment::STATUS_CONFIRMED
            ])
            ->firstOrFail();
            
        $appointment->status = ConsultationAppointment::STATUS_CANCELLED;
        $appointment->save();
        
        return redirect()->back()->with('success', '预约已取消');
    }
}
