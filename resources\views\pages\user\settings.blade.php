@extends('layouts.app')

@section('title', '设置 - 心理咨询平台')

@section('custom-styles')
<style>
    body {
        background-color: #f8f9fa;
    }
    
    .container {
        max-width: 900px;
        margin: 0 auto;
        padding-bottom: 80px !important; /* 为底部菜单栏预留空间 */
    }
    
    .page-header {
        display: flex;
        align-items: center;
        background: linear-gradient(135deg, #5b7cef 0%, #3c67e3 100%);
        color: white;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        border-radius: 0 0 10px 10px;
        padding: 12px 15px;
        margin-bottom: 15px;
    }
    
    .back-button {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 30px;
        height: 30px;
    }
    
    .back-icon {
        border-top: 2px solid white;
        border-left: 2px solid white;
        width: 12px;
        height: 12px;
        transform: rotate(-45deg);
    }
    
    .page-header h1 {
        font-size: 18px;
        font-weight: 500;
        margin: 0 auto;
        text-align: center;
        flex-grow: 1;
        margin-left: -30px;
        text-shadow: 0 1px 2px rgba(0,0,0,0.1);
    }
    
    .settings-card {
        background-color: #fff;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        margin: 15px;
        padding: 20px;
        border: 1px solid rgba(0, 0, 0, 0.03);
    }
    
    .section-title {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 20px;
        color: #333;
        border-bottom: 1px solid #f0f0f0;
        padding-bottom: 10px;
    }
    
    .setting-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px 0;
        border-bottom: 1px solid #f0f0f0;
    }
    
    .setting-item:last-child {
        border-bottom: none;
    }
    
    .setting-label {
        font-size: 15px;
        color: #333;
        font-weight: 500;
    }
    
    .setting-description {
        font-size: 13px;
        color: #888;
        margin-top: 4px;
    }
    
    .setting-toggle {
        position: relative;
        width: 50px;
        height: 24px;
    }
    
    .toggle-input {
        opacity: 0;
        width: 0;
        height: 0;
    }
    
    .toggle-slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        transition: .4s;
        border-radius: 24px;
    }
    
    .toggle-slider:before {
        position: absolute;
        content: "";
        height: 18px;
        width: 18px;
        left: 3px;
        bottom: 3px;
        background-color: white;
        transition: .4s;
        border-radius: 50%;
    }
    
    .toggle-input:checked + .toggle-slider {
        background-color: #3c67e3;
    }
    
    .toggle-input:checked + .toggle-slider:before {
        transform: translateX(26px);
    }
    
    .btn-primary {
        display: block;
        width: 100%;
        padding: 14px;
        background: linear-gradient(135deg, #2c7ffc, #7b58ff);
        color: white;
        text-align: center;
        border: none;
        border-radius: 8px;
        font-size: 16px;
        font-weight: 600;
        text-decoration: none;
        box-shadow: 0 4px 10px rgba(44, 127, 252, 0.2);
        transition: all 0.3s ease;
        cursor: pointer;
        margin-top: 10px;
    }
    
    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 15px rgba(44, 127, 252, 0.25);
    }
    
    .alert {
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 20px;
    }
    
    .alert-success {
        background-color: #e3fcef;
        color: #0d915c;
        border: 1px solid #c3f0da;
    }
    
    .about-section {
        margin-top: 30px;
        text-align: center;
        color: #888;
        font-size: 13px;
    }
    
    .version {
        margin-top: 8px;
        font-size: 12px;
        color: #aaa;
    }
</style>
@endsection

@section('content')
<div class="page-header">
    <a href="{{ route('my') }}" class="back-button">
        <div class="back-icon"></div>
    </a>
    <h1>设置</h1>
    <div style="width: 30px;"></div>
</div>

<div class="container">
    @if(session('success'))
    <div class="alert alert-success">
        {{ session('success') }}
    </div>
    @endif
    
    <div class="settings-card">
        <h2 class="section-title">通知与提醒</h2>
        
        <form action="{{ route('user.settings.update') }}" method="POST" id="settings-form">
            @csrf
            <div class="setting-item">
                <div>
                    <div class="setting-label">系统通知</div>
                    <div class="setting-description">接收重要系统通知和活动推送</div>
                </div>
                <label class="setting-toggle">
                    <input type="checkbox" class="toggle-input" name="notification_sms" value="1" checked>
                    <span class="toggle-slider"></span>
                </label>
            </div>
            
            <div class="setting-item">
                <div>
                    <div class="setting-label">学习提醒</div>
                    <div class="setting-description">接收课程相关的学习提醒</div>
                </div>
                <label class="setting-toggle">
                    <input type="checkbox" class="toggle-input" name="notification_email" value="1" checked>
                    <span class="toggle-slider"></span>
                </label>
            </div>
        </form>
    </div>
    
    <div class="settings-card">
        <h2 class="section-title">账号安全</h2>
        
        <a href="{{ route('user.account') }}" style="text-decoration: none; color: inherit;">
            <div class="setting-item">
                <div>
                    <div class="setting-label">账号安全设置</div>
                    <div class="setting-description">管理密码和账号安全</div>
                </div>
                <span class="material-icons" style="color: #999;">chevron_right</span>
            </div>
        </a>
        
        <div class="setting-item">
            <div>
                <div class="setting-label">隐私模式</div>
                <div class="setting-description">开启后将隐藏您的部分个人信息</div>
            </div>
            <label class="setting-toggle">
                <input type="checkbox" class="toggle-input" name="privacy_mode" value="1" form="settings-form">
                <span class="toggle-slider"></span>
            </label>
        </div>
    </div>
    
    <div class="settings-card">
        <h2 class="section-title">关于我们</h2>
        
        <div class="about-section">
            <p>心理咨询平台</p>
            <p>为您的心理健康保驾护航</p>
            <div class="version">版本号：1.0.0</div>
        </div>
    </div>
    
    <div style="text-align: center; margin: 30px 15px;">
        <button type="submit" class="btn-primary" form="settings-form">保存设置</button>
    </div>
</div>
@endsection
