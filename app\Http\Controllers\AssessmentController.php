<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Services\QuestionnaireService;
use App\Services\AssessmentService;
use App\Http\Requests\StoreAssessmentResponseRequest;
use App\Models\AssessmentQuestionnaire;
use App\Models\AssessmentResponse;
use App\Models\AssessmentAnswer;
use App\Models\AssessmentOption;
use App\Models\AssessmentResultConfig;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class AssessmentController extends Controller
{
    protected $questionnaireService;
    protected $assessmentService;

    public function __construct(QuestionnaireService $questionnaireService, AssessmentService $assessmentService)
    {
        $this->questionnaireService = $questionnaireService;
        $this->assessmentService = $assessmentService;
    }

    /**
     * 显示心理测评首页
     */
    public function index()
    {
        // 获取所有激活的问卷，按领域分组
        $questionnaires = AssessmentQuestionnaire::where('is_active', true)
            ->with(['questions'])
            ->withCount('responses')
            ->orderBy('created_at', 'desc')
            ->get();

        // 按中文领域名称分组
        $groupedQuestionnaires = $questionnaires->groupBy(function($questionnaire) {
            return self::getDomainName($questionnaire->domain);
        });

        // 统计数据
        $totalQuestionnaires = $questionnaires->count();
        $totalResponses = AssessmentResponse::count();

        return view('assessment.index', compact('questionnaires', 'groupedQuestionnaires', 'totalQuestionnaires', 'totalResponses'));
    }

    /**
     * 显示问卷详情页面
     */
    public function show($id)
    {
        $questionnaire = AssessmentQuestionnaire::with(['questions.options', 'resultConfigs'])
            ->where('is_active', true)
            ->findOrFail($id);

        // 获取问卷统计信息
        $totalResponses = $questionnaire->responses()->count();
        $averageScore = $questionnaire->responses()
            ->join('assessment_analyses', 'assessment_responses.id', '=', 'assessment_analyses.response_id')
            ->avg('overall_score') ?? 0;

        // 检查用户是否已完成此问卷
        $hasCompleted = false;
        if (Auth::check()) {
            $hasCompleted = AssessmentResponse::where('user_id', Auth::id())
                ->where('questionnaire_id', $id)
                ->whereNotNull('submitted_at')
                ->exists();
        }

        // 获取相关问卷（同领域的其他问卷）
        $relatedQuestionnaires = AssessmentQuestionnaire::where('domain', $questionnaire->domain)
            ->where('id', '!=', $id)
            ->where('is_active', true)
            ->withCount('responses')
            ->limit(3)
            ->get();

        return view('assessment.show', compact('questionnaire', 'totalResponses', 'averageScore', 'hasCompleted', 'relatedQuestionnaires'));
    }

    /**
     * 开始测评 - 显示问卷题目
     */
    public function start($id)
    {
        $questionnaire = AssessmentQuestionnaire::with(['questions' => function($query) {
            $query->orderBy('sort_order')->with('options');
        }])
        ->where('is_active', true)
        ->findOrFail($id);

        // 将问题转换为数组格式，便于JavaScript处理
        $questions = $questionnaire->questions->map(function($question) {
            return [
                'id' => $question->id,
                'content' => $question->content,
                'type' => $question->type,
                'options' => $question->options->map(function($option) {
                    return [
                        'id' => $option->id,
                        'content' => $option->content,
                        'score_value' => $option->score_value
                    ];
                })
            ];
        });

        return view('assessment.start', compact('questionnaire', 'questions'));
    }

    /**
     * 提交答卷
     */
    public function submit(Request $request)
    {
        Log::info('开始处理测评提交', ['request_data' => $request->all()]);
        
        // 验证请求数据
        $request->validate([
            'questionnaire_id' => 'required|exists:assessment_questionnaires,id',
            'answers' => 'required|array|min:1',
        ]);

        Log::info('请求数据验证通过');

        // 如果未登录，要求先登录
        if (!Auth::check()) {
            Log::warning('用户未登录，要求登录');
            return response()->json([
                'success' => false,
                'message' => '请先登录后再进行测评',
                'redirect' => route('login')
            ], 401);
        }

        $userId = Auth::id();
        Log::info('用户已登录', ['user_id' => $userId]);

        try {
            DB::beginTransaction();
            Log::info('开始数据库事务');

            // 创建答卷记录
            $response = AssessmentResponse::create([
                'user_id' => $userId,
                'questionnaire_id' => $request->questionnaire_id,
                'submitted_at' => now(),
            ]);

            Log::info('答卷记录创建成功', ['response_id' => $response->id]);

            // 保存答案并计算分数
            $totalScore = 0;
            foreach ($request->answers as $questionId => $optionId) {
                $option = AssessmentOption::findOrFail($optionId);
                
                AssessmentAnswer::create([
                    'response_id' => $response->id,
                    'question_id' => $questionId,
                    'option_id' => $optionId,
                ]);

                $totalScore += $option->score_value;
            }

            Log::info('答案保存完成', ['total_score' => $totalScore]);

            // 更新总分
            $response->update(['total_score' => $totalScore]);
            Log::info('总分更新完成');

            // 创建分析结果
            $analysis = $this->generateAnalysis($request->questionnaire_id,$response, $totalScore);
            Log::info('分析结果创建完成', ['analysis_id' => $analysis->id]);
            
            DB::commit();
            Log::info('数据库事务提交成功');

            return response()->json([
                'success' => true,
                'response_id' => $response->id,
                'message' => '测评提交成功！'
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            Log::error('测评提交失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'success' => false,
                'message' => '提交失败，请重试：' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * 显示测评结果
     */
    public function result($id)
    {
        $response = AssessmentResponse::with([
            'questionnaire.resultConfigs',
            'analysis',
            'answers.option'
        ])->findOrFail($id);

        // 如果没有分析结果，生成一个
        if (!$response->analysis) {
            $totalScore = $response->answers->sum('option.score_value');
            $this->generateAnalysis($response->questionnaire_id,$response, $totalScore);
            $response->load('analysis');
        }

        // 获取分析结果对象
        $analysis = $response->analysis;

        // 获取最大分数
        $maxScore = $response->questionnaire->questions()
            ->join('assessment_options', 'assessment_questions.id', '=', 'assessment_options.question_id')
            ->max('score_value') * $response->questionnaire->questions->count();

        // 获取相关问卷（同领域的其他问卷）
        $relatedQuestionnaires = AssessmentQuestionnaire::where('domain', $response->questionnaire->domain)
            ->where('id', '!=', $response->questionnaire_id)
            ->where('is_active', true)
            ->withCount('responses')
            ->limit(3)
            ->get();
            
        // return    $analysis;

        return view('assessment.result', compact('response', 'analysis', 'maxScore', 'relatedQuestionnaires'));
    }

    /**
     * 我的测评记录
     */
    public function myRecords()
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }

        $records = AssessmentResponse::with(['questionnaire', 'analysis'])
            ->where('user_id', Auth::id())
            ->whereNotNull('submitted_at')
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('assessment.my-records', compact('records'));
    }

    /**
     * 转换领域英文名称为中文
     */
    public static function getDomainName($domain)
    {
        $domainMap = [
            'anxiety' => '焦虑评估',
            'depression' => '抑郁评估',
            'stress' => '压力评估',
            'personality' => '人格测试',
            'emotion' => '情绪健康',
            'social' => '人际关系',
            'cognitive' => '认知能力',
            'career' => '职业发展',
            'learning' => '学习能力',
            'snappy' => '趣味测评',
            'other' => '其他'
        ];
        
        return $domainMap[$domain] ?? $domain;
    }

    /**
     * 获取领域图标
     */
    public static function getDomainIcon($domain)
    {
        $iconMap = [
            'anxiety' => 'fas fa-heart',
            'depression' => 'fas fa-cloud',
            'stress' => 'fas fa-exclamation-triangle',
            'personality' => 'fas fa-user',
            'emotion' => 'fas fa-heart',
            'social' => 'fas fa-users',
            'cognitive' => 'fas fa-brain',
            'career' => 'fas fa-briefcase',
            'learning' => 'fas fa-graduation-cap',
            'other' => 'fas fa-clipboard-list'
        ];
        
        return $iconMap[$domain] ?? 'fas fa-clipboard-list';
    }

    /**
     * 生成分析结果
     */
    private function generateAnalysis($questionnaire_id,$response, $totalScore)
    {
        Log::info('开始生成分析结果', ['response_id' => $response->id, 'total_score' => $totalScore]);
        
        $questionnaire = $response->questionnaire;
        
        // 计算最大可能分数
        $maxPossibleScore = $questionnaire->questions->sum(function($question) {
            return $question->options->max('score_value');
        });
        
        Log::info('计算最大可能分数', ['max_possible_score' => $maxPossibleScore]);
        
        // 计算百分比
        $percentage = round(($totalScore / max(1, $maxPossibleScore)) * 100, 1);
        
        // 获取等级和建议
        $level = $this->getScoreLevel($totalScore, $questionnaire);
        $suggestions = $this->generateSuggestions($totalScore, $questionnaire);
        
        // 生成详细描述
        $description = $this->generateDescription($questionnaire_id,$totalScore, $questionnaire, $percentage);
        
        // 生成详细分析
        $detailedAnalysis = $this->generateDetailedAnalysis($questionnaire_id,$totalScore, $questionnaire, $percentage);

        Log::info('准备创建分析记录', [
            'level' => $level,
            'percentage' => $percentage
        ]);

        // 创建分析记录
        $analysis = \App\Models\AssessmentAnalysis::create([
            'response_id' => $response->id,
            'overall_score' => $totalScore,
            'level_name' => $level,
            'description' => $description,
            'suggestions' => $suggestions,
            'detailed_analysis' => $detailedAnalysis,
            'detail_json' => [
                'total_score' => $totalScore,
                'max_score' => $maxPossibleScore,
                'percentage' => $percentage,
                'level' => $level
            ]
        ]);

        Log::info('分析记录创建成功', ['analysis_id' => $analysis->id]);

        // 异步生成AI分析（使用Redis队列）
        try {
            // 重新启用AI分析任务分发
            \App\Jobs\GenerateAiAnalysisJob::dispatch($response->id);
            Log::info('AI分析任务分发成功，response_id: ' . $response->id);
        } catch (\Exception $e) {
            // AI分析任务分发失败不影响主流程，只记录日志
            Log::warning('AI分析任务分发失败: ' . $e->getMessage(), [
                'response_id' => $response->id,
                'error' => $e->getTraceAsString()
            ]);
        }

        return $analysis;
    }

    /**
     * 生成描述
     */
    private function generateDescription($id,$score, $questionnaire, $percentage)
    {
        $domain = $questionnaire->domain;
        $domainName = self::getDomainName($domain);
        

        
        //启用数据库描述
        $re = AssessmentResultConfig::where(['questionnaire_id'=>$id])->where('min_score','<=',$score)->where('max_score','>',$score)->first();
        if($re){
            return "{$domainName}分析结果为:".$re->description;
        }else{
            if ($percentage >= 80) {
                return "您在{$domainName}方面表现优秀，心理状态良好，能够很好地应对相关挑战。";
            } elseif ($percentage >= 60) {
                return "您在{$domainName}方面表现良好，整体状态稳定，偶尔可能需要一些调整。";
            } elseif ($percentage >= 40) {
                return "您在{$domainName}方面存在一些需要关注的问题，建议采取积极措施进行改善。";
            } else {
                return "您在{$domainName}方面可能面临较大挑战，建议寻求专业帮助和支持。";
            }
        }
    }

    /**
     * 生成详细分析
     */
    private function generateDetailedAnalysis($id, $score, $questionnaire, $percentage)
    {
        $domain = $questionnaire->domain;
        $domainName = self::getDomainName($domain);
        
        $analysis = "根据您的测评结果，您的{$domainName}得分为{$score}分，";
        
        
        //启用数据库描述
        $re = AssessmentResultConfig::where(['questionnaire_id'=>$id])->where('min_score','<=',$score)->where('max_score','>',$score)->first();
        if($re){
                $analysis .= "{$domainName}详细分析建议为:".($re->suggestion??"暂无相关建议");
            }else{
                if ($percentage >= 80) {
                $analysis .= "这表明您在该领域具有很强的适应能力和积极的心理状态。您能够有效地处理相关问题，并保持良好的心理平衡。";
            } elseif ($percentage >= 60) {
                $analysis .= "这表明您在该领域整体表现良好，具备基本的应对能力。虽然偶尔可能遇到一些困难，但总体上能够维持稳定的状态。";
            } elseif ($percentage >= 40) {
                $analysis .= "这表明您在该领域存在一定的困扰或挑战。这些问题可能会影响您的日常生活和工作，需要引起重视并采取相应的改善措施。";
            } else {
                $analysis .= "这表明您在该领域面临较为严重的困扰。这些问题可能对您的生活质量产生显著影响，强烈建议您寻求专业的心理健康服务。";
            }
        }
        
       
        
        return $analysis;
    }

    /**
     * 获取分数等级
     */
    private function getScoreLevel($score, $questionnaire)
    {
        $resultConfig = $questionnaire->resultConfigs()
            ->where('min_score', '<=', $score)
            ->where('max_score', '>=', $score)
            ->first();

        return $resultConfig ? $resultConfig->level_name : '正常';
    }

    /**
     * 生成建议
     */
    private function generateSuggestions($score, $questionnaire)
    {
        $resultConfig = $questionnaire->resultConfigs()
            ->where('min_score', '<=', $score)
            ->where('max_score', '>=', $score)
            ->first();

        return $resultConfig ? $resultConfig->suggestion : '继续保持良好的心理状态。';
    }

    /**
     * 获取结果配置
     */
    private function getResultConfig($response)
    {
        $totalScore = $response->analysis->overall_score;
        
        return $response->questionnaire->resultConfigs()
            ->where('min_score', '<=', $totalScore)
            ->where('max_score', '>=', $totalScore)
            ->first();
    }

    /**
     * 检查AI分析状态
     */
    public function checkAiAnalysisStatus($responseId)
    {
        try {
            $response = AssessmentResponse::with('analysis')->find($responseId);
            
            if (!$response || !$response->analysis) {
                return response()->json([
                    'success' => false,
                    'message' => '记录不存在'
                ], 404);
            }

            $hasAiAnalysis = !empty($response->analysis->ai_analysis);
            
            return response()->json([
                'success' => true,
                'has_ai_analysis' => $hasAiAnalysis,
                'ai_analysis' => $hasAiAnalysis ? $response->analysis->ai_analysis : null
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '检查失败'
            ], 500);
        }
    }
    
    /**
     * 生成结果页面二维码
     */
    public function generateQrCode(Request $request, $responseId)
    {
        try {
            $response = AssessmentResponse::find($responseId);
            
            if (!$response) {
                return response()->json([
                    'success' => false,
                    'message' => '记录不存在'
                ], 404);
            }

            // 生成完整的结果页面URL
            $resultUrl = url("/assessment/result/{$responseId}");
            
            return response()->json([
                'success' => true,
                'url' => $resultUrl,
                'qr_text' => $resultUrl
            ]);
            
        } catch (\Exception $e) {
            Log::error('生成二维码失败', [
                'error' => $e->getMessage(),
                'response_id' => $responseId
            ]);
            
            return response()->json([
                'success' => false,
                'message' => '生成二维码失败'
            ], 500);
        }
    }

    /**
     * 保存截图到服务器
     */
    public function saveScreenshot(Request $request, $responseId)
    {
        try {
            $request->validate([
                'screenshot' => 'required|string'
            ]);

            $response = AssessmentResponse::find($responseId);
            
            if (!$response) {
                return response()->json([
                    'success' => false,
                    'message' => '记录不存在'
                ], 404);
            }

            // 解析base64图片数据
            $imageData = $request->screenshot;
            if (strpos($imageData, 'data:image/') === 0) {
                $imageData = substr($imageData, strpos($imageData, ',') + 1);
            }
            
            $imageData = base64_decode($imageData);
            
            if (!$imageData) {
                return response()->json([
                    'success' => false,
                    'message' => '无效的图片数据'
                ], 400);
            }

            // 创建保存目录
            $saveDir = storage_path('app/public/screenshots');
            if (!file_exists($saveDir)) {
                mkdir($saveDir, 0755, true);
            }

            // 生成文件名
            $filename = 'result_' . $responseId . '_' . time() . '.png';
            $filepath = $saveDir . '/' . $filename;

            // 保存文件
            if (file_put_contents($filepath, $imageData)) {
                $publicUrl = asset('storage/screenshots/' . $filename);
                
                return response()->json([
                    'success' => true,
                    'url' => $publicUrl,
                    'filename' => $filename
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => '保存截图失败'
                ], 500);
            }
            
        } catch (\Exception $e) {
            Log::error('保存截图失败', [
                'error' => $e->getMessage(),
                'response_id' => $responseId
            ]);
            
            return response()->json([
                'success' => false,
                'message' => '保存截图失败'
            ], 500);
        }
    }
}
