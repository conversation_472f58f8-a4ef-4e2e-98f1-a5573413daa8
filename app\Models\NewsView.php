<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class NewsView extends Model
{
    use HasFactory, HasDateTimeFormatter;

    /**
     * 表名
     *
     * @var string
     */
    protected $table = 'news_views';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'news_id', 'user_id', 'ip_address', 'user_agent'
    ];

    /**
     * 获取关联的新闻
     */
    public function news()
    {
        return $this->belongsTo(News::class);
    }

    /**
     * 获取关联的用户
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
