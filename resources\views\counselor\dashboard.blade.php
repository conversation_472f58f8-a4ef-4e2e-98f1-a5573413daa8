<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>咨询师工作台 - 心理健康平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: "SF Pro Text", "SF Pro Icons", "Helvetica Neue", Helvetica, Arial, sans-serif;
        }
        .sidebar {
            position: fixed;
            top: 0;
            bottom: 0;
            left: 0;
            z-index: 100;
            padding: 48px 0 0;
            box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
            background: #fff;
        }
        .sidebar-sticky {
            height: calc(100vh - 48px);
            overflow-x: hidden;
            overflow-y: auto;
        }
        .nav-link {
            color: #333;
            font-weight: 500;
            padding: 15px 25px;
            display: flex;
            align-items: center;
        }
        .nav-link:hover {
            background: rgba(52, 152, 219, 0.1);
        }
        .nav-link.active {
            color: #3498db;
            background: rgba(52, 152, 219, 0.15);
            position: relative;
        }
        .nav-link.active::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: #3498db;
        }
        .nav-icon {
            margin-right: 10px;
            font-size: 20px;
        }
        .badge-notifications {
            background: #e74c3c;
            color: white;
            font-size: 12px;
            padding: 3px 6px;
            border-radius: 10px;
        }
        .dashboard-header {
            background: linear-gradient(45deg, #3498db, #8e44ad);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
        }
        .dashboard-header h1 {
            font-size: 24px;
            font-weight: 600;
        }
        .dashboard-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .dashboard-card h2 {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }
        .dashboard-card h2 i {
            margin-right: 10px;
            color: #3498db;
        }
        .today-appointment {
            display: flex;
            align-items: center;
            padding: 15px;
            border-bottom: 1px solid #f1f1f1;
        }
        .today-appointment:last-child {
            border-bottom: none;
        }
        .appointment-time {
            width: 80px;
            text-align: center;
            font-weight: 600;
            color: #3498db;
        }
        .appointment-info {
            flex-grow: 1;
            padding: 0 15px;
        }
        .appointment-name {
            font-weight: 600;
            font-size: 16px;
        }
        .appointment-type {
            color: #666;
            font-size: 14px;
        }
        .appointment-status {
            padding: 3px 8px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 500;
        }
        .status-pending {
            background: #f39c12;
            color: white;
        }
        .status-confirmed {
            background: #27ae60;
            color: white;
        }
        .user-message {
            display: flex;
            align-items: center;
            padding: 15px;
            border-bottom: 1px solid #f1f1f1;
        }
        .user-message:last-child {
            border-bottom: none;
        }
        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            overflow: hidden;
            margin-right: 15px;
        }
        .user-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .message-info {
            flex-grow: 1;
        }
        .message-name {
            font-weight: 600;
            display: flex;
            justify-content: space-between;
        }
        .message-preview {
            color: #666;
            font-size: 14px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 250px;
        }
        .message-time {
            color: #999;
            font-size: 12px;
        }
        .message-unread {
            background: #e74c3c;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }
        .navbar {
            background: white;
        }
        .btn-action {
            border-radius: 20px;
            padding: 8px 15px;
            font-size: 12px;
            font-weight: 500;
            margin-left: 5px;
        }
        .btn-confirm {
            background: #27ae60;
            border-color: #27ae60;
            color: white;
        }
        .btn-reject {
            background: #e74c3c;
            border-color: #e74c3c;
            color: white;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-light bg-white fixed-top shadow-sm">
        <div class="container-fluid">
            <span class="navbar-brand">心理健康平台 - 咨询师工作台</span>
            <div class="d-flex">
                <div class="dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <span class="me-2">{{ session('counselor_name') }}</span>
                        <img src="{{ session('counselor_avatar') ? asset('storage/'.session('counselor_avatar')) : asset('images/default-avatar.jpg') }}" width="32" height="32" class="rounded-circle">
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                        <li>
                            <a href="{{ route('counselor.profile') }}" class="dropdown-item">
                                <i class="bi bi-person me-2"></i>个人资料
                            </a>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <form method="POST" action="{{ route('counselor.logout') }}">
                                @csrf
                                <button type="submit" class="dropdown-item">
                                    <i class="bi bi-box-arrow-right me-2"></i>退出登录
                                </button>
                            </form>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- 侧边导航 -->
            <nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block sidebar">
                <div class="position-sticky sidebar-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="{{ route('counselor.dashboard') }}">
                                <i class="bi bi-house-door nav-icon"></i>
                                首页
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ route('counselor.appointments') }}">
                                <i class="bi bi-calendar-check nav-icon"></i>
                                预约管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ route('counselor.messages') }}">
                                <i class="bi bi-chat-dots nav-icon"></i>
                                消息中心
                                @if($unreadMessages > 0)
                                <span class="ms-auto badge-notifications">{{ $unreadMessages }}</span>
                                @endif
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主要内容 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4" style="margin-top: 48px;">
                @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                @endif

                <div class="dashboard-header d-flex justify-content-between">
                    <div>
                        <h1>欢迎, {{ session('counselor_name') }}</h1>
                        <p class="mb-0">{{ date('Y年m月d日') }} {{ ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'][date('w')] }}</p>
                    </div>
                    <div class="d-flex align-items-center">
                        @if($unreadMessages > 0)
                        <div class="me-3 text-white">
                            <i class="bi bi-envelope-fill me-1"></i>
                            <span>{{ $unreadMessages }} 条未读消息</span>
                        </div>
                        @endif
                        <div>
                            <a href="{{ route('counselor.messages') }}" class="btn btn-light btn-sm">查看消息</a>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <!-- 今日预约 -->
                    <div class="col-md-6 mb-4">
                        <div class="dashboard-card h-100">
                            <h2><i class="bi bi-calendar-check"></i> 今日预约</h2>
                            
                            @if(count($todayAppointments) > 0)
                                @foreach($todayAppointments as $appointment)
                                <div class="today-appointment">
                                    <div class="appointment-time">
                                        {{ \Carbon\Carbon::parse($appointment->appointment_time)->format('H:i') }}
                                    </div>
                                    <div class="appointment-info">
                                        <div class="appointment-name">{{ $appointment->user->name }}</div>
                                        <div class="appointment-type">{{ $appointment->consultation_type }} · {{ \Carbon\Carbon::parse($appointment->appointment_time)->format('Y-m-d') }}</div>
                                    </div>
                                    <div>
                                        @if($appointment->status == 1)
                                        <span class="appointment-status status-pending">待确认</span>
                                        @elseif($appointment->status == 2)
                                        <span class="appointment-status status-confirmed">已确认</span>
                                        @elseif($appointment->status == 3)
                                        <span class="appointment-status status-confirmed">进行中</span>
                                        @endif
                                    </div>
                                </div>
                                @endforeach
                            @else
                                <div class="text-center py-4">
                                    <i class="bi bi-calendar-x" style="font-size: 48px; color: #ddd;"></i>
                                    <p class="mt-3 text-secondary">今日暂无预约</p>
                                </div>
                            @endif
                            
                            <div class="text-center mt-3">
                                <a href="{{ route('counselor.appointments') }}" class="btn btn-outline-primary">查看全部预约</a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 未来预约 -->
                    <div class="col-md-6 mb-4">
                        <div class="dashboard-card h-100">
                            <h2><i class="bi bi-calendar-plus"></i> 待处理预约</h2>
                            
                            @if(count($futureAppointments) > 0)
                                @foreach($futureAppointments as $appointment)
                                <div class="today-appointment">
                                    <div class="appointment-info">
                                        <div class="appointment-name">{{ $appointment->user->name }}</div>
                                        <div class="appointment-type">
                                            {{ $appointment->consultation_type }} · 
                                            {{ \Carbon\Carbon::parse($appointment->appointment_time)->format('Y-m-d H:i') }}
                                        </div>
                                    </div>
                                    <div>
                                        @if($appointment->status == 1)
                                            <form method="POST" action="{{ route('counselor.appointments.confirm', $appointment->id) }}" class="d-inline">
                                                @csrf
                                                <button type="submit" class="btn btn-action btn-confirm">确认</button>
                                            </form>
                                            <form method="POST" action="{{ route('counselor.appointments.reject', $appointment->id) }}" class="d-inline">
                                                @csrf
                                                <button type="submit" class="btn btn-action btn-reject">拒绝</button>
                                            </form>
                                        @elseif($appointment->status == 2)
                                            <span class="appointment-status status-confirmed">已确认</span>
                                        @endif
                                    </div>
                                </div>
                                @endforeach
                            @else
                                <div class="text-center py-4">
                                    <i class="bi bi-calendar-check" style="font-size: 48px; color: #ddd;"></i>
                                    <p class="mt-3 text-secondary">暂无待处理预约</p>
                                </div>
                            @endif
                        </div>
                    </div>
                    
                    <!-- 最近消息 -->
                    <div class="col-12">
                        <div class="dashboard-card">
                            <h2><i class="bi bi-chat-dots"></i> 最近消息</h2>
                            
                            @if(count($usersWithMessages) > 0)
                                @foreach($usersWithMessages as $userMessage)
                                <div class="user-message">
                                    <div class="user-avatar">
                                        <img src="{{ $userMessage['user']->avatar ?? 'https://via.placeholder.com/50' }}" alt="{{ $userMessage['user']->name }}">
                                    </div>
                                    <div class="message-info">
                                        <div class="message-name">
                                            <span>{{ $userMessage['user']->name }}</span>
                                            @if($userMessage['unread_count'] > 0)
                                            <span class="message-unread">{{ $userMessage['unread_count'] }}</span>
                                            @endif
                                        </div>
                                        <div class="message-preview">{{ $userMessage['latest_message']->content }}</div>
                                    </div>
                                    <div class="message-time">
                                        {{ \Carbon\Carbon::parse($userMessage['latest_message']->created_at)->diffForHumans() }}
                                    </div>
                                    <div class="ms-3">
                                        <a href="{{ route('counselor.messages.show', $userMessage['user']->id) }}" class="btn btn-sm btn-outline-primary">回复</a>
                                    </div>
                                </div>
                                @endforeach
                            @else
                                <div class="text-center py-4">
                                    <i class="bi bi-chat-square" style="font-size: 48px; color: #ddd;"></i>
                                    <p class="mt-3 text-secondary">暂无消息</p>
                                </div>
                            @endif
                            
                            <div class="text-center mt-3">
                                <a href="{{ route('counselor.messages') }}" class="btn btn-outline-primary">查看全部消息</a>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
