@extends('layouts.app')

@section('title', 'AI心理助手 - 心理咨询平台')

@section('custom-styles')
<style>
    /* 覆盖主要容器样式 */
    body .container {
        max-width: 500px;
        padding: 0;
        margin: 0 auto;
        overflow-x: hidden;
        height: auto;
        min-height: 100vh;
    }
    
    /* 页面标题样式 */
    .module-header {
        background: linear-gradient(45deg, #5b7cef, #3c67e3);
        background-size: cover;
        background-position: center;
        color: white;
        padding: 15px;
        position: relative;
        text-align: center;
        height: 56px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 10px rgba(60, 103, 227, 0.15);
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 100;
    }
    
    .module-header::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(circle at top right, rgba(255,255,255,0.1), transparent 70%);
        z-index: 1;
    }
    
    .module-title {
        font-size: 18px;
        font-weight: 600;
        position: relative;
        z-index: 2;
        color: white;
        text-shadow: 0 1px 2px rgba(0,0,0,0.2);
        letter-spacing: 0.5px;
    }
    
    .back-button {
        position: absolute;
        left: 15px;
        top: 50%;
        transform: translateY(-50%);
        z-index: 10;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: rgba(255,255,255,0.2);
        border-radius: 50%;
    }
    
    .back-icon {
        width: 16px;
        height: 16px;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z'/%3E%3C/svg%3E");
        background-repeat: no-repeat;
        background-size: contain;
    }
    
    /* 聊天容器样式 */
    .chat-container {
        max-width: 100%;
        height: 100vh;
        display: flex;
        flex-direction: column;
        background-color: #f8f8f8;
        padding-top: 56px; /* 为固定的顶部标题留出空间 */
        padding-bottom: 120px; /* 为底部输入框和菜单留出空间 */
        position: relative;
    }
    
    .chat-messages {
        flex: 1;
        padding: 15px;
        overflow-y: auto;
        display: flex;
        flex-direction: column;
    }
    
    .message {
        max-width: 75%;
        padding: 12px 16px;
        border-radius: 18px;
        margin-bottom: 12px;
        word-break: break-word;
        position: relative;
        font-size: 15px;
        line-height: 1.4;
        box-shadow: 0 1px 2px rgba(0,0,0,0.05);
    }
    
    .message-time {
        font-size: 10px;
        color: #999;
        margin-top: 4px;
        display: block;
        text-align: right;
    }
    
    .user-message {
        background-color: #007AFF;
        color: white;
        align-self: flex-end;
        border-bottom-right-radius: 6px;
    }
    
    .user-message .message-time {
        color: rgba(255,255,255,0.7);
    }
    
    .ai-message {
        background-color: #E9E9EB;
        color: #333;
        align-self: flex-start;
        border-bottom-left-radius: 6px;
    }
    
    .ai-message p {
        margin: 0 0 8px 0;
    }
    
    .ai-message p:last-child {
        margin-bottom: 0;
    }
    
    .ai-message-content {
        white-space: pre-wrap;
    }
    
    /* Markdown 样式 */
    .ai-message-content code {
        background-color: rgba(0, 0, 0, 0.06);
        padding: 2px 4px;
        border-radius: 3px;
        font-family: monospace;
        font-size: 0.9em;
    }
    
    .ai-message-content strong {
        font-weight: 600;
    }
    
    .ai-message-content em {
        font-style: italic;
    }
    
    .ai-message-content a {
        color: #0366d6;
        text-decoration: none;
    }
    
    .ai-message-content a:hover {
        text-decoration: underline;
    }
    
    .ai-message-content ul {
        padding-left: 20px;
        margin: 8px 0;
    }
    
    .ai-message-content li {
        margin-bottom: 4px;
    }
    
    .ai-message-content blockquote {
        border-left: 3px solid #ddd;
        padding-left: 10px;
        color: #666;
        margin: 8px 0 8px 5px;
    }
    
    .chat-input {
        padding: 12px 15px;
        background-color: #fff;
        box-shadow: 0 -1px 10px rgba(0,0,0,0.05);
        position: fixed;
        bottom: 50px; /* 为底部菜单留出空间 */
        left: 0;
        right: 0;
        z-index: 10;
    }
    
    .input-group {
        display: flex;
        align-items: center;
        background-color: #F2F2F7;
        border-radius: 22px;
        padding: 0 5px 0 15px;
    }
    
    #messageInput {
        flex: 1;
        border: none;
        background: transparent;
        padding: 12px 5px;
        outline: none;
        resize: none;
        max-height: 100px;
        overflow-y: auto;
        font-size: 15px;
        color: #333;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
    }
    
    .send-button {
        background-color: #007AFF;
        color: white;
        border: none;
        border-radius: 50%;
        width: 36px;
        height: 36px;
        margin-left: 5px;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
        transition: background-color 0.2s;
    }
    
    .send-button:active {
        background-color: #0062cc;
    }
    
    .send-icon {
        width: 18px;
        height: 18px;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M2.01 21L23 12 2.01 3 2 10l15 2-15 2z'/%3E%3C/svg%3E");
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
    }
    
    .welcome-message {
        text-align: center;
        padding: 20px;
        color: #555;
        background-color: rgba(255, 255, 255, 0.8);
        border-radius: 16px;
        margin: 10px 15px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        font-size: 15px;
        line-height: 1.5;
    }
    
    .disclaimer {
        font-size: 12px;
        color: #8E8E93;
        text-align: center;
        margin: 8px 0 12px;
        padding: 0 20px;
    }
    
    /* 加载动画 - iOS风格 */
    .typing-indicator {
        display: flex;
        padding: 8px 12px;
        background-color: #E9E9EB;
        border-radius: 18px;
        border-bottom-left-radius: 6px;
        align-self: flex-start;
        margin-bottom: 12px;
        box-shadow: 0 1px 2px rgba(0,0,0,0.05);
        width: 60px;
        justify-content: center;
    }
    
    .typing-indicator span {
        height: 6px;
        width: 6px;
        background-color: #8E8E93;
        border-radius: 50%;
        display: inline-block;
        margin: 0 2px;
        animation: typing 1.2s infinite ease-in-out;
    }
    
    .typing-indicator span:nth-child(1) {
        animation-delay: 0s;
    }
    
    .typing-indicator span:nth-child(2) {
        animation-delay: 0.2s;
    }
    
    .typing-indicator span:nth-child(3) {
        animation-delay: 0.4s;
    }
    
    @keyframes typing {
        0% {
            transform: scale(0.7);
            opacity: 0.5;
        }
        50% {
            transform: scale(1);
            opacity: 1;
        }
        100% {
            transform: scale(0.7);
            opacity: 0.5;
        }
    }
</style>
@endsection

@section('content')
<!-- 顶部标题区域 -->
<div class="module-header">
    <a href="{{ route('home') }}" class="back-button">
        <div class="back-icon"></div>
    </a>
    <div class="module-title">AI心理助手</div>
</div>

<div class="chat-container">
    <div class="chat-messages" id="chatMessages">
        <div class="welcome-message">
            <h3>欢迎使用 AI 心理助手</h3>
            <p>您可以随时向我倾诉您的困扰，我会尽力提供支持和建议。</p>
        </div>
        
        <div class="message ai-message">
            您好！我是您的AI心理助手。感谢您使用我们的服务。请告诉我您今天有什么需要帮助的？
            <span class="message-time">{{ date('H:i') }}</span>
        </div>
        
        <div class="disclaimer">
            注意：AI助手仅提供心理支持和建议，不能替代专业心理医生的诊断和治疗。如遇严重心理问题，请寻求专业咨询师帮助。
        </div>
    </div>
    
    <div class="chat-input">
        <div class="input-group">
            <textarea id="messageInput" placeholder="输入您想说的..." rows="1"></textarea>
            <button class="send-button" id="sendButton">
                <div class="send-icon"></div>
            </button>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const chatMessages = document.getElementById('chatMessages');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        let isTyping = false;
        
        // 添加消息到聊天区域
        function addMessage(content, isUser) {
            const messageDiv = document.createElement('div');
            messageDiv.className = isUser ? 'message user-message' : 'message ai-message';
            
            // 处理用户消息 - 直接显示
            if (isUser) {
                messageDiv.innerHTML = content + `<span class="message-time">${new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}</span>`;
                chatMessages.appendChild(messageDiv);
                scrollToBottom();
                return;
            }
            
            // 处理AI消息 - 添加打字效果
            const timeSpan = document.createElement('span');
            timeSpan.className = 'message-time';
            timeSpan.textContent = new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
            
            // 创建消息内容容器
            const contentDiv = document.createElement('div');
            contentDiv.className = 'ai-message-content';
            messageDiv.appendChild(contentDiv);
            messageDiv.appendChild(timeSpan);
            
            chatMessages.appendChild(messageDiv);
            scrollToBottom();
            
            // 格式化内容 - 处理换行符为段落
            const formattedContent = formatAiMessage(content);
            typeText(contentDiv, formattedContent);
        }
        
        // 格式化AI回复文本，解析Markdown并处理段落
        function formatAiMessage(text) {
            // 将连续两个换行符替换为段落分隔符
            const paragraphs = text.split(/\n\s*\n/);
            
            // 处理每个段落内的Markdown语法
            return paragraphs.map(p => {
                let processed = p;
                
                // 处理Markdown语法
                // 1. 处理粗体 **text** 或 __text__
                processed = processed.replace(/\*\*(.*?)\*\*|__(.*?)__/g, '<strong>$1$2</strong>');
                
                // 2. 处理斜体 *text* 或 _text_
                processed = processed.replace(/\*([^\*]+)\*|_([^_]+)_/g, '<em>$1$2</em>');
                
                // 3. 处理行内代码 `code`
                processed = processed.replace(/`([^`]+)`/g, '<code>$1</code>');
                
                // 4. 处理链接 [text](url)
                processed = processed.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank">$1</a>');
                
                // 5. 处理列表项
                processed = processed.replace(/^- (.+)$/gm, '<li>$1</li>');
                processed = processed.replace(/(<li>.+<\/li>\s*)+/g, '<ul>$&</ul>');
                
                // 6. 处理引用 > text
                processed = processed.replace(/^> (.+)$/gm, '<blockquote>$1</blockquote>');
                
                // 最后处理单个换行符为<br>
                return processed.replace(/\n/g, '<br>');
            });
        }
        
        // 打字效果动画 - 支持HTML标签
        function typeText(element, paragraphs) {
            // 完全重写打字效果，采用新的方法支持HTML标签
            let currentIndex = 0;
            
            // 初始化段落容器
            paragraphs.forEach((p, index) => {
                const para = document.createElement('p');
                para.style.margin = '0 0 8px 0';
                
                // 最后一个段落无需底部间距
                if (index === paragraphs.length - 1) {
                    para.style.marginBottom = '0';
                }
                
                // 初始时隐藏所有段落
                para.style.visibility = 'hidden';
                
                // 预先设置段落的HTML内容，但隐藏
                para.innerHTML = p;
                element.appendChild(para);
            });
            
            // 逐步显示段落
            function revealNextParagraph() {
                if (currentIndex >= paragraphs.length) {
                    return; // 所有段落已显示
                }
                
                const para = element.children[currentIndex];
                para.style.visibility = 'visible';
                
                // 应用打字机效果
                const originalContent = para.innerHTML;
                para.textContent = ''; // 清空内容开始打字
                
                let charIndex = 0;
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = originalContent;
                const textContent = tempDiv.textContent; // 提取纯文本内容
                
                const typeChar = () => {
                    if (charIndex < textContent.length) {
                        // 每次显示一个字符，但保留HTML标签
                        const percentage = (charIndex + 1) / textContent.length;
                        const visibleLength = Math.ceil(originalContent.length * percentage);
                        
                        // 创建一个正则表达式来提取HTML标签
                        let visibleContent = '';
                        let plainTextCount = 0;
                        let htmlIndex = 0;
                        
                        while (plainTextCount <= charIndex && htmlIndex < originalContent.length) {
                            const char = originalContent[htmlIndex];
                            visibleContent += char;
                            
                            // 如果不是在HTML标签内，则计入纯文本字符计数
                            if (char === '<') {
                                // 开始一个标签，找到对应的结束标签
                                const tagEnd = originalContent.indexOf('>', htmlIndex);
                                if (tagEnd !== -1) {
                                    // 复制整个标签
                                    visibleContent = visibleContent.substring(0, visibleContent.length - 1) + 
                                                    originalContent.substring(htmlIndex, tagEnd + 1);
                                    htmlIndex = tagEnd;
                                }
                            } else {
                                plainTextCount++;
                            }
                            
                            htmlIndex++;
                        }
                        
                        para.innerHTML = visibleContent;
                        charIndex++;
                        scrollToBottom();
                        setTimeout(typeChar, 10); // 打字速度
                    } else {
                        // 当前段落完成，移至下一段落
                        currentIndex++;
                        setTimeout(revealNextParagraph, 100); // 段落间间隔
                    }
                };
                
                typeChar();
            }
            
            // 开始显示第一段
            revealNextParagraph();
            scrollToBottom();
        }
        
        // 显示AI正在输入的状态
        function showTypingIndicator() {
            const typingDiv = document.createElement('div');
            typingDiv.className = 'ai-message typing-indicator';
            typingDiv.id = 'typingIndicator';
            
            typingDiv.innerHTML = `
                <span></span>
                <span></span>
                <span></span>
            `;
            
            chatMessages.appendChild(typingDiv);
            scrollToBottom();
        }
        
        // 隐藏AI正在输入的状态
        function hideTypingIndicator() {
            const typingIndicator = document.getElementById('typingIndicator');
            if (typingIndicator) {
                typingIndicator.remove();
            }
        }
        
        // 发送消息到服务器并获取AI响应
        async function sendMessage(userMessage) {
            try {
                // 显示正在输入状态
                showTypingIndicator();
                isTyping = true;
                
                // 发送请求到服务器
                const response = await fetch('{{ route("consultation.ai_consultation_send") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    body: JSON.stringify({ message: userMessage })
                });
                
                if (!response.ok) {
                    throw new Error('网络请求失败');
                }
                
                const data = await response.json();
                
                // 隐藏正在输入状态
                hideTypingIndicator();
                isTyping = false;
                
                // 添加AI回复 - 确保响应有效
                if (data.success && data.response) {
                    addMessage(data.response, false);
                } else {
                    addMessage('AI无法生成回复，请稍后再试。', false);
                    console.error('API返回无效响应:', data);
                }
            } catch (error) {
                hideTypingIndicator();
                isTyping = false;
                addMessage('很抱歉，连接出现问题。请稍后再试。', false);
                console.error('Error:', error);
            }
        }
        
        // 滚动到聊天区域底部
        function scrollToBottom() {
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }
        
        // 发送消息的处理函数
        function handleSendMessage() {
            const message = messageInput.value.trim();
            if (message && !isTyping) {
                addMessage(message, true);
                messageInput.value = '';
                
                // 自动调整输入框高度
                messageInput.style.height = 'auto';
                
                // 发送消息到服务器
                sendMessage(message);
            }
        }
        
        // 监听发送按钮点击事件
        sendButton.addEventListener('click', handleSendMessage);
        
        // 监听回车键发送消息
        messageInput.addEventListener('keydown', function(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                handleSendMessage();
            }
        });
        
        // 自动调整输入框高度
        messageInput.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = (this.scrollHeight) + 'px';
            
            // 限制最大高度
            if (this.scrollHeight > 100) {
                this.style.overflowY = 'auto';
            } else {
                this.style.overflowY = 'hidden';
            }
        });
        
        // 初始滚动到底部
        scrollToBottom();
    });
</script>
@endsection
