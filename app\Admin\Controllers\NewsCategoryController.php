<?php

namespace App\Admin\Controllers;

use App\Models\NewsCategory;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class NewsCategoryController extends AdminController
{
    /**
     * 页面标题
     *
     * @return string
     */
    protected function title()
    {
        return '新闻分类管理';
    }

    /**
     * 列表页面
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new NewsCategory(), function (Grid $grid) {
            $grid->column('id', 'ID')->sortable();
            $grid->column('name', '分类名称');
            $grid->column('slug', 'URL别名');
            $grid->column('description', '描述')->limit(30);
            $grid->column('sort_order', '排序值')->sortable()->editable();
            $grid->column('created_at', '创建时间')->sortable();
            
            // 快速搜索
            $grid->quickSearch(['name', 'slug', 'description']);
            
            // 快速创建
            $grid->quickCreate(function (Grid\Tools\QuickCreate $create) {
                $create->text('name', '分类名称')->required();
                $create->text('slug', 'URL别名')->required();
                $create->text('description', '描述');
                $create->text('sort_order', '排序值')->default(0);
            });
            
            // 批量操作, Dcat Admin框架默认已启用删除功能
            $grid->tools(function (Grid\Tools $tools) {
                $tools->batch(function (Grid\Tools\BatchActions $batch) {
                    // 启用默认批量删除
                });
            });
            
            // 表格过滤器
            $grid->filter(function (Grid\Filter $filter) {
                $filter->panel();
                $filter->like('name', '分类名称');
                $filter->like('slug', 'URL别名');
            });
        });
    }

    /**
     * 详情页面
     *
     * @param mixed $id
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new NewsCategory(), function (Show $show) {
            $show->field('id', 'ID');
            $show->field('name', '分类名称');
            $show->field('slug', 'URL别名');
            $show->field('description', '描述');
            $show->field('sort_order', '排序值');
            $show->field('created_at', '创建时间');
            $show->field('updated_at', '更新时间');
            
            $show->panel()->tools(function (Show\Tools $tools) {
                $tools->disableDelete();
            });
        });
    }

    /**
     * 表单页面
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new NewsCategory(), function (Form $form) {
            $form->display('id', 'ID');
            
            $form->text('name', '分类名称')
                ->required()
                ->rules('required|max:50');
                
            $form->text('slug', 'URL别名')
                ->required()
                ->rules('required|max:50|unique:news_categories,slug,'.$form->getKey());
                
            $form->textarea('description', '描述')
                ->rows(3);
                
            $form->number('sort_order', '排序值')
                ->default(0)
                ->help('数值越小排序越靠前');
                
            $form->display('created_at', '创建时间');
            $form->display('updated_at', '更新时间');
        });
    }
}
