<?php

namespace App\Admin\Actions;

use App\Models\ConsultationAppointment;
use Dcat\Admin\Grid\BatchAction;
use Illuminate\Database\Eloquent\Collection;

class BatchConfirmAppointment extends BatchAction
{
    // 确认弹窗信息
    public function confirm()
    {
        return '您确定要确认所选的预约吗？';
    }

    // 处理请求
    public function handle(Collection $collection)
    {
        $successCount = 0;
        foreach ($collection as $model) {
            if ($model->status == 1) { // 只处理待确认的预约
                $model->status = 2; // 已确认
                $model->save();
                $successCount++;
            }
        }

        return $this->response()
            ->success("成功确认 {$successCount} 条预约")
            ->refresh();
    }
}
