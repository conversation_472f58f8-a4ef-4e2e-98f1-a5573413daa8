<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreAssessmentResponseRequest extends FormRequest
{
    /**
     * 确定用户是否有权限提交答卷。
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * 定义请求的验证规则。
     */
    public function rules(): array
    {
        return [
            'questionnaire_id'        => 'required|exists:assessment_questionnaires,id',
            'answers'                 => 'required|array|min:1',
            'answers.*.question_id'   => 'required|exists:assessment_questions,id',
            'answers.*.option_id'     => 'nullable|exists:assessment_options,id',
            'answers.*.custom_value'  => 'nullable|string',
        ];
    }

    /**
     * 自定义验证错误时的字段名称。
     */
    public function attributes(): array
    {
        return [
            'questionnaire_id'        => '问卷 ID',
            'answers'                 => '答案列表',
            'answers.*.question_id'   => '题目 ID',
            'answers.*.option_id'     => '选项 ID',
            'answers.*.custom_value'  => '自定义答案',
        ];
    }
}
