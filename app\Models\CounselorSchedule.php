<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Dcat\Admin\Traits\HasDateTimeFormatter;

class CounselorSchedule extends Model
{
    use HasFactory, HasDateTimeFormatter;

    protected $fillable = [
        'counselor_id',
        'date',
        'start_time',
        'end_time',
        'is_available'
    ];

    protected $casts = [
        'date' => 'date',
        'start_time' => 'datetime',
        'end_time' => 'datetime',
        'is_available' => 'boolean'
    ];

    // 与咨询师的关联
    public function counselor()
    {
        return $this->belongsTo(Counselor::class);
    }

    // 与预约的关联
    public function appointments()
    {
        return $this->hasMany(ConsultationAppointment::class, 'schedule_id');
    }

    // 检查时间段是否已被预约
    public function isBooked()
    {
        return $this->appointments()->whereIn('status', [1, 2, 3])->exists();
    }

    // 格式化时间范围
    public function getTimeRangeAttribute()
    {
        return date('H:i', strtotime($this->start_time)) . ' - ' . date('H:i', strtotime($this->end_time));
    }
}
