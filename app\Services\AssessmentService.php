<?php

namespace App\Services;

use App\Models\AssessmentResponse;
use App\Http\Requests\StoreAssessmentResponseRequest;

/**
 * 处理用户提交的测评答卷，存储答案并初始化 AI 分析。
 */
class AssessmentService
{
    protected $questionnaireService;
    protected $aiService;

    public function __construct(
        QuestionnaireService $questionnaireService,
        AssessmentAiService $aiService
    ) {
        $this->questionnaireService = $questionnaireService;
        $this->aiService = $aiService;
    }

    /**
     * 处理并保存答卷
     *
     * @param StoreAssessmentResponseRequest $request
     * @param int $userId
     * @return AssessmentResponse
     */
    public function submitResponse(StoreAssessmentResponseRequest $request, int $userId)
    {
        $data = $request->validated();
        // 创建答卷
        $response = AssessmentResponse::create([
            'user_id'           => $userId,
            'questionnaire_id'  => $data['questionnaire_id'],
            'submitted_at'      => now(),
        ]);

        // 存储每个答案
        foreach ($data['answers'] as $item) {
            $response->answers()->create([
                'question_id'  => $item['question_id'],
                'option_id'    => $item['option_id'] ?? null,
                'custom_value' => $item['custom_value'] ?? null,
            ]);
        }

        // 异步调用 AI 分析
        dispatch(function() use ($response) {
            $this->aiService->analyze($response);
        });

        return $response;
    }
}
