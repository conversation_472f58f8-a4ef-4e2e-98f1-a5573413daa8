<?php

namespace App\Admin\Controllers;

use App\Models\QuizQuestion;
use App\Models\QuizActivity;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Admin;
use Dcat\Admin\Http\Controllers\AdminController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Dcat\Admin\Http\JsonResponse;

class QuizQuestionController extends AdminController
{
    protected $title = '问答题目';

    protected function grid()
    {
        return Grid::make(new QuizQuestion(), function (Grid $grid) {
            $grid->model()->with(['quizActivity']);
            
            // 获取问答活动ID过滤
            $quizActivityId = request()->get('quiz_activity_id');
            if ($quizActivityId) {
                $grid->model()->where('quiz_activity_id', $quizActivityId);
                $quizActivity = QuizActivity::find($quizActivityId);
                if ($quizActivity) {
                    $this->title = "问答题目 - {$quizActivity->title}";
                }
            }
            
            $grid->column('id', 'ID')->sortable();
            $grid->column('quiz_activity_id', '所属活动')->display(function () {
                return $this->quizActivity ? $this->quizActivity->title : '-';
            });
            $grid->column('question_text', '问题内容')->limit(50);
            $grid->column('question_type', '问题类型')->using([
                'single_choice' => '单选题',
                'multiple_choice' => '多选题',
                'true_false' => '判断题',
            ]);
            $grid->column('points', '分值');
            $grid->column('order', '排序');
            $grid->column('options_count', '选项数量')->display(function () {
                return $this->options->count();
            });
            
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id', 'ID');
                $filter->equal('quiz_activity_id', '所属活动')->select(function () {
                    return QuizActivity::pluck('title', 'id');
                });
                $filter->like('question_text', '问题内容');
                $filter->equal('question_type', '问题类型')->select([
                    'single_choice' => '单选题',
                    'multiple_choice' => '多选题',
                    'true_false' => '判断题',
                ]);
            });
            
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableView();
            });
            
            // 添加返回按钮
            if ($quizActivityId) {
                $grid->tools(function (Grid\Tools $tools) use ($quizActivityId) {
                    $tools->append('<a href="' . admin_url('quiz-activities') . '" class="btn btn-sm btn-white"><i class="feather icon-arrow-left"></i> 返回活动列表</a>');
                });
            }
            
            // 默认排序
            $grid->model()->orderBy('order', 'asc');
        });
    }

    protected function form()
    {
        return Form::make(new QuizQuestion(), function (Form $form) {
            // 忽略虚拟字段
            $form->ignore(['true_false_answer']);

            // 主键展示
            $form->display('id', 'ID');

            // 所属活动
            $quizActivityId = request('quiz_activity_id');
            if ($quizActivityId && $activity = QuizActivity::find($quizActivityId)) {
                $form->hidden('quiz_activity_id')->value($quizActivityId);
                $form->display('quiz_activity_name', '所属活动')->value($activity->title);
            } else {
                $form->select('quiz_activity_id', '所属活动')
                     ->options(QuizActivity::pluck('title', 'id'))
                     ->required();
            }

            // 问题内容
            $form->textarea('question_text', '问题内容')->rows(3)->required();

            // 答案解析
            $form->textarea('explanation', '答案解析')->rows(3)
                 ->help('解释为什么这是正确答案，将在用户提交后显示');

            // 分值 & 排序
            $form->number('points', '分值')->min(1)->default(1);
            $form->number('order', '排序')->default(0)
                 ->help('数字越小排序越靠前');

            // 题型分支
            $form->radio('question_type', '题目类型')
                 ->options([
                     'single_choice'   => '单选题',
                     'multiple_choice' => '多选题',
                     'true_false'      => '判断题',
                 ])
                 ->default($form->model()->exists ? $form->model()->question_type : 'single_choice')
                 ->required()
                 // 判断题场景
                 ->when('true_false', function (Form $form) {
    // 从已有数据中读取正确答案（1=是, 0=否）
    $defaultAnswer = 1;
    if ($form->model()->exists) {
        $correctOption = $form->model()->options->firstWhere('is_correct', true);
        $defaultAnswer = $correctOption && $correctOption->option_text === '否' ? 0 : 1;
    }
    $form->radio('true_false_answer', '正确答案')
                         ->options([1 => '是', 0 => '否'])
                         ->default($defaultAnswer)
                         ->rules('required_if:question_type,true_false');
})
                 // 单/多选题场景
                 ->when(['single_choice','multiple_choice'], function (Form $form) {
                     $form->hasMany('options', '选项', function (Form\NestedForm $option) {
                         $option->textarea('option_text', '选项内容')->rows(2)->required();
                         $option->switch('is_correct', '是否正确');
                         $option->number('order', '排序')->default(0);
                     })->useTable();
                 });

            // 保存前：校验 & 清理旧选项
            $form->saving(function (Form $form) {
                if ($form->question_type === 'single_choice' && !empty($form->options)) {
                    $count = collect($form->options)->where('is_correct', true)->count();
                    if ($count !== 1) {
                        return $form->error('单选题必须且只能有一个正确答案');
                    }
                }

                if ($form->question_type === 'true_false') {
                    // 判断题：在 saved 回调中统一删除并重建选项，无需在此处处理
                }
            });

            // 保存后：自动生成判断题选项并跳转
            $form->saved(function (Form $form) {
                if ($form->model()->question_type === 'true_false') {
                    // 删除旧选项
                    $form->model()->options()->delete();

                    // 获取用户选择
                    $answer = request('true_false_answer', 1);

                    // 创建新选项
                    $form->model()->options()->createMany([
                        ['option_text' => '是', 'is_correct' => $answer == 1, 'order' => 0],
                        ['option_text' => '否', 'is_correct' => $answer == 0, 'order' => 1],
                    ]);
                }

                return JsonResponse::make()->success('保存成功！')->location('quiz-questions');
            });
        });
    }




}
