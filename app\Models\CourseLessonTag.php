<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Dcat\Admin\Traits\HasDateTimeFormatter;

class CourseLessonTag extends Model
{
    use HasFactory, HasDateTimeFormatter;

    protected $fillable = [
        'name',
        'description',
        'is_active'
    ];

    protected $casts = [
        'is_active' => 'boolean'
    ];

    // 与课程的关联
    public function lessons()
    {
        return $this->belongsToMany(CourseLesson::class, 'course_lesson_tag', 'course_lesson_tag_id', 'course_lesson_id');
    }

    // 范围查询：启用的标签
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}
