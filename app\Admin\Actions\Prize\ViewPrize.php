<?php

namespace App\Admin\Actions\Prize;

use Dcat\Admin\Grid\RowAction;
use Illuminate\Http\Request;
use Dcat\Admin\Admin;

class ViewPrize extends RowAction
{
    protected $title = '查看';
    
    public function handle(Request $request)
    {
        // 获取当前行数据的ID
        $id = $this->getKey();
        
        // 查询数据库
        $model = \App\Models\QuizPrizeWinner::find($id);
        
        if (!$model) {
            return $this->response()->error('找不到对应的奖品记录');
        }
        
        // 这里只是查看，不做任何状态变更
        return $this->response()
                    ->success('查看奖品信息成功')
                    ->refresh();
    }
}
