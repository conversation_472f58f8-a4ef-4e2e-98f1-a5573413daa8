@extends('layouts.app')

@section('title', '我的奖品')

@section('custom-styles')
<link rel="stylesheet" href="{{ asset('css/quiz.css') }}">
<style>
    /* 页面特定样式 */
    
    .page-title {
        font-size: 20px;
        font-weight: 600;
        margin-bottom: 20px;
        color: #333;
        display: flex;
        align-items: center;
    }
    
    .page-title-icon {
        margin-right: 10px;
        color: #FF9800;
    }
    
    .empty-state {
        text-align: center;
        padding: 40px 20px;
        background-color: white;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        border: 1px solid #f0f0f0;
    }
    
    .empty-icon {
        font-size: 48px;
        color: #dee2e6;
        margin-bottom: 15px;
    }
    
    .empty-title {
        font-size: 17px;
        font-weight: 500;
        color: #495057;
        margin-bottom: 10px;
    }
    
    .empty-description {
        font-size: 14px;
        color: #6c757d;
        margin-bottom: 20px;
    }
    
    .tab-container {
        display: flex;
        margin-bottom: 15px;
        border-bottom: 1px solid #dee2e6;
        overflow-x: auto;
        white-space: nowrap;
        -webkit-overflow-scrolling: touch;
    }
    
    .tab-item {
        padding: 12px 16px;
        font-size: 14px;
        font-weight: 500;
        color: #495057;
        cursor: pointer;
        border-bottom: 2px solid transparent;
        transition: all 0.3s ease;
    }
    
    .tab-item.active {
        color: #FF9800;
        border-bottom-color: #FF9800;
    }
    
    .prize-card {
        background: white;
        border-radius: 12px;
        margin-bottom: 15px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        border: 1px solid #f0f0f0;
        overflow: hidden;
    }
    
    .prize-header {
        padding: 15px;
        background-color: #fff9e6;
        border-bottom: 1px solid #f0f0f0;
        display: flex;
        align-items: center;
    }
    
    .prize-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: linear-gradient(135deg, #FFC107, #FF9800);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        color: white;
        margin-right: 12px;
        flex-shrink: 0;
    }
    
    .prize-title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin-bottom: 3px;
    }
    
    .prize-quiz {
        font-size: 12px;
        color: #6c757d;
    }
    
    .prize-body {
        padding: 15px;
    }
    
    .prize-description {
        font-size: 14px;
        color: #666;
        line-height: 1.5;
        margin-bottom: 15px;
    }
    
    .prize-meta {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-bottom: 15px;
        font-size: 12px;
    }
    
    .meta-item {
        display: flex;
        align-items: center;
        color: #6c757d;
    }
    
    .meta-icon {
        margin-right: 5px;
        font-size: 14px;
    }
    
    .badge {
        display: inline-block;
        padding: 4px 8px;
        font-size: 12px;
        font-weight: 500;
        line-height: 1;
        text-align: center;
        white-space: nowrap;
        vertical-align: baseline;
        border-radius: 12px;
    }
    
    .badge-pending {
        background-color: #fff3cd;
        color: #856404;
    }
    
    .badge-shipping {
        background-color: #cce5ff;
        color: #004085;
    }
    
    .badge-delivered {
        background-color: #d4edda;
        color: #155724;
    }
    
    .badge-claimed {
        background-color: #d4edda;
        color: #155724;
    }
    
    .badge-unclaimed {
        background-color: #f8d7da;
        color: #721c24;
    }
    
    .badge-virtual {
        background-color: #e2e3e5;
        color: #383d41;
    }
    
    .badge-physical {
        background-color: #e2e3e5;
        color: #383d41;
    }
    
    .prize-actions {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
    }
    
    .action-button {
        display: inline-block;
        padding: 8px 16px;
        border-radius: 8px;
        font-size: 13px;
        font-weight: 500;
        text-decoration: none;
        transition: all 0.3s ease;
    }
    
    .primary-button {
        background-color: #FF9800;
        color: white;
    }
    
    .primary-button:hover {
        background-color: #F57C00;
        color: white;
    }
    
    .secondary-button {
        background-color: #f8f9fa;
        color: #495057;
        border: 1px solid #dee2e6;
    }
    
    .secondary-button:hover {
        background-color: #e9ecef;
        color: #495057;
    }
    
    .button-container {
        margin-top: 20px;
        display: flex;
        justify-content: center;
    }
    
    .tracking-info {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 12px;
        margin-bottom: 15px;
        font-size: 13px;
    }
    
    .tracking-title {
        font-weight: 600;
        margin-bottom: 5px;
        color: #495057;
        display: flex;
        align-items: center;
    }
    
    .tracking-title i {
        margin-right: 5px;
        color: #0d6efd;
    }
    
    .tracking-item {
        display: flex;
        margin-bottom: 8px;
    }
    
    .tracking-label {
        flex-basis: 80px;
        flex-shrink: 0;
        font-weight: 500;
        color: #6c757d;
    }
    
    .tracking-value {
        flex-grow: 1;
        color: #495057;
    }
    
    .status-timeline {
        display: flex;
        justify-content: space-between;
        margin: 20px 0;
        position: relative;
    }
    
    .status-timeline::before {
        content: '';
        position: absolute;
        top: 15px;
        left: 0;
        right: 0;
        height: 2px;
        background-color: #dee2e6;
        z-index: 0;
    }
    
    .status-step {
        position: relative;
        z-index: 1;
        text-align: center;
        width: 30px;
    }
    
    .status-dot {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background-color: white;
        border: 2px solid #dee2e6;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 5px;
        color: #6c757d;
        font-size: 14px;
    }
    
    .status-step.active .status-dot {
        background-color: #FF9800;
        border-color: #FF9800;
        color: white;
    }
    
    .status-step.completed .status-dot {
        background-color: #28a745;
        border-color: #28a745;
        color: white;
    }
    
    .status-label {
        font-size: 11px;
        color: #6c757d;
    }
    
    .status-step.active .status-label {
        color: #FF9800;
        font-weight: 500;
    }
    
    .status-step.completed .status-label {
        color: #28a745;
        font-weight: 500;
    }
    
    .delivery-info {
        margin-top: 5px;
        font-size: 12px;
        color: #6c757d;
    }
</style>
@endsection

@section('content')
<div class="quiz-header" style="background: linear-gradient(135deg, var(--quiz-secondary), var(--quiz-secondary-dark));">
    <a href="{{ route('quiz.index') }}" class="quiz-back-button">
        <div class="quiz-back-icon"></div>
    </a>
    <h1>我的奖品</h1>
    <div style="width: 30px;"></div>
</div>

<div class="quiz-content quiz-fade-in">
    <h1 class="page-title">
        <i class="fas fa-trophy page-title-icon"></i> 我的奖品
    </h1>
    
    <div class="quiz-tabs">
        <div class="quiz-tab active" data-target="all">全部</div>
        <div class="quiz-tab" data-target="unclaimed">待领取</div>
        <div class="quiz-tab" data-target="pending">处理中</div>
        <div class="quiz-tab" data-target="shipping">配送中</div>
        <div class="quiz-tab" data-target="delivered">已送达</div>
        <div class="quiz-tab" data-target="claimed">已领取</div>
    </div>
    
    @if($prizeWinners->isEmpty())
    <div class="quiz-empty-state">
        <div class="quiz-empty-icon">
            <i class="fas fa-gift"></i>
        </div>
        <div class="quiz-empty-title">还没有获得奖品</div>
        <div class="quiz-empty-description">参与有奖问答活动，回答问题赢取丰厚奖品！</div>
        <a href="{{ route('quiz.index') }}" class="quiz-btn quiz-btn-primary">查看问答活动</a>
    </div>
    @else
    
    <div id="all" class="tab-content">
        @foreach($prizeWinners as $winner)
        <div class="prize-card" data-status="{{ $winner->status }}">
            <div class="prize-header">
                <div class="prize-icon">
                    <i class="fas fa-trophy"></i>
                </div>
                <div>
                    <div class="prize-title">{{ $winner->prize->name }}</div>
                    <div class="prize-quiz">{{ $winner->attempt->quizActivity->title }}</div>
                </div>
            </div>
            
            <div class="prize-body">
                <div class="prize-description">
                    {{ $winner->prize->description }}
                </div>
                
                <div class="prize-meta">
                    <div class="meta-item">
                        <i class="fas fa-calendar-alt meta-icon"></i> 
                        获奖日期：{{ $winner->created_at->format('Y-m-d') }}
                    </div>
                    
                    <div class="meta-item">
                        <i class="fas {{ $winner->prize->prize_type == 'physical' ? 'fa-box' : ($winner->prize->prize_type == 'coupon' ? 'fa-ticket-alt' : 'fa-file-alt') }} meta-icon"></i> 
                        奖品类型：
                        <span class="badge badge-{{ $winner->prize->prize_type }}">
                            @if($winner->prize->prize_type == 'physical')
                                实物奖品
                            @elseif($winner->prize->prize_type == 'coupon')
                                优惠券
                            @else
                                虚拟奖品
                            @endif
                        </span>
                    </div>
                    
                    <div class="meta-item">
                        <i class="fas fa-info-circle meta-icon"></i> 
                        状态：
                        <span class="quiz-badge quiz-badge-{{ $winner->status }}">
                            {{ $winner->status === 'unclaimed' ? '待领取' : ($winner->status === 'pending' ? '处理中' : ($winner->status === 'shipping' ? '配送中' : ($winner->status === 'delivered' ? '已送达' : '已领取'))) }}
                        </span>
                    </div>
                </div>
                
                @if($winner->status === 'shipping' || $winner->status === 'delivered')
                <div class="tracking-info">
                    <div class="tracking-title">
                        <i class="fas fa-truck"></i> 物流信息
                    </div>
                    
                    @if($winner->tracking_number)
                    <div class="tracking-item">
                        <div class="tracking-label">快递公司：</div>
                        <div class="tracking-value">{{ $winner->shipping_company ?? '顺丰快递' }}</div>
                    </div>
                    
                    <div class="tracking-item">
                        <div class="tracking-label">运单号：</div>
                        <div class="tracking-value">{{ $winner->tracking_number }}</div>
                    </div>
                    @endif
                    
                    <div class="status-timeline">
                        <div class="status-step {{ in_array($winner->status, ['pending', 'shipping', 'delivered']) ? 'completed' : '' }}">
                            <div class="status-dot">
                                <i class="fas fa-check"></i>
                            </div>
                            <div class="status-label">已确认</div>
                        </div>
                        
                        <div class="status-step {{ in_array($winner->status, ['shipping', 'delivered']) ? 'completed' : ($winner->status === 'pending' ? 'active' : '') }}">
                            <div class="status-dot">
                                <i class="fas {{ in_array($winner->status, ['shipping', 'delivered']) ? 'fa-check' : 'fa-box' }}"></i>
                            </div>
                            <div class="status-label">处理中</div>
                        </div>
                        
                        <div class="status-step {{ $winner->status === 'delivered' ? 'completed' : ($winner->status === 'shipping' ? 'active' : '') }}">
                            <div class="status-dot">
                                <i class="fas {{ $winner->status === 'delivered' ? 'fa-check' : 'fa-truck' }}"></i>
                            </div>
                            <div class="status-label">配送中</div>
                        </div>
                        
                        <div class="status-step {{ $winner->status === 'delivered' ? 'active' : '' }}">
                            <div class="status-dot">
                                <i class="fas fa-home"></i>
                            </div>
                            <div class="status-label">已送达</div>
                        </div>
                    </div>
                    
                    @if($winner->status === 'delivered')
                    <div class="delivery-info">
                        送达时间：{{ $winner->delivered_at ? $winner->delivered_at->format('Y-m-d H:i') : '已送达，请注意查收' }}
                    </div>
                    @endif
                </div>
                @endif
                
                @if(($winner->prize->prize_type == 'coupon' || $winner->prize->prize_type == 'virtual') && $winner->status === 'claimed')
                <div class="tracking-info">
                    <div class="tracking-title">
                        <i class="fas fa-check-circle"></i> 
                        @if($winner->prize->prize_type == 'coupon')
                            优惠券信息
                        @else
                            虚拟奖品信息
                        @endif
                    </div>
                    
                    <div class="tracking-item">
                        <div class="tracking-label">兑换码：</div>
                        <div class="tracking-value">{{ $winner->code ?? '兑换码已发送至您的邮箱' }}</div>
                    </div>
                    
                    <div class="tracking-item">
                        <div class="tracking-label">领取日期：</div>
                        <div class="tracking-value">{{ $winner->claimed_at ? $winner->claimed_at->format('Y-m-d H:i') : $winner->updated_at->format('Y-m-d H:i') }}</div>
                    </div>
                </div>
                @endif
                
                <div class="prize-actions">
                    @if($winner->status === 'unclaimed')
                    <a href="{{ route('quiz.claim_prize', ['id' => $winner->attempt->quiz_activity_id, 'winner_id' => $winner->id]) }}" class="quiz-btn quiz-btn-secondary">立即领取</a>
                    @endif
                    
                    <a href="{{ route('quiz.result', ['id' => $winner->attempt->quiz_activity_id, 'attempt_id' => $winner->attempt_id]) }}" class="quiz-btn quiz-btn-outline">查看答题</a>
                </div>
                
                @if(count($prizeWinners) - 1 == $loop->index)
                <!-- 添加底部空间，避免被底部菜单遮挡 -->
                <div class="quiz-bottom-space"></div>
                @endif
            </div>
        </div>
        @endforeach
    </div>
    
    @endif
</div>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const tabItems = document.querySelectorAll('.quiz-tab');
        const prizeCards = document.querySelectorAll('.prize-card');
        
        // 标签切换
        tabItems.forEach((tab, index) => {
            tab.addEventListener('click', function() {
                // 移除其他标签的激活状态
                tabItems.forEach(item => {
                    item.classList.remove('active');
                });
                
                // 激活当前标签
                this.classList.add('active');
                
                const target = this.getAttribute('data-target');
                
                // 显示所有奖品或者过滤特定状态的奖品
                if (target === 'all') {
                    prizeCards.forEach((card, cardIndex) => {
                        card.style.display = 'block';
                        card.style.animation = 'fadeIn 0.5s ease forwards';
                        card.style.animationDelay = `${cardIndex * 0.1}s`;
                    });
                } else {
                    let visibleCount = 0;
                    prizeCards.forEach(card => {
                        if (card.getAttribute('data-status') === target) {
                            card.style.display = 'block';
                            card.style.animation = 'fadeIn 0.5s ease forwards';
                            card.style.animationDelay = `${visibleCount * 0.1}s`;
                            visibleCount++;
                        } else {
                            card.style.display = 'none';
                        }
                    });
                }
            });
        });
        
        // 初始化时为卡片添加动画延迟
        prizeCards.forEach((card, index) => {
            card.style.setProperty('--animation-order', index);
        });
    });
</script>
@endsection
