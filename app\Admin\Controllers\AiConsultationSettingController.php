<?php

namespace App\Admin\Controllers;

use App\Admin\Actions\TestAiConnectionAction;
use App\Models\AiConsultationSetting;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class AiConsultationSettingController extends AdminController
{
    /**
     * 设置标题
     */
    protected $title = 'AI咨询设置';
    
    // 已移除testConnection方法，改用TestAiConnectionAction类

    /**
     * 列表页面
     */
    protected function grid()
    {
        return Grid::make(new AiConsultationSetting(), function (Grid $grid) {
            $grid->column('id')->sortable();
            $grid->column('provider', 'AI提供商');
            $grid->column('api_key', 'API密钥')->display(function () {
                return !empty($this->api_key) ? '[已设置]' : '[未设置]';
            });
            $grid->column('api_url', 'API地址');
            $grid->column('model', '模型名称');
            $grid->column('max_tokens', '最大Token数');
            $grid->column('temperature', '温度参数');
            $grid->column('is_active', '是否启用')->switch();
            $grid->column('created_at', '创建时间');
            $grid->column('updated_at', '更新时间');
            
            // 限制新增只能有一个活跃配置
            $grid->disableCreateButton(AiConsultationSetting::where('is_active', true)->exists());
            
            // 默认隐藏系统提示词列，因为内容太长
            $grid->hideColumns(['system_prompt']);
            
            // 添加测试连接按钮
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                // 使用TestAiConnectionAction类
                $actions->append(new TestAiConnectionAction());                
            });
        });
    }

    /**
     * 详情页面
     */
    protected function detail($id)
    {
        return Show::make($id, new AiConsultationSetting(), function (Show $show) {
            $show->field('id');
            $show->field('provider', 'AI提供商');
            $show->field('api_key', 'API密钥');
            $show->field('api_url', 'API地址');
            $show->field('model', '模型名称');
            $show->field('max_tokens', '最大Token数');
            $show->field('temperature', '温度参数');
            $show->field('system_prompt', '系统提示词')->unescape()->as(function ($value) {
                return "<pre>$value</pre>";
            });
            $show->field('is_active', '是否启用')->as(function ($value) {
                return $value ? '启用' : '禁用';
            });
            $show->field('created_at', '创建时间');
            $show->field('updated_at', '更新时间');
        });
    }

    /**
     * 表单页面
     */
    protected function form()
    {
        return Form::make(new AiConsultationSetting(), function (Form $form) {
            $form->display('id');
            $form->select('provider', 'AI提供商')
                ->options([
                    'deepseek' => 'DeepSeek',
                    'openai' => 'OpenAI',
                    'custom' => '自定义'
                ])
                ->default('deepseek')
                ->required();
                
            // 直接使用普通文本框显示API密钥，不需要使用password字段
            $form->text('api_key', 'API密钥')
                ->required()
                ->help('API密钥直接显示和编辑');
                
            $form->url('api_url', 'API地址')
                ->required()
                ->default('https://api.deepseek.com/v1/chat/completions')
                ->help('默认为DeepSeek API地址');
                
            $form->text('model', '模型名称')
                ->required()
                ->default('deepseek-chat')
                ->help('例如：deepseek-chat');
                
            $form->number('max_tokens', '最大Token数')
                ->default(2000)
                ->required()
                ->help('返回的最大Token数量');
                
            $form->rate('temperature', '温度参数')
                ->default(0.7)
                ->attribute(['max' => 2, 'min' => 0])
                ->help('值越大，回复越随机，建议0.1-1之间');
                
            $form->textarea('system_prompt', '系统提示词')
                ->rows(10)
                ->required()
                ->default("你是一个专业的心理咨询助手，你的目标是提供有帮助的、符合心理学原则的回复。\n\n遵循以下准则：\n1. 提供支持性的回复，不做诊断\n2. 在适当情况下推荐寻求专业帮助\n3. 以同理心和理解的态度回应\n4. 提供基于循证心理学的建议\n5. 不建议用药或提供医疗建议\n6. 保持对话内容的隐私和保密\n\n如果遇到紧急情况（如自杀想法或危险行为），请建议用户立即联系紧急心理援助热线或当地医院。")
                ->help('AI回复时使用的系统指导语，会影响AI的回复风格和内容');
                
            $form->switch('is_active', '是否启用')
                ->default(true)
                ->help('只能有一个配置处于启用状态');
                
            // 保存前处理
            $form->saving(function (Form $form) {
                // 处理活跃状态
                if ($form->is_active) {
                    // 如果当前设置为活跃，其他设置应设为非活跃
                    AiConsultationSetting::where('id', '<>', $form->model()->id)
                        ->update(['is_active' => false]);
                } else {
                    // 检查是否还有其他活跃配置
                    $hasActive = AiConsultationSetting::where('id', '<>', $form->model()->id)
                        ->where('is_active', true)
                        ->exists();
                        
                    if (!$hasActive) {
                        return $form->response()->error('必须至少有一个活跃配置');
                    }
                }
                
                // 处理API Key - 只有当有修改时才更新
                if ($form->isEditing()) {
                    // 获取原始API Key
                    $originalApiKey = AiConsultationSetting::find($form->model()->id)->api_key;
                    
                    // 检查API Key是否修改
                    if ($form->api_key === '[已设置]' || $form->api_key === $originalApiKey) {
                        // 如果没有修改，则从表单数据中移除api_key字段
                        $form->deleteInput('api_key');
                    }
                }
            });
            
            $form->display('created_at', '创建时间');
            $form->display('updated_at', '更新时间');
        });
    }
}
