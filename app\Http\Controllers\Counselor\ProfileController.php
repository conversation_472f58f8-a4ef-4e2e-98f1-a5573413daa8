<?php

namespace App\Http\Controllers\Counselor;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Counselor;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class ProfileController extends Controller
{
    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->middleware('counselor.auth')->except(['showForgotPassword', 'sendResetCode', 'resetPassword']);
    }
    
    /**
     * 显示个人资料页面
     */
    public function show(Request $request)
    {
        $counselorId = $request->session()->get('counselor_id');
        $counselor = Counselor::findOrFail($counselorId);
        
        return view('counselor.profile', [
            'counselor' => $counselor
        ]);
    }
    
    /**
     * 更新个人资料
     */
    public function update(Request $request)
    {
        $counselorId = $request->session()->get('counselor_id');
        $counselor = Counselor::findOrFail($counselorId);
        
        // 表单验证
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => [
                'required',
                'email',
                Rule::unique('counselors')->ignore($counselorId),
            ],
            'phone' => [
                'required',
                'regex:/^1[3-9]\d{9}$/',
                Rule::unique('counselors')->ignore($counselorId),
            ],
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'introduction' => 'nullable|string|max:1000',
            'current_password' => 'nullable|string',
            'password' => 'nullable|string|min:6|confirmed',
        ]);
        
        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }
        
        // 更新基本信息
        $counselor->name = $request->name;
        $counselor->email = $request->email;
        $counselor->phone = $request->phone;
        $counselor->introduction = $request->introduction;
        
        // 处理头像上传
        if ($request->hasFile('avatar') && $request->file('avatar')->isValid()) {
            // 删除旧头像
            if ($counselor->avatar) {
                Storage::disk('public')->delete($counselor->avatar);
            }
            
            // 保存新头像
            $path = $request->file('avatar')->store('counselors/avatars', 'public');
            $counselor->avatar = $path;
            
            // 更新会话中的头像
            $request->session()->put('counselor_avatar', $path);
        }
        
        // 如果提供了当前密码，则更新密码
        if ($request->filled('current_password')) {
            // 验证当前密码
            if (!Hash::check($request->current_password, $counselor->password)) {
                return redirect()->back()
                    ->withErrors(['current_password' => '当前密码不正确'])
                    ->withInput();
            }
            
            // 设置新密码
            if ($request->filled('password')) {
                $counselor->password = Hash::make($request->password);
            }
        }
        
        $counselor->save();
        
        // 更新会话中的名称
        $request->session()->put('counselor_name', $counselor->name);
        
        return redirect()->route('counselor.profile')
            ->with('success', '个人资料已更新');
    }
    
    /**
     * 显示忘记密码页面
     */
    public function showForgotPassword()
    {
        return view('counselor.auth.forgot_password');
    }
    
    /**
     * 发送重置密码验证码
     */
    public function sendResetCode(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'phone' => 'required|regex:/^1[3-9]\d{9}$/',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first('phone')
            ], 422);
        }
        
        // 检查手机号是否存在
        $counselor = Counselor::where('phone', $request->phone)->first();
        
        if (!$counselor) {
            return response()->json([
                'success' => false,
                'message' => '该手机号未注册'
            ], 404);
        }
        
        // 生成6位随机验证码
        $code = sprintf('%06d', mt_rand(0, 999999));
        
        // 存储验证码到会话
        $request->session()->put('reset_code', $code);
        $request->session()->put('reset_phone', $request->phone);
        $request->session()->put('reset_time', now()->timestamp);
        
        // TODO: 集成短信服务发送验证码
        // 当前仅在响应中返回验证码（测试用途）
        
        return response()->json([
            'success' => true,
            'message' => '验证码已发送',
            'debug_code' => $code // 仅用于测试，生产环境应移除
        ]);
    }
    
    /**
     * 重置密码
     */
    public function resetPassword(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'phone' => 'required|regex:/^1[3-9]\d{9}$/',
            'code' => 'required|string|size:6',
            'password' => 'required|string|min:6|confirmed',
        ]);
        
        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }
        
        // 验证码验证
        $storedCode = $request->session()->get('reset_code');
        $storedPhone = $request->session()->get('reset_phone');
        $storedTime = $request->session()->get('reset_time');
        
        // 验证码有效期为10分钟
        $codeExpired = now()->timestamp - $storedTime > 600;
        
        if (!$storedCode || $request->code !== $storedCode || $request->phone !== $storedPhone || $codeExpired) {
            return redirect()->back()
                ->withErrors(['code' => '验证码无效或已过期'])
                ->withInput();
        }
        
        // 更新密码
        $counselor = Counselor::where('phone', $request->phone)->firstOrFail();
        $counselor->password = Hash::make($request->password);
        $counselor->save();
        
        // 清除会话中的验证码
        $request->session()->forget(['reset_code', 'reset_phone', 'reset_time']);
        
        return redirect()->route('counselor.login')
            ->with('success', '密码已重置，请使用新密码登录');
    }
}
