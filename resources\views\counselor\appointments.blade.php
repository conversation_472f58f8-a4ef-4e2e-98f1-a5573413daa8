<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>预约管理 - 心理健康平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: "SF Pro Text", "SF Pro Icons", "Helvetica Neue", Helvetica, Arial, sans-serif;
        }
        .sidebar {
            position: fixed;
            top: 0;
            bottom: 0;
            left: 0;
            z-index: 100;
            padding: 48px 0 0;
            box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
            background: #fff;
        }
        .sidebar-sticky {
            height: calc(100vh - 48px);
            overflow-x: hidden;
            overflow-y: auto;
        }
        .nav-link {
            color: #333;
            font-weight: 500;
            padding: 15px 25px;
            display: flex;
            align-items: center;
        }
        .nav-link:hover {
            background: rgba(52, 152, 219, 0.1);
        }
        .nav-link.active {
            color: #3498db;
            background: rgba(52, 152, 219, 0.15);
            position: relative;
        }
        .nav-link.active::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: #3498db;
        }
        .nav-icon {
            margin-right: 10px;
            font-size: 20px;
        }
        .badge-notifications {
            background: #e74c3c;
            color: white;
            font-size: 12px;
            padding: 3px 6px;
            border-radius: 10px;
        }
        .navbar {
            background: white;
        }
        .page-header {
            background: linear-gradient(45deg, #3498db, #8e44ad);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
        }
        .page-header h1 {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 0;
        }
        .appointment-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }
        .appointment-status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 13px;
            font-weight: 500;
        }
        .status-pending {
            background-color: #f39c12;
            color: white;
        }
        .status-confirmed {
            background-color: #27ae60;
            color: white;
        }
        .status-inprogress {
            background-color: #3498db;
            color: white;
        }
        .status-completed {
            background-color: #8e44ad;
            color: white;
        }
        .status-cancelled {
            background-color: #e74c3c;
            color: white;
        }
        .appointment-header {
            background-color: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
        }
        .appointment-body {
            padding: 20px;
        }
        .appointment-footer {
            padding: 15px 20px;
            background-color: #f8f9fa;
            border-top: 1px solid #e9ecef;
            display: flex;
            justify-content: flex-end;
        }
        .appointment-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .appointment-info {
            margin-bottom: 15px;
        }
        .appointment-info p {
            margin-bottom: 5px;
            display: flex;
        }
        .info-label {
            width: 100px;
            color: #6c757d;
            font-weight: 500;
        }
        .info-value {
            flex-grow: 1;
        }
        .btn-action {
            border-radius: 20px;
            padding: 8px 15px;
            font-size: 13px;
            font-weight: 500;
            margin-left: 10px;
        }
        .btn-confirm {
            background: #27ae60;
            border-color: #27ae60;
            color: white;
        }
        .btn-reject {
            background: #e74c3c;
            border-color: #e74c3c;
            color: white;
        }
        .btn-complete {
            background: #8e44ad;
            border-color: #8e44ad;
            color: white;
        }
        .btn-message {
            background: #3498db;
            border-color: #3498db;
            color: white;
        }
        .filter-bar {
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }
        .filter-label {
            margin-right: 10px;
            font-weight: 500;
        }
        .filter-options {
            display: flex;
        }
        .filter-option {
            padding: 8px 15px;
            margin-right: 10px;
            border-radius: 20px;
            font-size: 14px;
            cursor: pointer;
            background-color: white;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
            transition: background-color 0.2s;
        }
        .filter-option.active {
            background-color: #3498db;
            color: white;
        }
        .pagination {
            justify-content: center;
            margin: 20px 0;
        }
        .pagination .page-link {
            border-radius: 50%;
            margin: 0 5px;
            color: #3498db;
            border: none;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .pagination .page-item.active .page-link {
            background-color: #3498db;
            color: white;
        }
        .pagination .page-item.disabled .page-link {
            color: #6c757d;
            background-color: transparent;
        }
        .no-appointments {
            text-align: center;
            padding: 50px 20px;
        }
        .no-appointments i {
            font-size: 48px;
            color: #ddd;
            margin-bottom: 15px;
            display: block;
        }
        .no-appointments p {
            color: #999;
            font-size: 16px;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-light bg-white fixed-top shadow-sm">
        <div class="container-fluid">
            <span class="navbar-brand">心理健康平台 - 咨询师工作台</span>
            <div class="d-flex">
                <div class="dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <span class="me-2">{{ session('counselor_name') }}</span>
                        <img src="{{ session('counselor_avatar') ? asset('storage/'.session('counselor_avatar')) : asset('images/default-avatar.jpg') }}" width="32" height="32" class="rounded-circle">
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                        <li>
                            <a href="{{ route('counselor.profile') }}" class="dropdown-item">
                                <i class="bi bi-person me-2"></i>个人资料
                            </a>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <form method="POST" action="{{ route('counselor.logout') }}">
                                @csrf
                                <button type="submit" class="dropdown-item">
                                    <i class="bi bi-box-arrow-right me-2"></i>退出登录
                                </button>
                            </form>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- 侧边导航 -->
            <nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block sidebar">
                <div class="position-sticky sidebar-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="{{ route('counselor.dashboard') }}">
                                <i class="bi bi-house-door nav-icon"></i>
                                首页
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="{{ route('counselor.appointments') }}">
                                <i class="bi bi-calendar-check nav-icon"></i>
                                预约管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ route('counselor.messages') }}">
                                <i class="bi bi-chat-dots nav-icon"></i>
                                消息中心
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主要内容 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4" style="margin-top: 48px;">
                <div class="page-header d-flex justify-content-between align-items-center">
                    <h1>预约管理</h1>
                </div>

                @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                @endif

                <div class="filter-bar">
                    <div class="filter-label">筛选状态:</div>
                    <div class="filter-options">
                        <a href="{{ route('counselor.appointments') }}" class="filter-option {{ !request()->has('status') ? 'active' : '' }}">全部</a>
                        <a href="{{ route('counselor.appointments', ['status' => 1]) }}" class="filter-option {{ request()->get('status') == 1 ? 'active' : '' }}">待确认</a>
                        <a href="{{ route('counselor.appointments', ['status' => 2]) }}" class="filter-option {{ request()->get('status') == 2 ? 'active' : '' }}">已确认</a>
                        <a href="{{ route('counselor.appointments', ['status' => 3]) }}" class="filter-option {{ request()->get('status') == 3 ? 'active' : '' }}">进行中</a>
                        <a href="{{ route('counselor.appointments', ['status' => 4]) }}" class="filter-option {{ request()->get('status') == 4 ? 'active' : '' }}">已完成</a>
                        <a href="{{ route('counselor.appointments', ['status' => 0]) }}" class="filter-option {{ request()->get('status') == '0' ? 'active' : '' }}">已取消</a>
                    </div>
                </div>

                @if(count($appointments) > 0)
                    @foreach($appointments as $appointment)
                        <div class="appointment-card">
                            <div class="appointment-header">
                                <div class="appointment-title">
                                    <div>
                                        <span>预约编号: #{{ $appointment->id }}</span>
                                        <span class="ms-2">-</span>
                                        <span class="ms-2">{{ $appointment->consultation_type }}</span>
                                    </div>
                                    <div>
                                        @if($appointment->status == 0)
                                            <span class="appointment-status status-cancelled">已取消</span>
                                        @elseif($appointment->status == 1)
                                            <span class="appointment-status status-pending">待确认</span>
                                        @elseif($appointment->status == 2)
                                            <span class="appointment-status status-confirmed">已确认</span>
                                        @elseif($appointment->status == 3)
                                            <span class="appointment-status status-inprogress">进行中</span>
                                        @elseif($appointment->status == 4)
                                            <span class="appointment-status status-completed">已完成</span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                            <div class="appointment-body">
                                <div class="appointment-info">
                                    <p>
                                        <span class="info-label">预约用户:</span>
                                        <span class="info-value">{{ $appointment->user->name }}</span>
                                    </p>
                                    <p>
                                        <span class="info-label">预约时间:</span>
                                        <span class="info-value">{{ \Carbon\Carbon::parse($appointment->appointment_time)->format('Y年m月d日 H:i') }}</span>
                                    </p>
                                    <p>
                                        <span class="info-label">预约时长:</span>
                                        <span class="info-value">{{ $appointment->duration }} 分钟</span>
                                    </p>
                                    <p>
                                        <span class="info-label">问题描述:</span>
                                        <span class="info-value">{{ $appointment->issue_description ?? '用户未提供问题描述' }}</span>
                                    </p>
                                    <p>
                                        <span class="info-label">创建时间:</span>
                                        <span class="info-value">{{ \Carbon\Carbon::parse($appointment->created_at)->format('Y年m月d日 H:i') }}</span>
                                    </p>
                                    @if($appointment->status == 0 && $appointment->cancelled_at)
                                    <p>
                                        <span class="info-label">取消时间:</span>
                                        <span class="info-value">{{ \Carbon\Carbon::parse($appointment->cancelled_at)->format('Y年m月d日 H:i') }}</span>
                                    </p>
                                    @endif
                                    @if($appointment->status == 2 && $appointment->confirmed_at)
                                    <p>
                                        <span class="info-label">确认时间:</span>
                                        <span class="info-value">{{ \Carbon\Carbon::parse($appointment->confirmed_at)->format('Y年m月d日 H:i') }}</span>
                                    </p>
                                    @endif
                                    @if($appointment->status == 4 && $appointment->completed_at)
                                    <p>
                                        <span class="info-label">完成时间:</span>
                                        <span class="info-value">{{ \Carbon\Carbon::parse($appointment->completed_at)->format('Y年m月d日 H:i') }}</span>
                                    </p>
                                    @endif
                                </div>
                            </div>
                            <div class="appointment-footer">
                                <a href="{{ route('counselor.messages.show', $appointment->user_id) }}" class="btn btn-action btn-message">
                                    <i class="bi bi-chat-dots me-1"></i> 发送消息
                                </a>
                                
                                @if($appointment->status == 1)
                                    <form method="POST" action="{{ route('counselor.appointments.confirm', $appointment->id) }}" class="d-inline">
                                        @csrf
                                        <button type="submit" class="btn btn-action btn-confirm">
                                            <i class="bi bi-check-circle me-1"></i> 确认预约
                                        </button>
                                    </form>
                                    <form method="POST" action="{{ route('counselor.appointments.reject', $appointment->id) }}" class="d-inline">
                                        @csrf
                                        <button type="submit" class="btn btn-action btn-reject">
                                            <i class="bi bi-x-circle me-1"></i> 拒绝预约
                                        </button>
                                    </form>
                                @elseif($appointment->status == 2 || $appointment->status == 3)
                                    <form method="POST" action="{{ route('counselor.appointments.complete', $appointment->id) }}" class="d-inline">
                                        @csrf
                                        <button type="submit" class="btn btn-action btn-complete">
                                            <i class="bi bi-check-all me-1"></i> 标记完成
                                        </button>
                                    </form>
                                @endif
                            </div>
                        </div>
                    @endforeach
                    
                    <div class="pagination-container">
                        {{ $appointments->links() }}
                    </div>
                @else
                    <div class="no-appointments">
                        <i class="bi bi-calendar-x"></i>
                        <p>暂无预约记录</p>
                    </div>
                @endif
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
