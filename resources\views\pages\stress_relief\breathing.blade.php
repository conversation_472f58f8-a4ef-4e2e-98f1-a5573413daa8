@extends('layouts.app')

@section('title', '呼吸训练 - 心理减压')

@section('content')
<div class="breathing-container">
    <!-- 页面头部 - 自然融合设计 -->
    <div class="page-header">
        <div class="container">
            <div class="header-content">
                <div class="breadcrumb">
                    <a href="{{ route('stress_relief.index') }}">心理减压</a>
                    <span class="divider">/</span>
                    <span class="current">呼吸训练</span>
                </div>
                <h1 class="page-title">呼吸训练</h1>
                <p class="page-subtitle">吐纳之间，感受内缓的平静与放松</p>
            </div>
        </div>
    </div>

    <!-- 呼吸训练项目 -->
    <div class="breathing-programs">
        <div class="container">
            <div class="programs-grid">
                <!-- 4-7-8呼吸法 -->
                <div class="program-card" onclick="startBreathing('478')">
                    <div class="program-icon breathing-478">
                        <i class="fas fa-lungs"></i>
                    </div>
                    <h3 class="program-title">4-7-8呼吸法</h3>
                    <p class="program-desc">吸气4秒，屏息7秒，呼气8秒，有效缓解焦虑和压力</p>
                    <div class="program-features">
                        <span class="feature">适合新手</span>
                        <span class="feature">快速入睡</span>
                    </div>
                </div>

                <!-- 等长呼吸 -->
                <div class="program-card" onclick="startBreathing('square')">
                    <div class="program-icon breathing-square">
                        <i class="fas fa-square"></i>
                    </div>
                    <h3 class="program-title">等长呼吸</h3>
                    <p class="program-desc">吸气、屏息、呼气、暂停各4秒，建立呼吸节奏</p>
                    <div class="program-features">
                        <span class="feature">节奏稳定</span>
                        <span class="feature">提升专注</span>
                    </div>
                </div>

                <!-- 腹式呼吸 -->
                <div class="program-card" onclick="startBreathing('belly')">
                    <div class="program-icon breathing-belly">
                        <i class="fas fa-circle"></i>
                    </div>
                    <h3 class="program-title">腹式呼吸</h3>
                    <p class="program-desc">深度腹式呼吸，激活副交感神经，深度放松</p>
                    <div class="program-features">
                        <span class="feature">深度放松</span>
                        <span class="feature">缓解疲劳</span>
                    </div>
                </div>

                <!-- 觉察呼吸 -->
                <div class="program-card" onclick="startBreathing('mindful')">
                    <div class="program-icon breathing-mindful">
                        <i class="fas fa-leaf"></i>
                    </div>
                    <h3 class="program-title">觉察呼吸</h3>
                    <p class="program-desc">专注观察自然呼吸，培养正念觉察能力</p>
                    <div class="program-features">
                        <span class="feature">正念练习</span>
                        <span class="feature">提升觉察</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 底部导航栏占位 -->
    <div class="navbar-spacer"></div>
</div>

<!-- 呼吸训练器 -->
<div id="breathing-trainer" class="breathing-trainer" style="display: none;">
    <div class="trainer-overlay"></div>
    <div class="trainer-content">
        <div class="trainer-header">
            <h3 id="breathing-title">呼吸训练</h3>
            <button class="close-btn" onclick="closeBreathingTrainer()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="trainer-body">
            <div class="breathing-visual">
                <div class="breath-circle" id="breath-circle">
                    <div class="breath-guide">
                        <span id="breath-instruction">准备开始</span>
                        <div class="breath-count" id="breath-count">0</div>
                    </div>
                </div>
            </div>
            <div class="breathing-controls">
                <button id="start-breathing-btn" class="control-btn primary" onclick="toggleBreathing()">
                    <i class="fas fa-play"></i>
                    开始训练
                </button>
                <button class="control-btn" onclick="resetBreathing()">
                    <i class="fas fa-redo"></i>
                    重新开始
                </button>
            </div>
            <div class="breathing-info">
                <div class="info-item">
                    <span class="info-label">训练时长:</span>
                    <span id="session-time">00:00</span>
                </div>
                <div class="info-item">
                    <span class="info-label">呼吸次数:</span>
                    <span id="breath-cycles">0</span>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.breathing-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #06beb6 0%, #48b1bf 100%);
    /* 自然处理安全区域 */
    padding-top: calc(env(safe-area-inset-top, 0px) + 20px);
    padding-bottom: calc(env(safe-area-inset-bottom, 0px) + 80px);
}

/* 状态栏和导航栏占位 */
.status-bar-spacer {
    height: env(safe-area-inset-top, 0px);
    background: transparent;
}

.navbar-spacer {
    height: 50px;
    background: transparent;
}

/* 覆盖全局容器样式以适应呼吸训练页面 */
.breathing-container .container {
    max-width: none;
    width: 100%;
    padding: 0 15px;
    background-color: transparent !important;
    box-sizing: border-box;
}

/* 页面头部 - 自然设计，确保足够空间给卡片悬浮效果 */
.page-header {
    padding: 6rem 0 4rem;
    color: white;
    position: relative;
    background: transparent;
}

/* 装饰性背景 */
.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 100%;
    background: radial-gradient(ellipse at top, rgba(255,255,255,0.1) 0%, transparent 50%);
    pointer-events: none;
}

.header-content {
    position: relative;
    z-index: 1;
    text-align: center;
}

.breadcrumb {
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.breadcrumb a {
    color: rgba(255,255,255,0.8);
    text-decoration: none;
}

.breadcrumb a:hover {
    color: white;
}

.divider {
    margin: 0 0.5rem;
    opacity: 0.6;
}

.current {
    color: white;
}

.page-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 0 2px 10px rgba(0,0,0,0.3);
}

.page-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
}

/* 呼吸训练项目 */
.breathing-programs {
    padding: 3rem 0;
    width: 100%;
    box-sizing: border-box;
}

.programs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
    box-sizing: border-box;
    width: 100%;
}

.program-card {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
    cursor: pointer;
    text-align: center;
    position: relative;
}

.program-card:hover {
    transform: translateY(-5px);
}

.program-icon {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 1.8rem;
    color: white;
}

.breathing-478 {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.breathing-square {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.breathing-belly {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.breathing-mindful {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.program-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.program-desc {
    color: #5a6c7d;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.program-features {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.feature {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    color: #667eea;
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
}

/* 呼吸训练器 */
.breathing-trainer {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.trainer-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.9);
    backdrop-filter: blur(10px);
}

.trainer-content {
    position: relative;
    background: linear-gradient(135deg, #06beb6 0%, #48b1bf 100%);
    border-radius: 20px;
    width: 90%;
    max-width: 500px;
    overflow: hidden;
    color: white;
}

.trainer-header {
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.trainer-header h3 {
    margin: 0;
    font-size: 1.3rem;
}

.close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.3s ease;
}

.close-btn:hover {
    background: rgba(255,255,255,0.2);
}

.trainer-body {
    padding: 2rem;
    text-align: center;
}

.breathing-visual {
    margin-bottom: 2rem;
}

.breath-circle {
    width: 250px;
    height: 250px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0.1) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    position: relative;
    overflow: hidden;
    transition: transform 0.3s ease;
}

.breath-circle.inhale {
    transform: scale(1.2);
}

.breath-circle.exhale {
    transform: scale(0.8);
}

.breath-guide {
    text-align: center;
    z-index: 1;
}

#breath-instruction {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    display: block;
}

.breath-count {
    font-size: 3rem;
    font-weight: 700;
    opacity: 0.8;
}

.breathing-controls {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 2rem;
}

.control-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    border: none;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.control-btn.primary {
    background: rgba(255,255,255,0.2);
    color: white;
}

.control-btn.primary:hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-2px);
}

.control-btn:not(.primary) {
    background: rgba(255,255,255,0.1);
    color: white;
}

.control-btn:not(.primary):hover {
    background: rgba(255,255,255,0.2);
}

.breathing-info {
    display: flex;
    justify-content: space-around;
    padding-top: 1rem;
    border-top: 1px solid rgba(255,255,255,0.1);
}

.info-item {
    text-align: center;
}

.info-label {
    display: block;
    font-size: 0.9rem;
    opacity: 0.7;
    margin-bottom: 0.3rem;
}

.info-item span:last-child {
    font-size: 1.2rem;
    font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .breathing-container {
        padding-top: calc(env(safe-area-inset-top, 0px) + 15px);
        padding-bottom: calc(env(safe-area-inset-bottom, 0px) + 80px);
    }
    
    .page-title {
        font-size: 2.5rem;
    }
    
    .programs-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        padding: 0 10px;
    }
    
    .trainer-content {
        width: 95%;
        margin: 1rem;
    }
    
    .breath-circle {
        width: 200px;
        height: 200px;
    }
}

@media (max-width: 500px) {
    .breathing-container {
        padding-top: calc(env(safe-area-inset-top, 0px) + 10px);
        padding-bottom: calc(env(safe-area-inset-bottom, 0px) + 80px);
        width: 100%;
        overflow-x: hidden;
    }
    
    .breathing-container .container {
        max-width: 100% !important;
        width: 100% !important;
        padding: 0 10px !important;
        margin: 0 !important;
        background-color: transparent !important;
        box-sizing: border-box !important;
    }
    
    .page-header {
        padding: 1rem 0 1.5rem;
        width: 100%;
        box-sizing: border-box;
    }
    
    .page-title {
        font-size: 2rem;
    }
    
    .page-subtitle {
        font-size: 1rem;
    }
    
    .breathing-programs {
        padding: 1.5rem 0 2rem;
        width: 100%;
        box-sizing: border-box;
    }
    
    .programs-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
        padding: 0 !important;
        margin: 0 !important;
        width: 100% !important;
        max-width: 100% !important;
        box-sizing: border-box !important;
    }
    
    .program-card {
        padding: 1.5rem 1rem;
        transition: transform 0.2s ease;
        width: 100%;
        box-sizing: border-box;
    }
    
    .program-card:hover {
        transform: translateY(-3px);
    }
    
    .program-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
        margin-bottom: 1rem;
    }
    
    .program-title {
        font-size: 1rem;
        margin-bottom: 0.8rem;
    }
    
    .program-desc {
        font-size: 0.75rem;
        margin-bottom: 1rem;
        line-height: 1.4;
    }
    
    .program-features {
        gap: 0.3rem;
    }
    
    .feature {
        padding: 0.2rem 0.6rem;
        font-size: 0.7rem;
    }
}
</style>

<script>
let breathingTimer = null;
let isBreathing = false;
let currentCycles = 0;
let sessionStartTime;
let sessionTimer;
let currentBreathingType = '';

// 初始化音频上下文
let audioContext = null;

// 初始化音频系统
function initAudio() {
    if (!audioContext) {
        audioContext = new (window.AudioContext || window.webkitAudioContext)();
    }
}

const breathingPatterns = {
    '478': { inhale: 4, hold: 7, exhale: 8, title: '4-7-8呼吸法' },
    'square': { inhale: 4, hold: 4, exhale: 4, pause: 4, title: '等长呼吸' },
    'belly': { inhale: 6, exhale: 8, title: '腹式呼吸' },
    'mindful': { inhale: 4, exhale: 6, title: '觉察呼吸' }
};

function startBreathing(patternType) {
    // 初始化音频系统
    initAudio();
    
    const pattern = breathingPatterns[patternType];
    currentBreathingType = patternType;
    
    document.getElementById('breathing-title').textContent = pattern.title;
    document.getElementById('breathing-trainer').style.display = 'flex';
    
    resetBreathing();
}

function closeBreathingTrainer() {
    document.getElementById('breathing-trainer').style.display = 'none';
    stopBreathing();
}

function toggleBreathing() {
    if (isBreathing) {
        stopBreathing();
    } else {
        startBreathingSession();
    }
}

function startBreathingSession() {
    isBreathing = true;
    sessionStartTime = Date.now();
    currentCycles = 0;
    
    // 更新按钮
    const btn = document.getElementById('start-breathing-btn');
    btn.innerHTML = '<i class="fas fa-pause"></i> 暂停训练';
    
    // 开始会话计时器
    sessionTimer = setInterval(updateSessionTime, 1000);
    
    // 开始呼吸循环
    startBreathingCycle();
}

function stopBreathing() {
    isBreathing = false;
    clearInterval(breathingTimer);
    clearInterval(sessionTimer);
    
    // 更新按钮
    const btn = document.getElementById('start-breathing-btn');
    btn.innerHTML = '<i class="fas fa-play"></i> 开始训练';
    
    // 重置视觉效果
    const circle = document.getElementById('breath-circle');
    circle.className = 'breath-circle';
    
    document.getElementById('breath-instruction').textContent = '已暂停';
    document.getElementById('breath-count').textContent = '';
}

function resetBreathing() {
    stopBreathing();
    currentCycles = 0;
    
    document.getElementById('breath-instruction').textContent = '准备开始';
    document.getElementById('breath-count').textContent = '0';
    document.getElementById('breath-cycles').textContent = '0';
    document.getElementById('session-time').textContent = '00:00';
}

function startBreathingCycle() {
    if (!isBreathing) return;
    
    const pattern = breathingPatterns[currentBreathingType];
    const circle = document.getElementById('breath-circle');
    
    // 吸气阶段
    isBreathing = true;
    document.getElementById('breath-instruction').textContent = '缓慢吸气';
    circle.className = 'breath-circle inhale';
    
    let countdown = pattern.inhale;
    // 播放阶段开始音效
    playPhaseStartSound('inhale');
    // 立即播放第一个引导音
    playGuidanceSound('inhale', countdown, pattern.inhale);
    updateCountdown(countdown);
    
    breathingTimer = setInterval(() => {
        countdown--;
        updateCountdown(countdown);
        // 每秒都播放吸气引导音，音调随倒计时变化
        if (countdown > 0) {
            playGuidanceSound('inhale', countdown, pattern.inhale);
        }
        
        if (countdown <= 0) {
            clearInterval(breathingTimer);
            
            if (pattern.hold) {
                // 屏息阶段
                holdBreath(pattern);
            } else {
                // 直接进入呼气阶段
                exhalePhase(pattern);
            }
        }
    }, 1000);
}

function holdBreath(pattern) {
    if (!isBreathing) return;
    
    const circle = document.getElementById('breath-circle');
    isBreathing = true;
    document.getElementById('breath-instruction').textContent = '屏住呼吸';
    circle.className = 'breath-circle hold';
    
    let countdown = pattern.hold;
    // 播放阶段开始音效
    playPhaseStartSound('hold');
    // 立即播放第一个引导音
    playGuidanceSound('hold', countdown, pattern.hold);
    updateCountdown(countdown);
    
    breathingTimer = setInterval(() => {
        countdown--;
        updateCountdown(countdown);
        // 每秒都播放屏息引导音，音调随倒计时变化
        if (countdown > 0) {
            playGuidanceSound('hold', countdown, pattern.hold);
        }
        
        if (countdown <= 0) {
            clearInterval(breathingTimer);
            exhalePhase(pattern);
        }
    }, 1000);
}

function exhalePhase(pattern) {
    if (!isBreathing) return;
    
    isBreathing = true;
    document.getElementById('breath-instruction').textContent = '缓慢呼气';
    
    const circle = document.getElementById('breath-circle');
    circle.className = 'breath-circle exhale';
    
    let countdown = pattern.exhale;
    // 播放阶段开始音效
    playPhaseStartSound('exhale');
    // 立即播放第一个引导音
    playGuidanceSound('exhale', countdown, pattern.exhale);
    updateCountdown(countdown);
    
    breathingTimer = setInterval(() => {
        countdown--;
        updateCountdown(countdown);
        // 每秒都播放呼气引导音，音调随倒计时变化
        if (countdown > 0) {
            playGuidanceSound('exhale', countdown, pattern.exhale);
        }
        
        if (countdown <= 0) {
            clearInterval(breathingTimer);
            
            if (pattern.pause) {
                // 暂停阶段
                pausePhase(pattern);
            } else {
                // 完成一个循环
                completeCycle();
            }
        }
    }, 1000);
}

function pausePhase(pattern) {
    if (!isBreathing) return;
    
    isBreathing = true;
    document.getElementById('breath-instruction').textContent = '自然暂停';
    
    const circle = document.getElementById('breath-circle');
    circle.className = 'breath-circle pause';
    
    let countdown = pattern.pause;
    // 播放阶段开始音效
    playPhaseStartSound('pause');
    // 立即播放第一个引导音
    playGuidanceSound('pause', countdown, pattern.pause);
    updateCountdown(countdown);
    
    breathingTimer = setInterval(() => {
        countdown--;
        updateCountdown(countdown);
        // 每秒都播放暂停引导音，音调随倒计时变化
        if (countdown > 0) {
            playGuidanceSound('pause', countdown, pattern.pause);
        }
        
        if (countdown <= 0) {
            clearInterval(breathingTimer);
            completeCycle();
        }
    }, 1000);
}

function completeCycle() {
    currentCycles++;
    document.getElementById('breath-cycles').textContent = currentCycles;
    
    // 播放循环完成音效
    playCycleCompleteSound();
    
    // 短暂暂停后开始下一个循环
    setTimeout(() => {
        if (isBreathing) {
            startBreathingCycle();
        }
    }, 1000);
}

function updateCountdown(count) {
    document.getElementById('breath-count').textContent = count;
}

function updateSessionTime() {
    if (!sessionStartTime) return;
    
    const elapsed = Math.floor((Date.now() - sessionStartTime) / 1000);
    const minutes = Math.floor(elapsed / 60);
    const seconds = elapsed % 60;
    
    document.getElementById('session-time').textContent = 
        `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
}

// 播放循环完成音效
function playCycleCompleteSound() {
    if (!audioContext) return;
    
    // 播放一个愉悦的完成和弦 (C-E-G同时响起)
    const frequencies = [261.63, 329.63, 392.00]; // C4-E4-G4和弦
    
    frequencies.forEach((frequency, index) => {
        setTimeout(() => {
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);
            
            oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);
            gainNode.gain.setValueAtTime(0, audioContext.currentTime);
            gainNode.gain.linearRampToValueAtTime(0.08, audioContext.currentTime + 0.1);
            gainNode.gain.linearRampToValueAtTime(0, audioContext.currentTime + 0.6);
            
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.6);
        }, index * 100); // 错开100ms播放，产生层次感
    });
}

// 播放阶段开始提示音
function playPhaseStartSound(phase) {
    if (!audioContext) return;
    
    switch(phase) {
        case 'inhale':
            // 吸气开始：上升音阶，营造吸入感
            playRisingChord([220, 261.63, 329.63], 0.6); // A3-C4-E4上升
            break;
        case 'hold':
            // 屏息：几乎静音，只有极轻的引导
            playQuietHoldSound(261.63, 0.1);
            break;
        case 'exhale':
            // 呼气开始：下降音阶，营造呼出感
            playFallingChord([392.00, 329.63, 261.63], 0.8); // G4-E4-C4下降
            break;
        case 'pause':
            // 暂停开始：柔和低音波动，营造放松感
            playRelaxingWave(196.00, 0.7); // G3柔和波动
            break;
    }
}

// 播放上升和弦（吸气）
function playRisingChord(frequencies, duration) {
    // 吸气音效：模拟吸气的白噪音过滤
    const bufferSize = audioContext.sampleRate * duration;
    const buffer = audioContext.createBuffer(1, bufferSize, audioContext.sampleRate);
    const data = buffer.getChannelData(0);
    
    // 生成白噪音
    for (let i = 0; i < bufferSize; i++) {
        data[i] = (Math.random() * 2 - 1) * 0.1;
    }
    
    const source = audioContext.createBufferSource();
    const filter = audioContext.createBiquadFilter();
    const gainNode = audioContext.createGain();
    
    source.buffer = buffer;
    filter.type = 'highpass';
    filter.frequency.setValueAtTime(1000, audioContext.currentTime);
    filter.frequency.linearRampToValueAtTime(3000, audioContext.currentTime + duration); // 频率上升
    
    source.connect(filter);
    filter.connect(gainNode);
    gainNode.connect(audioContext.destination);
    
    gainNode.gain.setValueAtTime(0, audioContext.currentTime);
    gainNode.gain.linearRampToValueAtTime(0.15, audioContext.currentTime + 0.1);
    gainNode.gain.linearRampToValueAtTime(0, audioContext.currentTime + duration);
    
    source.start(audioContext.currentTime);
    source.stop(audioContext.currentTime + duration);
}

// 屏息音效
function playQuietHoldSound(frequency, duration) {
    // 几乎静音，只有极轻的引导
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();
    const filter = audioContext.createBiquadFilter();
    
    oscillator.type = 'sine';
    oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);
    // 极轻微的频率波动，营造平静感
    oscillator.frequency.linearRampToValueAtTime(frequency * 1.02, audioContext.currentTime + duration/2);
    oscillator.frequency.linearRampToValueAtTime(frequency, audioContext.currentTime + duration);
    
    filter.type = 'lowpass';
    filter.frequency.setValueAtTime(frequency * 1.5, audioContext.currentTime);
    filter.Q.setValueAtTime(2, audioContext.currentTime);
    
    oscillator.connect(filter);
    filter.connect(gainNode);
    gainNode.connect(audioContext.destination);
    
    gainNode.gain.setValueAtTime(0, audioContext.currentTime);
    gainNode.gain.linearRampToValueAtTime(0.005, audioContext.currentTime + 0.2); // 极低音量
    gainNode.gain.linearRampToValueAtTime(0.005, audioContext.currentTime + duration - 0.2);
    gainNode.gain.linearRampToValueAtTime(0, audioContext.currentTime + duration);
    
    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + duration);
}

// 播放下降和弦（呼气）
function playFallingChord(frequencies, duration) {
    // 呼气音效：低频风声（粉红噪音）
    const bufferSize = audioContext.sampleRate * duration;
    const buffer = audioContext.createBuffer(1, bufferSize, audioContext.sampleRate);
    const data = buffer.getChannelData(0);
    
    // 生成粉红噪音（低频更强）
    let b0 = 0, b1 = 0, b2 = 0, b3 = 0, b4 = 0, b5 = 0, b6 = 0;
    for (let i = 0; i < bufferSize; i++) {
        const white = Math.random() * 2 - 1;
        b0 = 0.99886 * b0 + white * 0.0555179;
        b1 = 0.99332 * b1 + white * 0.0750759;
        b2 = 0.96900 * b2 + white * 0.1538520;
        b3 = 0.86650 * b3 + white * 0.3104856;
        b4 = 0.55000 * b4 + white * 0.5329522;
        b5 = -0.7616 * b5 - white * 0.0168980;
        data[i] = (b0 + b1 + b2 + b3 + b4 + b5 + b6 + white * 0.5362) * 0.05;
        b6 = white * 0.115926;
    }
    
    const source = audioContext.createBufferSource();
    const filter = audioContext.createBiquadFilter();
    const gainNode = audioContext.createGain();
    
    source.buffer = buffer;
    filter.type = 'lowpass';
    filter.frequency.setValueAtTime(800, audioContext.currentTime);
    filter.frequency.linearRampToValueAtTime(200, audioContext.currentTime + duration); // 频率下降
    
    source.connect(filter);
    filter.connect(gainNode);
    gainNode.connect(audioContext.destination);
    
    gainNode.gain.setValueAtTime(0, audioContext.currentTime);
    gainNode.gain.linearRampToValueAtTime(0.2, audioContext.currentTime + 0.1);
    gainNode.gain.linearRampToValueAtTime(0, audioContext.currentTime + duration);
    
    source.start(audioContext.currentTime);
    source.stop(audioContext.currentTime + duration);
}

// 播放放松波动（暂停）
function playRelaxingWave(baseFreq, duration) {
    // 暂停音效：柔和的水滴铃声
    const frequencies = [523.25, 659.25, 783.99]; // C5-E5-G5铃声和弦
    
    frequencies.forEach((frequency, index) => {
        setTimeout(() => {
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();
            
            oscillator.type = 'sine';
            oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);
            oscillator.frequency.exponentialRampToValueAtTime(frequency * 0.8, audioContext.currentTime + 1.0);
            
            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);
            
            gainNode.gain.setValueAtTime(0.15, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + 1.0);
            
            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 1.0);
        }, index * 100); // 错开100ms，产生回响效果
    });
}

// 播放呼吸引导提示音
function playGuidanceSound(phase, countdown, duration) {
    if (!audioContext) return;
    
    // 计算当前阶段的进度 (0到1)
    const progress = (duration - countdown) / duration;
    
    switch(phase) {
        case 'inhale':
            // 吸气：短促的气泡声，频率递增
            playBubbleSound(330 + (220 * progress), 0.3);
            break;
        case 'hold':
            // 屏息：几乎静音，只有极轻的引导
            playQuietHoldSound(261.63, 0.1);
            break;
        case 'exhale':
            // 呼气：柔和钟声，频率递减
            playBellSound(550 - (200 * progress), 0.5);
            break;
        case 'pause':
            // 暂停：水滴声
            playPauseSound(220 + (Math.sin(audioContext.currentTime * 1.5) * 15), 0.4);
            break;
    }
}

// 气泡声（吸气）
function playBubbleSound(frequency, duration) {
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();
    const filter = audioContext.createBiquadFilter();
    
    oscillator.type = 'triangle';
    oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);
    oscillator.frequency.setValueAtTime(frequency * 1.5, audioContext.currentTime + 0.05);
    oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime + 0.1);
    
    filter.type = 'bandpass';
    filter.frequency.setValueAtTime(frequency, audioContext.currentTime);
    filter.Q.setValueAtTime(10, audioContext.currentTime);
    
    oscillator.connect(filter);
    filter.connect(gainNode);
    gainNode.connect(audioContext.destination);
    
    gainNode.gain.setValueAtTime(0, audioContext.currentTime);
    gainNode.gain.linearRampToValueAtTime(0.15, audioContext.currentTime + 0.05);
    gainNode.gain.linearRampToValueAtTime(0, audioContext.currentTime + duration);
    
    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + duration);
}

// 钟声（呼气）
function playBellSound(frequency, duration) {
    // 创建钟声的泛音
    const frequencies = [frequency, frequency * 2.5, frequency * 4.2, frequency * 6.8];
    const volumes = [0.8, 0.3, 0.15, 0.08];
    
    frequencies.forEach((freq, index) => {
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();
        
        oscillator.type = 'sine';
        oscillator.frequency.setValueAtTime(freq, audioContext.currentTime);
        
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);
        
        const vol = 0.06 * volumes[index];
        gainNode.gain.setValueAtTime(vol, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.001, audioContext.currentTime + duration);
        
        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + duration);
    });
}

// 暂停音效
function playPauseSound(frequency, duration) {
    // 极轻柔的环境音，像微风轻抚
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();
    const filter = audioContext.createBiquadFilter();
    
    oscillator.type = 'sine';
    oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);
    // 非常缓慢的频率波动
    oscillator.frequency.linearRampToValueAtTime(frequency * 0.95, audioContext.currentTime + duration/2);
    oscillator.frequency.linearRampToValueAtTime(frequency, audioContext.currentTime + duration);
    
    filter.type = 'lowpass';
    filter.frequency.setValueAtTime(frequency * 2, audioContext.currentTime);
    filter.Q.setValueAtTime(3, audioContext.currentTime);
    
    oscillator.connect(filter);
    filter.connect(gainNode);
    gainNode.connect(audioContext.destination);
    
    gainNode.gain.setValueAtTime(0, audioContext.currentTime);
    gainNode.gain.linearRampToValueAtTime(0.03, audioContext.currentTime + 0.3);
    gainNode.gain.linearRampToValueAtTime(0.03, audioContext.currentTime + duration - 0.3);
    gainNode.gain.linearRampToValueAtTime(0, audioContext.currentTime + duration);
    
    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + duration);
}
</script>
@endsection 