@extends('layouts.app')

@section('content')
<link href="{{ asset('css/knowledge-section.css') }}" rel="stylesheet">
<style>
    .course-container {
        padding: 0;
        background: #f8fafc;
        min-height: 100vh;
    }
    
    /* 顶部导航栏 */
    .top-nav {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 100;
        height: 56px;
        background: linear-gradient(135deg, #4338ca, #3b82f6);
        box-shadow: 0 2px 12px rgba(0,0,0,0.1);
        display: flex;
        align-items: center;
        padding: 0 16px;
    }
    
    .back-btn {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: rgba(255,255,255,0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        transition: all 0.3s;
    }
    
    .back-btn:hover {
        background: rgba(255,255,255,0.3);
        transform: scale(1.05);
    }
    
    .page-title {
        color: white;
        font-size: 18px;
        font-weight: 600;
        margin: 0 auto;
    }
    
    .action-btn {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: rgba(255,255,255,0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        cursor: pointer;
        transition: all 0.3s;
    }
    
    .action-btn:hover {
        background: rgba(255,255,255,0.3);
        transform: scale(1.05);
    }
    
    /* 搜索框样式 */
    .search-container {
        position: fixed;
        top: 56px;
        left: 0;
        right: 0;
        background: #fff;
        padding: 16px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        transform: translateY(-100%);
        opacity: 0;
        z-index: 99;
        transition: all 0.3s cubic-bezier(0.68, -0.55, 0.27, 1.55);
    }
    
    .search-container.active {
        transform: translateY(0);
        opacity: 1;
    }
    
    .search-box {
        display: flex;
        align-items: center;
        background: #f1f5f9;
        border-radius: 12px;
        padding: 0 12px;
        overflow: hidden;
        box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);
    }
    
    #course-search {
        flex: 1;
        height: 46px;
        border: none;
        background: transparent;
        font-size: 16px;
        color: #334155;
        padding: 0 8px;
        outline: none;
    }
    
    .search-clear, .search-submit {
        background: transparent;
        border: none;
        color: #64748b;
        font-size: 16px;
        cursor: pointer;
        transition: all 0.2s;
        padding: 8px;
        border-radius: 50%;
    }
    
    .search-submit {
        color: #3b82f6;
    }
    
    .search-clear:hover, .search-submit:hover {
        background: rgba(0,0,0,0.05);
    }
    
    /* 搜索结果高亮 */
    .highlight {
        background: rgba(59, 130, 246, 0.1);
        padding: 0 2px;
        border-radius: 3px;
    }
    
    /* 分类标签栏 */
    .category-section {
        margin-top: 56px;
        background: white;
        border-bottom: 1px solid #e5e7eb;
        position: relative;
    }
    
    .category-tabs-container {
        position: relative;
        width: 100%;
    }
    
    .category-tabs {
        padding: 16px 0;
        background: white;
        overflow-x: auto;
        white-space: nowrap;
        -webkit-overflow-scrolling: touch;
        scrollbar-width: none;
        position: relative;
    }
    
    .category-tabs::-webkit-scrollbar {
        display: none;
    }
    
    .category-tab {
        display: inline-flex;
        align-items: center;
        padding: 8px 16px;
        margin: 0 8px;
        border-radius: 20px;
        font-size: 14px;
        color: #64748b;
        background: #f1f5f9;
        transition: all 0.3s;
        cursor: pointer;
    }
    
    .category-tab.active {
        background: linear-gradient(135deg, #4338ca, #3b82f6);
        color: white;
        box-shadow: 0 4px 12px rgba(59,130,246,0.3);
    }
    
    .category-tab i {
        margin-right: 6px;
        font-size: 16px;
    }
    
    /* 滚动指示器 */
    .category-scroll-indicator {
        position: absolute;
        right: 0;
        top: 0;
        height: 100%;
        width: 40px;
        background: linear-gradient(to right, rgba(255,255,255,0), rgba(255,255,255,1));
        display: flex;
        align-items: center;
        justify-content: flex-end;
        pointer-events: none;
        z-index: 5;
    }
    
    .scroll-indicator-arrow {
        width: 20px;
        height: 20px;
        margin-right: 10px;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%233b82f6'%3E%3Cpath d='M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z'/%3E%3C/svg%3E");
        background-size: contain;
        background-repeat: no-repeat;
    }
    
    /* 展开更多分类 */
    .category-expand {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 10px 0;
        font-size: 14px;
        color: #64748b;
        background: white;
        border-top: 1px solid #f0f0f0;
        cursor: pointer;
    }
    
    .category-expand-icon {
        width: 16px;
        height: 16px;
        margin-left: 5px;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%2364748b'%3E%3Cpath d='M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z'/%3E%3C/svg%3E");
        background-size: contain;
        background-repeat: no-repeat;
        transition: transform 0.3s;
    }
    
    .category-expand.open .category-expand-icon {
        transform: rotate(180deg);
    }
    
    /* 分类网格 */
    .category-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 10px;
        padding: 0;
        background: white;
        max-height: 0;
        overflow: hidden;
        visibility: hidden;
        opacity: 0;
        transition: all 0.3s ease-out;
    }
    
    .category-grid.visible {
        max-height: 500px;
        padding: 15px;
        border-top: 1px solid #f0f0f0;
        visibility: visible;
        opacity: 1;
    }
    
    .category-grid-item {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 10px;
        border-radius: 20px;
        font-size: 14px;
        color: #64748b;
        background: #f1f5f9;
        cursor: pointer;
    }
    
    .category-grid-item.active {
        background: linear-gradient(135deg, #4338ca, #3b82f6);
        color: white;
        box-shadow: 0 4px 12px rgba(59,130,246,0.3);
    }
    
    /* 课程卡片列表 */
    .course-list {
        padding: 16px;
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;
        margin-bottom: 80px; /* 预留底部空间 */
    }
    
    .course-card {
        background: white;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 3px 10px rgba(0,0,0,0.08);
        transition: all 0.3s;
        position: relative;
        border: 1px solid #f0f0f0;
    }
    
    .course-card:active {
        transform: scale(0.98);
    }
    
    .course-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(0,0,0,0.1);
    }
    
    .course-image-container {
        position: relative;
        overflow: hidden;
    }
    
    .course-image {
        width: 100%;
        height: 80px;
        object-fit: cover;
        border-radius: 12px 12px 0 0;
        transition: transform 0.3s ease;
    }
    
    .course-card:hover .course-image {
        transform: scale(1.05);
    }
    
    /* 课程类型角标 */
    .course-type-badge {
        position: absolute;
        top: 8px;
        right: 8px;
        padding: 3px 8px;
        border-radius: 10px;
        font-size: 12px;
        font-weight: 500;
        color: white;
        z-index: 1;
        display: flex;
        align-items: center;
        line-height: 1;
    }
    
    .course-type-badge.text {
        background: rgba(59, 130, 246, 0.8);
    }
    
    .course-type-badge.video {
        background: rgba(244, 63, 94, 0.8);
    }
    
    .course-type-badge i {
        margin-right: 3px;
        font-size: 10px;
    }
    
    .course-badges {
        position: absolute;
        top: 12px;
        left: 12px;
        display: flex;
        gap: 8px;
    }
    
    .badge {
        padding: 4px 8px;
        border-radius: 6px;
        font-size: 12px;
        font-weight: 600;
        color: white;
    }
    
    .badge-new {
        background: linear-gradient(135deg, #f43f5e, #e11d48);
    }
    
    .badge-hot {
        background: linear-gradient(135deg, #f97316, #ea580c);
    }
    
    .course-content {
        padding: 12px;
        border-top: 1px solid #f0f0f0;
        position: relative;
    }
    
    .course-content::before {
        content: '';
        position: absolute;
        top: -1px;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(to right, #4338ca, #3b82f6);
        opacity: 0.7;
    }
    
    .course-title {
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 8px;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        color: #333;
        line-height: 1.4;
    }
    
    .course-tags {
        display: flex;
        gap: 6px;
        margin-top: 8px;
        flex-wrap: wrap;
    }
    
    .course-tag {
        font-size: 10px;
        padding: 3px 8px;
        border-radius: 10px;
        background: #f0f7ff;
        color: #3b82f6;
        border: 1px solid rgba(59, 130, 246, 0.2);
    }
    
    .course-info {
        display: flex;
        align-items: center;
        gap: 16px;
        padding-top: 12px;
        border-top: 1px solid #e5e7eb;
    }
    
    .info-item {
        display: flex;
        align-items: center;
        gap: 4px;
        color: #64748b;
        font-size: 13px;
    }
    
    .info-item i {
        font-size: 16px;
        color: #94a3b8;
    }
    
    .course-price {
        font-size: 16px;
        font-weight: 600;
        color: #f43f5e;
    }
    
    /* 空状态 */
    .empty-state {
        padding: 48px 24px;
        text-align: center;
    }
    
    .empty-icon {
        width: 120px;
        height: 120px;
        margin: 0 auto 24px;
        opacity: 0.5;
    }
    
    .empty-text {
        color: #64748b;
        font-size: 15px;
    }
</style>

<div class="course-container">
    <!-- 顶部导航栏 -->
    <div class="top-nav">
        <a href="{{ url('/') }}" class="back-btn">
            <i class="fas fa-arrow-left"></i>
        </a>
        <h1 class="page-title">课程中心</h1>
        <div class="action-btn search-toggle" id="search-toggle">
            <i class="fas fa-search"></i>
        </div>
    </div>
    
    <!-- 搜索栏 -->
    <div class="search-container" id="search-container">
        <div class="search-box">
            <input type="text" id="course-search" placeholder="输入课程标题搜索..." autocomplete="off">
            <button id="search-clear" class="search-clear">
                <i class="fas fa-times"></i>
            </button>
            <button id="search-submit" class="search-submit">
                <i class="fas fa-search"></i>
            </button>
        </div>
    </div>

    <!-- 分类标签栏 -->
    <div class="category-section">
        <div class="category-tabs-container">
            <div class="category-tabs">
                @foreach($categories as $category)
                <div class="category-tab {{ $loop->first ? 'active' : '' }}" data-category-id="{{ $category['id'] }}">
                    <i class="fas fa-book"></i>
                    {{ $category['name'] }}
                </div>
                @endforeach
            </div>
            <div class="category-scroll-indicator">
                <div class="scroll-indicator-arrow"></div>
            </div>
        </div>
        <div class="category-expand">
            展开更多分类 <div class="category-expand-icon"></div>
        </div>
        <div class="category-grid">
            @foreach($categories as $category)
            <div class="category-grid-item {{ $loop->first ? 'active' : '' }}" data-category-id="{{ $category['id'] }}">
                {{ $category['name'] }}
            </div>
            @endforeach
        </div>
    </div>

    <!-- 课程列表 -->
    <div class="course-list">
        @forelse($courses as $course)
        <div class="course-card" onclick="window.location.href='{{ route('knowledge.course_detail', $course['id']) }}'">
            <div class="course-image-container" style="position: relative;">
                <img src="{{ $course['image'] }}" alt="{{ $course['title'] }}" class="course-image">
                @if(isset($course['type']))
                    @if($course['type'] == 2)
                        <div class="course-type-badge video">
                            <i class="fas fa-play-circle"></i> 视频
                        </div>
                    @else
                        <div class="course-type-badge text">
                            <i class="fas fa-file-alt"></i> 图文
                        </div>
                    @endif
                @else
                    <div class="course-type-badge text">
                        <i class="fas fa-file-alt"></i> 图文
                    </div>
                @endif
            </div>
            <div class="course-content">
                <h2 class="course-title">{{ $course['title'] }}</h2>
                <div class="course-tags">
                    <span class="course-tag">{{ $course['category_name'] }}</span>
                </div>

            </div>
        </div>
        @empty
        <div class="empty-state">
            <img src="{{ asset('images/empty-courses.svg') }}" alt="暂无课程" class="empty-icon">
            <div class="empty-text">暂无课程数据</div>
        </div>
        @endforelse
        
        <!-- 加载更多指示器 -->
        <div class="loading-indicator" id="loadingIndicator" style="display: none;">
            <div class="loading-spinner"></div>
        </div>
        
        <!-- 没有更多课程提示 -->
        <div class="load-more-end" id="loadMoreEnd" style="display: none;">
            没有更多课程了
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    $(document).ready(function() {
        // 无限滚动加载变量
        let currentPage = 1; // 从第1页开始，初始页面已经加载了第1页
        let isLoading = false;
        let hasMoreCourses = true;
        let currentCategoryId = 0; // 默认为"全部"分类
        let currentSearch = '';
        
        // 搜索框相关功能
        const $searchToggle = $('#search-toggle');
        const $searchContainer = $('#search-container');
        const $searchInput = $('#course-search');
        const $searchClear = $('#search-clear');
        const $searchSubmit = $('#search-submit');
        const $courseList = $('.course-list');
        let originalCourses = null;
        
        // 打开/关闭搜索框
        $searchToggle.on('click', function() {
            $searchContainer.toggleClass('active');
            if($searchContainer.hasClass('active')) {
                setTimeout(() => $searchInput.focus(), 300); // 动画结束后聚焦
                // 保存原始课程列表
                if(!originalCourses) {
                    originalCourses = $courseList.html();
                }
            } else {
                $searchInput.val(''); // 关闭时清空输入
                // 恢复原始课程列表
                if(originalCourses) {
                    $courseList.html(originalCourses);
                }
            }
        });
        
        // 点击其他地方关闭搜索框
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.search-container, .search-toggle').length) {
                $searchContainer.removeClass('active');
                $searchInput.val(''); // 关闭时清空输入
                // 恢复原始课程列表
                if(originalCourses) {
                    $courseList.html(originalCourses);
                }
            }
        });
        
        // 搜索功能
        function performSearch() {
            const searchText = $searchInput.val().trim();
            if(!searchText) {
                // 空搜索恢复原始课程列表
                if(originalCourses) {
                    $courseList.html(originalCourses);
                }
                // 重置当前搜索文本
                currentSearch = '';
                return;
            }
            
            // 设置当前搜索文本
            currentSearch = searchText;
            
            // ——最小改动：每次搜索前，先清空已有卡片和"没有更多"提示——
            $courseList.find('.course-card').remove();
            $('#loadMoreEnd').hide();
            // ——改动结束——
            
            // 显示加载中
            $courseList.append('<div class="loading" id="searchLoading">搜索中...</div>');
            
            // 重置分页状态
            currentPage = 1;
            hasMoreCourses = true;
            
            // 发送AJAX请求搜索课程的第一页
            $.ajax({
                url: '{{ route("knowledge.courses") }}',
                method: 'GET',
                data: {
                    search: searchText,
                    page: 1,
                    category_id: currentCategoryId
                },
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                },
                success: function(response) {
                    // 移除搜索加载中指示器
                    $('#searchLoading').remove();
                    
                    // 处理搜索结果
                    if (response.courses && response.courses.length > 0) {
                        // 使用通用的课程添加函数
                        appendCourses(response.courses);
                        hasMoreCourses = response.hasMore;
                        
                        // 如果没有更多课程，显示结束提示
                        if (!hasMoreCourses) {
                            $('#loadMoreEnd').show();
                        }
                        
                        // 高亮搜索关键词
                        highlightSearchText(searchText);
                    } else {
                        // 没有课程时显示空状态
                        $courseList.append(`
                            <div class="empty-state">
                                <img src="{{ asset('images/empty-courses.svg') }}" alt="暂无课程" class="empty-icon">
                                <div class="empty-text">找不到符合"${searchText}"的课程</div>
                            </div>
                        `);
                        hasMoreCourses = false;
                    }
                },
                error: function() {
                    // 如果AJAX失败，显示错误信息
                    $('#searchLoading').remove();
                    $courseList.append(`
                        <div class="empty-state">
                            <img src="{{ asset('images/empty-courses.svg') }}" alt="暂无课程" class="empty-icon">
                            <div class="empty-text">搜索请求失败，请重试</div>
                        </div>
                    `);
                    hasMoreCourses = false;
                }
            });
        }
        
        // 高亮搜索关键词
        function highlightSearchText(searchText) {
            if (!searchText) return;
            
            const regex = new RegExp(searchText, 'gi');
            $('.course-title').each(function() {
                const title = $(this).text();
                const highlightedTitle = title.replace(regex, '<span class="highlight">$&</span>');
                $(this).html(highlightedTitle);
            });
        }
        
        // 搜索按钮点击
        $searchSubmit.off('click').on('click', function() {
            performSearch();
        });
        
        // 回车搜索
        $searchInput.off('keypress').on('keypress', function(e) {
            if(e.which === 13) {
                e.preventDefault();
                performSearch();
            }
        });
        
        // 输入时即时搜索（300ms延迟）
        let searchTimeout;
        $searchInput.off('input').on('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(performSearch, 300);
        });
        
        // 清除按钮事件
        $searchClear.off('click').on('click', function() {
            $searchInput.val('').focus();
            currentSearch = ''; // 重置搜索状态
            
            // 重置课程列表和分页状态
            resetCourseList();
            if(originalCourses) {
                $courseList.html(originalCourses);
            }
            
            // 重置分页状态
            currentPage = 1;
            hasMoreCourses = true;
            $('#loadMoreEnd').hide();
        });
        
        // 分类标签点击事件
        function bindCategoryEvents(selector) {
            $(selector).on('click', function() {
                // 移除所有标签的active状态
                $('.category-tab, .category-grid-item').removeClass('active');
                
                // 获取分类ID
                const categoryId = $(this).data('category-id');
                
                // 为当前点击的标签和对应的网格项添加active状态
                $(`.category-tab[data-category-id="${categoryId}"], .category-grid-item[data-category-id="${categoryId}"]`).addClass('active');
                
                // 如果是从网格视图点击的，关闭网格视图
                if ($('.category-grid').hasClass('visible')) {
                    $('.category-grid').removeClass('visible');
                    $('.category-expand').removeClass('open');
                }
                
                // 如果点击的是选项卡，滚动到视图中央
                if (selector === '.category-tab') {
                    const $tabsContainer = $('.category-tabs');
                    const $tab = $(this);
                    const tabLeft = $tab.position().left;
                    const containerWidth = $tabsContainer.width();
                    const tabWidth = $tab.outerWidth();
                    
                    const scrollLeft = $tabsContainer.scrollLeft() + tabLeft - (containerWidth / 2) + (tabWidth / 2);
                    $tabsContainer.animate({
                        scrollLeft: scrollLeft
                    }, 300);
                }
                
                console.log('Selected category ID:', categoryId);
                
                // 设置当前分类 ID
                currentCategoryId = categoryId;
                
                // 重置课程列表和分页状态
                resetCourseList();
                
                // 如果是第一页，通过AJAX请求加载初始课程
                $.ajax({
                    url: '{{ route("knowledge.courses") }}',
                    method: 'GET',
                    data: {
                        category_id: categoryId,
                        page: 1
                    },
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    success: function(response) {
                        // 清除原始加载中提示
                        $courseList.find('.loading').remove();
                        
                        // 检查是否有课程数据
                        if (response.courses && response.courses.length > 0) {
                            // 添加新课程卡片
                            appendCourses(response.courses);
                            hasMoreCourses = response.hasMore;
                            
                            // 如果没有更多课程，显示结束提示
                            if (!hasMoreCourses) {
                                $('#loadMoreEnd').show();
                            }
                        } else {
                            // 没有课程时显示空状态
                            $courseList.append(`
                                <div class="empty-state">
                                    <img src="{{ asset('images/empty-courses.svg') }}" alt="暂无课程" class="empty-icon">
                                    <div class="empty-text">暂无相关课程</div>
                                </div>
                            `);
                            hasMoreCourses = false;
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('AJAX error:', error);
                        $('.course-list').html('<p class="empty-text">加载失败，请稍后重试</p>');
                    }
                });
            });
        }
        
        // 绑定分类标签和网格项点击事件
        bindCategoryEvents('.category-tab');
        bindCategoryEvents('.category-grid-item');
        
        // 展开/收起分类网格
        $('.category-expand').on('click', function() {
            $(this).toggleClass('open');
            $('.category-grid').toggleClass('visible');
        });
        
        // 重置课程列表和分页状态
        function resetCourseList() {
            // 保存初始化课程，但在分类切换时清空列表
            originalCourses = $courseList.html();
            // 清空列表内容（除了加载指示器和结束提示）
            $courseList.find('.course-card, .empty-state').remove();
            currentPage = 1; // 重置为第1页
            isLoading = false;
            hasMoreCourses = true;
            $('#loadingIndicator').hide();
            $('#loadMoreEnd').hide();
        }
        
        // 加载更多课程
        function loadMoreCourses() {
            if (isLoading || !hasMoreCourses) return;
            
            isLoading = true;
            $('#loadingIndicator').show();
            
            // 递增页码
            currentPage++;
            
            // 构建请求URL
            let url = '{{ route("knowledge.courses") }}?page=' + currentPage;
            
            // 添加分类参数
            if (currentCategoryId > 0) {
                url += '&category_id=' + currentCategoryId;
            }
            
            // 添加搜索参数
            if (currentSearch) {
                url += '&search=' + encodeURIComponent(currentSearch);
            }
            
            console.log('Loading page:', currentPage, 'URL:', url);
            
            // 发送AJAX请求
            $.ajax({
                url: url,
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                },
                success: function(response) {
                    isLoading = false;
                    $('#loadingIndicator').hide();
                    
                    console.log('Loaded page:', currentPage, 'Courses count:', response.courses ? response.courses.length : 0);
                    
                    if (response.courses && response.courses.length > 0) {
                        // 添加新课程卡片
                        appendCourses(response.courses);
                        hasMoreCourses = response.hasMore;
                        
                        // 如果在搜索状态，高亮搜索关键词
                        if (currentSearch && currentSearch.trim() !== '') {
                            highlightSearchText(currentSearch);
                        }
                    } else {
                        hasMoreCourses = false;
                    }
                    
                    // 如果没有更多课程，显示结束提示
                    if (!hasMoreCourses) {
                        $('#loadMoreEnd').show();
                    }
                },
                error: function(error) {
                    console.error('加载课程失败:', error);
                    isLoading = false;
                    currentPage--; // 失败时回退页码
                    $('#loadingIndicator').hide();
                }
            });
        }
        
        // 添加课程卡片到列表
        function appendCourses(courses) {
            courses.forEach(function(course) {
                // 确定课程类型图标
                let typeBadge = '';
                if (course.type == 2) {
                    typeBadge = `<div class="course-type-badge video"><i class="fas fa-play-circle"></i> 视频</div>`;
                } else {
                    typeBadge = `<div class="course-type-badge text"><i class="fas fa-file-alt"></i> 图文</div>`;
                }
                
                // 创建课程卡片HTML
                const courseHTML = `
                    <div class="course-card" onclick="window.location.href='{{ route("knowledge.course_detail", "__ID__") }}'.replace('__ID__', ${course.id})">
                        <div class="course-image-container" style="position: relative;">
                            <img src="${course.image}" alt="${course.title}" class="course-image">
                            ${typeBadge}
                        </div>
                        <div class="course-content">
                            <h2 class="course-title">${course.title}</h2>
                            <div class="course-tags">
                                <span class="course-tag">${course.category_name}</span>
                            </div>
                        </div>
                    </div>
                `;
                
                // 将卡片添加到加载指示器前
                $(courseHTML).insertBefore('#loadingIndicator');
            });
        }
        
        // 滚动到底部时加载更多课程
        $(window).on('scroll', function() {
            if (isLoading || !hasMoreCourses) return;
            
            // 当滚动到离底部200px距离时加载更多
            if ($(window).scrollTop() + $(window).height() > $(document).height() - 200) {
                loadMoreCourses();
            }
        });
        
        // 处理横向滚动指示器
        const $tabsContainer = $('.category-tabs');
        function handleScrollIndicator() {
            const $indicator = $('.category-scroll-indicator');
            // 如果已经滚动到最右侧，隐藏指示器
            if ($tabsContainer.scrollLeft() + $tabsContainer.width() >= $tabsContainer[0].scrollWidth - 10) {
                $indicator.css('opacity', '0');
            } else {
                $indicator.css('opacity', '1');
            }
        }
        
        // 初始化滚动指示器
        handleScrollIndicator();
        $tabsContainer.on('scroll', handleScrollIndicator);
        
        // 点击滚动指示器时，滚动到右侧
        $('.category-scroll-indicator').on('click', function() {
            $tabsContainer.animate({
                scrollLeft: $tabsContainer.scrollLeft() + 200
            }, 300);
        });
    });
</script>
@endsection