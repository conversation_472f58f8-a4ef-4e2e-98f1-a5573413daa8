@extends('layouts.app')

@section('title', '咨询 - 心理咨询平台')

@section('custom-styles')
<style>
    /* 搜索栏样式 */
    .search-bar {
        display: flex;
        align-items: center;
        background-color: #fff;
        padding: 10px 15px;
        position: sticky;
        top: 0;
        z-index: 100;
    }
    
    .search-input-wrap {
        flex: 1;
        height: 34px;
        border-radius: 17px;
        background-color: #f5f5f5;
        display: flex;
        align-items: center;
        padding: 0 15px;
    }
    
    .search-icon {
        width: 16px;
        height: 16px;
        margin-right: 6px;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999'%3E%3Cpath d='M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z'/%3E%3C/svg%3E");
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
    }
    
    .search-input {
        flex: 1;
        border: none;
        background: transparent;
        font-size: 14px;
        color: #333;
    }
    
    .search-input::placeholder {
        color: #999;
    }
    
    /* 分类标签样式 */
    .category-tabs {
        display: flex;
        background-color: #fff;
        padding: 10px 0;
        overflow-x: auto;
        white-space: nowrap;
        -webkit-overflow-scrolling: touch;
    }
    
    .category-tab {
        padding: 6px 15px;
        margin: 0 5px;
        font-size: 14px;
        color: #666;
        border-radius: 15px;
        flex-shrink: 0;
    }
    
    .category-tab.active {
        color: #fff;
        background-color: #4e9cff;
    }
    
    .category-tab:first-child {
        margin-left: 15px;
    }
    
    .category-tab:last-child {
        margin-right: 15px;
    }
    
    /* 筛选栏样式 */
    .filter-bar {
        display: flex;
        background-color: #fff;
        padding: 10px 15px;
        margin-bottom: 10px;
        border-bottom: 1px solid #f0f0f0;
    }
    
    .filter-item {
        display: flex;
        align-items: center;
        margin-right: 20px;
        font-size: 12px;
        color: #666;
    }
    
    .filter-item.active {
        color: #4e9cff;
    }
    
    .filter-item i {
        width: 12px;
        height: 12px;
        margin-left: 3px;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23666'%3E%3Cpath d='M7 10l5 5 5-5z'/%3E%3C/svg%3E");
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
    }
    
    .filter-item.active i {
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234e9cff'%3E%3Cpath d='M7 10l5 5 5-5z'/%3E%3C/svg%3E");
    }
    
    /* 心理师列表样式 */
    .counselor-list {
        padding: 0 15px;
    }
    
    .counselor-card {
        display: flex;
        background-color: #fff;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
    }
    
    .counselor-avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background-size: cover;
        background-position: center;
        margin-right: 12px;
        flex-shrink: 0;
    }
    
    .counselor-info {
        flex: 1;
    }
    
    .counselor-name-row {
        display: flex;
        align-items: center;
        margin-bottom: 5px;
    }
    
    .counselor-name {
        font-size: 16px;
        font-weight: bold;
        color: #333;
        margin-right: 8px;
    }
    
    .counselor-tag {
        font-size: 10px;
        color: #4e9cff;
        background-color: rgba(78, 156, 255, 0.1);
        padding: 2px 6px;
        border-radius: 4px;
    }
    
    .counselor-title {
        font-size: 12px;
        color: #666;
        margin-bottom: 5px;
    }
    
    .counselor-skills {
        display: flex;
        flex-wrap: wrap;
        margin-bottom: 5px;
    }
    
    .counselor-skill {
        font-size: 10px;
        color: #999;
        background-color: #f5f5f5;
        padding: 2px 6px;
        border-radius: 4px;
        margin-right: 6px;
        margin-bottom: 6px;
    }
    
    .counselor-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .counselor-rating {
        display: flex;
        align-items: center;
        font-size: 12px;
        color: #999;
    }
    
    .counselor-rating i {
        color: #ffb800;
        margin-right: 2px;
    }
    
    .counselor-price {
        font-size: 14px;
        color: #ff6b6b;
        font-weight: bold;
    }
    
    .counselor-button {
        display: block;
        width: 70px;
        height: 30px;
        line-height: 30px;
        text-align: center;
        background-color: #4e9cff;
        color: #fff;
        font-size: 12px;
        border-radius: 15px;
        text-decoration: none;
    }
</style>
@endsection

@section('content')
<div class="search-bar">
    <div class="search-input-wrap">
        <div class="search-icon"></div>
        <input type="text" class="search-input" placeholder="搜索心理师">
    </div>
</div>

<div class="category-tabs">
    <div class="category-tab active">全部</div>
    <div class="category-tab">情绪管理</div>
    <div class="category-tab">婚恋情感</div>
    <div class="category-tab">家庭关系</div>
    <div class="category-tab">职场压力</div>
    <div class="category-tab">青少年成长</div>
    <div class="category-tab">人格障碍</div>
</div>

<div class="filter-bar">
    <div class="filter-item active">综合排序<i></i></div>
    <div class="filter-item">评分最高<i></i></div>
    <div class="filter-item">价格<i></i></div>
    <div class="filter-item">咨询量<i></i></div>
</div>

<div class="counselor-list">
    <div class="counselor-card">
        <div class="counselor-avatar" style="background-image: url('https://via.placeholder.com/120x120/4e9cff/ffffff?text=张医生')"></div>
        <div class="counselor-info">
            <div class="counselor-name-row">
                <div class="counselor-name">张医生</div>
                <div class="counselor-tag">金牌心理师</div>
            </div>
            <div class="counselor-title">国家二级心理咨询师 | 5年经验</div>
            <div class="counselor-skills">
                <div class="counselor-skill">情绪管理</div>
                <div class="counselor-skill">婚恋情感</div>
                <div class="counselor-skill">抑郁症</div>
            </div>
            <div class="counselor-meta">
                <div class="counselor-rating"><i>★</i>4.9 (382次咨询)</div>
                <div class="counselor-price">¥299/次</div>
            </div>
        </div>
    </div>
    
    <div class="counselor-card">
        <div class="counselor-avatar" style="background-image: url('https://via.placeholder.com/120x120/ff6b6b/ffffff?text=李医生')"></div>
        <div class="counselor-info">
            <div class="counselor-name-row">
                <div class="counselor-name">李医生</div>
                <div class="counselor-tag">资深心理师</div>
            </div>
            <div class="counselor-title">临床心理学硕士 | 8年经验</div>
            <div class="counselor-skills">
                <div class="counselor-skill">青少年心理</div>
                <div class="counselor-skill">家庭关系</div>
                <div class="counselor-skill">焦虑症</div>
            </div>
            <div class="counselor-meta">
                <div class="counselor-rating"><i>★</i>4.8 (256次咨询)</div>
                <div class="counselor-price">¥350/次</div>
            </div>
        </div>
    </div>
    
    <div class="counselor-card">
        <div class="counselor-avatar" style="background-image: url('https://via.placeholder.com/120x120/52c41a/ffffff?text=王医生')"></div>
        <div class="counselor-info">
            <div class="counselor-name-row">
                <div class="counselor-name">王医生</div>
                <div class="counselor-tag">精神科医师</div>
            </div>
            <div class="counselor-title">精神科主治医师 | 10年经验</div>
            <div class="counselor-skills">
                <div class="counselor-skill">抑郁症</div>
                <div class="counselor-skill">焦虑症</div>
                <div class="counselor-skill">强迫症</div>
            </div>
            <div class="counselor-meta">
                <div class="counselor-rating"><i>★</i>4.7 (198次咨询)</div>
                <div class="counselor-price">¥399/次</div>
            </div>
        </div>
    </div>
    
    <div class="counselor-card">
        <div class="counselor-avatar" style="background-image: url('https://via.placeholder.com/120x120/faad14/ffffff?text=赵医生')"></div>
        <div class="counselor-info">
            <div class="counselor-name-row">
                <div class="counselor-name">赵医生</div>
                <div class="counselor-tag">婚恋专家</div>
            </div>
            <div class="counselor-title">婚姻家庭咨询师 | 6年经验</div>
            <div class="counselor-skills">
                <div class="counselor-skill">婚恋情感</div>
                <div class="counselor-skill">家庭关系</div>
                <div class="counselor-skill">亲子教育</div>
            </div>
            <div class="counselor-meta">
                <div class="counselor-rating"><i>★</i>4.6 (145次咨询)</div>
                <div class="counselor-price">¥320/次</div>
            </div>
        </div>
    </div>
</div>
@endsection
