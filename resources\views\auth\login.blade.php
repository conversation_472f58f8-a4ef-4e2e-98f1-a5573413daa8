@extends('layouts.app')

@section('title', '登录 - 心理健康平台')

@section('custom-styles')
<style>
    /* 覆盖主要容器样式 */
    body .container {
        max-width: 500px;
        padding: 0;
        margin: 0 auto;
        overflow-x: hidden;
        height: auto;
        min-height: 100vh;
    }
    
    /* 页面标题样式 */
    .module-header {
        background: linear-gradient(45deg, #5b7cef, #3c67e3);
        background-size: cover;
        background-position: center;
        color: white;
        padding: 15px;
        position: relative;
        text-align: center;
        height: 56px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 10px rgba(60, 103, 227, 0.15);
    }
    
    .module-header::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(circle at top right, rgba(255,255,255,0.1), transparent 70%);
        z-index: 1;
    }
    
    .module-title {
        font-size: 18px;
        font-weight: 600;
        position: relative;
        z-index: 2;
        color: white;
        text-shadow: 0 1px 2px rgba(0,0,0,0.2);
        letter-spacing: 0.5px;
    }
    
    .back-button {
        position: absolute;
        left: 15px;
        top: 50%;
        transform: translateY(-50%);
        z-index: 10;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: rgba(255,255,255,0.2);
        border-radius: 50%;
    }
    
    .back-icon {
        width: 16px;
        height: 16px;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z'/%3E%3C/svg%3E");
        background-repeat: no-repeat;
        background-size: contain;
    }
    
    /* 登录表单容器 */
    .login-container {
        padding: 25px 20px;
        background-color: #fff;
    }
    
    .login-card {
        background-color: #fff;
        border-radius: 12px;
        box-shadow: 0 2px 15px rgba(0,0,0,0.05);
        padding: 25px;
        margin-bottom: 20px;
    }
    
    .login-tabs {
        display: flex;
        border-bottom: 1px solid #f0f0f0;
        margin-bottom: 20px;
    }
    
    .login-tab {
        flex: 1;
        text-align: center;
        padding: 12px 0;
        font-size: 15px;
        color: #666;
        cursor: pointer;
        position: relative;
    }
    
    .login-tab.active {
        color: #3c67e3;
        font-weight: 600;
    }
    
    .login-tab.active::after {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 50%;
        transform: translateX(-50%);
        width: 30%;
        height: 3px;
        background-color: #3c67e3;
        border-radius: 3px;
    }
    
    .form-group {
        margin-bottom: 20px;
    }
    
    .form-control {
        background-color: #f2f2f7;
        border: none;
        border-radius: 10px;
        padding: 15px;
        width: 100%;
        font-size: 15px;
        transition: all 0.3s ease;
    }
    
    .form-control:focus {
        outline: none;
        box-shadow: 0 0 0 2px rgba(60, 103, 227, 0.2);
        background-color: #fff;
    }
    
    .btn-primary {
        background: #3c67e3;
        border: none;
        color: white;
        padding: 15px;
        border-radius: 10px;
        width: 100%;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        box-shadow: 0 4px 10px rgba(60, 103, 227, 0.2);
        transition: all 0.3s ease;
    }
    
    .btn-primary:hover {
        background: #345bd4;
        box-shadow: 0 6px 15px rgba(60, 103, 227, 0.3);
    }
    
    .form-hint {
        font-size: 12px;
        color: #8a8a8a;
        margin-top: 5px;
    }
    
    .login-footer {
        text-align: center;
        margin-top: 25px;
        font-size: 14px;
        color: #666;
    }
    
    .login-footer a {
        color: #3c67e3;
        text-decoration: none;
        font-weight: 500;
    }
    
    /* 验证码按钮 */
    .verify-code-group {
        display: flex;
        align-items: center;
    }
    
    .verify-code-group .form-control {
        flex: 1;
        margin-right: 10px;
    }
    
    .btn-code {
        height: 50px;
        min-width: 120px;
        background-color: #f2f2f7;
        border: none;
        color: #3c67e3;
        border-radius: 10px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .btn-code:hover {
        background-color: #e9e9f0;
    }
    
    .btn-code:disabled {
        color: #999;
        cursor: not-allowed;
    }
    
    /* 表单切换动画 */
    .form-tab {
        display: none;
    }
    
    .form-tab.active {
        display: block;
        animation: fadeIn 0.5s;
    }
    
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }
    
    /* 错误信息样式 */
    .error-message {
        color: #ff3b30;
        font-size: 12px;
        margin-top: 5px;
    }
    
    /* 记住我复选框 */
    .remember-group {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
    }
    
    .custom-checkbox {
        width: 20px;
        height: 20px;
        margin-right: 8px;
        position: relative;
    }
    
    .custom-checkbox input {
        position: absolute;
        opacity: 0;
        cursor: pointer;
        height: 0;
        width: 0;
    }
    
    .checkmark {
        position: absolute;
        top: 0;
        left: 0;
        height: 20px;
        width: 20px;
        background-color: #f2f2f7;
        border-radius: 5px;
    }
    
    .custom-checkbox input:checked ~ .checkmark {
        background-color: #3c67e3;
    }
    
    .checkmark:after {
        content: "";
        position: absolute;
        display: none;
    }
    
    .custom-checkbox input:checked ~ .checkmark:after {
        display: block;
    }
    
    .custom-checkbox .checkmark:after {
        left: 7px;
        top: 3px;
        width: 5px;
        height: 10px;
        border: solid white;
        border-width: 0 2px 2px 0;
        transform: rotate(45deg);
    }
</style>
@endsection

@section('content')
<!-- 顶部标题区域 -->
<div class="module-header">
    <a href="{{ route('home') }}" class="back-button">
        <div class="back-icon"></div>
    </a>
    <div class="module-title">登录</div>
</div>

<div class="login-container">
    <div class="login-card">
        <!-- 切换标签 -->
        <div class="login-tabs">
            <!--<div class="login-tab active" id="tab-password">密码登录</div>-->
            <div class="login-tab active" id="tab-code">验证码登录</div>
        </div>
        
        <!-- 密码登录表单 -->
        <!--<div class="form-tab active" id="form-password">-->
        <!--    <form action="{{ route('login.password') }}" method="POST">-->
        <!--        @csrf-->
        <!--        <div class="form-group">-->
        <!--            <input type="text" name="phone" class="form-control" placeholder="请输入手机号" value="{{ old('phone') }}">-->
        <!--            @error('phone')-->
        <!--                <div class="error-message">{{ $message }}</div>-->
        <!--            @enderror-->
        <!--        </div>-->
                
        <!--        <div class="form-group">-->
        <!--            <input type="password" name="password" class="form-control" placeholder="请输入密码">-->
        <!--            @error('password')-->
        <!--                <div class="error-message">{{ $message }}</div>-->
        <!--            @enderror-->
        <!--        </div>-->
                
        <!--        <div class="remember-group">-->
        <!--            <label class="custom-checkbox">-->
        <!--                <input type="checkbox" name="remember" {{ old('remember') ? 'checked' : '' }}>-->
        <!--                <span class="checkmark"></span>-->
        <!--            </label>-->
        <!--            <span>记住我</span>-->
        <!--        </div>-->
                
        <!--        <button type="submit" class="btn-primary">登录</button>-->
        <!--    </form>-->
        <!--</div>-->
        
        <!-- 验证码登录表单 -->
        <div class="form-tab active" id="form-code">
            <form action="{{ route('login.code') }}" method="POST">
                @csrf
                <div class="form-group">
                    <input type="text" name="phone" id="codePhone" class="form-control" placeholder="请输入手机号" value="{{ old('phone') }}">
                    @error('phone')
                        <div class="error-message">{{ $message }}</div>
                    @enderror
                </div>
                
                <div class="form-group verify-code-group">
                    <input type="text" name="code" class="form-control" placeholder="请输入验证码">
                    <button type="button" id="sendCodeBtn" class="btn-code">获取验证码</button>
                </div>
                @error('code')
                    <div class="error-message">{{ $message }}</div>
                @enderror
                
                <div class="remember-group">
                    <label class="custom-checkbox">
                        <input type="checkbox" name="remember" {{ old('remember') ? 'checked' : '' }}>
                        <span class="checkmark"></span>
                    </label>
                    <span>记住我</span>
                </div>
                
                <button type="submit" class="btn-primary">登录</button>
            </form>
        </div>
    </div>
    
    <div class="login-footer">
        还没有账号？<a href="{{ route('register') }}">立即注册</a>
    </div>
</div>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // // 切换登录方式
        // const tabPassword = document.getElementById('tab-password');
        // const tabCode = document.getElementById('tab-code');
        // const formPassword = document.getElementById('form-password');
        // const formCode = document.getElementById('form-code');
        
        // tabPassword.addEventListener('click', function() {
        //     tabPassword.classList.add('active');
        //     tabCode.classList.remove('active');
        //     formPassword.classList.add('active');
        //     formCode.classList.remove('active');
        // });
        
        // tabCode.addEventListener('click', function() {
        //     tabCode.classList.add('active');
        //     tabPassword.classList.remove('active');
        //     formCode.classList.add('active');
        //     formPassword.classList.remove('active');
        // });
        
        // 发送验证码逻辑
        const sendCodeBtn = document.getElementById('sendCodeBtn');
        const codePhone = document.getElementById('codePhone');
        
        let countdown = 0;
        let timer = null;
        
        function startCountdown(seconds) {
            countdown = seconds;
            sendCodeBtn.disabled = true;
            
            timer = setInterval(function() {
                countdown--;
                sendCodeBtn.textContent = `${countdown}秒后重发`;
                
                if (countdown <= 0) {
                    clearInterval(timer);
                    sendCodeBtn.disabled = false;
                    sendCodeBtn.textContent = '获取验证码';
                }
            }, 1000);
        }
        
        sendCodeBtn.addEventListener('click', async function() {
            // 验证手机号
            const phone = codePhone.value.trim();
            const phoneRegex = /^1[3-9]\d{9}$/;
            
            if (!phone) {
                alert('请输入手机号');
                return;
            }
            
            if (!phoneRegex.test(phone)) {
                alert('手机号格式不正确');
                return;
            }
            
            // 发送验证码请求
            try {
                const response = await fetch('{{ route("login.send_code") }}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}'
                    },
                    body: JSON.stringify({ phone: phone })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    startCountdown(60);
                    
                    if (data.debug_code) {
                        alert('开发模式：验证码是 ' + data.debug_code);
                    } else {
                        alert('验证码已发送，请注意查收');
                    }
                } else {
                    alert(data.message || '验证码发送失败，请稍后再试');
                }
            } catch (error) {
                console.error('Error:', error);
                alert('网络错误，请稍后再试');
            }
        });
    });
</script>
@endsection
