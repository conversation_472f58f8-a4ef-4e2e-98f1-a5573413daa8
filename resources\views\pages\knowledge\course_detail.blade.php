@extends('layouts.app')

@section('title', $course['title'] . ' - 课程详情')

@section('head')
<!-- 确保meta标签和头部设置统一 -->
<meta name="description" content="{{ $course['title'] }} - 课程详情">
<link href="https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.css" rel="stylesheet">
<script src="https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.js"></script>
@endsection

@section('custom-styles')
<style>
/* 重新设计，确保与全局样式兼容 */
.card-container {
    width: 100%;
    max-width: 500px;
    margin: 0 auto;
    background-color: #fff;
    overflow-x: hidden;
    position: relative;
}

/* 顶部导航栏 */
.course-header {
    position: sticky;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
    background: linear-gradient(135deg, #4338ca, #3b82f6);
    color: white;
    height: 56px;
    display: flex;
    align-items: center;
    padding: 0 15px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.15);
}

.back-btn {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255,255,255,0.2);
    cursor: pointer;
}

.header-title {
    font-size: 18px;
    font-weight: 600;
    margin: 0 auto;
    text-align: center;
}

/* 课程信息卡片 */
.course-hero {
    padding: 25px 15px 20px;
    text-align: center;
    position: relative;
    background: linear-gradient(to bottom, rgba(59, 130, 246, 0.05), rgba(255, 255, 255, 0));
    border-bottom: 1px solid #f0f0f0;
}

.course-cover {
    width: 120px;
    height: 120px;
    border-radius: 15px;
    object-fit: cover;
    margin: 0 auto 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.course-title {
    font-size: 20px;
    font-weight: 700;
    color: #333;
    margin-bottom: 10px;
}

.course-meta {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 15px;
    font-size: 13px;
    color: #666;
    margin-bottom: 15px;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 5px;
}

/* 标签容器样式 */
.tags-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 8px;
    margin: 5px 0;
    padding: 0 10px;
}

.course-tag {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 12px;
    background: #f0f7ff;
    color: #3c67e3;
    border: 1px solid rgba(60, 103, 227, 0.2);
}

/* 学习进度 */
.progress-section {
    margin: 10px auto;
    width: 80px;
    height: 80px;
    position: relative;
    overflow: visible;
    border-radius: 50%;
}

.progress-ring-container {
    width: 100%;
    height: 100%;
    position: relative;
    animation: progress-glow 2s alternate infinite ease-in-out;
    border-radius: 50%;
    overflow: hidden;
}

.progress-ring-bg {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: #e0e7ff;
    position: absolute;
    top: 0;
    left: 0;
}

.progress-ring-value {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: conic-gradient(#3c67e3 var(--progress), transparent 0);
    position: absolute;
    top: 0;
    left: 0;
    /* 确保没有溢出内容 */
    overflow: hidden;
    /* 使用裁剪路径确保完全是圆形 */
    clip-path: circle(50% at center);
}

.progress-ring-center {
    width: 78%;
    height: 78%;
    border-radius: 50%;
    background: white;
    position: absolute;
    top: 11%;
    left: 11%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: inset 0 0 5px rgba(0,0,0,0.05);
}

@keyframes progress-glow {
    0% {
        box-shadow: 0 0 5px rgba(60, 103, 227, 0.3);
    }
    100% {
        box-shadow: 0 0 15px rgba(60, 103, 227, 0.6);
    }
}

.progress-text {
    position: relative;
    z-index: 1;
    font-weight: 700;
    color: #3c67e3;
}

.progress-label {
    font-size: 12px;
    color: #666;
    text-align: center;
    margin-top: 5px;
}

/* 内容导航标签页 */
.tabs-container {
    display: flex;
    border-bottom: 1px solid #eee;
    background: #fff;
    position: relative;
}

.tab {
    flex: 1;
    text-align: center;
    padding: 15px 0;
    font-size: 15px;
    font-weight: 500;
    color: #666;
    position: relative;
    cursor: pointer;
}

.tab.active {
    color: #3c67e3;
    font-weight: 600;
}

.tab-indicator {
    position: absolute;
    bottom: 0;
    height: 3px;
    width: 50%;
    background: #3c67e3;
    transition: transform 0.3s;
}

/* 内容区 */
.content-section {
    padding: 15px;
    background: #fff;
}

/* 视频容器 */
.video-container {
    width: 100%;
    aspect-ratio: 16/9;
    background: #000;
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 15px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.video-container video {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* 内容卡片 */
.content-card {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 6px rgba(0,0,0,0.05);
    padding: 15px;
    margin-bottom: 15px;
}

/* 分页导航 */
.paging-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 15px;
}

.page-info {
    font-size: 14px;
    color: #666;
}

.nav-btn {
    padding: 8px 15px;
    background-color: #3c67e3;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
}

.nav-btn:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

/* 测试按钮 */
.test-btn {
    padding: 8px 20px;
    background-color: #8367ff;
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
}

/* 内容容器 */
.course-content-container {
    line-height: 1.6;
    font-size: 16px;
    color: #333;
}

/* 资料列表 */
.resources-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.resource-item {
    display: flex;
    align-items: center;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 8px;
    box-shadow: 0 1px 4px rgba(0,0,0,0.05);
}

.resource-icon {
    width: 36px;
    height: 36px;
    background: #e0e7ff;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
}

.resource-info {
    flex: 1;
}

.resource-title {
    font-size: 15px;
    font-weight: 600;
    color: #3c67e3;
}

.resource-desc {
    font-size: 13px;
    color: #666;
    margin-top: 2px;
}

.resource-download {
    color: #3c67e3;
    padding: 8px;
    border-radius: 50%;
    background: #e0e7ff;
    display: flex;
    align-items: center;
    justify-content: center;
}

.empty-message {
    padding: 20px;
    text-align: center;
    color: #999;
    font-size: 14px;
}

/* 标签列表 */
.tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    padding: 15px;
    justify-content: center;
}

.course-tag {
    background-color: #e0e7ff;
    color: #3c67e3;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
}

/* 相关课程 */
.related-section {
    padding: 15px;
}

.section-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
    text-align: center;
}

.related-courses {
    display: flex;
    overflow-x: auto;
    gap: 12px;
    padding-bottom: 10px;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
}

.related-courses::-webkit-scrollbar {
    display: none;
}

.related-course-card {
    min-width: 150px;
    border-radius: 8px;
    overflow: hidden;
    background: #fff;
    box-shadow: 0 2px 8px rgba(0,0,0,0.06);
    transition: transform 0.2s;
}

.related-course-card:hover {
    transform: translateY(-2px);
}

.related-img {
    width: 100%;
    height: 80px;
    object-fit: cover;
}

.related-info {
    padding: 10px;
}

.related-title {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.related-date {
    font-size: 12px;
    color: #999;
}

/* 底部操作栏 */
.action-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    box-shadow: 0 -2px 10px rgba(0,0,0,0.05);
    padding: 12px 15px;
    display: flex;
    justify-content: center;
    z-index: 100;
}

.button-group {
    display: flex;
    gap: 12px;
    width: 100%;
    max-width: 500px;
}

.primary-btn {
    flex: 1;
    background: linear-gradient(90deg, #3c67e3, #5b7cef);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px;
    font-size: 16px;
    font-weight: 600;
    text-align: center;
    cursor: pointer;
}

.outline-btn {
    flex: 1;
    background: white;
    color: #3c67e3;
    border: 1px solid #3c67e3;
    border-radius: 8px;
    padding: 12px;
    font-size: 16px;
    font-weight: 600;
    text-align: center;
    cursor: pointer;
}

/* Tab内容控制 */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* 内容分页 */
.content-page {
    display: none;
    animation: fadeIn 0.3s;
}

.content-page.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.xa-content {
    padding: 38px 48px 0 48px;
    font-size: 18px;
    color: #222;
    line-height: 2.1;
    word-break: break-word;
    background: transparent;
}
.xa-content img {
    max-width: 100%;
    border-radius: 18px;
    margin: 22px 0;
    display: block;
}
.xa-video {
    width: 100%;
    background: #000;
    border-radius: 18px;
    overflow: hidden;
    margin-bottom: 32px;
    aspect-ratio: 16/9;
    box-shadow: 0 2px 12px rgba(60,103,227,0.10);
}
.xa-video video {
    width: 100%;
    height: 100%;
    background: #000;
    border-radius: 18px;
    object-fit: cover;
}

.xa-paging-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.xa-nav-btn {
    padding: 8px 15px;
    background-color: var(--main-color);
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: background 0.3s;
    font-weight: 500;
}

.xa-nav-btn:hover {
    background-color: var(--main-color-dark);
}

.xa-nav-btn:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

.content-page {
    display: none;
    animation: fadeIn 0.5s;
}

.content-page.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.xa-resource-list {
    display: flex;
    flex-direction: column;
    gap: 22px;
    margin-top: 22px;
}
.xa-resource-item {
    display: flex;
    align-items: center;
    background: #fff;
    border-radius: 14px;
    padding: 18px 22px;
    box-shadow: 0 1px 8px rgba(60,103,227,0.04);
    gap: 18px;
    transition: box-shadow 0.2s, transform 0.2s;
}
.xa-resource-item:hover {
    box-shadow: 0 4px 24px rgba(60,103,227,0.10);
    transform: translateY(-2px) scale(1.02);
}
.xa-resource-icon {
    width: 40px;
    height: 40px;
    background: #e0e7ff;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
}
.xa-resource-title {
    font-size: 17px;
    font-weight: 700;
    color: var(--main-color);
}
.xa-resource-desc {
    font-size: 15px;
    color: #666;
    margin-top: 2px;
}
.xa-resource-download {
    margin-left: auto;
    color: var(--main-color);
    font-size: 20px;
    font-weight: 700;
    border: none;
    background: none;
    cursor: pointer;
    transition: color 0.2s;
}
.xa-resource-download:hover { color: #5b7cef; }

.xa-tag-list {
    margin: 38px 0 0 0;
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    justify-content: center;
}
.xa-tag {
    background: #e0e7ff;
    color: var(--main-color);
    font-size: 16px;
    padding: 8px 26px;
    border-radius: 14px;
    font-weight: 700;
}

.xa-related {
    margin: 56px 0 0 0;
}
.xa-related-title {
    font-size: 22px;
    font-weight: 800;
    color: #222;
    margin-bottom: 22px;
    text-align: left;
}
.xa-related-list {
    display: flex;
    gap: 28px;
    overflow-x: auto;
    padding-bottom: 8px;
}
.xa-related-card {
    min-width: 220px;
    background: #fff;
    border-radius: 18px;
    box-shadow: 0 2px 12px rgba(60,103,227,0.08);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    transition: box-shadow 0.2s, transform 0.2s;
}
.xa-related-card:hover {
    box-shadow: 0 8px 32px rgba(60,103,227,0.13);
    transform: translateY(-2px) scale(1.04);
}
.xa-related-img {
    width: 100%;
    height: 120px;
    object-fit: cover;
    border-top-left-radius: 18px;
    border-top-right-radius: 18px;
}
.xa-related-info {
    padding: 16px 18px 12px 18px;
}
.xa-related-title2 {
    font-size: 17px;
    font-weight: 800;
    color: #222;
    margin-bottom: 4px;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}
.xa-related-date {
    font-size: 14px;
    color: #999;
}

.xa-bottom-bar {
    position: relative !important;
    left: 0;
    right: 0;
    background: white;
    padding: 20px 16px !important;
    margin-top: 30px !important;
    box-shadow: 0 0 12px rgba(0,0,0,0.08);
    z-index: 10 !important;
    display: flex;
    justify-content: center;
    border-radius: 12px;
    align-items: center;
    gap: 20px;
    z-index: 100;
    margin-bottom: env(safe-area-inset-bottom, 0);
    padding-bottom: calc(16px + env(safe-area-inset-bottom, 0));
}
.xa-price {
    font-size: 26px;
    font-weight: 900;
    color: #ff6b6b;
    margin-right: 18px;
}
.xa-free {
    color: #52c41a;
    font-weight: 900;
}
.xa-btn-group {
    display: flex;
    gap: 16px;
}
.xa-btn {
    background: var(--main-gradient);
    color: #fff;
    border: none;
    border-radius: 32px;
    padding: 16px 32px;
    font-size: 18px;
    font-weight: 700;
    box-shadow: 0 2px 8px rgba(60,103,227,0.10);
    transition: all 0.2s;
    cursor: pointer;
}
.xa-btn:hover {
    background: linear-gradient(90deg, #5b7cef 0%, #3c67e3 100%);
    box-shadow: 0 6px 24px rgba(60,103,227,0.18);
    transform: translateY(-2px);
}
.xa-btn-outline {
    background: transparent;
    color: var(--main-color);
    border: 2px solid var(--main-color);
    box-shadow: none;
}
.xa-btn-outline:hover {
    background: rgba(60,103,227,0.08);
    box-shadow: 0 4px 16px rgba(60,103,227,0.12);
}

@media (max-width: 900px) {
    .xa-main { padding: 0 0 120px 0; }
    .xa-hero { padding: 18px 0 0 0; }
    .xa-cover { width: 100%; height: 180px; border-radius: 18px; }
    .xa-content.xa-tab-content { display: none; padding: 38px 48px 0 48px; font-size: 18px; color: #222; line-height: 2.1; word-break: break-word; background: transparent; }
    .xa-content.xa-tab-content.active { display: block !important; }
    .xa-content.xa-tab-content.active { display: block; }
    .xa-meta-list { gap: 10px 12px; font-size: 13px; }
    .xa-content { padding: 18px 10px 0 10px; font-size: 15px; }
    .xa-tab { font-size: 15px; padding: 12px 0 8px 0; }
    .xa-bottom-bar { gap: 12px; padding: 16px 10px; }
    .xa-btn-group { width: 100%; justify-content: space-around; }
    .xa-btn { font-size: 16px; padding: 10px 24px; width: 45%; }
    .xa-related-card { min-width: 140px; }
    .xa-related-img { height: 70px; }
}
</style>
@endsection

@section('content')
<div class="card-container">
    <!-- 顶部导航栏 -->
    <div class="course-header">
        <a href="{{ route('knowledge.courses') }}" class="back-btn">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none"><path d="M15 18L9 12L15 6" stroke="white" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"/></svg>
        </a>
        <div class="header-title">课程详情</div>
    </div>
    
    <!-- 课程信息 -->
    <div class="course-hero" data-aos="fade-up" data-aos-duration="800">
        <img src="{{ $course['image'] }}" alt="{{ $course['title'] }}" class="course-cover" data-aos="zoom-in" data-aos-delay="200">
        <h1 class="course-title" data-aos="fade-up" data-aos-delay="300">{{ $course['title'] }}</h1>
        <div class="course-meta" data-aos="fade-up" data-aos-delay="400">
            <div class="meta-item">
                <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                    <path d="M12 12C14.7614 12 17 9.76142 17 7C17 4.23858 14.7614 2 12 2C9.23858 2 7 4.23858 7 7C7 9.76142 9.23858 12 12 12Z" fill="#4FACFE"/>
                    <path d="M12 14.5C6.99 14.5 3 17.86 3 22C3 22 8.96 22 12 22C15.04 22 21 22 21 22C21 17.86 17.01 14.5 12 14.5Z" fill="#4FACFE"/>
                </svg>
                {{ $course['author'] }}
            </div>
            <div class="meta-item">
                <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                    <path d="M19 3H5C3.9 3 3 3.9 3 5V19C3 20.1 3.9 21 5 21H19C20.1 21 21 20.1 21 19V5C21 3.9 20.1 3 19 3Z" fill="#FF6B6B"/>
                    <path d="M12 17L6.5 11.5L7.91 10.09L12 14.17L16.09 10.09L17.5 11.5L12 17Z" fill="white"/>
                </svg>
                {{ $course['category_name'] }}
            </div>
            <div class="meta-item">
                <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                    <path d="M3 3H21V21H3V3Z" fill="#FFD166"/>
                    <path d="M7 17H10V11H7V17ZM10.5 17H13.5V7H10.5V17ZM14 17H17V14H14V17Z" fill="#2A2A2A"/>
                </svg>
                {{ $course['level_text'] }}
            </div>
            <div class="meta-item">
                <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                    <path d="M19 4H5C3.9 4 3 4.9 3 6V20C3 21.1 3.9 22 5 22H19C20.1 22 21 21.1 21 20V6C21 4.9 20.1 4 19 4Z" fill="#06D6A0"/>
                    <path d="M19 4H5C3.9 4 3 4.9 3 6V10H21V6C21 4.9 20.1 4 19 4Z" fill="#36B89C"/>
                    <path d="M7 1V4" stroke="#36B89C" stroke-width="2" stroke-linecap="round"/>
                    <path d="M17 1V4" stroke="#36B89C" stroke-width="2" stroke-linecap="round"/>
                    <path d="M12 16H8V12H12V16Z" fill="white"/>
                </svg>
                {{ $course['date'] }}
            </div>
        </div>
        
        <!-- 标签列表 -->
        @if(!empty($course['tags']))
            <div class="tags-container" data-aos="fade-up" data-aos-delay="500">
                @foreach($course['tags'] as $tag)
                    <span class="course-tag">{{ $tag }}</span>
                @endforeach
            </div>
        @endif

        @if(isset($course['user_progress']) && $course['user_progress'])
            <div class="progress-section" data-aos="zoom-in" data-aos-delay="600">
                <div class="progress-ring-container">
                    <div class="progress-ring-bg"></div>
                    <div class="progress-ring-value" style="--progress: {{ $course['user_progress']['progress'] ?? 0 }}%;"></div>
                    <div class="progress-ring-center">
                        <div class="progress-text"><span>{{ $course['user_progress']['progress'] ?? 0 }}</span>%</div>
                    </div>
                </div>
                <div class="progress-label">
                    @if($course['user_progress']['is_completed'])
                        已完成
                    @else
                        进行中
                    @endif
                </div>
            </div>
        @endif
    </div>

    <!-- 标签页导航 -->
    <div class="tabs-container" data-aos="fade-up">
        <div class="tab xa-tab active" data-tab="content-tab">内容</div>
        <div class="tab xa-tab" data-tab="resources-tab">资料</div>
        <div class="tab-indicator" id="xa-tabbar-indicator" style="left: 0;"></div>
    </div>

    <!-- 内容区域 -->
    <div class="content-section tab-content xa-tab-content active" id="content-tab" data-aos="fade-up" data-aos-delay="100">
        @if($course['type'] == 2 || $course['type'] == 3)
            <div class="video-container">
                <video id="course-video" controls poster="{{ $course['image'] }}">
                    <source src="{{ $course['video_url'] }}" type="video/mp4">
                    您的浏览器不支持HTML5视频播放
                </video>
            </div>
        @endif
        
        <!-- 只有文章类型才显示文章内容 -->
        @if($course['type'] == 1)
        <div class="content-card">
            <!-- 分页导航 -->
            <div class="paging-nav">
                <button id="prev-page" class="nav-btn" disabled>上一页</button>
                <div class="page-info">第 <span id="current-page">1</span> 页 / 共 <span id="total-pages">1</span> 页</div>
                <button id="next-page" class="nav-btn">下一页</button>
            </div>
            
            <!-- 原始内容容器(处理分页用) -->
            <div id="original-content" style="display:none">
            {!! $course['content'] !!}
            </div>
            
            <!-- 分页后的内容容器 -->
            <div id="paged-content" style="margin-bottom: 40px;"></div>
        </div>
        @else
        <!-- 视频课程的描述信息 -->
        <div class="content-card video-info-card">
            <h3>课程简介</h3>
            <div class="video-description">
                {!! $course['description'] ?? '' !!}
            </div>
        </div>
        @endif
    </div>

    <!-- 资料区域 -->
    <div class="content-section tab-content xa-tab-content" id="resources-tab" style="display: none; margin-bottom: 100px;" data-aos="fade-up" data-aos-delay="100">
        @if(!empty($course['resources']))
            <div class="resources-list">
                @foreach($course['resources'] as $resource)
                    <div class="resource-item">
                        <div class="resource-icon">
                            <svg width="20" height="20" fill="#3c67e3" viewBox="0 0 24 24"><path d="M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 1.99 2H18c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z"/></svg>
                        </div>
                        <div class="resource-info">
                            <div class="resource-title">{{ $resource['title'] ?? '学习资料' }}</div>
                            <div class="resource-desc">{{ $resource['description'] ?? '' }}</div>
                        </div>
                        <a href="{{ $resource['url'] ?? '#' }}" class="resource-download" download title="下载">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="#3c67e3"><path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z"/></svg>
                        </a>
                    </div>
                @endforeach
            </div>
        @else
            <div class="empty-message">暂无课程资料</div>
        @endif
    </div>

    <!-- 标签列表已移至头部 -->

    <!-- 相关课程 -->
    @if(!empty($relatedCourses))
        <div class="related-section" style="margin-bottom: 100px;">
            <h3 class="section-title">相关课程</h3>
            <div class="related-courses">
                @foreach($relatedCourses as $related)
                    <a href="{{ route('knowledge.course_detail', ['id' => $related['id']]) }}" class="related-course-card">
                        <img src="{{ $related['image'] }}" alt="{{ $related['title'] }}" class="related-img">
                        <div class="related-info">
                            <div class="related-title">{{ $related['title'] }}</div>
                            <div class="related-date">{{ $related['date'] }}</div>
                        </div>
                    </a>
                @endforeach
            </div>
        </div>
    @endif

    <!-- 底部按钮区 -->
    <div class="action-bar floating-button-area" data-aos="fade-up" style="margin-bottom: 60px; padding-bottom: 15px;">
        @if(Auth::check() && isset($course['user_progress']) && $course['user_progress'])
            <div class="button-group">
                <button class="primary-btn" id="start-learning">完成学习</button>
                <button class="outline-btn" id="restart-learning">重新学习</button>
            </div>
        @else
            <button class="primary-btn" id="start-learning">{{ Auth::check() ? '开始学习' : '登录后学习' }}</button>
        @endif
    </div>
</div>
@endsection



@section('scripts')
<script>
    // 初始化AOS动画库
    AOS.init({
        duration: 800,
        easing: 'ease',
        once: true,
        offset: 50
    });
    
    // 1. 全局 AJAX CSRF
$.ajaxSetup({
  headers: { 'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') }
});

$(function(){
  console.log('开始初始化课程');
  
  // 5. 学习进度相关变量
  var initProg = {{ $course['user_progress']['progress'] ?? 0 }}, prog = initProg;
  // 使用后端定义的常量判断课程类型
  var courseType = {{ $course['type'] ?? 1 }};
  // 视频课程类型：视频课程为2 和 视频系列为3
  var isVideoType = (courseType == 2 || courseType == 3);
  console.log('课程类型:', courseType, isVideoType ? '视频课程' : '文章课程');
  
  // 定义默认变量
  var pages = [], total = 1, current = 1;
  
  // 只有文章类型才初始化分页
  if (!isVideoType) {
    console.log('文章课程，初始化分页');
    // 2. 读原始内容并分页
    var raw = $('#original-content').html() || '';
    if (!raw.trim()) {
      $('#paged-content').html('<p style="color:#999;">暂无课程内容</p>');
      return;
    }
    
    // 拆分页函数 - 只在文章课程中使用
    function splitPages(html) {
      var wrapper = $('<div>').html(html);
      var elems   = wrapper.children();
      if (elems.length <= 1) return [html]; 
  
      var pages = [], buf = '', limit = 2000, count = 0;
      elems.each(function(){
        var h = $(this).prop('outerHTML');
        buf   += h;
        count += h.length;
        // 每当超出 limit，就开新页
        if (count >= limit) {
          pages.push(buf);
          buf = '';
          count = 0;
        }
      });
      if (buf) pages.push(buf);
      return pages;
    }
    
    // 3. 构建分页
    pages = splitPages(raw);
    total = pages.length;
    
    // 构建分页内容
    $('#total-pages').text(total);
    var $pc = $('#paged-content').empty();
    for (var i = 0; i < total; i++) {
      $pc.append(
        '<div class="content-page" id="page-'+(i+1)+'" style="display:none;">'
        + pages[i] +
        '</div>'
      );
    }
    
    // 4. 翻页逻辑
    function showPage(n) {
      if (n < 1 || n > total) return;
      $('.content-page').hide();
      $('#page-'+n).show();
      $('#current-page').text(n);
      $('#prev-page').prop('disabled', n===1);
      $('#next-page').prop('disabled', n===total);
      updateProg(n);
      current = n;
    }
    
    // 绑定翻页事件
    $('#prev-page').click(function(){ showPage(current-1); });
    $('#next-page').click(function(){ showPage(current+1); });
    
    // 显示初始页
    var lastPosition = {{ $course['user_progress']['current_position'] ?? 1 }};
    showPage(Math.min(lastPosition, total) || 1);
  } else {
    console.log('视频课程，不需要初始化分页');
    // 隐藏分页导航按钮
    $('.paging-nav').hide();
  }
  
  // 文章课程的进度更新函数
  function updateProg(pageNum) {
    // 只有文章类型才使用分页更新进度
    if (!isVideoType) {
      // 线性计算进度，确保每页进度平均分配
      var pct = Math.round(pageNum/total*100);
      // 如果计算出的进度大于当前进度，才更新
      var np = Math.max(prog, pct);
      prog = np;
      // 防止数值溢出，限制最大为100%
      if (np > 100) np = 100;
      // 更新进度环的显示
      $('.progress-ring-value').css('--progress', np + '%');
      $('.progress-text span').text(np); // 更新进度文字
      scheduleSave();
    }
  }

  // 6. 保存进度
  var saveTimer;
  function scheduleSave() {
    // 只有登录用户才保存进度
    if ({{ Auth::check() ? 'true' : 'false' }}) {
      clearTimeout(saveTimer);
      saveTimer = setTimeout(function(){
        // 只有文章课程才使用这个函数保存进度
        if (!isVideoType) {
          // 发送Ajax请求保存进度
          $.post('/knowledge/course_progress/{{ $course["id"] }}', { 
            position: current, // 当前页码
            progress: prog,   // 当前进度
            completed: prog>=100?1:0 // 是否完成
          });
          console.log('文章进度保存：', prog + '%', '当前页码：', current);
        }
      }, 500);
    }
  }

  // 定义全局变量解决作用域问题
  window.videoElement = null;
  
  // 视频课程的进度处理
  if (isVideoType) {
    // 获取视频元素
    window.videoElement = document.getElementById('course-video');
    if (window.videoElement) {
      // 初始化时先将进度显示为0%，避免默认显示100%
      if (prog == 0) {
        $('.progress-ring-value').css('--progress', '0%');
        $('.progress-text span').text('0');
      }
      
      // 获取上次播放位置
      var lastPlayPosition = {{ $course['user_progress']['current_position'] ?? 0 }};
      
      // 如果有上次播放位置，跳转到该位置
      if (lastPlayPosition > 0) {
        window.videoElement.addEventListener('loadedmetadata', function() {
          // 如果上次播放位置有效，则跳转到该位置
          if (lastPlayPosition > 0 && lastPlayPosition < window.videoElement.duration) {
            window.videoElement.currentTime = lastPlayPosition;
            console.log('从上次播放位置继续:', lastPlayPosition);
            // 立即触发进度更新
            var resumePercent = Math.floor((lastPlayPosition / window.videoElement.duration) * 100);
            prog = Math.max(1, resumePercent);
            $('.progress-ring-value').css('--progress', prog + '%');
            $('.progress-text span').text(prog);
          }
        });
      }
      
      // 不需要开启额外的定时器了，因为已经在timeupdate事件中进行定期保存
      var savePositionTimer = null;
      
      // 开始播放时更新状态
      window.videoElement.addEventListener('play', function() {
        console.log('视频开始播放');
      });
      
      // 暂停时立即保存当前位置
      window.videoElement.addEventListener('pause', function() {
        if (window.videoElement.currentTime > 0) {
          // 更新播放进度
          var currentPercent = Math.floor((window.videoElement.currentTime / window.videoElement.duration) * 100);
          currentPercent = Math.max(1, currentPercent);
          prog = Math.max(prog, currentPercent);
          
          // 更新进度环显示
          $('.progress-ring-value').css('--progress', prog + '%');
          $('.progress-text span').text(prog);
          
          // 立即保存当前进度
          $.post(
            '/knowledge/course_progress/{{ $course["id"] }}',
            { 
              position: window.videoElement.currentTime, 
              progress: prog, 
              completed: prog>=100?1:0
            }
          );
          console.log('暂停时保存进度:', prog + '%', '位置:', window.videoElement.currentTime);
        }
      });
      
      // 视频进度更新事件 - 简化代码且去除重复逻辑
      var lastSaveTime = 0;
      window.videoElement.addEventListener('timeupdate', function() {
        if (window.videoElement.duration > 0) {
          // 计算观看进度百分比
          var percent = Math.floor((window.videoElement.currentTime / window.videoElement.duration) * 100);
          
          // 视频观看必须至少计算 1% 进度
          percent = Math.max(1, percent);
          
          // 确保进度只能增加不能减少
          if (percent > prog) {
            prog = percent;
            // 即时更新进度环显示
            $('.progress-ring-value').css('--progress', prog + '%');
            $('.progress-text span').text(prog);
            
            // 仅在以下情况下保存进度
            var now = Date.now();
            // 1. 当进度是5%倍数时保存 (5%, 10%, 15%...)
            // 2. 间隔10秒以上
            if (prog % 5 === 0 || now - lastSaveTime > 10000) {
              $.post(
                '/knowledge/course_progress/{{ $course["id"] }}',
                { 
                  position: window.videoElement.currentTime, 
                  progress: prog, 
                  completed: prog>=100?1:0
                }
              );
              lastSaveTime = now;
              console.log('视频进度已自动保存:', prog + '%');
            }
          }
        }
      });
      
      // 视频结束事件 - 自动完成课程
      window.videoElement.addEventListener('ended', function() {
        // 当视频播放完成时，将进度设为100%
        prog = 100;
        $('.progress-ring-value').css('--progress', '100%');
        $('.progress-text span').text('100');
        
        // 保存最终完成状态
        $.post(
          '/knowledge/course_progress/{{ $course["id"] }}',
          { position: window.videoElement.duration, progress: 100, completed: 1 }
        );
        console.log('视频播放完成，进度设为100%');
        
        // 弹出提示消息
        setTimeout(function() {
          alert('恭喜您完成了该视频课程的学习！');
        }, 500);
      });
    }
  }

  // 8. Tab 切换（保持不变）
  $('.xa-tab').click(function(){
    var t = $(this).data('tab');
    $('.xa-tab').removeClass('active');
    $(this).addClass('active');
    $('.xa-tab-content').hide().removeClass('active');
    $('#'+t).show().addClass('active');
    var idx = $('.xa-tab').index(this), w=100/$('.xa-tab').length;
    $('#xa-tabbar-indicator').css({left:(idx*w)+'%',width:w+'%'});
  });
  
  // 9. 添加完成学习按钮功能 - 可以快速完成课程
  $('#start-learning').click(function(e){
    e.preventDefault();
    
    // 检查登录状态
    if (!{{ Auth::check() ? 'true' : 'false' }}) {
      alert('请先登录后再学习');
      window.location.href = '{{ route("login") }}';
      return false;
    }
    
    // 如果是视频课程，先停止视频播放
    if (isVideoType && window.videoElement) {
      if (!window.videoElement.paused) {
        window.videoElement.pause();
      }
    }
    
    // 确认是否完成学习
    if (!confirm('确认将该课程标记为已完成吗？进度将设置为100%')) {
      return false;
    }
    
    // 将进度设为100%
    prog = 100;
    
    // 更新进度环显示
    $('.progress-ring-value').css('--progress', '100%');
    $('.progress-text span').text('100');
    
    // 发送完成学习请求
    $.ajax({
      url: '/knowledge/course_progress/{{ $course["id"] }}',
      type: 'POST',
      data: { 
        // 根据课程类型使用不同的位置参数
        position: isVideoType && window.videoElement ? window.videoElement.duration : total,
        progress: 100,
        completed: 1 
      },
      success: function(response) {
        alert('恭喜完成课程学习！');
        location.reload(); // 刷新页面显示更新后的状态
      },
      error: function(xhr) {
        alert('保存完成状态失败，请稍后再试');
      }
    });
  });
  
  // 重新学习按钮
  $('#restart-learning').click(function(e){
    e.preventDefault();
    
    // 确认重置进度
    if (!confirm('确认要重新学习该课程吗？当前进度将被重置为0%')) {
      return false;
    }
    
    // 如果是视频课程，先停止视频播放
    if (isVideoType && window.videoElement) {
      if (!window.videoElement.paused) {
        window.videoElement.pause();
      }
    }

    // 先重置界面显示
    prog = 0;
    $('.progress-ring-value').css('--progress', '0%');
    $('.progress-text span').text('0');
    
    // 直接调用reset专用API重置进度
    $.ajax({
      url: '/knowledge/course_progress/{{ $course["id"] }}/reset',
      type: 'POST',
      success: function(response) {
        // 结束前，重置视频位置到起始
        if (isVideoType && window.videoElement) {
          window.videoElement.currentTime = 0;
        }
        
        alert('进度已重置为0%，可以重新开始学习了');
        
        // 清除本地存储
        if (isVideoType) {
          // 对于视频课程，清除所有相关存储
          try {
            window.localStorage.removeItem('last_course_position_{{ $course["id"] }}');
            window.sessionStorage.removeItem('video_progress_{{ $course["id"] }}');
          } catch(e) { console.error(e); }
        }
        
        // 强制刷新页面确保重置生效
        location.reload(true);
      },
      error: function(xhr) {
        alert('重置进度失败，请稍后再试');
      }
    });
  });
});
</script>
@endsection
