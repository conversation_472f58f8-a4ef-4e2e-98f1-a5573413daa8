<?php

namespace App\Admin\Controllers;

use App\Models\QuizPrize;
use App\Models\QuizActivity;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Http\JsonResponse;

class QuizPrizeController extends AdminController
{
    protected $title = '问答奖品';

    protected function grid()
    {
        return Grid::make(new QuizPrize(), function (Grid $grid) {
            $grid->model()->with(['quizActivity']);
            
            // 获取问答活动ID过滤
            $quizActivityId = request()->get('quiz_activity_id');
            if ($quizActivityId) {
                $grid->model()->where('quiz_activity_id', $quizActivityId);
                $quizActivity = QuizActivity::find($quizActivityId);
                if ($quizActivity) {
                    $this->title = "问答奖品 - {$quizActivity->title}";
                }
            }
            
            $grid->column('id', 'ID')->sortable();
            $grid->column('quiz_activity_id', '所属活动')->display(function () {
                return $this->quizActivity ? $this->quizActivity->title : '-';
            });
            $grid->column('name', '奖品名称');
            $grid->column('image', '奖品图片')->image('', 60, 60);
            $grid->column('prize_type', '奖品类型')->using([
                'physical' => '实物奖品',
                'coupon' => '优惠券',
                'virtual' => '虚拟物品',
            ]);
            $grid->column('quantity', '奖品数量');
            $grid->column('remaining', '剩余数量')->display(function () {
                return $this->remainingQuantity();
            });
            $grid->column('min_score', '最低获奖分数');
            
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id', 'ID');
                $filter->equal('quiz_activity_id', '所属活动')->select(function () {
                    return QuizActivity::pluck('title', 'id');
                });
                $filter->like('name', '奖品名称');
                $filter->equal('prize_type', '奖品类型')->select([
                    'physical' => '实物奖品',
                    'coupon' => '优惠券',
                    'virtual' => '虚拟物品',
                ]);
            });
            
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableView();
            });
            
            // 添加返回按钮
            if ($quizActivityId) {
                $grid->tools(function (Grid\Tools $tools) use ($quizActivityId) {
                    $tools->append('<a href="' . admin_url('quiz-activities') . '" class="btn btn-sm btn-white"><i class="feather icon-arrow-left"></i> 返回活动列表</a>');
                });
            }
        });
    }

    protected function form()
    {
        return Form::make(new QuizPrize(), function (Form $form) {
            $form->display('id', 'ID');
            
            // 获取问答活动ID
            $quizActivityId = request()->get('quiz_activity_id');
            
            if ($quizActivityId && !$form->isEditing()) {
                $form->hidden('quiz_activity_id')->value($quizActivityId);
            } else {
                $form->select('quiz_activity_id', '所属活动')
                    ->options(QuizActivity::pluck('title', 'id'))
                    ->required();
            }
            
            $form->text('name', '奖品名称')
                ->required()
                ->rules('required|max:100');
                
            $form->textarea('description', '奖品描述')
                ->rows(3);
                
            $form->image('image', '奖品图片')
                ->autoUpload()
                ->uniqueName()
                ->help('建议尺寸: 400 x 400 像素');
                
            $form->radio('prize_type', '奖品类型')
                ->options([
                    'physical' => '实物奖品',
                    'coupon' => '优惠券',
                    'virtual' => '虚拟物品',
                ])
                ->required()
                ->default('physical');
                
            $form->number('quantity', '奖品数量')
                ->required()
                ->min(1)
                ->default(1)
                ->help('设置奖品总数量');
                
            $form->number('min_score', '最低获奖分数')
                ->required()
                ->min(0)
                ->default(60)
                ->help('用户需要达到的最低分数才能获得此奖品');
                
            $form->display('created_at', '创建时间');
            $form->display('updated_at', '更新时间');
            
            $form->disableViewButton();
            
            $form->saved(function (Form $form) {
                // 创建/更新成功后重定向回列表页面，保留问答活动ID参数
                $quizActivityId = $form->quiz_activity_id;
                return JsonResponse::make()->success('保存成功！')->location('quiz-prizes');
            });
        });
    }
}
