<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Dcat\Admin\Traits\HasDateTimeFormatter;
use App\Models\Traits\HasContentIndex;
use App\Contracts\HasContentIndex as HasContentIndexContract;

class CourseLesson extends Model implements HasContentIndexContract
{
    use HasFactory, SoftDeletes, HasDateTimeFormatter, HasContentIndex;

    protected $fillable = [
        'title',
        'slug',
        'image',
        'description',
        'content',
        'video_url',
        'video_duration',
        'resources',
        'category_id',
        'author_id',
        'views',
        'likes',
        'lessons_count',
        'publish_time',
        'type',
        'level',
        'status',
        'is_recommended',
        'is_free',
        'price'
    ];

    protected $casts = [
        'publish_time' => 'datetime',
        'resources' => 'array',
        'is_recommended' => 'boolean',
        'is_free' => 'boolean'
    ];

    // 课程类型常量
    const TYPE_ARTICLE = 1;
    const TYPE_VIDEO = 2;
    const TYPE_MIXED = 3;

    // 难度级别常量
    const LEVEL_BEGINNER = 1;
    const LEVEL_INTERMEDIATE = 2;
    const LEVEL_ADVANCED = 3;

    // 状态常量
    const STATUS_DRAFT = 0;
    const STATUS_PUBLISHED = 1;
    const STATUS_OFFLINE = 2;

    // 类型映射
    public static function getTypeMap()
    {
        return [
            self::TYPE_ARTICLE => '文章',
            self::TYPE_VIDEO => '视频',
            self::TYPE_MIXED => '混合',
        ];
    }

    // 难度映射
    public static function getLevelMap()
    {
        return [
            self::LEVEL_BEGINNER => '入门',
            self::LEVEL_INTERMEDIATE => '进阶',
            self::LEVEL_ADVANCED => '高级'
        ];
    }

    // 状态映射
    public static function getStatusMap()
    {
        return [
            self::STATUS_DRAFT => '草稿',
            self::STATUS_PUBLISHED => '已发布',
            self::STATUS_OFFLINE => '已下架'
        ];
    }

    // 获取类型文本
    public function getTypeTextAttribute()
    {
        return self::getTypeMap()[$this->type] ?? '未知';
    }

    // 获取难度文本
    public function getLevelTextAttribute()
    {
        return self::getLevelMap()[$this->level] ?? '未知';
    }

    // 获取状态文本
    public function getStatusTextAttribute()
    {
        return self::getStatusMap()[$this->status] ?? '未知';
    }

    // 与分类的关联
    public function category()
    {
        return $this->belongsTo(CourseLessonCategory::class, 'category_id');
    }

    // 与作者的关联
    public function author()
    {
        return $this->belongsTo(User::class, 'author_id');
    }

    // 与标签的关联
    public function tags()
    {
        return $this->belongsToMany(CourseLessonTag::class, 'course_lesson_tag', 'course_lesson_id', 'course_lesson_tag_id');
    }

    // 与学习进度的关联
    public function progress()
    {
        return $this->hasMany(CourseLessonProgress::class);
    }

    // 获取用户的学习进度
    public function getUserProgress($userId)
    {
        return $this->progress()->where('user_id', $userId)->first();
    }

    // 判断是否是视频课程
    public function isVideoType()
    {
        return in_array($this->type, [self::TYPE_VIDEO, self::TYPE_MIXED]);
    }

    // 判断是否是文章课程
    public function isArticleType()
    {
        return in_array($this->type, [self::TYPE_ARTICLE, self::TYPE_MIXED]);
    }

    // 获取时长格式化显示
    public function getDurationAttribute()
    {
        if (!$this->video_duration) {
            return '--:--';
        }
        return $this->video_duration;
    }

    // 范围查询：已发布的课程
    public function scopePublished($query)
    {
        return $query->where('status', self::STATUS_PUBLISHED);
    }

    // 范围查询：推荐的课程
    public function scopeRecommended($query)
    {
        return $query->where('is_recommended', true);
    }

    /**
     * 实现HasContentIndex接口：获取内容索引类型
     */
    public function getContentIndexType(): string
    {
        return 'course_lesson';
    }

    /**
     * 实现HasContentIndex接口：获取内容索引标题
     */
    public function getContentIndexTitle(): string
    {
        return $this->title ?? '';
    }

    /**
     * 实现HasContentIndex接口：获取内容索引摘要
     */
    public function getContentIndexSummary(): ?string
    {
        return $this->description;
    }

    /**
     * 实现HasContentIndex接口：获取内容索引URL
     */
    public function getContentIndexUrl(): ?string
    {
        try {
            // 尝试生成路由，如果失败则返回null
            if (\Route::has('course-lessons.show')) {
                return route('course-lessons.show', $this->id);
            }
            // 如果路由不存在，返回一个简单的URL
            return url("/course-lessons/{$this->id}");
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * 实现HasContentIndex接口：获取内容索引元数据
     */
    public function getContentIndexMetadata(): array
    {
        $metadata = [
            'category' => $this->category?->name,
            'author' => $this->author?->name,
            'views' => $this->views,
            'likes' => $this->likes,
            'lessons_count' => $this->lessons_count,
            'is_recommended' => $this->is_recommended,
            'is_free' => $this->is_free,
            'price' => $this->price,
            'status' => $this->status,
            'status_text' => $this->status_text,
            'type' => $this->type,
            'type_text' => $this->type_text,
            'level' => $this->level,
            'level_text' => $this->level_text,
            'publish_time' => $this->publish_time ? (is_string($this->publish_time) ? $this->publish_time : $this->publish_time->toISOString()) : null,
            'video_duration' => $this->video_duration,
            'video_url' => $this->video_url,
            'slug' => $this->slug,
        ];

        // 添加标签信息
        if ($this->relationLoaded('tags')) {
            $metadata['tags'] = $this->tags->pluck('name')->toArray();
        }

        // 添加资源信息
        if ($this->resources) {
            $metadata['resources'] = $this->resources;
        }

        return array_filter($metadata, fn($value) => $value !== null);
    }

    /**
     * 实现HasContentIndex接口：检查是否应该被索引
     */
    public function shouldBeIndexed(): bool
    {
        return $this->status === self::STATUS_PUBLISHED;
    }

    /**
     * 实现HasContentIndex接口：获取用于向量生成的内容
     */
    public function getContentForVector(): string
    {
        $parts = [];
        
        if ($this->title) {
            $parts[] = $this->title;
        }
        
        if ($this->description) {
            $parts[] = $this->description;
        }
        
        if ($this->content) {
            // 清理HTML标签并截取前2000字符
            $cleanContent = strip_tags($this->content);
            $parts[] = mb_substr($cleanContent, 0, 2000);
        }
        
        // 添加类型和难度信息
        $parts[] = $this->type_text;
        $parts[] = $this->level_text;
        
        return implode(' ', $parts);
    }
}
