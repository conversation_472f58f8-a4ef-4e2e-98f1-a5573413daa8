<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class NewsCategory extends Model
{
    use HasFactory, HasDateTimeFormatter;

    /**
     * 表名
     *
     * @var string
     */
    protected $table = 'news_categories';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'name', 'slug', 'description', 'sort_order'
    ];

    /**
     * 获取该分类下的所有新闻
     */
    public function news()
    {
        return $this->hasMany(News::class, 'category_id');
    }

    /**
     * 本地作用域：按排序
     */
    public function scopeSorted($query)
    {
        return $query->orderBy('sort_order', 'asc');
    }
}
