@extends('layouts.app')

@section('title', $counselor->name . ' - 心理咨询师详情')

@section('custom-styles')
<style>
    body {
        background-color: #f8f9fa;
    }
    
    .container {
        max-width: 900px;
        margin: 0 auto;
        padding-bottom: 80px !important; /* 为底部菜单栏预留更多空间 */
    }
    
    .counselor-detail {
        padding: 20px;
        border-radius: 12px;
        background-color: #fff;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        margin-bottom: 20px;
    }
    
    /* 咨询师顶部信息卡片 */
    .counselor-card {
        background: linear-gradient(135deg, #4e9cff 0%, #2979ff 100%);
        color: white;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 25px;
        box-shadow: 0 4px 15px rgba(41, 121, 255, 0.2);
    }
    
    .counselor-header {
        display: flex;
        flex-direction: column;
        align-items: center !important;
        justify-content: center !important;
        margin-bottom: 15px;
        text-align: center !important;
    }
    
    .counselor-avatar {
        width: 90px;
        height: 90px;
        border-radius: 50%;
        overflow: hidden;
        margin-bottom: 15px;
        flex-shrink: 0;
        border: 3px solid rgba(255, 255, 255, 0.7);
        box-shadow: 0 3px 8px rgba(0, 0, 0, 0.15);
    }
    
    .counselor-avatar img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    
    .counselor-basic-info {
        width: 100%;
        display: flex;
        flex-direction: column;
        align-items: center !important;
        text-align: center !important;
        padding: 0 20px;
        box-sizing: border-box;
    }
    
    .counselor-name {
        font-size: 22px;
        font-weight: bold;
        margin-bottom: 5px;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        text-align: center !important;
        width: 100%;
        display: block;
        margin-left: auto !important;
        margin-right: auto !important;
    }
    
    .counselor-title {
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 8px;
        font-size: 14px;
    }
    
    .counselor-stats {
        display: flex;
        justify-content: space-between;
        margin-top: 15px;
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        padding: 10px;
    }
    
    .stat-item {
        text-align: center;
        flex: 1;
    }
    
    .stat-value {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 2px;
    }
    
    .stat-label {
        font-size: 12px;
        opacity: 0.9;
    }
    
    .counselor-price {
        font-size: 18px;
        font-weight: bold;
        color: #fff;
        background-color: rgba(255, 255, 255, 0.2);
        padding: 4px 12px;
        border-radius: 20px;
        display: inline-block;
    }
    
    .consultation-types {
        display: flex;
        gap: 10px;
        margin-top: 15px;
    }
    
    .type-badge {
        background-color: rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        padding: 5px 15px;
        font-size: 12px;
    }
    
    /* 简介部分 */
    .counselor-sections {
        background-color: #fff;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
        margin-bottom: 25px;
    }
    
    .section {
        margin-bottom: 25px;
    }
    
    .section:last-child {
        margin-bottom: 0;
    }
    
    .section-title {
        font-size: 18px;
        font-weight: 600;
        color: #333;
        margin-bottom: 12px;
        padding-bottom: 8px;
        border-bottom: 2px solid #f0f0f0;
        position: relative;
    }
    
    .section-title:after {
        content: '';
        position: absolute;
        left: 0;
        bottom: -2px;
        width: 50px;
        height: 2px;
        background-color: #4e9cff;
    }
    
    .section-content {
        line-height: 1.7;
        color: #555;
        font-size: 15px;
        padding: 0 5px;
    }
    
    /* 预约部分 */
    .appointment-section {
        background-color: #fff;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    }
    
    .appointment-title {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 20px;
        color: #333;
        text-align: center;
    }
    
    .schedule-section {
        margin: 20px 0;
    }
    
    .date-tabs {
        display: flex;
        overflow-x: auto;
        padding-bottom: 5px;
        margin-bottom: 20px;
        scrollbar-width: thin;
        scrollbar-color: #ddd #f5f5f5;
    }
    
    .date-tabs::-webkit-scrollbar {
        height: 6px;
    }
    
    .date-tabs::-webkit-scrollbar-track {
        background: #f5f5f5;
        border-radius: 10px;
    }
    
    .date-tabs::-webkit-scrollbar-thumb {
        background-color: #ddd;
        border-radius: 10px;
    }
    
    .date-tab {
        padding: 10px 15px;
        background-color: #f5f5f5;
        border-radius: 8px;
        margin-right: 10px;
        min-width: 70px;
        text-align: center;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.2s ease;
        flex-shrink: 0;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }
    
    .date-tab:hover {
        background-color: #e9ecef;
    }
    
    .date-tab.active {
        background-color: #4e9cff;
        color: white;
        box-shadow: 0 2px 5px rgba(78, 156, 255, 0.3);
    }
    
    .time-slots {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 15px;
        margin-bottom: 25px;
    }
    
    .time-slot {
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 10px;
        text-align: center;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.2s ease;
        box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    }
    
    .time-slot.available {
        border-color: #4e9cff;
        color: #4e9cff;
    }
    
    .time-slot.available:hover {
        background-color: #f0f7ff;
    }
    
    .time-slot.unavailable {
        background-color: #f5f5f5;
        color: #aaa;
        cursor: not-allowed;
        opacity: 0.7;
    }
    
    .time-slot.selected {
        background-color: #4e9cff;
        color: white;
        border-color: #4e9cff;
        box-shadow: 0 2px 5px rgba(78, 156, 255, 0.3);
        transform: translateY(-2px);
    }
    
    .consultation-type-selector {
        margin: 25px 0;
    }
    
    .type-options {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
        margin-top: 15px;
    }
    
    .type-option {
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 12px 20px;
        display: flex;
        align-items: center;
        cursor: pointer;
        transition: all 0.2s ease;
        flex-grow: 1;
        min-width: 120px;
        background-color: #fff;
        box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    }
    
    .type-option:hover:not(.disabled) {
        background-color: #f9f9f9;
    }
    
    .type-option.selected {
        border-color: #4e9cff;
        background-color: #f0f7ff;
        box-shadow: 0 2px 5px rgba(78, 156, 255, 0.2);
    }
    
    .type-option.disabled {
        background-color: #f5f5f5;
        color: #aaa;
        cursor: not-allowed;
        opacity: 0.7;
    }
    
    .issue-description {
        margin: 25px 0;
    }
    
    .issue-description textarea {
        width: 100%;
        padding: 15px;
        border-radius: 8px;
        border: 1px solid #e0e0e0;
        font-size: 15px;
        resize: vertical;
        min-height: 120px;
        transition: border-color 0.2s ease;
        box-shadow: inset 0 1px 3px rgba(0,0,0,0.05);
    }
    
    .issue-description textarea:focus {
        border-color: #4e9cff;
        outline: none;
        box-shadow: inset 0 1px 3px rgba(78, 156, 255, 0.1);
    }
    
    .submit-btn {
        background-color: #4e9cff;
        color: white;
        border: none;
        padding: 14px;
        border-radius: 8px;
        font-size: 16px;
        width: 100%;
        margin-top: 25px;
        cursor: pointer;
        font-weight: 600;
        box-shadow: 0 3px 6px rgba(78, 156, 255, 0.25);
        transition: all 0.2s ease;
    }
    
    .submit-btn:hover:not(:disabled) {
        background-color: #3d8df0;
        box-shadow: 0 4px 8px rgba(78, 156, 255, 0.3);
    }
    
    .submit-btn:disabled {
        background-color: #c4c4c4;
        box-shadow: none;
        cursor: not-allowed;
    }
    
    .error-message {
        color: #f44336;
        font-size: 14px;
        margin-top: 8px;
        padding-left: 5px;
    }
    
    /* 日期显示格式 */
    .date-day {
        font-weight: bold;
        font-size: 16px;
    }
    
    .date-weekday {
        font-size: 12px;
        margin-top: 3px;
    }
    
    /* 没有可用时间段提示 */
    .no-slots {
        padding: 30px;
        text-align: center;
        color: #777;
        background-color: #f9f9f9;
        border-radius: 8px;
        font-size: 15px;
        margin: 10px 0;
    }
    
    /* 响应式调整 */
    @media (max-width: 768px) {
        .counselor-header {
            flex-direction: column;
            align-items: center;
            text-align: center;
        }
        
        .counselor-avatar {
            margin-right: 0;
            margin-bottom: 15px;
        }
        
        .counselor-stats {
            flex-wrap: wrap;
        }
        
        .type-options {
            flex-direction: column;
        }
    }
</style>
@endsection

@section('content')
<div class="page-header" style="display: flex; align-items: center; background: #4e9cff; color: white; box-shadow: 0 2px 5px rgba(0,0,0,0.1); border-radius: 0 0 10px 10px; padding: 12px 15px; margin-bottom: 15px;">
    <a href="{{ route('consultation.counselors') }}" class="back-button" style="display: flex; align-items: center; justify-content: center; width: 30px; height: 30px;">
        <div class="back-icon" style="border-top: 2px solid white; border-left: 2px solid white; width: 12px; height: 12px; transform: rotate(-45deg);"></div>
    </a>
    <h1 style="font-size: 18px; font-weight: 500; margin: 0 auto; text-align: center; flex-grow: 1; margin-left: -30px; text-shadow: 0 1px 2px rgba(0,0,0,0.1);">咨询师详情</h1>
    <div style="width: 30px;"></div>
</div>

<div class="container">
    @if(session('success'))
    <div class="alert alert-success" style="background-color: #e7f3ee; color: #1a7348; border-left: 4px solid #28a745; border-radius: 8px; padding: 12px 15px; margin-bottom: 15px; font-size: 15px; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
        <i class="fas fa-check-circle" style="margin-right: 8px;"></i> {{ session('success') }}
    </div>
    @endif

    @if(session('error'))
    <div class="alert alert-danger" style="background-color: #f8eaed; color: #a12a43; border-left: 4px solid #dc3545; border-radius: 8px; padding: 12px 15px; margin-bottom: 15px; font-size: 15px; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
        <i class="fas fa-exclamation-circle" style="margin-right: 8px;"></i> {{ session('error') }}
    </div>
    @endif

    <div class="counselor-detail">
        <!-- 咨询师基本信息卡片 -->
        <div class="counselor-card">
            <div class="counselor-header">
                <div class="counselor-avatar">
                    <img src="{{ $counselor->avatar ? asset('storage/'.$counselor->avatar) : asset('images/default-avatar.jpg') }}" alt="{{ $counselor->name }}">
                </div>
                <div class="counselor-basic-info">
                    <div class="counselor-name" style="text-align: center; width: 100%;">{{ $counselor->name }}</div>
                    <div class="counselor-title">{{ $counselor->title ?: '心理咨询师' }}</div>
                    <div class="counselor-price">
                        {{ $counselor->price > 0 ? '¥'.$counselor->price.'/次' : '免费咨询' }}
                    </div>
                    <div class="consultation-types">
                        @if($counselor->support_text)
                        <span class="type-badge">文字咨询</span>
                        @endif
                        @if($counselor->support_voice)
                        <span class="type-badge">语音咨询</span>
                        @endif
                        @if($counselor->support_video)
                        <span class="type-badge">视频咨询</span>
                        @endif
                    </div>
                </div>
            </div>
            
            <!-- 统计数据 -->
            <div class="counselor-stats">
                <div class="stat-item">
                    <div class="stat-value">{{ isset($completedAppointments) ? $completedAppointments : '0' }}</div>
                    <div class="stat-label">已完成咨询</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">{{ isset($rating) ? $rating : '5.0' }}<small>/5.0</small></div>
                    <div class="stat-label">用户评分</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">{{ $counselor->schedules()->where('is_available', true)->count() }}</div>
                    <div class="stat-label">可预约时段</div>
                </div>
            </div>
        </div>
        
        <!-- 咨询师详细介绍 -->
        <div class="counselor-sections">
            <div class="section">
                <div class="section-title">个人简介</div>
                <div class="section-content">
                    {!! $counselor->introduction ?: '暂无简介' !!}
                </div>
            </div>
            
            <div class="section">
                <div class="section-title">专业领域</div>
                <div class="section-content">
                    {!! $counselor->expertise ?: '暂无专业领域信息' !!}
                </div>
            </div>
            
            <div class="section">
                <div class="section-title">从业经历</div>
                <div class="section-content">
                    {!! $counselor->experience ?: '暂无从业经历信息' !!}
                </div>
            </div>
            
            <div class="section">
                <div class="section-title">教育背景</div>
                <div class="section-content">
                    {!! $counselor->education ?: '暂无教育背景信息' !!}
                </div>
            </div>
        </div>
        
        <!-- 预约区域 -->
        <div class="appointment-section">
            <div class="appointment-title">预约咨询</div>
        
        <form action="{{ route('consultation.create_appointment') }}" method="POST" id="appointmentForm">
            @csrf
            <input type="hidden" name="counselor_id" value="{{ $counselor->id }}">
            <input type="hidden" name="schedule_id" id="scheduleIdInput">
            
            <!-- 日期选择 -->
            <div class="schedule-section">
                <h3 class="section-title">选择日期和时间</h3>
                
                <div class="date-tabs" id="dateTabs">
                    @foreach($dates as $index => $date)
                    <div class="date-tab{{ isset($nextAvailableDate) && $index === $nextAvailableDate ? ' active' : ($index === 0 && !isset($nextAvailableDate) ? ' active' : '') }}" data-date="{{ $date['date'] }}">
                        <div class="date-day">{{ $date['day'] }}</div>
                        <div class="date-weekday">{{ $date['weekday'] }}</div>
                        @if(isset($date['has_available']) && $date['has_available'])
                            <div style="width: 6px; height: 6px; background-color: #4CAF50; border-radius: 50%; margin: 3px auto 0;"></div>
                        @endif
                    </div>
                    @endforeach
                </div>
                
                <!-- 时间段选择 -->
                @foreach($dates as $index => $date)
                <div class="time-slots-container" id="timeSlotsContainer{{ $date['date'] }}" style="{{ isset($nextAvailableDate) && $index === $nextAvailableDate ? '' : ($index === 0 && !isset($nextAvailableDate) ? '' : 'display: none;') }}">
                    @if(count($date['schedules']) > 0)
                    <div class="time-slots">
                        @foreach($date['schedules'] as $schedule)
                        <div class="time-slot {{ $schedule->is_available ? 'available' : 'unavailable' }}" 
                             data-schedule-id="{{ $schedule->id }}" 
                             data-available="{{ $schedule->is_available ? 'true' : 'false' }}">
                            {{ date('H:i', strtotime($schedule->start_time)) }} - {{ date('H:i', strtotime($schedule->end_time)) }}
                        </div>
                        @endforeach
                    </div>
                    @else
                    <div class="no-slots">
                        <i class="fa fa-calendar-times-o" style="margin-right: 8px;"></i>
                        当天暂无可预约时间段
                    </div>
                    @endif
                </div>
                @endforeach
                
                <!--@if(!isset($hasAvailableSlots) || !$hasAvailableSlots)-->
                <!--<div class="no-slots" style="margin-top: 15px;">-->
                <!--    <i class="fa fa-info-circle" style="margin-right: 8px;"></i>-->
                <!--    该咨询师目前没有可预约的时间段，请尝试其他咨询师或稍后再来查看-->
                <!--</div>-->
                <!--@endif-->
            </div>
            
            <!-- 咨询方式选择 -->
            <div class="consultation-type-selector">
                <h3 class="section-title">选择咨询方式</h3>
                <div class="type-options">
                    <div class="type-option {{ $counselor->support_text ? '' : 'disabled' }}" data-type="1">
                        <input type="radio" name="consultation_type" value="1" id="typeText" {{ $counselor->support_text ? '' : 'disabled' }}>
                        <label for="typeText" style="margin-left: 8px;">
                            <i class="fa fa-commenting-o" style="margin-right: 5px;"></i>
                            文字咨询
                        </label>
                    </div>
                    <!--<div class="type-option {{ $counselor->support_voice ? '' : 'disabled' }}" data-type="2">-->
                    <!--    <input type="radio" name="consultation_type" value="2" id="typeVoice" {{ $counselor->support_voice ? '' : 'disabled' }}>-->
                    <!--    <label for="typeVoice" style="margin-left: 8px;">-->
                    <!--        <i class="fa fa-phone" style="margin-right: 5px;"></i>-->
                    <!--        语音咨询-->
                    <!--    </label>-->
                    <!--</div>-->
                    <!--<div class="type-option {{ $counselor->support_video ? '' : 'disabled' }}" data-type="3">-->
                    <!--    <input type="radio" name="consultation_type" value="3" id="typeVideo" {{ $counselor->support_video ? '' : 'disabled' }}>-->
                    <!--    <label for="typeVideo" style="margin-left: 8px;">-->
                    <!--        <i class="fa fa-video-camera" style="margin-right: 5px;"></i>-->
                    <!--        视频咨询-->
                    <!--    </label>-->
                    <!--</div>-->
                </div>
                @error('consultation_type')
                <div class="error-message">{{ $message }}</div>
                @enderror
            </div>
            
            <!-- 问题描述 -->
            <div class="issue-description">
                <h3 class="section-title">问题描述</h3>
                <textarea name="issue_description" rows="5" placeholder="请简要描述您想咨询的问题,不少于10个字，帮助咨询师更好地了解您的需求..." maxlength="500"></textarea>
                <div style="text-align: right; color: #888; font-size: 12px; margin-top: 5px;">
                    <span id="charCount">0</span>/500字
                </div>
                @error('issue_description')
                <div class="error-message">{{ $message }}</div>
                @enderror
            </div>
            
            <!-- 提交按钮 -->
            <button type="submit" class="submit-btn" id="submitBtn" >
                <i class="fa fa-calendar-check-o" style="margin-right: 8px;"></i>
                确认预约
            </button>
        </form>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 日期选项卡切换
        const dateTabs = document.querySelectorAll('.date-tab');
        const timeSlotContainers = document.querySelectorAll('[id^="timeSlotsContainer"]');
        
        dateTabs.forEach(tab => {
            tab.addEventListener('click', function() {
                // 移除所有活跃状态
                dateTabs.forEach(t => t.classList.remove('active'));
                timeSlotContainers.forEach(c => c.style.display = 'none');
                
                // 设置当前选中
                this.classList.add('active');
                const date = this.getAttribute('data-date');
                document.getElementById('timeSlotsContainer' + date).style.display = 'block';
                
                // 清除已选择的时间段
                document.querySelectorAll('.time-slot.selected').forEach(slot => {
                    slot.classList.remove('selected');
                });
                document.getElementById('scheduleIdInput').value = '';
                validateForm();
            });
        });
        
        // 时间段选择
        const timeSlots = document.querySelectorAll('.time-slot');
        timeSlots.forEach(slot => {
            slot.addEventListener('click', function() {
                if (this.getAttribute('data-available') === 'true') {
                    // 清除其他选中
                    document.querySelectorAll('.time-slot.selected').forEach(s => {
                        s.classList.remove('selected');
                    });
                    
                    // 设置当前选中
                    this.classList.add('selected');
                    document.getElementById('scheduleIdInput').value = this.getAttribute('data-schedule-id');
                    validateForm();
                }
            });
        });
        
        // 咨询方式选择
        const typeOptions = document.querySelectorAll('.type-option:not(.disabled)');
        typeOptions.forEach(option => {
            option.addEventListener('click', function() {
                const radio = this.querySelector('input[type="radio"]');
                radio.checked = true;
                
                // 清除其他选中
                document.querySelectorAll('.type-option').forEach(o => {
                    o.classList.remove('selected');
                });
                
                // 设置当前选中
                this.classList.add('selected');
                validateForm();
            });
        });
        
        // 表单验证
        const form = document.getElementById('appointmentForm');
        const submitBtn = document.getElementById('submitBtn');
        const issueDescription = document.querySelector('textarea[name="issue_description"]');
        
        issueDescription.addEventListener('input', function () {
            document.getElementById('charCount').textContent = issueDescription.value.length;
        });

        submitBtn.addEventListener('click', function(e) {
            const scheduleId = document.getElementById('scheduleIdInput').value;
            const consultationType = document.querySelector('input[name="consultation_type"]:checked');
            const description = issueDescription.value.trim();

            let errors = [];

            if (!scheduleId) {
                errors.push('请选择一个可用的时间段');
            }

            if (!consultationType) {
                errors.push('请选择咨询方式');
            }

            if (description.length < 10) {
                errors.push('问题描述不少于10个字');
            }

            if (errors.length > 0) {
                e.preventDefault(); // 阻止表单提交
                alert(errors.join('\n'));
            }
        });
    });
</script>
@endsection
