@extends('layouts.app')

@section('title', '测评结果 - ' . $response->questionnaire->title)

@section('custom-styles')
<link rel="stylesheet" href="{{ asset('css/assessment.css') }}">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
<!-- Markdown 解析库 -->
<script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
<!-- jQuery库 -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<!-- 二维码生成库 -->
<script src="{{ asset('vendor/dcat-admin/dcat/plugins/jquery-qrcode/dist/jquery-qrcode.min.js') }}"></script>
<!-- 截图库 -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>

<style>
/* 模态框样式 */
.modal {
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: #fefefe;
    margin: 0;
    padding: 0;
    border: none;
    border-radius: 12px;
    width: 90%;
    max-width: 400px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    position: fixed;
    bottom: 120px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1001;
    max-height: 60vh;
    overflow-y: auto;
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: #333;
    font-size: 18px;
}

.close {
    color: #aaa;
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    line-height: 1;
}

.close:hover {
    color: #000;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    padding: 20px;
    border-top: 1px solid #eee;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

/* 二维码容器样式 */
#qrcode-container {
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

#qrcode-container .loading {
    color: #666;
    font-size: 14px;
}

#qrcode-container .error {
    color: #dc3545;
    font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .modal-content {
        width: 95%;
        margin: 5% auto;
    }
    
    .result-actions {
        flex-direction: column;
        gap: 10px;
    }
    
    .result-actions .action-btn {
        width: 100%;
    }
}
</style>
@endsection

@section('content')
<div class="result-container">
    <!-- 页面头部 -->
    <div class="assessment-header">
        <div class="header-content">
            <button class="back-btn" onclick="goToAssessmentList()">
                <i class="fas fa-arrow-left"></i>
            </button>
            <h1 class="header-title">测评结果</h1>
            <button class="back-btn" onclick="shareResult()">
                <i class="fas fa-share-alt"></i>
            </button>
        </div>
    </div>

    <!-- 结果卡片 -->
    <div class="result-card">
        <!-- 结果头部 -->
        <div class="result-header">
            <h1 class="result-title">{{ $response->questionnaire->title }}</h1>
            <p class="result-subtitle">测评完成时间：{{ $response->created_at->format('Y-m-d H:i') }}</p>
        </div>

        <!-- 分数展示 -->
        <div class="score-display">
            <div class="score-circle" id="scoreCircle">
                <div class="score-inner">
                    <span class="score-number" id="scoreNumber">0</span>
                    <span class="score-total">/ {{ $maxScore ?? 100 }}</span>
                </div>
            </div>
            <div class="result-level" id="resultLevel">{{ $analysis->level_name ?? '正在分析...' }}</div>
        </div>

        <!-- 结果描述 -->
        @if($analysis && $analysis->description)
        <div class="result-description">
            <p>{{ $analysis->description }}</p>
        </div>
        @endif

        <!-- 建议 -->
        @if($analysis && $analysis->suggestions)
        <div class="suggestions">
            <h3 class="suggestions-title">
                <i class="fas fa-lightbulb" style="color: #667eea; margin-right: 10px;"></i>
                专业建议
            </h3>
            <div class="suggestions-content">
                {!! nl2br(e($analysis->suggestions)) !!}
            </div>
        </div>
        @endif

        <!-- 详细分析 -->
        @if($analysis && $analysis->detailed_analysis)
        <div class="suggestions">
            <h3 class="suggestions-title">
                <i class="fas fa-chart-line" style="color: #667eea; margin-right: 10px;"></i>
                详细分析
            </h3>
            <div class="suggestions-content">
                {!! nl2br(e($analysis->detailed_analysis)) !!}
            </div>
        </div>
        @endif

        <!-- AI智能分析 -->
        @if($analysis && $analysis->ai_analysis)
        <div class="suggestions ai-analysis-section">
            <h3 class="suggestions-title">
                <i class="fas fa-robot"></i> AI智能分析
            </h3>
            <div class="suggestions-content ai-analysis-content">
                <!-- 加载动画 -->
                <div class="ai-analysis-loading" id="aiAnalysisLoading" style="
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    padding: 40px 20px;
                    background: rgba(102, 126, 234, 0.05);
                    border-radius: 12px;
                    margin-bottom: 15px;
                ">
                    <div style="text-align: center;">
                        <div style="
                            width: 40px;
                            height: 40px;
                            border: 3px solid rgba(102, 126, 234, 0.2);
                            border-top: 3px solid #667eea;
                            border-radius: 50%;
                            animation: spin 1s linear infinite;
                            margin: 0 auto 15px;
                        "></div>
                        <p style="color: #667eea; font-size: 14px; margin: 0;">正在解析AI分析内容...</p>
                        <small style="color: #999; font-size: 12px;">请稍候，内容正在渲染中</small>
                    </div>
                </div>
                
                <div class="ai-analysis-text" id="aiAnalysisText" style="display: none;">
                    <!-- AI 分析内容将通过 JavaScript 渲染 -->
                </div>
                <div class="ai-analysis-footer" id="aiAnalysisFooter" style="display: none;">
                    <small><i class="fas fa-info-circle"></i> 此分析由AI生成，仅供参考</small>
                </div>
            </div>
        </div>
        @elseif($analysis)
        <div class="suggestions ai-loading">
            <h3 class="suggestions-title">
                <i class="fas fa-robot" style="color: #764ba2; margin-right: 10px;"></i>
                AI智能分析
                <span style="font-size: 12px; color: #666; font-weight: normal; margin-left: 10px;">
                    <i class="fas fa-spinner fa-spin"></i> 正在生成中...
                </span>
            </h3>
            <div class="suggestions-content">
                <div class="ai-loading-placeholder">
                    <div class="loading-dots">
                        <span></span>
                        <span></span>
                        <span></span>
                    </div>
                    <p>AI正在分析您的测评结果，请稍候...</p>
                    <small>通常需要10-30秒完成分析</small>
                </div>
            </div>
        </div>
        @endif

        <!-- 操作按钮 -->
        <div class="result-actions">
            <button class="action-btn secondary-btn" onclick="retakeAssessment()">
                <i class="fas fa-redo"></i> 重新测评
            </button>
            <button class="action-btn primary-btn" onclick="viewMyRecords()">
                <i class="fas fa-history"></i> 我的记录
            </button>
            <button class="action-btn secondary-btn" onclick="generateQrCode()">
                <i class="fas fa-qrcode"></i> 生成二维码
            </button>
        </div>

        <!-- 二维码模态框 -->
        <div id="qrModal" class="modal" style="display: none;">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>测评结果截图二维码</h3>
                    <span class="close" onclick="closeQrModal()">&times;</span>
                </div>
                <div class="modal-body">
                    <div id="qrcode-container" style="text-align: center; margin: 20px 0;">
                        <!-- 二维码将在这里生成 -->
                    </div>
                    <p style="text-align: center; color: #666; font-size: 14px;">
                        扫描二维码查看您的测评结果截图
                    </p>
                </div>
                <div class="modal-footer">
                    <button class="action-btn secondary-btn" onclick="downloadQrCode()">
                        <i class="fas fa-download"></i> 下载二维码
                    </button>
                    <button class="action-btn primary-btn" onclick="closeQrModal()">
                        关闭
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 相关推荐 -->
    @if($relatedQuestionnaires && $relatedQuestionnaires->count() > 0)
    <div class="questionnaire-list">
        <h2 class="section-title">推荐测评</h2>
        @foreach($relatedQuestionnaires as $related)
            <div class="questionnaire-card">
                <div class="card-header" style="height: 80px;">
                    <div class="card-icon" style="font-size: 30px;">
                        <i class="{{ App\Http\Controllers\AssessmentController::getDomainIcon($related->domain) }}"></i>
                    </div>
                    <div class="domain-badge">{{ App\Http\Controllers\AssessmentController::getDomainName($related->domain) }}</div>
                </div>
                
                <div class="card-content">
                    <h3 class="card-title">{{ $related->title }}</h3>
                    <p class="card-description">{{ $related->description }}</p>
                    
                    <div class="card-meta">
                        <div class="meta-item">
                            <i class="fas fa-question-circle"></i>
                            <span>{{ $related->question_count }} 题</span>
                        </div>
                        <div class="meta-item">
                            <i class="fas fa-clock"></i>
                            <span>约 {{ $related->est_duration }} 分钟</span>
                        </div>
                    </div>
                    
                    <div class="card-footer">
                        <span class="response-count">{{ $related->responses_count ?? 0 }} 人已测评</span>
                        <button class="start-btn" onclick="goToQuestionnaire({{ $related->id }})">
                            开始测评
                        </button>
                    </div>
                </div>
            </div>
        @endforeach
    </div>
    @endif
</div>
@endsection

@section('scripts')
<script>
// 结果数据
const resultData = {
    score: {{ $response->total_score ?? 0 }},
    maxScore: {{ $maxScore ?? 100 }},
    level: '{{ $analysis->level_name ?? '' }}',
    questionnaireId: {{ $response->questionnaire_id }},
    responseId: {{ $response->id }}
};

// AI分析轮询相关变量
let aiAnalysisPolling = null;
let pollingAttempts = 0;
const maxPollingAttempts = 60; // 最多轮询60次（5分钟）

// 页面加载动画
document.addEventListener('DOMContentLoaded', function() {
    // 移除之前的AI分析加载遮罩（如果存在）
    const previousLoadingOverlay = document.getElementById('aiAnalysisLoading');
    if (previousLoadingOverlay) {
        previousLoadingOverlay.remove();
    }
    
    animateScore();
    animateCircle();
    
    // 初始化 AI 分析显示
    @if($analysis && $analysis->ai_analysis)
    initializeAiAnalysis(`{!! addslashes($analysis->ai_analysis) !!}`);
    @endif
    
    // 如果AI分析不存在，开始轮询
    @if(!$analysis->ai_analysis)
    startAiAnalysisPolling();
    @endif
});

// 初始化 AI 分析显示
function initializeAiAnalysis(aiAnalysisText) {
    const aiTextElement = document.getElementById('aiAnalysisText');
    const aiLoadingElement = document.getElementById('aiAnalysisLoading');
    const aiFooterElement = document.getElementById('aiAnalysisFooter');
    
    if (aiTextElement) {
        // 模拟解析延迟，让用户看到加载动画
        setTimeout(() => {
            // 解析 Markdown 内容
            if (typeof marked !== 'undefined') {
                aiTextElement.innerHTML = marked.parse(aiAnalysisText);
            } else {
                aiTextElement.innerHTML = aiAnalysisText.replace(/\n/g, '<br>');
            }
            
            // 隐藏加载动画
            if (aiLoadingElement) {
                aiLoadingElement.style.opacity = '0';
                aiLoadingElement.style.transition = 'opacity 0.3s ease-out';
                setTimeout(() => {
                    aiLoadingElement.style.display = 'none';
                }, 300);
            }
            
            // 显示内容和底部信息
            aiTextElement.style.display = 'block';
            aiTextElement.style.opacity = '0';
            aiTextElement.style.transition = 'opacity 0.5s ease-in';
            
            if (aiFooterElement) {
                aiFooterElement.style.display = 'block';
                aiFooterElement.style.opacity = '0';
                aiFooterElement.style.transition = 'opacity 0.5s ease-in';
            }
            
            // 淡入动画
            setTimeout(() => {
                aiTextElement.style.opacity = '1';
                if (aiFooterElement) {
                    aiFooterElement.style.opacity = '1';
                }
            }, 100);
            
        }, 800); // 延迟800ms显示解析结果
    }
}

// 开始AI分析状态轮询
function startAiAnalysisPolling() {
    if (aiAnalysisPolling) {
        clearInterval(aiAnalysisPolling);
    }
    
    pollingAttempts = 0;
    aiAnalysisPolling = setInterval(checkAiAnalysisStatus, 5000); // 每5秒检查一次
}

// 检查AI分析状态
function checkAiAnalysisStatus() {
    pollingAttempts++;
    
    // 超过最大尝试次数，停止轮询
    if (pollingAttempts > maxPollingAttempts) {
        clearInterval(aiAnalysisPolling);
        showAiAnalysisTimeout();
        return;
    }
    
    fetch(`/assessment/ai-analysis-status/${resultData.responseId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.ai_analysis) {
                // AI分析完成，更新页面
                clearInterval(aiAnalysisPolling);
                updateAiAnalysisDisplay(data.ai_analysis);
            }
        })
        .catch(error => {
            console.error('检查AI分析状态失败:', error);
            // 继续轮询，不中断
        });
}

// 更新AI分析显示
function updateAiAnalysisDisplay(aiAnalysis) {
    const aiSection = document.querySelector('.ai-loading');
    if (aiSection) {
        // 更新整个 AI 分析区域的 HTML 结构
        aiSection.className = 'suggestions ai-analysis-section';
        aiSection.innerHTML = `
            <h3 class="suggestions-title">
                <i class="fas fa-robot"></i> AI智能分析
            </h3>
            <div class="suggestions-content ai-analysis-content">
                <!-- 加载动画 -->
                <div class="ai-analysis-loading" id="aiAnalysisLoadingDynamic" style="
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    padding: 40px 20px;
                    background: rgba(102, 126, 234, 0.05);
                    border-radius: 12px;
                    margin-bottom: 15px;
                ">
                    <div style="text-align: center;">
                        <div style="
                            width: 40px;
                            height: 40px;
                            border: 3px solid rgba(102, 126, 234, 0.2);
                            border-top: 3px solid #667eea;
                            border-radius: 50%;
                            animation: spin 1s linear infinite;
                            margin: 0 auto 15px;
                        "></div>
                        <p style="color: #667eea; font-size: 14px; margin: 0;">正在解析AI分析内容...</p>
                        <small style="color: #999; font-size: 12px;">请稍候，内容正在渲染中</small>
                    </div>
                </div>
                
                <div class="ai-analysis-text" id="aiAnalysisTextDynamic" style="display: none;">
                    <!-- 内容将通过 JavaScript 渲染 -->
                </div>
                <div class="ai-analysis-footer" id="aiAnalysisFooterDynamic" style="display: none;">
                    <small><i class="fas fa-info-circle"></i> 此分析由AI生成，仅供参考</small>
                </div>
            </div>
        `;
        
        // 延迟解析和显示内容
        setTimeout(() => {
            const aiTextElement = document.getElementById('aiAnalysisTextDynamic');
            const aiLoadingElement = document.getElementById('aiAnalysisLoadingDynamic');
            const aiFooterElement = document.getElementById('aiAnalysisFooterDynamic');
            
            // 渲染 markdown 内容
            if (aiTextElement) {
                if (typeof marked !== 'undefined') {
                    aiTextElement.innerHTML = marked.parse(aiAnalysis);
                } else {
                    aiTextElement.innerHTML = aiAnalysis.replace(/\n/g, '<br>');
                }
                
                // 隐藏加载动画
                if (aiLoadingElement) {
                    aiLoadingElement.style.opacity = '0';
                    aiLoadingElement.style.transition = 'opacity 0.3s ease-out';
                    setTimeout(() => {
                        aiLoadingElement.style.display = 'none';
                    }, 300);
                }
                
                // 显示内容
                aiTextElement.style.display = 'block';
                aiTextElement.style.opacity = '0';
                aiTextElement.style.transition = 'opacity 0.5s ease-in';
                
                if (aiFooterElement) {
                    aiFooterElement.style.display = 'block';
                    aiFooterElement.style.opacity = '0';
                    aiFooterElement.style.transition = 'opacity 0.5s ease-in';
                }
                
                // 淡入动画
                setTimeout(() => {
                    aiTextElement.style.opacity = '1';
                    if (aiFooterElement) {
                        aiFooterElement.style.opacity = '1';
                    }
                }, 100);
            }
        }, 800); // 延迟800ms显示解析结果
        
        // 添加整体淡入动画
        aiSection.style.opacity = '0';
        setTimeout(() => {
            aiSection.style.transition = 'opacity 0.5s ease-in';
            aiSection.style.opacity = '1';
        }, 100);
    }
}

// 显示AI分析超时提示
function showAiAnalysisTimeout() {
    const aiSection = document.querySelector('.ai-analysis-section');
    if (aiSection) {
        const title = aiSection.querySelector('h3');
        title.innerHTML = '<i class="fas fa-exclamation-triangle"></i> AI分析';
        
        const content = aiSection.querySelector('.suggestions-content');
        content.innerHTML = `
            <div class="ai-analysis-content">
                <div class="ai-analysis-text">
                    <p style="color: #666; text-align: center;">
                        <i class="fas fa-clock"></i> AI分析生成时间较长，请稍后刷新页面查看
                    </p>
                    <div style="text-align: center; margin-top: 15px;">
                        <button class="action-btn secondary-btn" onclick="location.reload()">
                            <i class="fas fa-refresh"></i> 刷新页面
                        </button>
                    </div>
                </div>
            </div>
        `;
    }
}

// 分数动画
function animateScore() {
    const scoreElement = document.getElementById('scoreNumber');
    const targetScore = resultData.score;
    let currentScore = 0;
    const increment = targetScore / 50;
    
    const timer = setInterval(() => {
        currentScore += increment;
        if (currentScore >= targetScore) {
            currentScore = targetScore;
            clearInterval(timer);
        }
        scoreElement.textContent = Math.floor(currentScore);
    }, 30);
}

// 圆形进度条动画
function animateCircle() {
    const circle = document.getElementById('scoreCircle');
    const percentage = (resultData.score / resultData.maxScore) * 100;
    
    // 根据分数设置颜色
    let color1, color2;
    if (percentage >= 80) {
        color1 = '#28a745';
        color2 = '#20c997';
    } else if (percentage >= 60) {
        color1 = '#ffc107';
        color2 = '#fd7e14';
    } else {
        color1 = '#dc3545';
        color2 = '#e83e8c';
    }
    
    setTimeout(() => {
        const angle = (percentage / 100) * 360;
        circle.style.background = `conic-gradient(${color1} 0deg, ${color2} ${angle}deg, #f0f0f0 ${angle}deg)`;
    }, 500);
}

// 重新测评
function retakeAssessment() {
    if (confirm('确定要重新进行测评吗？')) {
        window.location.href = `/assessment/${resultData.questionnaireId}/start`;
    }
}

// 查看我的记录
function viewMyRecords() {
    window.location.href = '/assessment/my-records';
}

// 返回测评列表
function goToAssessmentList() {
    window.location.href = '/assessment';
}

// 跳转到其他问卷
function goToQuestionnaire(id) {
    window.location.href = `/assessment/${id}`;
}

// 分享结果
function shareResult() {
    if (navigator.share) {
        navigator.share({
            title: '我的心理测评结果',
            text: `我在"{{ $response->questionnaire->title }}"测评中获得了 ${resultData.score} 分，等级：${resultData.level}`,
            url: window.location.href
        });
    } else {
        // 复制链接到剪贴板
        navigator.clipboard.writeText(window.location.href).then(() => {
            alert('结果链接已复制到剪贴板');
        });
    }
}

// 添加触摸反馈
document.querySelectorAll('.action-btn, .start-btn').forEach(btn => {
    btn.addEventListener('touchstart', function() {
        this.style.transform = 'scale(0.95)';
    });
    
    btn.addEventListener('touchend', function() {
        this.style.transform = '';
    });
});

// 结果保存提示
setTimeout(() => {
    if (!localStorage.getItem('result_saved_tip')) {
        const tip = document.createElement('div');
        tip.className = 'alert alert-success';
        tip.innerHTML = '<i class="fas fa-info-circle"></i> 您的测评结果已自动保存，可在"我的记录"中查看';
        tip.style.position = 'fixed';
        tip.style.top = '20px';
        tip.style.left = '20px';
        tip.style.right = '20px';
        tip.style.zIndex = '1000';
        document.body.appendChild(tip);
        
        setTimeout(() => {
            tip.remove();
        }, 3000);
        
        localStorage.setItem('result_saved_tip', 'shown');
    }
}, 2000);

// 生成二维码（保存截图链接的二维码）
function generateQrCode() {
    const button = event.target;
    const originalText = button.innerHTML;
    
    // 显示加载状态
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 生成中...';
    button.disabled = true;
    
    // 先截图，然后生成二维码
    takeScreenshotForQr().then(screenshotUrl => {
        if (screenshotUrl) {
            // 显示二维码模态框
            const modal = document.getElementById('qrModal');
            const container = document.getElementById('qrcode-container');
            
            container.innerHTML = '<div class="loading">正在生成二维码...</div>';
            modal.style.display = 'block';
            
            // 使用在线二维码API生成截图链接的二维码
            // const qrUrl = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(screenshotUrl)}`;
            // const qrUrl = `https://my.tv.sohu.com/user/a/wvideo/getQRCode.do?text=${encodeURIComponent(screenshotUrl)}&width=200&height=200`;
            const qrUrl = `https://api.2dcode.biz/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(screenshotUrl)}`;
            
            const img = document.createElement('img');
            img.src = qrUrl;
            img.alt = '截图链接二维码';
            img.style.width = '200px';
            img.style.height = '200px';
            img.style.border = '1px solid #ddd';
            img.style.borderRadius = '8px';
            
            img.onload = function() {
                container.innerHTML = '';
                container.appendChild(img);
            };
            
            img.onerror = function() {
                container.innerHTML = '<div class="error">生成二维码失败</div>';
            };
        } else {
            showToast('截图失败，无法生成二维码', 'error');
        }
    }).catch(error => {
        console.error('生成二维码失败:', error);
        showToast('生成二维码失败', 'error');
    }).finally(() => {
        // 恢复按钮状态
        button.innerHTML = originalText;
        button.disabled = false;
    });
}

// 为二维码截图（只截取结果内容部分）
function takeScreenshotForQr() {
    return new Promise((resolve, reject) => {
        const originalCard = document.querySelector('.result-card');
        if (!originalCard) {
            reject('找不到截图目标元素');
            return;
        }
        
        // 创建完全独立的截图容器
        const screenshotWrapper = document.createElement('div');
        screenshotWrapper.style.cssText = `
            position: fixed;
            top: -10000px;
            left: -10000px;
            width: 500px;
            background: transparent;
            z-index: -999;
        `;
        
        // 手动创建纯净的卡片，不使用任何CSS类
        const cleanCard = document.createElement('div');
        cleanCard.style.cssText = `
            background: #ffffff;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            width: 100%;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        `;
        
        // 手动提取并重建内容，避免CSS类冲突
        
        // 1. 结果头部
        const originalHeader = originalCard.querySelector('.result-header');
        if (originalHeader) {
            const header = document.createElement('div');
            header.style.cssText = `
                background: linear-gradient(45deg, #667eea, #764ba2);
                color: white;
                padding: 30px 25px;
                text-align: center;
            `;
            
            const title = document.createElement('h1');
            title.style.cssText = `
                font-size: 24px;
                font-weight: 700;
                margin: 0 0 10px 0;
                color: white;
            `;
            title.textContent = originalHeader.querySelector('.result-title')?.textContent || '';
            
            const subtitle = document.createElement('p');
            subtitle.style.cssText = `
                font-size: 16px;
                opacity: 0.9;
                margin: 0;
                color: white;
            `;
            subtitle.textContent = originalHeader.querySelector('.result-subtitle')?.textContent || '';
            
            header.appendChild(title);
            header.appendChild(subtitle);
            cleanCard.appendChild(header);
        }
        
        // 2. 分数展示区域
        const originalScore = originalCard.querySelector('.score-display');
        if (originalScore) {
            const scoreDiv = document.createElement('div');
            scoreDiv.style.cssText = `
                padding: 30px 25px;
                text-align: center;
                border-bottom: 1px solid #eee;
                background: #ffffff;
            `;
            
            // 创建圆环图表容器
            const scoreCircleContainer = document.createElement('div');
            scoreCircleContainer.style.cssText = `
                width: 120px;
                height: 120px;
                margin: 0 auto 20px;
                position: relative;
                display: flex;
                align-items: center;
                justify-content: center;
            `;
            
            // 计算百分比和颜色
            const percentage = (resultData.score / resultData.maxScore) * 100;
            let color1, color2;
            if (percentage >= 80) {
                color1 = '#28a745';
                color2 = '#20c997';
            } else if (percentage >= 60) {
                color1 = '#ffc107';
                color2 = '#fd7e14';
            } else {
                color1 = '#dc3545';
                color2 = '#e83e8c';
            }
            
            // 创建SVG圆环
            const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
            svg.setAttribute('width', '120');
            svg.setAttribute('height', '120');
            svg.setAttribute('viewBox', '0 0 120 120');
            svg.style.cssText = `
                position: absolute;
                top: 0;
                left: 0;
                transform: rotate(-90deg);
            `;
            
            // 背景圆环
            const bgCircle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
            bgCircle.setAttribute('cx', '60');
            bgCircle.setAttribute('cy', '60');
            bgCircle.setAttribute('r', '50');
            bgCircle.setAttribute('fill', 'none');
            bgCircle.setAttribute('stroke', '#f0f0f0');
            bgCircle.setAttribute('stroke-width', '10');
            
            // 进度圆环
            const progressCircle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
            const circumference = 2 * Math.PI * 50;
            const strokeDasharray = circumference;
            const strokeDashoffset = circumference - (percentage / 100) * circumference;
            
            progressCircle.setAttribute('cx', '60');
            progressCircle.setAttribute('cy', '60');
            progressCircle.setAttribute('r', '50');
            progressCircle.setAttribute('fill', 'none');
            progressCircle.setAttribute('stroke', color1);
            progressCircle.setAttribute('stroke-width', '10');
            progressCircle.setAttribute('stroke-linecap', 'round');
            progressCircle.setAttribute('stroke-dasharray', strokeDasharray);
            progressCircle.setAttribute('stroke-dashoffset', strokeDashoffset);
            
            // 创建渐变
            const defs = document.createElementNS('http://www.w3.org/2000/svg', 'defs');
            const gradient = document.createElementNS('http://www.w3.org/2000/svg', 'linearGradient');
            gradient.setAttribute('id', 'circleGradient');
            gradient.setAttribute('x1', '0%');
            gradient.setAttribute('y1', '0%');
            gradient.setAttribute('x2', '100%');
            gradient.setAttribute('y2', '100%');
            
            const stop1 = document.createElementNS('http://www.w3.org/2000/svg', 'stop');
            stop1.setAttribute('offset', '0%');
            stop1.setAttribute('stop-color', color1);
            
            const stop2 = document.createElementNS('http://www.w3.org/2000/svg', 'stop');
            stop2.setAttribute('offset', '100%');
            stop2.setAttribute('stop-color', color2);
            
            gradient.appendChild(stop1);
            gradient.appendChild(stop2);
            defs.appendChild(gradient);
            
            progressCircle.setAttribute('stroke', 'url(#circleGradient)');
            
            svg.appendChild(defs);
            svg.appendChild(bgCircle);
            svg.appendChild(progressCircle);
            
            // 中心内容
            const scoreInner = document.createElement('div');
            scoreInner.style.cssText = `
                width: 90px;
                height: 90px;
                background: white;
                border-radius: 50%;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                position: relative;
                z-index: 1;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            `;
            
            const scoreNumber = document.createElement('span');
            scoreNumber.style.cssText = `
                font-size: 24px;
                font-weight: 700;
                color: #333;
                line-height: 1;
            `;
            scoreNumber.textContent = resultData.score;
            
            const scoreTotal = document.createElement('span');
            scoreTotal.style.cssText = `
                font-size: 12px;
                color: #666;
                margin-top: 2px;
            `;
            scoreTotal.textContent = `/ ${resultData.maxScore}`;
            
            scoreInner.appendChild(scoreNumber);
            scoreInner.appendChild(scoreTotal);
            
            scoreCircleContainer.appendChild(svg);
            scoreCircleContainer.appendChild(scoreInner);
            
            const resultLevel = document.createElement('div');
            resultLevel.style.cssText = `
                font-size: 18px;
                font-weight: 600;
                color: #667eea;
                margin-bottom: 10px;
            `;
            resultLevel.textContent = resultData.level;
            
            scoreDiv.appendChild(scoreCircleContainer);
            scoreDiv.appendChild(resultLevel);
            cleanCard.appendChild(scoreDiv);
        }
        
        // 3. 结果描述
        const originalDesc = originalCard.querySelector('.result-description');
        if (originalDesc) {
            const descDiv = document.createElement('div');
            descDiv.style.cssText = `
                padding: 25px;
                line-height: 1.6;
                color: #666;
                background: #ffffff;
            `;
            descDiv.innerHTML = originalDesc.innerHTML;
            cleanCard.appendChild(descDiv);
        }
        
        // 4. 建议区域
        const originalSuggestions = originalCard.querySelectorAll('.suggestions');
        originalSuggestions.forEach(originalSugg => {
            // 跳过按钮区域
            if (originalSugg.closest('.result-actions')) return;
            
            const suggDiv = document.createElement('div');
            suggDiv.style.cssText = `
                padding: 25px;
                background: #f8f9fa;
                line-height: 1.6;
            `;
            
            const title = originalSugg.querySelector('.suggestions-title');
            if (title) {
                const titleDiv = document.createElement('h3');
                titleDiv.style.cssText = `
                    font-size: 18px;
                    font-weight: 600;
                    color: #333;
                    margin: 0 0 15px 0;
                `;
                titleDiv.innerHTML = title.innerHTML;
                suggDiv.appendChild(titleDiv);
            }
            
            const content = originalSugg.querySelector('.suggestions-content');
            if (content) {
                const contentDiv = document.createElement('div');
                contentDiv.style.cssText = `
                    line-height: 1.6;
                    color: #666;
                `;
                contentDiv.innerHTML = content.innerHTML;
                suggDiv.appendChild(contentDiv);
            }
            
            cleanCard.appendChild(suggDiv);
        });
        
        screenshotWrapper.appendChild(cleanCard);
        document.body.appendChild(screenshotWrapper);
        
        // 等待渲染完成
        setTimeout(() => {
            html2canvas(cleanCard, {
                backgroundColor: '#ffffff',
                scale: 3,
                useCORS: true,
                allowTaint: false,
                logging: false,
                width: cleanCard.offsetWidth,
                height: cleanCard.offsetHeight,
                scrollX: 0,
                scrollY: 0,
                imageTimeout: 0,
                removeContainer: false
            }).then(canvas => {
                // 清理临时元素
                document.body.removeChild(screenshotWrapper);
                
                // 转换为PNG
                const imageData = canvas.toDataURL('image/png', 1.0);
                
                // 发送到服务器保存
                fetch(`/assessment/screenshot/${resultData.responseId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '{{ csrf_token() }}',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        screenshot: imageData
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        resolve(data.url);
                    } else {
                        reject('保存截图失败: ' + data.message);
                    }
                })
                .catch(error => {
                    reject('保存截图失败: ' + error.message);
                });
            }).catch(error => {
                // 清理临时元素
                if (document.body.contains(screenshotWrapper)) {
                    document.body.removeChild(screenshotWrapper);
                }
                reject('截图失败: ' + error.message);
            });
        }, 1000); // 等待1秒确保渲染完成
    });
}

// 关闭二维码模态框
function closeQrModal() {
    document.getElementById('qrModal').style.display = 'none';
}

// 下载二维码
function downloadQrCode() {
    const img = document.querySelector('#qrcode-container img');
    const canvas = document.querySelector('#qrcode-container canvas');
    
    if (img) {
        // 处理img元素（在线API生成的二维码）
        // 直接使用图片URL下载
        const link = document.createElement('a');
        link.download = `测评结果二维码_${resultData.responseId}.png`;
        link.href = img.src;
        link.target = '_blank';
        link.click();
    } else if (canvas) {
        // 处理canvas元素（jQuery插件生成的二维码）
        const link = document.createElement('a');
        link.download = `测评结果二维码_${resultData.responseId}.png`;
        link.href = canvas.toDataURL();
        link.click();
    } else {
        showToast('无法下载二维码', 'error');
    }
}

// 显示提示消息
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.innerHTML = message;
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#007bff'};
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        z-index: 10000;
        font-size: 14px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        transform: translateX(100%);
        transition: transform 0.3s ease;
    `;
    
    document.body.appendChild(toast);
    
    // 显示动画
    setTimeout(() => {
        toast.style.transform = 'translateX(0)';
    }, 100);
    
    // 自动隐藏
    setTimeout(() => {
        toast.style.transform = 'translateX(100%)';
        setTimeout(() => {
            toast.remove();
        }, 300);
    }, 3000);
}
</script>
@endsection
