<?php

namespace App\Admin\Controllers;

use Dcat\Admin\Http\Controllers\AdminController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class UploadController extends AdminController
{
    /**
     * 处理视频上传
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function video(Request $request)
    {
        // 验证请求 - 移除硬编码的大小限制，使用PHP配置的限制
        $request->validate([
            'file' => 'required|file', // 移除max:102400限制
        ]);
        
        // 检查文件扩展名
        $file = $request->file('file');
        $extension = strtolower($file->getClientOriginalExtension());
        $allowedExtensions = ['mp4', 'mov', 'ogg', 'webm', 'avi', 'mkv', 'flv'];
        
        if (!in_array($extension, $allowedExtensions)) {
            return response()->json([
                'status' => false,
                'message' => '不支持的文件格式，请上传 mp4, mov, ogg, webm, avi, mkv 或 flv 格式的视频'
            ], 400);
        }

        // 检查是否有文件上传，支持file和fileas两种字段名
        $fieldName = 'file';
        if ($request->hasFile('fileas')) {
            $fieldName = 'fileas';
        } elseif (!$request->hasFile('file')) {
            return response()->json(['status' => false, 'message' => '没有上传文件']);
        }

        $file = $request->file($fieldName);
        
        // 生成唯一文件名
        $fileName = Str::random(40) . '.' . $file->getClientOriginalExtension();
        
        // 存储视频文件
        $path = $file->storeAs('videos/content', $fileName, 'public');
        
        if (!$path) {
            return response()->json(['status' => false, 'message' => '上传失败']);
        }
        
        // 返回视频URL
        $url = Storage::disk('public')->url($path);
        
        // 返回 TinyMCE 期望的响应格式
        return response()->json([
            'location' => $url,  // TinyMCE 需要 location 字段
            'url' => $url,       // 兼容其他组件
            'path' => $path,
            'name' => $fileName,
        ]);
    }
    
    /**
     * 删除上传的视频文件
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function deleteVideo(Request $request)
    {
        $key = $request->input('key');
        
        if (empty($key)) {
            return response()->json(['error' => '文件路径不能为空']);
        }
        
        // 删除文件
        if (Storage::disk('public')->exists($key)) {
            Storage::disk('public')->delete($key);
        }
        
        return response()->json(['success' => true]);
    }
}
