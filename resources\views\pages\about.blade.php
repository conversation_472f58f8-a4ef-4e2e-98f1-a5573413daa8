@extends('layouts.app')

@section('title', '关于我们 - 心理咨询平台')

@section('custom-styles')
<style>
    body {
        background-color: #f8f9fa;
    }
    
    .container {
        max-width: 960px;
        margin: 0 auto;
        padding-bottom: 80px !important; /* 为底部菜单栏预留空间 */
    }
    
    .page-header {
        display: flex;
        align-items: center;
        background: linear-gradient(135deg, #5b7cef 0%, #3c67e3 100%);
        color: white;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        border-radius: 0 0 10px 10px;
        padding: 12px 15px;
        margin-bottom: 15px;
    }
    
    .back-button {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 30px;
        height: 30px;
    }
    
    .back-icon {
        border-top: 2px solid white;
        border-left: 2px solid white;
        width: 12px;
        height: 12px;
        transform: rotate(-45deg);
    }
    
    .page-header h1 {
        font-size: 18px;
        font-weight: 500;
        margin: 0 auto;
        text-align: center;
        flex-grow: 1;
        margin-left: -30px;
        text-shadow: 0 1px 2px rgba(0,0,0,0.1);
    }
    
    .about-card {
        background-color: #fff;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        margin: 15px;
        padding: 20px;
        border: 1px solid rgba(0, 0, 0, 0.03);
    }
    
    .section-title {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 20px;
        color: #333;
        border-bottom: 1px solid #f0f0f0;
        padding-bottom: 10px;
    }
    
    .about-banner {
        margin: -20px -20px 20px -20px;
        height: 180px;
        border-radius: 12px 12px 0 0;
        overflow: hidden;
        position: relative;
    }
    
    .about-banner img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    
    .about-logo {
        text-align: center;
        margin-top: -50px;
        margin-bottom: 20px;
        position: relative;
    }
    
    .about-logo-inner {
        width: 80px;
        height: 80px;
        border-radius: 20px;
        background: linear-gradient(135deg, #5b7cef 0%, #3c67e3 100%);
        display: inline-flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 40px;
        font-weight: bold;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
        border: 3px solid white;
    }
    
    .about-intro {
        text-align: center;
        margin-bottom: 30px;
    }
    
    .about-name {
        font-size: 22px;
        font-weight: 600;
        color: #333;
        margin-bottom: 5px;
    }
    
    .about-slogan {
        font-size: 14px;
        color: #777;
        margin-bottom: 15px;
    }
    
    .about-mission {
        background-color: #f7f9ff;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 25px;
        border-left: 4px solid #5b7cef;
    }
    
    .mission-title {
        font-weight: 600;
        color: #333;
        margin-bottom: 8px;
        font-size: 16px;
    }
    
    .mission-text {
        color: #555;
        line-height: 1.6;
        font-size: 15px;
    }
    
    .about-content {
        color: #444;
        line-height: 1.7;
        font-size: 15px;
        margin-bottom: 20px;
    }
    
    .about-content p {
        margin-bottom: 15px;
    }
    
    .team-section {
        margin-top: 30px;
    }
    
    .team-members {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
        margin-top: 20px;
    }
    
    .team-member {
        flex: 1;
        min-width: 120px;
        text-align: center;
    }
    
    .member-avatar {
        width: 70px;
        height: 70px;
        border-radius: 50%;
        overflow: hidden;
        margin: 0 auto 10px auto;
        border: 2px solid #f0f0f0;
    }
    
    .member-avatar img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    
    .member-name {
        font-weight: 600;
        font-size: 14px;
        color: #333;
        margin-bottom: 2px;
    }
    
    .member-role {
        font-size: 12px;
        color: #777;
    }
    
    .about-footer {
        text-align: center;
        margin-top: 40px;
        padding-top: 20px;
        border-top: 1px solid #f0f0f0;
        font-size: 13px;
        color: #999;
    }
    
    .contact-info {
        margin-top: 20px;
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
    }
    
    .contact-item {
        flex: 1;
        min-width: 150px;
        background-color: #f7f9ff;
        padding: 12px;
        border-radius: 8px;
    }
    
    .contact-label {
        font-size: 13px;
        color: #777;
        margin-bottom: 5px;
    }
    
    .contact-value {
        font-size: 14px;
        color: #333;
        font-weight: 500;
    }
</style>
@endsection

@section('content')
<div class="page-header">
    <a href="{{ route('my') }}" class="back-button">
        <div class="back-icon"></div>
    </a>
    <h1>关于我们</h1>
    <div style="width: 30px;"></div>
</div>

<div class="container">
    <div class="about-card">
        <div class="about-banner">
            <img src="https://images.unsplash.com/photo-1504805572947-34fad45aed93?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2070&q=80" alt="心理咨询平台">
        </div>
        
        <div class="about-logo">
            <div class="about-logo-inner">心</div>
        </div>
        
        <div class="about-intro">
            <div class="about-name">心理咨询平台</div>
            <div class="about-slogan">为您的心理健康保驾护航</div>
        </div>
        
        <div class="about-mission">
            <div class="mission-title">我们的使命</div>
            <div class="mission-text">
                让每个人都能便捷获取专业的心理健康服务，打破心理健康的壁垒，让心理健康成为日常生活的一部分。
            </div>
        </div>
        
        <h2 class="section-title">平台介绍</h2>
        <div class="about-content">
            <p>心理咨询平台成立于2022年，是一家专注于心理健康服务的综合性平台。我们致力于连接用户与专业心理咨询师，通过科技手段降低心理咨询的门槛，让更多人能够获得及时、便捷、专业的心理健康服务。</p>
            
            <p>平台汇聚了来自全国各地的专业心理咨询师，他们拥有专业的资质和丰富的实践经验。我们提供线上咨询、心理科普、心理课程等多种服务，满足用户多样化的心理健康需求。</p>
            
            <p>我们深信，心理健康与身体健康同等重要。在快节奏的现代生活中，我们希望成为您心灵的港湾，陪伴您面对生活中的各种挑战，共同成长。</p>
        </div>
        
        <h2 class="section-title">我们的优势</h2>
        <div class="about-content">
            <p><strong>专业团队</strong>：所有咨询师均具备国家认证的专业资质，持证上岗，并经过严格筛选和定期培训。</p>
            
            <p><strong>便捷服务</strong>：突破时间和空间限制，随时随地获取心理支持，预约灵活，操作简单。</p>
            
            <p><strong>隐私保障</strong>：采用银行级加密技术保护用户数据，咨询内容严格保密，为您提供安全的交流环境。</p>
            
            <p><strong>多元内容</strong>：除一对一咨询外，还提供丰富的心理科普文章、专业课程和AI心理助手，满足不同层次的心理健康需求。</p>
        </div>
        
        <!-- <div class="team-section">
            <h2 class="section-title">核心团队</h2>
            <div class="team-members">
                <div class="team-member">
                    <div class="member-avatar">
                        <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="张博士">
                    </div>
                    <div class="member-name">张博士</div>
                    <div class="member-role">创始人 / 心理学博士</div>
                </div>
                
                <div class="team-member">
                    <div class="member-avatar">
                        <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="李教授">
                    </div>
                    <div class="member-name">李教授</div>
                    <div class="member-role">首席心理顾问</div>
                </div>
                
                <div class="team-member">
                    <div class="member-avatar">
                        <img src="https://randomuser.me/api/portraits/men/22.jpg" alt="王工">
                    </div>
                    <div class="member-name">王工</div>
                    <div class="member-role">技术总监</div>
                </div>
                
                <div class="team-member">
                    <div class="member-avatar">
                        <img src="https://randomuser.me/api/portraits/women/29.jpg" alt="刘女士">
                    </div>
                    <div class="member-name">刘女士</div>
                    <div class="member-role">运营总监</div>
                </div>
            </div>
        </div> -->
        
        
        <div class="about-footer">
            <p>© 2022-2025 心理咨询平台 版权所有</p>
            <p>京ICP备123456789号-1 | 京公网安备11010800000000号</p>
        </div>
    </div>
</div>
@endsection
