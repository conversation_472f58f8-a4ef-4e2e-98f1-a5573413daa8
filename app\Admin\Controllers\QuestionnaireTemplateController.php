<?php

namespace App\Admin\Controllers;

use App\Models\AssessmentQuestionnaire;
use App\Models\AssessmentQuestion;
use App\Models\AssessmentOption;
use App\Models\AssessmentResultConfig;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Widgets\Card;
use Dcat\Admin\Widgets\Alert;
use Dcat\Admin\Layout\Content;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class QuestionnaireTemplateController extends AdminController
{
    protected $title = '问卷模板';

    /**
     * 显示模板列表页面
     */
    public function index(Content $content)
    {
        return $content
            ->title('心理测评问卷模板')
            ->description('选择模板快速创建问卷')
            ->body($this->templates());
    }

    /**
     * 获取预定义模板
     */
    protected function templates()
    {
        $templates = [
            'anxiety' => [
                'title' => '焦虑自评量表(SAS)',
                'description' => '用于评估个体的焦虑水平，包含20个题目',
                'domain' => '焦虑测评',
                'icon' => 'fa-heart-o',
                'color' => 'primary',
                'questions' => [
                    ['content' => '我觉得比平常容易紧张和着急', 'type' => 'scale'],
                    ['content' => '我无缘无故地感到害怕', 'type' => 'scale'],
                    ['content' => '我容易心里烦乱或感到惊恐', 'type' => 'scale'],
                    ['content' => '我觉得我可能将要发疯', 'type' => 'scale'],
                    ['content' => '我觉得一切都很好', 'type' => 'scale'],
                    ['content' => '我手脚发抖打颤', 'type' => 'scale'],
                    ['content' => '我因为头痛、颈痛和背痛而苦恼', 'type' => 'scale'],
                    ['content' => '我感觉容易衰弱和疲乏', 'type' => 'scale'],
                    ['content' => '我觉得心平气和，并且容易安静坐着', 'type' => 'scale'],
                    ['content' => '我觉得心跳得很快', 'type' => 'scale']
                ]
            ],
            'depression' => [
                'title' => '抑郁自评量表(SDS)',
                'description' => '用于评估个体的抑郁程度，包含20个题目',
                'domain' => 'depression',
                'icon' => 'fa-cloud',
                'color' => 'info',
                'questions' => [
                    ['content' => '我感到情绪沮丧，郁闷', 'type' => 'scale'],
                    ['content' => '我感到早晨心情最好', 'type' => 'scale'],
                    ['content' => '我要哭或想哭', 'type' => 'scale'],
                    ['content' => '我夜间睡眠不好', 'type' => 'scale'],
                    ['content' => '我吃饭象平时一样多', 'type' => 'scale'],
                    ['content' => '我的性功能正常', 'type' => 'scale'],
                    ['content' => '我感到体重减轻', 'type' => 'scale'],
                    ['content' => '我为便秘烦恼', 'type' => 'scale'],
                    ['content' => '我的心跳比平时快', 'type' => 'scale'],
                    ['content' => '我无故感到疲劳', 'type' => 'scale']
                ]
            ],
            'personality' => [
                'title' => '大五人格测试',
                'description' => '评估个体在五个主要人格维度上的特征',
                'domain' => 'personality',
                'icon' => 'fa-user',
                'color' => 'success',
                'questions' => [
                    ['content' => '我是一个健谈的人', 'type' => 'scale'],
                    ['content' => '我倾向于挑别人的毛病', 'type' => 'scale'],
                    ['content' => '我做事很彻底', 'type' => 'scale'],
                    ['content' => '我容易沮丧、忧郁', 'type' => 'scale'],
                    ['content' => '我有很多新奇的想法', 'type' => 'scale'],
                    ['content' => '我比较内向、沉默', 'type' => 'scale'],
                    ['content' => '我对人很有同情心', 'type' => 'scale'],
                    ['content' => '我做事有些粗心', 'type' => 'scale'],
                    ['content' => '我很放松，能很好地应对压力', 'type' => 'scale'],
                    ['content' => '我对很多不同的事物都很好奇', 'type' => 'scale']
                ]
            ],
            'stress' => [
                'title' => '压力感知量表',
                'description' => '评估个体对生活压力的感知程度',
                'domain' => 'stress',
                'icon' => 'fa-exclamation-triangle',
                'color' => 'warning',
                'questions' => [
                    ['content' => '在过去的一个月中，您有多经常因为发生了意想不到的事情而感到心烦？', 'type' => 'scale'],
                    ['content' => '在过去的一个月中，您有多经常感到无法控制生活中的重要事情？', 'type' => 'scale'],
                    ['content' => '在过去的一个月中，您有多经常感到神经紧张和有压力？', 'type' => 'scale'],
                    ['content' => '在过去的一个月中，您有多经常成功地处理恼人的生活麻烦？', 'type' => 'scale'],
                    ['content' => '在过去的一个月中，您有多经常感到您有效地应对了生活中发生的重要变化？', 'type' => 'scale']
                ]
            ]
        ];

        $cards = '';
        foreach ($templates as $key => $template) {
            $cards .= $this->templateCard($key, $template);
        }

        return "<div class='row'>{$cards}</div>";
    }

    /**
     * 生成模板卡片
     */
    protected function templateCard($key, $template)
    {
        $questionCount = count($template['questions']);
        $createUrl = admin_url('questionnaire-templates/create-from-template?template='.$key);
        
        // 将domain键值转换为中文显示
        $domainMap = [
            'anxiety' => '焦虑评估',
            'depression' => '抑郁评估',
            'stress' => '压力评估',
            'personality' => '人格测试',
            'emotion' => '情绪评估',
            'social' => '社交能力',
            'cognitive' => '认知能力',
            'other' => '其他'
        ];
        $domainName = $domainMap[$template['domain']] ?? $template['domain'];
        
        return "
        <div class='col-md-6 col-lg-4 mb-4'>
            <div class='card h-100'>
                <div class='card-header bg-{$template['color']} text-white'>
                    <h5 class='card-title mb-0'>
                        <i class='fa {$template['icon']}'></i> {$template['title']}
                    </h5>
                </div>
                <div class='card-body'>
                    <p class='card-text'>{$template['description']}</p>
                    <ul class='list-unstyled'>
                        <li><strong>领域：</strong>{$domainName}</li>
                        <li><strong>题目数：</strong>{$questionCount}题</li>
                        <li><strong>预计时长：</strong>" . max($questionCount, 5) . "分钟</li>
                    </ul>
                </div>
                <div class='card-footer'>
                    <a href='{$createUrl}' class='btn btn-{$template['color']} btn-block'>
                        <i class='fa fa-plus'></i> 使用此模板
                    </a>
                </div>
            </div>
        </div>";
    }

    /**
     * 根据模板创建问卷
     */
    public function createFromTemplate(Request $request)
    {
        $templateKey = $request->get('template');
        $templates = $this->getTemplateData();
        
        if (!isset($templates[$templateKey])) {
            return redirect()->back()->with('error', '模板不存在');
        }

        $template = $templates[$templateKey];
        
        try {
            DB::beginTransaction();
            
            // 创建问卷
            $questionnaire = AssessmentQuestionnaire::create([
                'title' => $template['title'],
                'description' => $template['description'],
                'domain' => $template['domain'],
                'question_count' => count($template['questions']),
                'est_duration' => max(count($template['questions']), 5),
                'is_active' => false // 默认不激活，需要用户确认后激活
            ]);

            // 创建题目和选项
            foreach ($template['questions'] as $index => $questionData) {
                $question = AssessmentQuestion::create([
                    'questionnaire_id' => $questionnaire->id,
                    'type' => $questionData['type'],
                    'content' => $questionData['content'],
                    'sort_order' => $index + 1
                ]);

                // 为量表题创建标准选项
                if ($questionData['type'] === 'scale') {
                    $scaleOptions = [
                        ['content' => '从不', 'score_value' => 1],
                        ['content' => '很少', 'score_value' => 2],
                        ['content' => '有时', 'score_value' => 3],
                        ['content' => '经常', 'score_value' => 4],
                        ['content' => '总是', 'score_value' => 5]
                    ];
                    
                    foreach ($scaleOptions as $optionData) {
                        AssessmentOption::create([
                            'question_id' => $question->id,
                            'content' => $optionData['content'],
                            'score_value' => $optionData['score_value']
                        ]);
                    }
                }
            }

            // 创建结果分析配置
            if (isset($template['result_configs'])) {
                foreach ($template['result_configs'] as $configData) {
                    AssessmentResultConfig::create([
                        'questionnaire_id' => $questionnaire->id,
                        'dimension_name' => $configData['dimension_name'],
                        'min_score' => $configData['min_score'],
                        'max_score' => $configData['max_score'],
                        'level_name' => $configData['level_name'],
                        'description' => $configData['description'],
                        'suggestion' => $configData['suggestion'],
                        'color' => $configData['color'],
                        'sort_order' => $configData['sort_order']
                    ]);
                }
            }

            DB::commit();
            
            admin_success('创建成功', "模板「{$template['title']}」创建成功！您可以进一步编辑和完善。");
            
            return redirect()
                ->to(admin_url('assessment-questionnaires/'.$questionnaire->id.'/edit'));
                
        } catch (\Exception $e) {
            DB::rollBack();
            admin_error('创建失败', '创建失败：' . $e->getMessage());
            return redirect()->back();
        }
    }

    /**
     * 获取模板数据
     */
    protected function getTemplateData()
    {
        return [
            'anxiety' => [
                'title' => '焦虑自评量表(SAS)',
                'description' => '用于评估个体的焦虑水平，包含20个题目',
                'domain' => 'anxiety',
                'questions' => [
                    ['content' => '我觉得比平常容易紧张和着急', 'type' => 'scale'],
                    ['content' => '我无缘无故地感到害怕', 'type' => 'scale'],
                    ['content' => '我容易心里烦乱或感到惊恐', 'type' => 'scale'],
                    ['content' => '我觉得我可能将要发疯', 'type' => 'scale'],
                    ['content' => '我觉得一切都很好', 'type' => 'scale'],
                    ['content' => '我手脚发抖打颤', 'type' => 'scale'],
                    ['content' => '我因为头痛、颈痛和背痛而苦恼', 'type' => 'scale'],
                    ['content' => '我感觉容易衰弱和疲乏', 'type' => 'scale'],
                    ['content' => '我觉得心平气和，并且容易安静坐着', 'type' => 'scale'],
                    ['content' => '我觉得心跳得很快', 'type' => 'scale'],
                    ['content' => '我因为一阵阵头晕而苦恼', 'type' => 'scale'],
                    ['content' => '我有晕倒发作，或觉得要晕倒似的', 'type' => 'scale'],
                    ['content' => '我吸气呼气都感到很容易', 'type' => 'scale'],
                    ['content' => '我的手脚麻木和刺痛', 'type' => 'scale'],
                    ['content' => '我因为胃痛和消化不良而苦恼', 'type' => 'scale'],
                    ['content' => '我常常要小便', 'type' => 'scale'],
                    ['content' => '我的手脚常常是干燥温暖的', 'type' => 'scale'],
                    ['content' => '我脸红发热', 'type' => 'scale'],
                    ['content' => '我容易入睡并且一夜睡得很好', 'type' => 'scale'],
                    ['content' => '我作恶梦', 'type' => 'scale']
                ],
                'result_configs' => [
                    [
                        'dimension_name' => '焦虑水平',
                        'min_score' => 20,
                        'max_score' => 39,
                        'level_name' => '正常',
                        'description' => '您的焦虑水平在正常范围内，心理状态良好。',
                        'suggestion' => '继续保持良好的生活习惯，适当运动，保持积极心态。',
                        'color' => '#28a745',
                        'sort_order' => 1
                    ],
                    [
                        'dimension_name' => '焦虑水平',
                        'min_score' => 40,
                        'max_score' => 59,
                        'level_name' => '轻度焦虑',
                        'description' => '您存在轻度的焦虑情绪，需要适当关注。',
                        'suggestion' => '建议学习放松技巧，如深呼吸、冥想等，必要时寻求专业帮助。',
                        'color' => '#ffc107',
                        'sort_order' => 2
                    ],
                    [
                        'dimension_name' => '焦虑水平',
                        'min_score' => 60,
                        'max_score' => 79,
                        'level_name' => '中度焦虑',
                        'description' => '您的焦虑水平较高，已经影响到日常生活。',
                        'suggestion' => '强烈建议寻求专业心理咨询师的帮助，学习有效的应对策略。',
                        'color' => '#fd7e14',
                        'sort_order' => 3
                    ],
                    [
                        'dimension_name' => '焦虑水平',
                        'min_score' => 80,
                        'max_score' => 100,
                        'level_name' => '重度焦虑',
                        'description' => '您的焦虑水平很高，严重影响生活质量。',
                        'suggestion' => '请立即寻求专业医生或心理治疗师的帮助，可能需要药物治疗配合心理治疗。',
                        'color' => '#dc3545',
                        'sort_order' => 4
                    ]
                ]
            ],
            'depression' => [
                'title' => '抑郁自评量表(SDS)',
                'description' => '用于评估个体的抑郁程度，包含20个题目',
                'domain' => 'depression',
                'questions' => [
                    ['content' => '我感到情绪沮丧，郁闷', 'type' => 'scale'],
                    ['content' => '我感到早晨心情最好', 'type' => 'scale'],
                    ['content' => '我要哭或想哭', 'type' => 'scale'],
                    ['content' => '我夜间睡眠不好', 'type' => 'scale'],
                    ['content' => '我吃饭象平时一样多', 'type' => 'scale'],
                    ['content' => '我的性功能正常', 'type' => 'scale'],
                    ['content' => '我感到体重减轻', 'type' => 'scale'],
                    ['content' => '我为便秘烦恼', 'type' => 'scale'],
                    ['content' => '我的心跳比平时快', 'type' => 'scale'],
                    ['content' => '我无故感到疲劳', 'type' => 'scale'],
                    ['content' => '我的头脑象往常一样清楚', 'type' => 'scale'],
                    ['content' => '我做事情象平时一样不感到困难', 'type' => 'scale'],
                    ['content' => '我坐卧不安，难以保持平静', 'type' => 'scale'],
                    ['content' => '我对未来感到有希望', 'type' => 'scale'],
                    ['content' => '我比平时更容易激怒', 'type' => 'scale'],
                    ['content' => '我觉得决定什么事很容易', 'type' => 'scale'],
                    ['content' => '我感到自己是有用的和不可缺少的人', 'type' => 'scale'],
                    ['content' => '我的生活很有意义', 'type' => 'scale'],
                    ['content' => '假若我死了别人会过得更好', 'type' => 'scale'],
                    ['content' => '我仍旧喜爱那些我过去喜爱的东西', 'type' => 'scale']
                ],
                'result_configs' => [
                    [
                        'dimension_name' => '抑郁水平',
                        'min_score' => 20,
                        'max_score' => 39,
                        'level_name' => '正常',
                        'description' => '您的情绪状态良好，没有明显的抑郁症状。',
                        'suggestion' => '继续保持积极的生活态度，多参与社交活动，保持规律作息。',
                        'color' => '#28a745',
                        'sort_order' => 1
                    ],
                    [
                        'dimension_name' => '抑郁水平',
                        'min_score' => 40,
                        'max_score' => 59,
                        'level_name' => '轻度抑郁',
                        'description' => '您可能存在轻度的抑郁情绪，需要关注。',
                        'suggestion' => '建议增加户外活动，培养兴趣爱好，与朋友家人多交流。',
                        'color' => '#ffc107',
                        'sort_order' => 2
                    ],
                    [
                        'dimension_name' => '抑郁水平',
                        'min_score' => 60,
                        'max_score' => 79,
                        'level_name' => '中度抑郁',
                        'description' => '您的抑郁程度较为严重，建议寻求专业帮助。',
                        'suggestion' => '建议咨询专业心理医生，考虑心理治疗，同时注意生活规律。',
                        'color' => '#fd7e14',
                        'sort_order' => 3
                    ],
                    [
                        'dimension_name' => '抑郁水平',
                        'min_score' => 80,
                        'max_score' => 100,
                        'level_name' => '重度抑郁',
                        'description' => '您的抑郁症状很严重，需要立即寻求专业治疗。',
                        'suggestion' => '请立即联系专业医生，可能需要药物治疗配合心理治疗，切勿延误。',
                        'color' => '#dc3545',
                        'sort_order' => 4
                    ]
                ]
            ],
            'personality' => [
                'title' => '大五人格测试',
                'description' => '评估个体在五个主要人格维度上的特征',
                'domain' => 'personality',
                'questions' => [
                    ['content' => '我是一个健谈的人', 'type' => 'scale'],
                    ['content' => '我倾向于挑别人的毛病', 'type' => 'scale'],
                    ['content' => '我做事很彻底', 'type' => 'scale'],
                    ['content' => '我容易沮丧、忧郁', 'type' => 'scale'],
                    ['content' => '我有很多新奇的想法', 'type' => 'scale'],
                    ['content' => '我比较内向、沉默', 'type' => 'scale'],
                    ['content' => '我对人很有同情心', 'type' => 'scale'],
                    ['content' => '我做事有些粗心', 'type' => 'scale'],
                    ['content' => '我很放松，能很好地应对压力', 'type' => 'scale'],
                    ['content' => '我对很多不同的事物都很好奇', 'type' => 'scale']
                ],
                'result_configs' => [
                    [
                        'dimension_name' => '外向性',
                        'min_score' => 8,
                        'max_score' => 16,
                        'level_name' => '内向型',
                        'description' => '您倾向于内向，喜欢安静的环境和深度思考。',
                        'suggestion' => '发挥内向性格的优势，如专注力强、善于倾听等。',
                        'color' => '#6f42c1',
                        'sort_order' => 1
                    ],
                    [
                        'dimension_name' => '外向性',
                        'min_score' => 17,
                        'max_score' => 32,
                        'level_name' => '中等外向',
                        'description' => '您在内向和外向之间保持平衡。',
                        'suggestion' => '继续保持这种平衡，根据情况灵活调整社交方式。',
                        'color' => '#28a745',
                        'sort_order' => 2
                    ],
                    [
                        'dimension_name' => '外向性',
                        'min_score' => 33,
                        'max_score' => 50,
                        'level_name' => '外向型',
                        'description' => '您是一个外向的人，喜欢社交和刺激。',
                        'suggestion' => '利用您的社交优势，但也要注意给自己留出独处时间。',
                        'color' => '#fd7e14',
                        'sort_order' => 3
                    ]
                ]
            ],
            'stress' => [
                'title' => '压力感知量表',
                'description' => '评估个体对生活压力的感知程度',
                'domain' => 'stress',
                'questions' => [
                    ['content' => '在过去的一个月中，您有多经常因为发生了意想不到的事情而感到心烦？', 'type' => 'scale'],
                    ['content' => '在过去的一个月中，您有多经常感到无法控制生活中的重要事情？', 'type' => 'scale'],
                    ['content' => '在过去的一个月中，您有多经常感到神经紧张和有压力？', 'type' => 'scale'],
                    ['content' => '在过去的一个月中，您有多经常成功地处理恼人的生活麻烦？', 'type' => 'scale'],
                    ['content' => '在过去的一个月中，您有多经常感到您有效地应对了生活中发生的重要变化？', 'type' => 'scale'],
                    ['content' => '在过去的一个月中，您有多经常对处理个人问题的能力感到信心？', 'type' => 'scale'],
                    ['content' => '在过去的一个月中，您有多经常感到事情的发展都如您所愿？', 'type' => 'scale'],
                    ['content' => '在过去的一个月中，您有多经常发现您无法处理所有您必须要做的事情？', 'type' => 'scale'],
                    ['content' => '在过去的一个月中，您有多经常能够控制生活中恼人的事情？', 'type' => 'scale'],
                    ['content' => '在过去的一个月中，您有多经常感到您能够掌控一切？', 'type' => 'scale']
                ],
                'result_configs' => [
                    [
                        'dimension_name' => '压力水平',
                        'min_score' => 10,
                        'max_score' => 19,
                        'level_name' => '低压力',
                        'description' => '您的压力水平较低，能够很好地应对生活挑战。',
                        'suggestion' => '继续保持良好的压力管理能力，可以帮助他人应对压力。',
                        'color' => '#28a745',
                        'sort_order' => 1
                    ],
                    [
                        'dimension_name' => '压力水平',
                        'min_score' => 20,
                        'max_score' => 29,
                        'level_name' => '中等压力',
                        'description' => '您感受到中等程度的压力，这是正常的。',
                        'suggestion' => '学习一些压力管理技巧，如时间管理、放松训练等。',
                        'color' => '#ffc107',
                        'sort_order' => 2
                    ],
                    [
                        'dimension_name' => '压力水平',
                        'min_score' => 30,
                        'max_score' => 39,
                        'level_name' => '高压力',
                        'description' => '您承受着较高的压力，需要采取措施缓解。',
                        'suggestion' => '建议寻求专业帮助，学习有效的压力应对策略。',
                        'color' => '#fd7e14',
                        'sort_order' => 3
                    ],
                    [
                        'dimension_name' => '压力水平',
                        'min_score' => 40,
                        'max_score' => 50,
                        'level_name' => '极高压力',
                        'description' => '您的压力水平非常高，可能影响身心健康。',
                        'suggestion' => '请立即寻求专业心理咨询，考虑调整生活方式和工作安排。',
                        'color' => '#dc3545',
                        'sort_order' => 4
                    ]
                ]
            ]
        ];
    }
} 