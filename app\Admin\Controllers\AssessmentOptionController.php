<?php
namespace App\Admin\Controllers;

use App\Models\AssessmentOption;
use App\Models\AssessmentQuestion;
use Dcat\Admin\Grid;
use Dcat\Admin\Form;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class AssessmentOptionController extends AdminController
{
    protected $title = '心理测评选项';

    protected function grid(): Grid
    {
        return Grid::make(new AssessmentOption(), function (Grid $grid) {
            $grid->column('id', '编号')->sortable();
            $grid->column('question.content', '题目内容');
            $grid->column('content', '选项内容');
            $grid->column('score_value', '分值');
            $grid->column('created_at', '创建时间');
            $grid->column('updated_at', '更新时间');

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id', '编号');
                $filter->equal('question_id', '题目')->select(AssessmentQuestion::pluck('content', 'id'));
                $filter->between('created_at', '创建时间')->datetime();
                $filter->between('updated_at', '更新时间')->datetime();
            });
        });
    }

    protected function form(): Form
    {
        return Form::make(new AssessmentOption(), function (Form $form) {
            $form->display('id', '编号');
            $form->select('question_id', '题目')->options(AssessmentQuestion::pluck('content', 'id'))->required();
            $form->textarea('content', '选项内容')->required();
            $form->number('score_value', '分值')->default(0)->required();
            $form->display('created_at', '创建时间');
            $form->display('updated_at', '更新时间');
        });
    }

    /**
     * 详情视图
     */
    protected function detail($id): Show
    {
        return Show::make($id, new AssessmentOption(), function (Show $show) {
            $show->field('id', '编号');
            $show->field('question.content', '题目内容');
            $show->field('content', '选项内容');
            $show->field('score_value', '分值');
            $show->field('created_at', '创建时间');
            $show->field('updated_at', '更新时间');
        });
    }
}
