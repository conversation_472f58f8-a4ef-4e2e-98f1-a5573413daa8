<?php
// app/Models/SystemSetting.php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;
use Dcat\Admin\Traits\HasDateTimeFormatter;

class SystemSetting extends Model
{
    use HasDateTimeFormatter;
    protected $fillable = ['key', 'value', 'label'];

    /**  
     * 获取配置值（自动 JSON 解析），并做缓存  
     */
    public static function get(string $key, $default = null)
    {
        return Cache::remember("system_setting:{$key}", 60, function () use ($key, $default) {
            $row = static::where('key', $key)->first();
            if (! $row) {
                return $default;
            }
            $value = $row->value;
            // 尝试 JSON 解析
            if (json_decode($value, true) !== null) {
                return json_decode($value, true);
            }
            return $value;
        });
    }

    /**  
     * 设置配置值（自动清缓存）  
     */
    public static function set(string $key, $value): bool
    {
        $payload = is_array($value) ? json_encode($value) : $value;
        $row = static::updateOrCreate(['key' => $key], ['value' => $payload]);
        Cache::forget("system_setting:{$key}");
        return (bool) $row;
    }
}
