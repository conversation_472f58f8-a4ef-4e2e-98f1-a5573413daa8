<?php

namespace App\Admin\Controllers;

use App\Models\Consultation;
use App\Models\ConsultationReply;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Admin;
use Illuminate\Http\Request;

class ConsultationController extends AdminController
{
    protected $title = '留言咨询';
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(Consultation::with(['user', 'latestReply']), function (Grid $grid) {
            $grid->column('id', 'ID')->sortable();
            $grid->column('name', '姓名');
            $grid->column('phone', '电话');
            $grid->column('email', '邮箱');
            $grid->column('subject', '主题')->limit(30);
            $grid->column('content', '内容')->limit(50)->help('点击查看详情');
            $grid->column('status', '状态')->using(Consultation::getStatusOptions())->label([
                'pending' => 'warning',
                'replied' => 'success',
                'closed' => 'default',
            ]);
            $grid->column('user.name', '用户');
            $grid->column('replies_count', '回复数')->display(function () {
                return $this->replies()->count();
            });
            $grid->column('created_at', '提交时间')->sortable();
            $grid->column('replied_at', '最后咨询回复时间')->sortable();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('status', '状态')->select(Consultation::getStatusOptions());
                $filter->like('name', '姓名');
                $filter->like('subject', '主题');
                $filter->between('created_at', '提交时间')->datetime();
            });

            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->append('<a href="'.admin_url('consultations/'.$actions->getKey().'/detail').'" class="btn btn-primary btn-xs">查看详情</a>');
            });

            // $grid->batchActions(function (Grid\Tools\BatchActions $batch) {
            //     $batch->add('批量标记为已回复', new \App\Admin\Actions\Grid\BatchMarkReplied());
            //     $batch->add('批量关闭', new \App\Admin\Actions\Grid\BatchClose());
            // });

            $grid->quickSearch(['name', 'subject', 'content']);
            $grid->enableDialogCreate();
            $grid->disableCreateButton();
            $grid->disableEditButton();
            $grid->disableDeleteButton();
            $grid->disableViewButton();
            $grid->disableBatchDelete();
        });
    }

    /**
     * 留言详情页面
     */
    public function detail($id, Content $content)
    {
        $consultation = Consultation::with(['user', 'replies.user', 'replies.admin'])->findOrFail($id);
        
        return $content
            ->title('留言详情')
            ->description('查看和回复留言')
            ->body(view('admin.consultation.detail', compact('consultation')));
    }

    /**
     * 添加回复
     */
    public function addReply(Request $request, $id)
    {
        $request->validate([
            'content' => 'required|string|max:1000',
            'identity' => 'string'
        ], [
            'content.required' => '请输入回复内容',
            'content.max' => '回复内容不能超过1000字',
        ]);

        $consultation = Consultation::findOrFail($id);

        $reply = ConsultationReply::create([
            'identity' => $request->identity,
            'consultation_id' => $consultation->id,
            'admin_id' => Admin::user()->id,
            'content' => $request->content,
            'is_admin_reply' => true,
        ]);

        // 更新留言状态
        $consultation->update([
            'status' => Consultation::STATUS_REPLIED,
            'replied_at' => now(),
            'replied_by' => Admin::user()->id,
        ]);

        return response()->json([
            'status' => true,
            'message' => '回复成功',
        ]);
    }

    /**
     * 更新留言状态
     */
    public function updateStatus(Request $request, $id)
    {
        $request->validate([
            'status' => 'required|in:pending,replied,closed',
        ]);

        $consultation = Consultation::findOrFail($id);
        $consultation->update([
            'status' => $request->status,
        ]);

        return response()->json([
            'status' => true,
            'message' => '状态更新成功',
        ]);
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail_old($id)
    {
        return Show::make($id, Consultation::with(['user', 'replies']), function (Show $show) {
            $show->field('id', 'ID');
            $show->field('name', '姓名');
            $show->field('phone', '电话');
            $show->field('email', '邮箱');
            $show->field('subject', '主题');
            $show->field('content', '内容')->unescape();
            $show->field('status', '状态')->using(Consultation::getStatusOptions());
            $show->field('user.name', '用户');
            $show->field('created_at', '提交时间');
            $show->field('replied_at', '回复时间');
            
            $show->field('replies', '回复记录')->as(function ($replies) {
                $html = '<div class="replies-list">';
                foreach ($replies as $reply) {
                    $author = $reply->is_admin_reply ? '管理员' : ($reply->user->name ?? '用户');
                    $html .= '<div class="reply-item" style="margin-bottom: 15px; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">';
                    $html .= '<div style="font-weight: bold; color: #666; margin-bottom: 5px;">' . $author . ' · ' . $reply->created_at->format('Y-m-d H:i:s') . '</div>';
                    $html .= '<div>' . nl2br(e($reply->content)) . '</div>';
                    $html .= '</div>';
                }
                $html .= '</div>';
                return $html;
            })->unescape();
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(Consultation::class, function (Form $form) {
            $form->display('id', 'ID');
            $form->text('name', '姓名')->required();
            $form->text('phone', '电话');
            $form->email('email', '邮箱');
            $form->text('subject', '主题')->required();
            $form->textarea('content', '内容')->required();
            $form->select('status', '状态')->options(Consultation::getStatusOptions())->required();
            $form->textarea('admin_reply', '管理员回复');
            
            $form->display('created_at', '创建时间');
            $form->display('updated_at', '更新时间');
        });
    }
} 