@extends('layouts.app')

@section('title', '正念冥想 - 心理减压')

@section('content')
<div class="meditation-container">
    <!-- 页面头部 - 自然融合设计 -->
    <div class="page-header">
        <div class="container">
            <div class="header-content">
                <div class="breadcrumb">
                    <a href="{{ route('stress_relief.index') }}">心理减压</a>
                    <span class="divider">/</span>
                    <span class="current">正念冥想</span>
                </div>
                <h1 class="page-title">正念冥想</h1>
                <p class="page-subtitle">正念内观，清净入耳，宁静入心</p>
            </div>
        </div>
    </div>

    <div class="meditation-programs">
        <div class="container">
            <div class="programs-grid">
                <div class="program-card" onclick="startMeditation('basic')">
                    <div class="program-icon">
                        <i class="fas fa-lotus-position"></i>
                    </div>
                    <h3 class="program-title">基础冥想</h3>
                    <p class="program-desc">初学者友好的基础冥想练习</p>
                    <div class="program-duration">10分钟</div>
                </div>

                <div class="program-card" onclick="startMeditation('body')">
                    <div class="program-icon">
                        <i class="fas fa-hand-paper"></i>
                    </div>
                    <h3 class="program-title">身体扫描</h3>
                    <p class="program-desc">觉察身体各部分的感受</p>
                    <div class="program-duration">15分钟</div>
                </div>

                <div class="program-card" onclick="startMeditation('loving')">
                    <div class="program-icon">
                        <i class="fas fa-heart"></i>
                    </div>
                    <h3 class="program-title">慈悲冥想</h3>
                    <p class="program-desc">培养对自己和他人的慈悲心</p>
                    <div class="program-duration">12分钟</div>
                </div>

                <div class="program-card" onclick="startMeditation('mindful')">
                    <div class="program-icon">
                        <i class="fas fa-eye"></i>
                    </div>
                    <h3 class="program-title">觉察冥想</h3>
                    <p class="program-desc">提升当下觉察力的练习</p>
                    <div class="program-duration">20分钟</div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="meditation-guide" class="meditation-guide" style="display: none;">
    <div class="guide-overlay"></div>
    <div class="guide-content">
        <div class="guide-header">
            <h3 id="meditation-title">正念冥想</h3>
            <button class="close-btn" onclick="closeMeditationGuide()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="guide-body">
            <div class="meditation-visual">
                <div class="mandala">
                    <div class="circle outer"></div>
                    <div class="circle middle"></div>
                    <div class="circle inner"></div>
                    <div class="center-dot"></div>
                </div>
            </div>
            <div class="guide-text">
                <p id="meditation-instruction">找一个舒适的坐姿，轻轻闭上眼睛...</p>
            </div>
            <div class="meditation-controls">
                <button class="control-btn" onclick="toggleMeditation()">
                    <i class="fas fa-play" id="meditation-play-icon"></i>
                    <span id="meditation-btn-text">开始冥想</span>
                </button>
            </div>
            <div class="meditation-timer">
                <span id="meditation-time">00:00</span>
            </div>
        </div>
    </div>
</div>

<style>
.meditation-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #a855f7 0%, #8b5cf6 100%);
    /* 自然处理安全区域 */
    padding-top: calc(env(safe-area-inset-top, 0px) + 20px);
    padding-bottom: calc(env(safe-area-inset-bottom, 0px) + 80px);
}

/* 状态栏和导航栏占位 */
.status-bar-spacer {
    height: env(safe-area-inset-top, 0px);
    background: transparent;
}

.navbar-spacer {
    height: 50px;
    background: transparent;
}

/* 覆盖全局容器样式 */
.meditation-container .container {
    max-width: none;
    width: 100%;
    padding: 0 15px;
    background-color: transparent !important;
    box-sizing: border-box;
}

/* 页面头部 - 自然设计，确保足够空间给卡片悬浮效果 */
.page-header {
    padding: 6rem 0 4rem;
    color: white;
    position: relative;
    background: transparent;
}

/* 装饰性背景 */
.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 100%;
    background: radial-gradient(ellipse at top, rgba(255,255,255,0.1) 0%, transparent 50%);
    pointer-events: none;
}

.header-content {
    position: relative;
    z-index: 1;
    text-align: center;
}

.breadcrumb {
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.breadcrumb a {
    color: rgba(255,255,255,0.8);
    text-decoration: none;
}

.breadcrumb a:hover {
    color: white;
}

.divider {
    margin: 0 0.5rem;
    opacity: 0.6;
}

.current {
    color: white;
}

.page-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 0 2px 10px rgba(0,0,0,0.3);
}

.page-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
}

/* 冥想项目 */
.meditation-programs {
    padding: 3rem 0;
    width: 100%;
    box-sizing: border-box;
}

.programs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
    box-sizing: border-box;
    width: 100%;
}

.program-card {
    background: white;
    border-radius: 20px;
    padding: 2rem 2rem 4rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
    cursor: pointer;
    text-align: center;
    position: relative;
}

.program-card:hover {
    transform: translateY(-5px);
}

.program-icon {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 1.8rem;
    color: white;
}

.program-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.program-desc {
    color: #5a6c7d;
    line-height: 1.6;
    margin-bottom: 2rem;
}

.program-duration {
    position: absolute;
    bottom: 1.5rem;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 15px;
    font-size: 0.9rem;
    font-weight: 500;
}

/* 冥想引导器 */
.meditation-guide {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.guide-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.9);
    backdrop-filter: blur(10px);
}

.guide-content {
    position: relative;
    background: linear-gradient(135deg, #8360c3 0%, #2ebf91 100%);
    border-radius: 20px;
    width: 90%;
    max-width: 500px;
    overflow: hidden;
    color: white;
}

.guide-header {
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.guide-header h3 {
    margin: 0;
    font-size: 1.3rem;
}

.close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.3s ease;
}

.close-btn:hover {
    background: rgba(255,255,255,0.2);
}

.guide-body {
    padding: 2rem;
    text-align: center;
}

.meditation-visual {
    margin-bottom: 2rem;
}

.mandala {
    width: 200px;
    height: 200px;
    margin: 0 auto;
    position: relative;
}

.circle {
    position: absolute;
    border: 2px solid rgba(255,255,255,0.3);
    border-radius: 50%;
    animation: rotate 20s linear infinite;
}

.circle.outer {
    width: 200px;
    height: 200px;
    top: 0;
    left: 0;
}

.circle.middle {
    width: 140px;
    height: 140px;
    top: 30px;
    left: 30px;
    animation-duration: 15s;
    animation-direction: reverse;
}

.circle.inner {
    width: 80px;
    height: 80px;
    top: 60px;
    left: 60px;
    animation-duration: 10s;
}

.center-dot {
    position: absolute;
    top: 90px;
    left: 90px;
    width: 20px;
    height: 20px;
    background: white;
    border-radius: 50%;
    animation: pulse 3s infinite;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

@keyframes pulse {
    0%, 100% { opacity: 1; transform: scale(1); }
    50% { opacity: 0.5; transform: scale(1.2); }
}

.meditation-instruction {
    margin-bottom: 2rem;
}

#meditation-instruction {
    font-size: 1rem;
    line-height: 1.6;
    opacity: 0.9;
}

.meditation-controls {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 2rem;
}

.control-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    border: none;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.control-btn.primary {
    background: rgba(255,255,255,0.2);
    color: white;
}

.control-btn.primary:hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-2px);
}

.control-btn:not(.primary) {
    background: rgba(255,255,255,0.1);
    color: white;
}

.control-btn:not(.primary):hover {
    background: rgba(255,255,255,0.2);
}

.meditation-info {
    display: flex;
    justify-content: center;
    padding-top: 1rem;
    border-top: 1px solid rgba(255,255,255,0.1);
}

.info-item {
    text-align: center;
}

.info-label {
    display: block;
    font-size: 0.9rem;
    opacity: 0.7;
    margin-bottom: 0.3rem;
}

.info-item span:last-child {
    font-size: 1.2rem;
    font-weight: 600;
}

@media (max-width: 768px) {
    .meditation-container {
        padding-top: calc(env(safe-area-inset-top, 0px) + 15px);
        padding-bottom: calc(env(safe-area-inset-bottom, 0px) + 80px);
    }
    
    .programs-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        padding: 0 10px;
    }
    
    .guide-content {
        width: 95%;
        margin: 1rem;
    }
    
    .mandala {
        width: 150px;
        height: 150px;
    }
    
    .circle.outer {
        width: 150px;
        height: 150px;
    }
    
    .circle.middle {
        width: 105px;
        height: 105px;
        top: 22.5px;
        left: 22.5px;
    }
    
    .circle.inner {
        width: 60px;
        height: 60px;
        top: 45px;
        left: 45px;
    }
    
    .center-dot {
        top: 67.5px;
        left: 67.5px;
    }
}

@media (max-width: 500px) {
    .meditation-container {
        padding-top: calc(env(safe-area-inset-top, 0px) + 10px);
        padding-bottom: calc(env(safe-area-inset-bottom, 0px) + 80px);
        width: 100%;
        overflow-x: hidden;
    }
    
    .meditation-container .container {
        max-width: 100% !important;
        width: 100% !important;
        padding: 0 10px !important;
        margin: 0 !important;
        background-color: transparent !important;
        box-sizing: border-box !important;
    }
    
    .page-header {
        padding: 1rem 0 1.5rem;
        width: 100%;
        box-sizing: border-box;
    }
    
    .page-title {
        font-size: 2rem;
    }
    
    .page-subtitle {
        font-size: 1rem;
    }
    
    .meditation-programs {
        padding: 1.5rem 0 2rem;
        width: 100%;
        box-sizing: border-box;
    }
    
    .programs-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
        padding: 0 !important;
        margin: 0 !important;
        width: 100% !important;
        max-width: 100% !important;
        box-sizing: border-box !important;
    }
    
    .program-card {
        padding: 1.5rem 1rem 3rem;
        transition: transform 0.2s ease;
        width: 100%;
        box-sizing: border-box;
    }
    
    .program-card:hover {
        transform: translateY(-3px);
    }
    
    .program-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
        margin-bottom: 1rem;
    }
    
    .program-title {
        font-size: 1rem;
        margin-bottom: 0.8rem;
    }
    
    .program-desc {
        font-size: 0.75rem;
        margin-bottom: 1rem;
        line-height: 1.4;
    }
    
    .program-duration {
        bottom: 1rem;
        font-size: 0.8rem;
        padding: 0.3rem 0.8rem;
    }
}
</style>

<script>
let meditationActive = false;
let meditationTimer;
let meditationStartTime;

const meditationPrograms = {
    basic: {
        title: '基础冥想',
        instruction: '找一个舒适的坐姿，轻轻闭上眼睛。专注于你的呼吸，感受每一次吸气和呼气...'
    },
    body: {
        title: '身体扫描',
        instruction: '舒适地躺下或坐下，开始从头顶到脚趾，逐一感受身体每个部分的感觉...'
    },
    loving: {
        title: '慈悲冥想',
        instruction: '将注意力集中在心脏部位，想象温暖的光芒从心中散发出来，首先祝福自己...'
    },
    mindful: {
        title: '觉察冥想',
        instruction: '保持觉察，观察当下的想法、情绪和感受，不评判，只是静静地观察...'
    }
};

function startMeditation(type) {
    const program = meditationPrograms[type];
    
    document.getElementById('meditation-title').textContent = program.title;
    document.getElementById('meditation-instruction').textContent = program.instruction;
    document.getElementById('meditation-guide').style.display = 'flex';
    
    resetMeditationTimer();
}

function closeMeditationGuide() {
    document.getElementById('meditation-guide').style.display = 'none';
    stopMeditation();
}

function toggleMeditation() {
    if (meditationActive) {
        stopMeditation();
    } else {
        startMeditationSession();
    }
}

function startMeditationSession() {
    meditationActive = true;
    meditationStartTime = Date.now();
    
    document.getElementById('meditation-play-icon').className = 'fas fa-pause';
    document.getElementById('meditation-btn-text').textContent = '暂停冥想';
    
    meditationTimer = setInterval(updateMeditationTimer, 1000);
}

function stopMeditation() {
    meditationActive = false;
    clearInterval(meditationTimer);
    
    document.getElementById('meditation-play-icon').className = 'fas fa-play';
    document.getElementById('meditation-btn-text').textContent = '开始冥想';
}

function resetMeditationTimer() {
    stopMeditation();
    document.getElementById('meditation-time').textContent = '00:00';
}

function updateMeditationTimer() {
    if (!meditationStartTime) return;
    
    const elapsed = Math.floor((Date.now() - meditationStartTime) / 1000);
    const minutes = Math.floor(elapsed / 60);
    const seconds = elapsed % 60;
    
    document.getElementById('meditation-time').textContent = 
        `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
}
</script>
@endsection 