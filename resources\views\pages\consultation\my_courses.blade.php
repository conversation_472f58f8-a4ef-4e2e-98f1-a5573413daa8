@extends('layouts.app')

@section('title', '我的课程')

@section('custom-styles')
<style>
    .my-courses-page {
        padding: 15px 15px 120px 15px; /* 为底部导航留出120px空间 */
    }
    
    .my-courses-page-header {
        display: flex;
        align-items: center;
        padding: 15px;
        background-color: #fff;
        position: sticky;
        top: 0;
        z-index: 100;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    }
    
    .my-courses-back-button {
        width: 24px;
        height: 24px;
        margin-right: 15px;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23333'%3E%3Cpath d='M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z'/%3E%3C/svg%3E");
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
        cursor: pointer;
    }
    
    .my-courses-empty {
        text-align: center;
        padding: 60px 20px;
        color: #666;
    }
    
    .my-courses-empty-icon {
        font-size: 48px;
        color: #ddd;
        margin-bottom: 15px;
    }
    
    .my-courses-empty-text {
        font-size: 16px;
        margin-bottom: 20px;
    }
    
    .my-courses-empty-button {
        background-color: #4CAF50;
        color: white;
        padding: 10px 20px;
        border: none;
        border-radius: 5px;
        text-decoration: none;
        display: inline-block;
    }
    
    .my-courses-item {
        background-color: #fff;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 15px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }
    
    .my-courses-item-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 10px;
    }
    
    .my-courses-item-title {
        font-size: 16px;
        font-weight: bold;
        color: #333;
        flex: 1;
        margin-right: 10px;
    }
    
    .my-courses-status-badge {
        padding: 4px 10px;
        border-radius: 15px;
        font-size: 12px;
        color: white;
    }
    
    .my-courses-status-registered {
        background-color: #4CAF50;
    }
    
    .my-courses-status-cancelled {
        background-color: #F44336;
    }
    
    .my-courses-status-confirmed {
        background-color: #2196F3;
    }
    
    .my-courses-status-pending {
        background-color: #FF9800;
    }
    
    .my-courses-item-meta {
        color: #666;
        font-size: 14px;
        margin-bottom: 8px;
    }
    
    .my-courses-item-meta-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 5px;
    }
    
    .my-courses-item-actions {
        margin-top: 10px;
        display: flex;
        flex-direction: column;
        gap: 10px;
    }
    
    .my-courses-btn {
        padding: 8px 15px;
        border: none;
        border-radius: 5px;
        font-size: 14px;
        cursor: pointer;
        text-decoration: none;
        display: inline-block;
        text-align: center;
    }
    
    .my-courses-btn-primary {
        background-color: #4CAF50;
        color: white;
    }
    
    .my-courses-btn-danger {
        background-color: #F44336;
        color: white;
    }
    
    .my-courses-btn-secondary {
        background-color: #f5f5f5;
        color: #333;
        border: 1px solid #ddd;
    }

    /* 新增提醒文本 */
    .my-courses-cancel-note {
        font-size: 13px;
        color: #F44336;
        margin-top: 4px;
    }
</style>
@endsection

@section('content')
<div class="my-courses-page-header">
    <a href="{{ route('consultation.offline_courses') }}" class="my-courses-back-button"></a>
    <h1 style="font-size: 18px; margin: 0;">我的课程</h1>
</div>

<div class="my-courses-page">
    @if(session('success'))
        <div class="alert alert-success">
            {{ session('success') }}
        </div>
    @endif
    
    @if(session('error'))
        <div class="alert alert-danger">
            {{ session('error') }}
        </div>
    @endif
    
    @if($registrations->count() > 0)
        @foreach($registrations as $registration)
            <div class="my-courses-item">
                <div class="my-courses-item-header">
                    <div class="my-courses-item-title">{{ $registration->course->title }}</div>
                    <span class="my-courses-status-badge my-courses-status-{{ 
                        $registration->status == 3 ? 'registered' 
                        : ($registration->status == 2 ? 'confirmed' 
                        : ($registration->status == 1 ? 'pending' 
                        : 'cancelled')) }}">
                        {{ $registration->status_text }}
                    </span>
                </div>
                
                <div class="my-courses-item-meta">
                    <div class="my-courses-item-meta-row">
                        <span>开始时间：</span>
                        <span>{{ date('Y-m-d H:i', strtotime($registration->course->start_time)) }}</span>
                    </div>
                    <div class="my-courses-item-meta-row">
                        <span>结束时间：</span>
                        <span>{{ date('Y-m-d H:i', strtotime($registration->course->end_time)) }}</span>
                    </div>
                    <div class="my-courses-item-meta-row">
                        <span>课程地点：</span>
                        <span>{{ $registration->course->location ?: '待定' }}</span>
                    </div>
                    <div class="my-courses-item-meta-row">
                        <span>报名时间：</span>
                        <span>{{ $registration->created_at->format('Y-m-d H:i') }}</span>
                    </div>
                    <div class="my-courses-item-meta-row">
                        <span>报名单位：</span>
                        <span>{{ $registration->department }}</span>
                    </div>
                    <div class="my-courses-item-meta-row">
                        <span>报名人数：</span>
                        <span>{{ $registration->participants_count }}</span>
                    </div>
                </div>
                
                <div class="my-courses-item-actions">
                    <a href="{{ route('consultation.offline_course_detail', $registration->course->id) }}" class="my-courses-btn my-courses-btn-secondary">
                        查看详情
                    </a>
                    
                    @if($registration->status == 3 && now()->lt($registration->course->start_time))
                        <form method="POST" action="{{ route('consultation.cancel_course_registration', $registration->id) }}" style="display: inline;">
                            @csrf
                            <button type="submit" class="my-courses-btn my-courses-btn-danger" 
                                onclick="return confirm('确定要取消报名吗？')">
                                取消报名
                            </button>
                        </form>
                        <!--{{-- 提示：取消后无法再次报名 --}}-->
                        <!--<div class="my-courses-cancel-note">-->
                        <!--    提醒：取消后无法再次报名该课程-->
                        <!--</div>-->
                    @endif
                </div>
            </div>
        @endforeach
        
        <div class="my-courses-pagination">
            {{ $registrations->links() }}
        </div>
    @else
        <div class="my-courses-empty">
            <div class="my-courses-empty-icon">📚</div>
            <div class="my-courses-empty-text">您还没有报名任何课程</div>
            <a href="{{ route('consultation.offline_courses') }}" class="my-courses-empty-button">
                浏览课程
            </a>
        </div>
    @endif
</div>
@endsection
