<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable,HasDateTimeFormatter;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'phone',
        'phone_verified',
        'avatar',
        'last_login_at',
        'last_login_ip',
        'is_contact',
        'enterprise'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
    ];
    
    /**
     * 用户所有的课程报名记录
     */
    public function courseRegistrations(): HasMany
    {
        return $this->hasMany(CourseRegistration::class, 'user_id');
    }

    /**
     * 用户已报名的线下课程（讲座）
     */
    public function offlineCourses(): BelongsToMany
    {
        return $this->belongsToMany(
            OfflineCourse::class,
            'course_registrations',
            'user_id',
            'course_id'
        )
        ->withPivot('participants_count', 'status', 'created_at')
        ->orderBy('course_registrations.created_at', 'desc');
    }
    
    /**
     * 获取用户的咨询预约
     */
    public function consultationAppointments()
    {
        return $this->hasMany(ConsultationAppointment::class);
    }
    
    /**
     * 获取用户的测评记录
     */
    public function assessments()
    {
        return $this->hasMany(AssessmentResponse::class);
    }
    
    /**
     * 获取用户的收藏
     */
    public function favorites()
    {
        // 假设收藏模型是Favorite，如果没有可以后续添加
        // 临时使用关联空数组返回
        return collect([]);
    }
    
    /**
     * 获取用户学习的课程
     */
    public function courses()
    {
        // 假设课程学习记录模型是UserCourse，如果不是请调整
        return $this->hasMany(UserCourse::class);
    }
}
