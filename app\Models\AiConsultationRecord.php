<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Dcat\Admin\Traits\HasDateTimeFormatter;

class AiConsultationRecord extends Model
{
    use HasFactory, HasDateTimeFormatter;

    protected $fillable = [
        'user_id',
        'appointment_id',
        'user_query',
        'ai_response',
        'recommended_resources'
    ];

    protected $casts = [
        'recommended_resources' => 'array',
    ];

    // 与用户的关联
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    // 与咨询预约的关联
    public function appointment()
    {
        return $this->belongsTo(ConsultationAppointment::class, 'appointment_id');
    }
}
