<div class="card">
    <div class="card-header">
        <h3 class="card-title">视频信息</h3>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <div class="form-group">
                    <label>标题</label>
                    <div>{{ $video->title }}</div>
                </div>
                <div class="form-group">
                    <label>分类</label>
                    <div>{{ $video->category ? $video->category->name : '未分类' }}</div>
                </div>
                <div class="form-group">
                    <label>标签</label>
                    <div>
                        @if($video->tags && $video->tags->count() > 0)
                            @foreach($video->tags as $tag)
                                <span class="badge badge-info mr-1">{{ $tag->name }}</span>
                            @endforeach
                        @else
                            无标签
                        @endif
                    </div>
                </div>
                <div class="form-group">
                    <label>提交人</label>
                    <div>{{ $video->author ? $video->author->name : '未知' }}</div>
                </div>
                <div class="form-group">
                    <label>提交时间</label>
                    <div>{{ $video->created_at }}</div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label>封面图片</label>
                    <div>
                        @if($video->image)
                            <img src="{{ \Illuminate\Support\Facades\Storage::disk(config('admin.upload.disk'))->url($video->image) }}" style="max-width: 100%; max-height: 200px;" class="img-thumbnail">
                        @else
                            <span class="text-muted">无封面图片</span>
                        @endif
                    </div>
                </div>
            </div>
        </div>
        
        <div class="form-group">
            <label>视频描述</label>
            <div>{{ $video->description ?: '无描述' }}</div>
        </div>
        
        <div class="form-group">
            <label>视频链接</label>
            <div>
                <a href="{{ $video->video_url }}" target="_blank">{{ $video->video_url }}</a>
            </div>
        </div>
        
        @if($video->content)
            <div class="form-group">
                <label>视频内容</label>
                <div class="content-preview">{!! $video->content !!}</div>
            </div>
        @endif
    </div>
</div>

<div class="card mt-3">
    <div class="card-header">
        <h3 class="card-title">审核操作</h3>
    </div>
    <div class="card-body">
        <form id="review-form">
            <div class="form-group">
                <label>审核结果</label>
                <div class="radio">
                    <label class="radio-inline">
                        <input type="radio" name="status" value="approved" checked> 通过
                    </label>
                    <label class="radio-inline ml-3">
                        <input type="radio" name="status" value="rejected"> 拒绝
                    </label>
                </div>
            </div>
            
            <div class="form-group">
                <label>审核意见</label>
                <textarea name="reason" class="form-control" rows="3" placeholder="请输入审核意见，拒绝时必填"></textarea>
            </div>
            
            <div class="form-group">
                <button type="button" id="submit-review" class="btn btn-primary">提交审核</button>
                <a href="{{ admin_url('videos/pending-review') }}" class="btn btn-default ml-1">返回列表</a>
            </div>
        </form>
    </div>
</div>

<style>
.content-preview {
    max-height: 300px;
    overflow-y: auto;
    padding: 10px;
    border: 1px solid #eee;
    border-radius: 3px;
}
</style>

<script>
$(function() {
    // 提交审核
    $('#submit-review').on('click', function() {
        var status = $('input[name="status"]:checked').val();
        var reason = $('textarea[name="reason"]').val();
        
        if (!reason) {
            Dcat.error('请填写审核意见');
            return;
        }
        
        // 使用Swal直接调用，避免Dcat.confirm的封装问题
        Swal.fire({
            title: '确认操作',
            text: '确认要执行此审核操作吗？',
            type: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            showLoaderOnConfirm: true,
            preConfirm: function() {
                return new Promise(function(resolve, reject) {
                    $.ajax({
                        url: '{{ admin_url("videos/{$video->id}/handle-review") }}',
                        type: 'POST',
                        dataType: 'json',
                        data: {
                            _token: '{{ csrf_token() }}',
                            status: status,
                            reason: reason
                        },
                        success: function(data) {
                            console.log('请求成功:', data);
                            resolve(data);
                        },
                        error: function(xhr) {
                            console.log('请求失败:', xhr);
                            reject(xhr);
                        }
                    });
                });
            },
            allowOutsideClick: false
        }).then(function(result) {
            if (result.value) {
                var data = result.value;
                if (data.status) {
                    Dcat.success(data.message || '审核成功');
                    setTimeout(function() {
                        location.href = '{{ admin_url("videos") }}';
                    }, 1500);
                } else {
                    Dcat.error(data.message || '操作失败');
                }
            }
        }).catch(function(error) {
            var message = '';
            if (error.responseJSON && error.responseJSON.message) {
                message = error.responseJSON.message;
            } else {
                message = '服务器错误，请稍后再试';
            }
            Dcat.error(message);
        });
    });
});
</script>
