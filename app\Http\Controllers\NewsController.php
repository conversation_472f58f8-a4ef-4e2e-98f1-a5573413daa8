<?php

namespace App\Http\Controllers;

use App\Models\News;
use App\Models\NewsCategory;
use App\Models\NewsTag;
use App\Models\NewsView;
use Illuminate\Http\Request;
use Carbon\Carbon;

class NewsController extends Controller
{
    /**
     * 显示新闻列表
     */
    public function index(Request $request)
    {
        $categorySlug = $request->input('category');
        $tagSlug      = $request->input('tag');
        $keyword      = $request->input('keyword');
        
        // 基础查询
        $query = News::with('category')
            ->published()
            ->orderBy('published_at', 'desc');
        
        // 分类过滤
        if ($categorySlug) {
            $category = NewsCategory::where('slug', $categorySlug)->first();
            if ($category) {
                $query->where('category_id', $category->id);
            }
        }
        
        // 标签过滤
        if ($tagSlug) {
            $tag = NewsTag::where('slug', $tagSlug)->first();
            if ($tag) {
                $query->whereHas('tags', function ($q) use ($tag) {
                    $q->where('news_tags.id', $tag->id);
                });
            }
        }
        
        // 关键词搜索
        if ($keyword) {
            $query->search($keyword);
        }
        
        // 推荐新闻（不分页）
        $featuredNews = News::with('category')
            ->published()
            ->featured()
            ->orderBy('published_at', 'desc')
            ->limit(5)
            ->get();
        
        // 普通新闻（分页）
        $news = $query
            ->paginate(12)
            ->withQueryString();                // 保留 category/tag/keyword
        $news->withPath($request->getPathInfo()); // 强制相对路径 "/news"
        
        // 侧边栏数据
        $categories  = NewsCategory::withCount(['news' => function ($q) {
            $q->published();
        }])->sorted()->get();
        $popularTags = NewsTag::withCount(['news' => function ($q) {
            $q->published();
        }])->orderBy('news_count', 'desc')->limit(15)->get();
        $popularNews = News::published()
            ->orderBy('view_count', 'desc')
            ->limit(5)
            ->get();
            
        return view('pages.news.index', compact(
            'news', 'featuredNews', 'categories',
            'popularTags', 'popularNews',
            'categorySlug', 'tagSlug', 'keyword'
        ));
    }
    
    /**
     * 显示新闻详情
     */
    public function show($slug)
    {
        $news = News::with(['category', 'tags'])
            ->published()
            ->where('slug', $slug)
            ->firstOrFail();
        
        // 记录浏览
        $news->recordView(auth()->id());
        
        // 相关新闻 (同分类或同标签)
        $relatedNews = News::with('category')
            ->published()
            ->where('id', '!=', $news->id)
            ->where(function ($query) use ($news) {
                $query->where('category_id', $news->category_id)
                    ->orWhereHas('tags', function ($q) use ($news) {
                        $q->whereIn('news_tags.id', $news->tags->pluck('id'));
                    });
            })
            ->orderBy('published_at', 'desc')
            ->limit(6)
            ->get();
            
        // 所有分类（用于侧边栏）
        $categories = NewsCategory::withCount(['news' => function ($query) {
            $query->published();
        }])->sorted()->get();
        
        // 热门标签
        $popularTags = NewsTag::withCount(['news' => function ($query) {
            $query->published();
        }])->orderBy('news_count', 'desc')->limit(15)->get();
        
        // 热门新闻
        $popularNews = News::published()
            ->where('id', '!=', $news->id)
            ->orderBy('view_count', 'desc')
            ->orderBy('published_at', 'desc')
            ->limit(5)
            ->get();
            
        return view('pages.news.show', compact(
            'news', 'relatedNews', 'categories', 'popularTags', 'popularNews'
        ));
    }
    
    /**
     * 智能推荐新闻
     */
    public function recommended(Request $request)
    {
        $userId = auth()->id();
        $limit = $request->input('limit', 6);
        
        if ($userId) {
            // 已登录用户，基于用户历史行为推荐
            $userViewedCategories = NewsView::where('user_id', $userId)
                ->join('news', 'news_views.news_id', '=', 'news.id')
                ->select('news.category_id', \DB::raw('count(*) as view_count'))
                ->groupBy('news.category_id')
                ->orderBy('view_count', 'desc')
                ->limit(3)
                ->pluck('category_id')
                ->toArray();
                
            $userViewedNewsIds = NewsView::where('user_id', $userId)
                ->orderBy('created_at', 'desc')
                ->limit(20)
                ->pluck('news_id')
                ->toArray();
                
            $recommendedNews = News::published()
                ->where(function ($query) use ($userViewedCategories, $userViewedNewsIds) {
                    // 优先推荐用户常看的分类
                    if (!empty($userViewedCategories)) {
                        $query->whereIn('category_id', $userViewedCategories);
                    }
                    
                    // 排除已看过的新闻
                    if (!empty($userViewedNewsIds)) {
                        $query->whereNotIn('id', $userViewedNewsIds);
                    }
                })
                ->orderBy('published_at', 'desc')
                ->limit($limit)
                ->get();
                
            // 如果推荐数量不足，补充最新新闻
            if ($recommendedNews->count() < $limit) {
                $moreNewsNeeded = $limit - $recommendedNews->count();
                $moreNews = News::published()
                    ->whereNotIn('id', $recommendedNews->pluck('id')->toArray())
                    ->whereNotIn('id', $userViewedNewsIds)
                    ->orderBy('published_at', 'desc')
                    ->limit($moreNewsNeeded)
                    ->get();
                    
                $recommendedNews = $recommendedNews->concat($moreNews);
            }
        } else {
            // 未登录用户，推荐热门和最新内容
            $recommendedNews = News::published()
                ->orderBy('view_count', 'desc')
                ->orderBy('published_at', 'desc')
                ->limit($limit)
                ->get();
        }
        
        return response()->json([
            'success' => true,
            'data' => $recommendedNews
        ]);
    }
    
    /**
     * 记录分享
     */
    public function recordShare($id)
    {
        $news = News::findOrFail($id);
        $news->recordShare();
        
        return response()->json([
            'success' => true,
            'count' => $news->share_count
        ]);
    }
}
