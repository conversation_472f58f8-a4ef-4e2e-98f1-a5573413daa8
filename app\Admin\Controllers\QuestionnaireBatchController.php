<?php

namespace App\Admin\Controllers;

use App\Models\AssessmentQuestionnaire;
use App\Models\AssessmentQuestion;
use App\Models\AssessmentOption;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Widgets\Alert;
use Dcat\Admin\Widgets\Card;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class QuestionnaireBatchController extends AdminController
{
    protected $title = '批量导入问卷';

    /**
     * 显示批量导入页面
     */
    public function index(Content $content)
    {
        return $content
            ->title('批量导入问卷')
            ->description('支持JSON格式批量导入题目和选项')
            ->body($this->importForm());
             // 把它渲染成 HTML，然后用 response() 包一层
    $html = $content->render();

    return response($html);
    }

    /**
     * 生成导入表单
     */
    protected function importForm()
    {
        $exampleJson = [
            'title' => '示例心理测评问卷',
            'description' => '这是一个示例问卷，用于演示批量导入格式',
            'domain' => '示例测评',
            'questions' => [
                [
                    'type' => 'single',
                    'content' => '您觉得自己的心情如何？',
                    'sort_order' => 1,
                    'options' => [
                        ['content' => '非常好', 'score_value' => 5],
                        ['content' => '比较好', 'score_value' => 4],
                        ['content' => '一般', 'score_value' => 3],
                        ['content' => '比较差', 'score_value' => 2],
                        ['content' => '非常差', 'score_value' => 1]
                    ]
                ],
                [
                    'type' => 'scale',
                    'content' => '您对工作的满意度如何？',
                    'sort_order' => 2,
                    'options' => [
                        ['content' => '非常满意', 'score_value' => 5],
                        ['content' => '比较满意', 'score_value' => 4],
                        ['content' => '一般', 'score_value' => 3],
                        ['content' => '不太满意', 'score_value' => 2],
                        ['content' => '非常不满意', 'score_value' => 1]
                    ]
                ]
            ]
        ];

        $exampleJsonStr = json_encode($exampleJson, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);

        return Card::make('批量导入问卷', "
            <form method='POST' action='" . admin_url('questionnaire-batch/import') . "' enctype='multipart/form-data'>
                " . csrf_field() . "
                
                <div class='form-group'>
                    <label>导入方式</label>
                    <div class='radio'>
                        <label>
                            <input type='radio' name='import_type' value='json' checked> JSON格式导入
                        </label>
                    </div>
                    <div class='radio'>
                        <label>
                            <input type='radio' name='import_type' value='file'> 文件上传导入
                        </label>
                    </div>
                </div>

                <div id='json-import' class='form-group'>
                    <label>JSON数据 <small class='text-muted'>请按照下面的格式输入JSON数据</small></label>
                    <textarea name='json_data' class='form-control' rows='15' placeholder='请输入JSON格式的问卷数据'></textarea>
                </div>

                <div id='file-import' class='form-group' style='display:none;'>
                    <label>上传文件 <small class='text-muted'>支持.json格式</small></label>
                    <input type='file' name='import_file' class='form-control' accept='.json'>
                </div>

                <div class='form-group'>
                    <button type='submit' class='btn btn-primary'>
                        <i class='fa fa-upload'></i> 开始导入
                    </button>
                    <button type='button' class='btn btn-info' onclick='fillExample()'>
                        <i class='fa fa-file-text-o'></i> 填入示例数据
                    </button>
                    <a href='" . admin_url('assessment-questionnaires') . "' class='btn btn-default'>
                        <i class='fa fa-arrow-left'></i> 返回问卷列表
                    </a>
                </div>
            </form>

            <script>
                // 切换导入方式
                $('input[name=\"import_type\"]').change(function() {
                    if ($(this).val() === 'json') {
                        $('#json-import').show();
                        $('#file-import').hide();
                    } else {
                        $('#json-import').hide();
                        $('#file-import').show();
                    }
                });

                // 填入示例数据
                function fillExample() {
                    var exampleData = " . json_encode($exampleJsonStr) . ";
                    $('textarea[name=\"json_data\"]').val(exampleData);
                }
            </script>
        ")->style('info');
    }

    /**
     * 处理导入请求
     */
    public function import(Request $request)
    {
        $importType = $request->input('import_type', 'json');
        $jsonData = '';

        try {
            if ($importType === 'json') {
                $jsonData = $request->input('json_data');
                if (empty($jsonData)) {
                    return redirect()->back()->with('error', '请输入JSON数据');
                }
            } else {
                $file = $request->file('import_file');
                if (!$file || !$file->isValid()) {
                    return redirect()->back()->with('error', '请上传有效的文件');
                }
                
                $jsonData = file_get_contents($file->getPathname());
            }

            // 解析JSON数据
            $data = json_decode($jsonData, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                return redirect()->back()->with('error', 'JSON格式错误：' . json_last_error_msg());
            }

            // 验证数据格式
            $validator = $this->validateImportData($data);
            if ($validator->fails()) {
                return redirect()->back()->with('error', '数据验证失败：' . $validator->errors()->first());
            }

            // 开始导入
            $result = $this->processImport($data);
            
            // 使用右上角提示，不跳转页面
            return redirect()->back()->with('success', "导入成功！创建了问卷「{$result['title']}」，包含 {$result['question_count']} 个题目。您可以在问卷列表中查看和编辑。");

        } catch (\Exception $e) {
            return redirect()->back()->with('error', '导入失败：' . $e->getMessage());
        }
    }

    /**
     * 验证导入数据
     */
    protected function validateImportData($data)
    {
        $rules = [
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'domain' => 'required|string|max:100',
            'questions' => 'required|array|min:1',
            'questions.*.type' => 'required|in:single,multiple,scale',
            'questions.*.content' => 'required|string',
            'questions.*.sort_order' => 'nullable|integer|min:0',
            'questions.*.options' => 'required|array|min:1',
            'questions.*.options.*.content' => 'required|string',
            'questions.*.options.*.score_value' => 'required|numeric|min:0'
        ];

        $messages = [
            'title.required' => '问卷标题不能为空',
            'domain.required' => '测评领域不能为空',
            'questions.required' => '至少需要一个题目',
            'questions.*.type.required' => '题目类型不能为空',
            'questions.*.type.in' => '题目类型必须是：single、multiple、scale',
            'questions.*.content.required' => '题目内容不能为空',
            'questions.*.options.required' => '题目选项不能为空',
            'questions.*.options.*.content.required' => '选项内容不能为空',
            'questions.*.options.*.score_value.required' => '选项分值不能为空'
        ];

        return Validator::make($data, $rules, $messages);
    }

    /**
     * 处理导入数据
     */
    protected function processImport($data)
    {
        DB::beginTransaction();
        
        try {
            // 创建问卷
            $questionnaire = AssessmentQuestionnaire::create([
                'title' => $data['title'],
                'description' => $data['description'] ?? '',
                'domain' => $data['domain'],
                'question_count' => count($data['questions']),
                'est_duration' => max(count($data['questions']), 5),
                'is_active' => false // 默认不激活
            ]);

            // 创建题目和选项
            foreach ($data['questions'] as $index => $questionData) {
                $question = AssessmentQuestion::create([
                    'questionnaire_id' => $questionnaire->id,
                    'type' => $questionData['type'],
                    'content' => $questionData['content'],
                    'sort_order' => $questionData['sort_order'] ?? ($index + 1)
                ]);

                // 创建选项
                foreach ($questionData['options'] as $optionData) {
                    AssessmentOption::create([
                        'question_id' => $question->id,
                        'content' => $optionData['content'],
                        'score_value' => $optionData['score_value']
                    ]);
                }
            }

            DB::commit();

            return [
                'questionnaire_id' => $questionnaire->id,
                'title' => $questionnaire->title,
                'question_count' => count($data['questions'])
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 导出问卷为JSON格式
     */
    public function export($id)
    {
        $questionnaire = AssessmentQuestionnaire::with(['questions.options'])->findOrFail($id);
        
        $exportData = [
            'title' => $questionnaire->title,
            'description' => $questionnaire->description,
            'domain' => $questionnaire->domain,
            'questions' => []
        ];

        foreach ($questionnaire->questions->sortBy('sort_order') as $question) {
            $questionData = [
                'type' => $question->type,
                'content' => $question->content,
                'sort_order' => $question->sort_order,
                'options' => []
            ];

            foreach ($question->options as $option) {
                $questionData['options'][] = [
                    'content' => $option->content,
                    'score_value' => $option->score_value
                ];
            }

            $exportData['questions'][] = $questionData;
        }

        $filename = "questionnaire_{$questionnaire->id}_{$questionnaire->title}.json";
        $jsonContent = json_encode($exportData, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);

        return response($jsonContent)
            ->header('Content-Type', 'application/json')
            ->header('Content-Disposition', "attachment; filename=\"{$filename}\"");
    }
} 