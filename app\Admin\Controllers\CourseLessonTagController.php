<?php

namespace App\Admin\Controllers;

use App\Models\CourseLessonTag;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class CourseLessonTagController extends AdminController
{
    /**
     * 设置标题
     *
     * @return string
     */
    protected $title = '心理课堂标签';

    /**
     * 列表页
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new CourseLessonTag(), function (Grid $grid) {
            $grid->column('id')->sortable();
            $grid->column('name', '标签名称');
            $grid->column('description', '描述');
            $grid->column('is_active', '是否启用')->switch();
            $grid->column('created_at', '创建时间');
            $grid->column('updated_at', '更新时间');

            $grid->filter(function (Grid\Filter $filter) {
                $filter->panel();

                $filter->equal('id');
                $filter->like('name', '标签名称');
                $filter->equal('is_active', '是否启用')->select([
                    1 => '是',
                    0 => '否',
                ]);
            });

            $grid->quickSearch('name');

            // 启用弹窗编辑功能
            $grid->showQuickEditButton();

            // 正确的弹窗实现方式
            $grid->enableDialogCreate();
        });
    }

    /**
     * 详情页
     *
     * @param mixed $id
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new CourseLessonTag(), function (Show $show) {
            $show->field('id');
            $show->field('name', '标签名称');
            $show->field('description', '描述');
            $show->field('is_active', '是否启用')->as(function ($value) {
                return $value ? '是' : '否';
            });
            $show->field('created_at', '创建时间');
            $show->field('updated_at', '更新时间');

            $show->relation('lessons', '关联课程', function ($model) {
                $grid = new Grid(new \App\Models\CourseLesson());

                $grid->model()->whereHas('tags', function ($query) use ($model) {
                    $query->where('course_lesson_tags.id', $model->id);
                });

                $grid->column('id')->sortable();
                $grid->column('title', '标题');
                $grid->column('status', '状态')->using(\App\Models\CourseLesson::getStatusMap());
                $grid->column('publish_time', '发布时间');

                $grid->disableCreateButton();
                $grid->disableActions();
                $grid->disableBatchDelete();
                $grid->disableRowSelector();

                return $grid;
            });
        });
    }

    /**
     * 表单页
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new CourseLessonTag(), function (Form $form) {
            $form->display('id');
            $form->text('name', '标签名称')->required();
            $form->textarea('description', '描述');
            $form->switch('is_active', '是否启用')->default(true);

            $form->display('created_at', '创建时间');
            $form->display('updated_at', '更新时间');
        });
    }
}
