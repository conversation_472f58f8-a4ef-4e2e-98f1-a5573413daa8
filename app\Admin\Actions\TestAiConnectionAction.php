<?php

namespace App\Admin\Actions;

use Dcat\Admin\Actions\Response;
use Dcat\Admin\Grid\RowAction;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use App\Models\AiConsultationSetting;
use Illuminate\Support\Facades\Log;

class TestAiConnectionAction extends RowAction
{
    /**
     * 按钮标题
     *
     * @return string
     */
    public function title()
    {
        return '测试连接';
    }

    /**
     * 设置按钮样式
     * 
     * @return string
     */
    protected $style = 'btn btn-sm btn-info';

    /**
     * 处理请求
     *
     * @param Request $request
     * @return Response
     */
    public function handle(Request $request)
    {
        try {
            // 获取设置ID
            $id = $request->get('id');
            
            // 直接从数据库中获取AI设置，包括原始的API密钥
            $setting = AiConsultationSetting::select([\DB::raw('id, provider, api_url, model, max_tokens, temperature, original.api_key as raw_api_key')])
                ->fromRaw('ai_consultation_settings as original')
                ->where('id', $id)
                ->first();
                
            if (!$setting) {
                throw new \Exception('找不到ID为 ' . $id . ' 的AI设置');
            }
            
            // 根据不同的AI提供商构建测试请求
            $apiKey = $setting->raw_api_key; // 使用原始的API密钥
            $headers = [
                'Content-Type' => 'application/json',
                'Authorization' => 'Bearer ' . $apiKey
            ];
            
            // 记录日志，但不记录完整密钥
            Log::info('测试AI连接', [
                'setting_id' => $id,
                'provider' => $setting->provider,
                'api_key_prefix' => substr($apiKey, 0, 4) . '***'
            ]);
            
            $payload = [];
            
            // DeepSeek和OpenAI使用类似的API
            if ($setting->provider == 'deepseek' || $setting->provider == 'openai') {
                $payload = [
                    'model' => $setting->model,
                    'messages' => [
                        ['role' => 'system', 'content' => 'You are a helpful assistant.'],
                        ['role' => 'user', 'content' => 'Hello, this is a test message. Please respond with a brief confirmation.']
                    ],
                    'max_tokens' => 50,
                    'temperature' => 0.5
                ];
            }
            
            // 发送HTTP请求测试连接
            $response = Http::withHeaders($headers)
                ->timeout(10) // 10秒超时
                ->post($setting->api_url, $payload);
            
            // 检查响应
            if ($response->successful()) {
                // 记录成功日志
                Log::info('AI连接测试成功', [
                    'setting_id' => $id,
                    'provider' => $setting->provider,
                    'response_status' => $response->status(),
                    'response_snippet' => substr(json_encode($response->json()), 0, 100) . '...'
                ]);
                
                return $this->response()->success('连接测试成功!');
            } else {
                // 记录错误日志
                Log::error('AI连接测试失败: HTTP错误', [
                    'setting_id' => $id,
                    'provider' => $setting->provider,
                    'response_status' => $response->status(),
                    'response_body' => $response->body()
                ]);
                
                return $this->response()->error('连接测试失败: ' . $response->status() . ' ' . $response->body());
            }
        } catch (\Exception $e) {
            // 记录异常日志
            Log::error('AI连接测试异常', [
                'exception' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return $this->response()->error('连接测试异常: ' . $e->getMessage());
        }
    }

    /**
     * 请求前确认
     *
     * @return string
     */
    public function confirm()
    {
        return '确定要测试与AI提供商的连接吗？';
    }
    
    /**
     * 设置请求参数
     *
     * @return array
     */
    public function parameters()
    {
        // 在RowAction中可直接获取行数据
        return [
            'id' => $this->getKey(),
        ];
    }
}
