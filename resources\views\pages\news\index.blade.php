@extends('layouts.app')

@section('title', '新闻资讯')

@section('custom-styles')
<style>
    body {
        background-color: #f5f5f7;
        min-height: 100vh;
        padding-bottom: 80px;
    }
    .news-header {
        background: linear-gradient(135deg, #5b7cef 0%, #3c67e3 100%);
        padding: 25px 20px;
        color: white;
        border-radius: 0 0 15px 15px;
        box-shadow: 0 4px 12px rgba(91, 124, 239, 0.2);
        margin-bottom: 20px;
        position: relative;
    }
    .search-bar {
        background: white;
        border-radius: 20px;
        padding: 8px 15px;
        display: flex;
        align-items: center;
        margin: 15px 0 5px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
    .search-icon {
        width: 20px;
        height: 20px;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999'%3E%3Cpath d='M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z'/%3E%3C/svg%3E");
        background-size: contain;
        background-repeat: no-repeat;
        margin-right: 10px;
    }
    .search-input {
        flex: 1;
        border: none;
        background: transparent;
        font-size: 14px;
        padding: 5px 0;
        outline: none;
    }
    .search-button {
        background: none;
        border: none;
        color: #3c67e3;
        cursor: pointer;
        padding: 0;
    }
    .news-card {
        background: white;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        overflow: hidden;
        margin-bottom: 20px;
        transition: transform 0.2s, box-shadow 0.2s;
        height: 100%;
        display: flex;
        flex-direction: column;
    }
    .news-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }
    .news-link {
        display: flex;
        flex-direction: column;
        height: 100%;
        text-decoration: none;
        color: inherit;
    }
    .news-card-image {
        height: 150px;
        background-size: cover;
        background-position: center;
    }
    .news-card-content {
        padding: 15px;
    }
    .news-card-title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin-bottom: 8px;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        height: 44px;
    }
    .news-summary {
        font-size: 13px;
        color: #666;
        margin: 10px 0;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        line-height: 1.4;
    }
    .news-card-meta {
        display: flex;
        justify-content: space-between;
        color: #888;
        font-size: 12px;
    }
    .category-badge {
        font-size: 12px;
        color: #5b7cef;
        background: rgba(91, 124, 239, 0.1);
        padding: 2px 8px;
        border-radius: 4px;
    }
    .sidebar {
        background: white;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        transition: transform 0.2s, box-shadow 0.2s;
    }
    .sidebar-title {
        font-size: 17px;
        font-weight: 600;
        margin-bottom: 15px;
        color: #3c67e3;
        padding-bottom: 10px;
        border-bottom: 1px solid #eee;
        letter-spacing: 0.02em;
    }
    .tag-cloud a {
        display: inline-block;
        padding: 6px 12px;
        background: #f0f4ff;
        border-radius: 20px;
        color: #3c67e3;
        font-size: 13px;
        margin: 0 5px 8px 0;
        text-decoration: none;
        transition: all 0.2s ease;
    }
    .tag-cloud a:hover {
        background: #3c67e3;
        color: #fff;
        transform: translateY(-2px);
        box-shadow: 0 3px 8px rgba(60, 103, 227, 0.2);
    }
    .category-filter {
        overflow-x: auto;
        white-space: nowrap;
        padding: 10px 0;
        margin-bottom: 20px;
        -ms-overflow-style: none;
        scrollbar-width: none;
    }
    .category-filter::-webkit-scrollbar {
        display: none;
    }
    .category-filter a {
        display: inline-block;
        padding: 8px 15px;
        border-radius: 20px;
        background: white;
        color: #666;
        margin-right: 8px;
        text-decoration: none;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        transition: all 0.2s ease;
    }
    .category-filter a:hover:not(.active) {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        color: #333;
    }
    .category-filter a.active {
        background: linear-gradient(135deg, #5b7cef 0%, #3c67e3 100%);
        color: white;
        box-shadow: 0 4px 12px rgba(91, 124, 239, 0.3);
    }
    .pagination {
        margin-bottom: 80px;
    }
    /* 自定义分页样式 */
    .news-pagination {
        margin: 40px 0;
        text-align: center;
    }

    .pagination-list {
        display: inline-flex;
        padding: 0;
        margin: 0 0 15px 0;
        list-style: none;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .page-item {
        margin: 0 2px;
    }

    .page-item:first-child .page-link {
        border-top-left-radius: 8px;
        border-bottom-left-radius: 8px;
    }

    .page-item:last-child .page-link {
        border-top-right-radius: 8px;
        border-bottom-right-radius: 8px;
    }

    .page-link {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 10px 15px;
        min-width: 44px;
        height: 44px;
        color: #495057;
        text-decoration: none;
        background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        border: 1px solid #e9ecef;
        transition: all 0.3s ease;
        font-weight: 500;
        font-size: 14px;
        line-height: 1;
        overflow: hidden;
    }

    .page-link:before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
        transition: all 0.6s;
    }

    .page-link:hover:before {
        left: 100%;
    }

    .page-link:hover {
        color: #ffffff;
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        border-color: #0056b3;
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0, 123, 255, 0.3);
    }

    .page-item.active .page-link {
        color: #ffffff;
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        border-color: #0056b3;
        box-shadow: 0 4px 15px rgba(0, 123, 255, 0.4);
        transform: scale(1.05);
        z-index: 3;
    }

    .page-item.disabled .page-link {
        color: #adb5bd;
        background: #f8f9fa;
        border-color: #e9ecef;
        cursor: not-allowed;
        opacity: 0.6;
    }

    .page-item.disabled .page-link:hover {
        color: #adb5bd;
        background: #f8f9fa;
        border-color: #e9ecef;
        transform: none;
        box-shadow: none;
    }

    .page-number {
        font-weight: 600;
    }

    .page-dots {
        pointer-events: none;
        font-weight: bold;
    }

    .page-text {
        font-size: 13px;
        margin: 0 5px;
    }

    .pagination-info {
        margin-top: 15px;
    }

    .pagination-text {
        color: #6c757d;
        font-size: 14px;
    }

    .pagination-text strong {
        color: #495057;
        font-weight: 600;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .pagination-list {
            flex-wrap: wrap;
            gap: 5px;
        }
        
        .page-link {
            padding: 8px 12px;
            min-width: 38px;
            height: 38px;
            font-size: 13px;
        }
        
        .page-text {
            display: none;
        }
        
        .pagination-info {
            font-size: 12px;
        }
    }

    @media (max-width: 480px) {
        .page-link {
            padding: 6px 10px;
            min-width: 34px;
            height: 34px;
            font-size: 12px;
        }
    }
</style>
@endsection

@section('content')
<div class="news-header">
    <h1 class="mb-1" style="font-size: 22px; font-weight: 600;">新闻资讯</h1>
    <p class="mb-0" style="opacity: 0.8; font-size: 14px;">了解最新的心理健康资讯和行业动态</p>
    <form action="{{ route('news.index') }}" method="GET" class="search-form">
        <div class="search-bar">
            <div class="search-icon"></div>
            <input type="text" name="keyword" class="search-input" value="{{ $keyword ?? '' }}" placeholder="搜索新闻资讯...">
            <button type="submit" class="search-button"><i class="fas fa-search"></i></button>
        </div>
    </form>
</div>

<div class="container">
    <!-- 分类过滤器 -->
    <div class="category-filter">
        <a href="{{ route('news.index') }}" class="{{ empty($categorySlug) ? 'active' : '' }}">全部</a>
        @foreach($categories as $category)
            <a href="{{ route('news.index', ['category' => $category->slug]) }}" class="{{ (isset($categorySlug) && $categorySlug == $category->slug) ? 'active' : '' }}">
                {{ $category->name }} ({{ $category->news_count }})
            </a>
        @endforeach
    </div>

    <div class="row">
        <!-- 主内容区 -->
        <div class="col-lg-8">
            <!-- 全部新闻列表（分页） -->
            <div class="row">
                @forelse($news as $item)
                    <div class="col-md-6 mb-4">
                        <a href="{{ route('news.show', $item->slug) }}" class="news-link">
                            <div class="news-card">
                                <div class="news-card-image" style="background-image: url('{{ $item->cover_image ? asset('storage/'.$item->cover_image) : asset('images/default-news.jpg') }}');"></div>
                                <div class="news-card-content">
                                    <h3 class="news-card-title">{{ $item->title }}</h3>
                                    <p class="news-summary">{{ Str::limit($item->summary, 80) }}</p>
                                    <div class="news-card-meta">
                                        <span class="category-badge">{{ $item->category->name }}</span>
                                        <span>{{ $item->published_at->format('Y-m-d') }}</span>
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                @empty
                    <div class="col-12 text-center py-5">
                        <img src="{{ asset('images/no-data.svg') }}" alt="暂无数据" style="width: 120px; opacity: 0.5;">
                        <p class="mt-3 text-muted">暂无相关新闻</p>
                    </div>
                @endforelse
            </div>

            <!-- 分页 -->
            @if(isset($news) && $news->hasPages())
            <div class="news-pagination">
                <nav aria-label="页面导航">
                    <ul class="pagination-list">
                        {{-- 上一页 --}}
                        @if ($news->onFirstPage())
                            <li class="page-item disabled">
                                <span class="page-link">
                                    <i class="fas fa-chevron-left"></i>
                                    <span class="page-text">上一页</span>
                                </span>
                            </li>
                        @else
                            <li class="page-item">
                                <a class="page-link" href="{{ $news->previousPageUrl() }}">
                                    <i class="fas fa-chevron-left"></i>
                                    <span class="page-text">上一页</span>
                                </a>
                            </li>
                        @endif

                        {{-- 页码 --}}
                        @php
                            $start = max($news->currentPage() - 2, 1);
                            $end = min($news->currentPage() + 2, $news->lastPage());
                        @endphp

                        @if($start > 1)
                            <li class="page-item">
                                <a class="page-link page-number" href="{{ $news->url(1) }}">1</a>
                            </li>
                            @if($start > 2)
                                <li class="page-item disabled"><span class="page-link page-dots">...</span></li>
                            @endif
                        @endif

                        @for($page = $start; $page <= $end; $page++)
                            @if ($page == $news->currentPage())
                                <li class="page-item active">
                                    <span class="page-link page-number">{{ $page }}</span>
                                </li>
                            @else
                                <li class="page-item">
                                    <a class="page-link page-number" href="{{ $news->url($page) }}">{{ $page }}</a>
                                </li>
                            @endif
                        @endfor

                        @if($end < $news->lastPage())
                            @if($end < $news->lastPage() - 1)
                                <li class="page-item disabled"><span class="page-link page-dots">...</span></li>
                            @endif
                            <li class="page-item">
                                <a class="page-link page-number" href="{{ $news->url($news->lastPage()) }}">{{ $news->lastPage() }}</a>
                            </li>
                        @endif

                        {{-- 下一页 --}}
                        @if ($news->hasMorePages())
                            <li class="page-item">
                                <a class="page-link" href="{{ $news->nextPageUrl() }}">
                                    <span class="page-text">下一页</span>
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                        @else
                            <li class="page-item disabled">
                                <span class="page-link">
                                    <span class="page-text">下一页</span>
                                    <i class="fas fa-chevron-right"></i>
                                </span>
                            </li>
                        @endif
                    </ul>
                </nav>

                <div class="pagination-info">
                    <span class="pagination-text">
                        显示第 <strong>{{ $news->firstItem() ?? 0 }}</strong> 到 <strong>{{ $news->lastItem() ?? 0 }}</strong> 条
                        (共 <strong>{{ $news->total() }}</strong> 条记录)
                    </span>
                </div>
            </div>
            @endif
        </div>

        <!-- 侧边栏 -->
        <div class="col-lg-4">
            <div class="sidebar">
                <h4 class="sidebar-title">热门阅读</h4>
                @foreach($popularNews as $item)
                    <div class="sidebar-item mb-3">
                        <a href="{{ route('news.show', $item->slug) }}" class="d-flex">
                            <img src="{{ $item->cover_image ? asset('storage/'.$item->cover_image) : asset('images/default-news.jpg') }}" alt="{{ $item->title }}" style="width:70px;height:50px;object-fit:cover;border-radius:8px;box-shadow:0 2px 6px rgba(0,0,0,0.1);">
                            <div class="ms-3">
                                <p class="mb-1" style="font-size:14px;font-weight:600;color:#333;line-height:1.3;">{{ $item->title }}</p>
                                <small style="color:#999;font-size:12px;"><i class="far fa-eye me-1"></i>{{ $item->view_count }} 次阅读</small>
                            </div>
                        </a>
                    </div>
                @endforeach
            </div>

            <div class="sidebar">
                <h4 class="sidebar-title">热门标签</h4>
                <div class="tag-cloud">
                    @foreach($popularTags as $tag)
                        <a href="{{ route('news.index', array_merge(request()->except('page'), ['tag' => $tag->slug])) }}">
                            #{{ $tag->name }} <span style="opacity:.7;">({{ $tag->news_count }})</span>
                        </a>
                    @endforeach
                </div>
            </div>
        </div>
    </div>

    <!-- 推荐新闻区域 - 放在底部 -->
    @if($featuredNews->count())
    <div class="mt-4 pt-3 mb-5 pb-5" style="border-top: 1px solid #eee;">
        <div class="sidebar">
            <h4 class="sidebar-title">
                <i class="fas fa-star me-2" style="color: #ffc107; font-size: 14px;"></i>精选推荐
            </h4>
            @foreach($featuredNews as $item)
                <div class="sidebar-item mb-3">
                    <a href="{{ route('news.show', $item->slug) }}" class="d-flex">
                        <img src="{{ $item->cover_image ? asset('storage/'.$item->cover_image) : asset('images/default-news.jpg') }}" alt="{{ $item->title }}" style="width:70px;height:50px;object-fit:cover;border-radius:8px;box-shadow:0 2px 6px rgba(0,0,0,0.1);">
                        <div class="ms-3">
                            <p class="mb-1" style="font-size:14px;font-weight:600;color:#333;line-height:1.3;">{{ $item->title }}</p>
                            <small style="color:#999;font-size:12px;">
                                <span class="me-2">{{ $item->category->name }}</span>
                                <span>{{ $item->published_at->format('m-d') }}</span>
                            </small>
                        </div>
                    </a>
                </div>
            @endforeach
        </div>
    </div>
    @endif
</div>
@endsection

@section('custom-scripts')
<script>
    // 加载智能推荐新闻
    function loadRecommendedNews() {
        fetch('{{ route("news.recommended") }}')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.data.length > 0) {
                    let html = '';
                    data.data.forEach(item => {
                        html += `
                            <div class="col-md-6 col-lg-4 mb-4">
                                <div class="news-card">
                                    <div class="news-card-image" style="background-image: url('${item.cover_image ? '/storage/'+item.cover_image : '/images/default-news.jpg'}')"></div>
                                    <div class="news-card-content">
                                        <h3 class="news-card-title">${item.title}</h3>
                                        <div class="news-card-meta">
                                            <span class="category-badge">${item.category_name}</span>
                                            <span>${new Date(item.published_at).toLocaleDateString()}</span>
                                        </div>
                                    </div>
                                    <a href="/news/${item.slug}" class="stretched-link"></a>
                                </div>
                            </div>
                        `;
                    });
                    document.querySelector('.recommended-news-list').innerHTML = html;
                }
            })
            .catch(error => console.error('加载推荐新闻失败:', error));
    }
    
    document.addEventListener('DOMContentLoaded', function() {
        loadRecommendedNews();
    });
</script>
@endsection
