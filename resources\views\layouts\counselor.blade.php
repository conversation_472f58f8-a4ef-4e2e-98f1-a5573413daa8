<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@yield('title', '咨询师工作台') - 心理健康平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: "SF Pro Text", "SF Pro Icons", "Helvetica Neue", Helvetica, Arial, sans-serif;
        }
        .sidebar {
            position: fixed;
            top: 0;
            bottom: 0;
            left: 0;
            z-index: 100;
            padding: 48px 0 0;
            box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
            background: #fff;
        }
        .sidebar-sticky {
            height: calc(100vh - 48px);
            overflow-x: hidden;
            overflow-y: auto;
        }
        .nav-link {
            color: #333;
            font-weight: 500;
            padding: 15px 25px;
            display: flex;
            align-items: center;
        }
        .nav-link:hover {
            background: rgba(52, 152, 219, 0.1);
        }
        .nav-link.active {
            color: #3498db;
            background: rgba(52, 152, 219, 0.15);
            position: relative;
        }
        .nav-link.active::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: #3498db;
        }
        .nav-icon {
            margin-right: 10px;
            font-size: 18px;
        }
        .main-content {
            padding-top: 58px;
        }
    </style>
    @yield('custom-styles')
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-light bg-white fixed-top shadow-sm">
        <div class="container-fluid">
            <span class="navbar-brand">心理健康平台 - 咨询师工作台</span>
            <div class="d-flex">
                <div class="dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <span class="me-2">{{ session('counselor_name') }}</span>
                        <img src="{{ session('counselor_avatar') ? asset('storage/'.session('counselor_avatar')) : asset('images/default-avatar.jpg') }}" width="32" height="32" class="rounded-circle">
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                        <li>
                            <a href="{{ route('counselor.profile') }}" class="dropdown-item">
                                <i class="bi bi-person me-2"></i>个人资料
                            </a>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <form method="POST" action="{{ route('counselor.logout') }}">
                                @csrf
                                <button type="submit" class="dropdown-item">
                                    <i class="bi bi-box-arrow-right me-2"></i>退出登录
                                </button>
                            </form>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- 侧边导航 -->
            <nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block sidebar">
                <div class="position-sticky sidebar-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('counselor.dashboard') ? 'active' : '' }}" href="{{ route('counselor.dashboard') }}">
                                <i class="bi bi-house-door nav-icon"></i>
                                首页
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('counselor.appointments*') ? 'active' : '' }}" href="{{ route('counselor.appointments') }}">
                                <i class="bi bi-calendar-check nav-icon"></i>
                                预约管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('counselor.messages*') ? 'active' : '' }}" href="{{ route('counselor.messages') }}">
                                <i class="bi bi-chat-dots nav-icon"></i>
                                消息管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {{ request()->routeIs('counselor.profile*') ? 'active' : '' }}" href="{{ route('counselor.profile') }}">
                                <i class="bi bi-person nav-icon"></i>
                                个人资料
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主内容区 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                @yield('content')
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    @yield('scripts')
</body>
</html>
