<?php

namespace App\Http\Controllers\Counselor;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\Counselor;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;

class AuthController extends Controller
{
    /**
     * 显示咨询师登录页面
     */
    public function showLogin()
    {
        return view('counselor.auth.login');
    }
    
    /**
     * 处理咨询师登录请求
     */
    public function login(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'phone' => 'required|string',
            'password' => 'required|string',
        ]);
        
        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }
        
        $counselor = Counselor::where('phone', $request->phone)->first();
        
        if (!$counselor || !Hash::check($request->password, $counselor->password)) {
            return back()->withErrors([
                'login' => '手机号或密码不正确'
            ])->withInput();
        }
        
        if (!$counselor->is_active) {
            return back()->withErrors([
                'login' => '您的账号未激活，请联系管理员'
            ])->withInput();
        }
        
        // 记录登录信息到会话
        $request->session()->put('counselor_id', $counselor->id);
        $request->session()->put('counselor_name', $counselor->name);
        $request->session()->put('counselor_avatar', $counselor->avatar);
        
        return redirect()->route('counselor.dashboard');
    }
    
    /**
     * 咨询师登出
     */
    public function logout(Request $request)
    {
        $request->session()->forget([
            'counselor_id',
            'counselor_name',
            'counselor_avatar'
        ]);
        
        return redirect()->route('counselor.login');
    }
}
