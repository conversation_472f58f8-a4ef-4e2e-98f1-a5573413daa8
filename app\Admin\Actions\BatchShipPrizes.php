<?php

namespace App\Admin\Actions;

use App\Models\QuizPrizeWinner;
use Dcat\Admin\Grid\BatchAction;
use Illuminate\Http\Request;
use Illuminate\Database\Eloquent\Collection;
use Dcat\Admin\Admin;

class BatchShipPrizes extends BatchAction
{
    protected $title = '批量发货';
    
    // 按照Dcat Admin文档修改方法签名
    public function handle(Request $request)
    {
        // 获取选中的行的ID数组
        $keys = $this->getKey();
        
        // 查询所有选中的数据
        $models = QuizPrizeWinner::find($keys);
        
        $count = 0;
        
        foreach ($models as $model) {
            // 只处理申领中或已申领的奖品
            if (in_array($model->status, ['pending', 'claimed'])) {
                $model->status = 'shipped';
                $model->admin_notes = ($model->admin_notes ? $model->admin_notes . "\n" : '') . 
                                       Admin::user()->name . ' 于 ' . now() . ' 标记为已发货';
                $model->save();
                $count++;
            }
        }
        
        return $this->response()
                    ->success("成功发货 {$count} 件奖品")
                    ->refresh();
    }
    
    public function confirm()
    {
        return '确定要将选中的奖品标记为已发货吗？';
    }
}
