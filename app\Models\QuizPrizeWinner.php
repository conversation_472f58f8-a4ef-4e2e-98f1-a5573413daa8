<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class QuizPrizeWinner extends Model
{
    use HasFactory, HasDateTimeFormatter;

    protected $fillable = [
        'quiz_activity_id',
        'prize_id',
        'user_id',
        'attempt_id',
        'winner_name',
        'contact_info',
        'shipping_address',
        'status',
        'admin_notes',
        'claimed_at',
    ];

    protected $casts = [
        'claimed_at' => 'datetime',
    ];

    // 关联问答活动
    public function quizActivity()
    {
        return $this->belongsTo(QuizActivity::class);
    }

    // 关联奖品
    public function prize()
    {
        return $this->belongsTo(QuizPrize::class);
    }

    // 关联用户
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    // 关联答题尝试
    public function attempt()
    {
        return $this->belongsTo(QuizAttempt::class);
    }

    // 标记为已领取
    public function markAsClaimed()
    {
        return $this->update([
            'status' => 'shipped',
            'claimed_at' => now(),
        ]);
    }

    // 标记为已送达
    public function markAsDelivered()
    {
        return $this->update([
            'status' => 'delivered',
        ]);
    }

    // 取消奖品
    public function cancel($reason = null)
    {
        return $this->update([
            'status' => 'cancelled',
            'admin_notes' => $reason,
        ]);
    }
}
