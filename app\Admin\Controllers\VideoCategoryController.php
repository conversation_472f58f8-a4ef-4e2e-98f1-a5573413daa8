<?php

namespace App\Admin\Controllers;

use App\Models\VideoCategory;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class VideoCategoryController extends AdminController
{
    /**
     * 页面标题
     *
     * @return string
     */
    protected function title()
    {
        return '视频分类管理';
    }

    /**
     * 表格构建
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new VideoCategory(), function (Grid $grid) {
            $grid->column('id', 'ID')->sortable();
            $grid->column('name', '分类名称');
            $grid->column('description', '分类描述')->limit(30);
            $grid->column('sort', '排序')->sortable();
            $grid->column('is_active', '状态')->switch();
            $grid->column('created_at', '创建时间')->sortable();
            $grid->column('updated_at', '更新时间')->sortable();
            
            // 快速搜索
            $grid->quickSearch('name', 'description');
            
            // 筛选器
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id', 'ID');
                $filter->like('name', '分类名称');
                $filter->equal('is_active', '状态')->select([
                    0 => '禁用',
                    1 => '启用',
                ]);
            });
            
            // 默认排序
            $grid->model()->orderBy('sort', 'desc');
            
            // 操作设置
            $grid->disableViewButton();
            
            // 启用弹窗编辑功能
            $grid->showQuickEditButton();
            
            // 正确的弹窗实现方式
            $grid->enableDialogCreate();
        });
    }

    /**
     * 详情页构建
     *
     * @param mixed $id
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new VideoCategory(), function (Show $show) {
            $show->field('id', 'ID');
            $show->field('name', '分类名称');
            $show->field('description', '分类描述');
            $show->field('sort', '排序');
            $show->field('is_active', '状态')->as(function ($is_active) {
                return $is_active ? '启用' : '禁用';
            });
            $show->field('created_at', '创建时间');
            $show->field('updated_at', '更新时间');
        });
    }

    /**
     * 表单构建
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new VideoCategory(), function (Form $form) {
            $form->display('id', 'ID');
            $form->text('name', '分类名称')->required()->rules('required|max:50');
            $form->textarea('description', '分类描述')->rows(3)->rules('nullable|max:255');
            $form->number('sort', '排序')->default(0)->help('数字越大越靠前');
            $form->switch('is_active', '状态')->default(1);
            
            $form->display('created_at', '创建时间');
            $form->display('updated_at', '更新时间');
        });
    }
}
