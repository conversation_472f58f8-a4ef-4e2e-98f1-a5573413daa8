@extends('layouts.app')

@section('title', $quiz->title)

@section('custom-styles')
<link rel="stylesheet" href="{{ asset('css/quiz.css') }}">
<style>
    /* 页面特定样式 */
    
    .quiz-cover {
        width: 100%;
        height: 180px;
        border-radius: 12px;
        overflow: hidden;
        margin-bottom: 20px;
        box-shadow: 0 3px 10px rgba(0,0,0,0.1);
    }
    
    .quiz-cover img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    
    .quiz-title {
        font-size: 20px;
        font-weight: 600;
        margin-bottom: 10px;
        color: #333;
    }
    
    .quiz-meta {
        display: flex;
        flex-wrap: wrap;
        margin-bottom: 15px;
        font-size: 14px;
        color: #666;
    }
    
    .quiz-meta-item {
        display: flex;
        align-items: center;
        margin-right: 15px;
        margin-bottom: 5px;
    }
    
    .quiz-meta-icon {
        margin-right: 5px;
        color: #3c67e3;
    }
    
    .quiz-description {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 20px;
        font-size: 14px;
        line-height: 1.6;
        color: #555;
        border-left: 4px solid #3c67e3;
    }
    
    .section-title {
        font-size: 17px;
        font-weight: 500;
        margin-bottom: 15px;
        color: #333;
        display: flex;
        align-items: center;
    }
    
    .section-title-icon {
        margin-right: 8px;
        color: #3c67e3;
    }
    
    .prize-list {
        margin-bottom: 20px;
    }
    
    .prize-item {
        background: white;
        border-radius: 10px;
        padding: 12px;
        margin-bottom: 10px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        display: flex;
        align-items: center;
        border: 1px solid #f0f0f0;
    }
    
    .prize-image {
        width: 60px;
        height: 60px;
        border-radius: 8px;
        overflow: hidden;
        margin-right: 12px;
    }
    
    .prize-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    
    .prize-info {
        flex: 1;
    }
    
    .prize-name {
        font-size: 15px;
        font-weight: 500;
        margin-bottom: 3px;
        color: #333;
    }
    
    .prize-description {
        font-size: 12px;
        color: #666;
        margin-bottom: 3px;
    }
    
    .prize-meta {
        display: flex;
        align-items: center;
        font-size: 12px;
        color: #999;
    }
    
    .prize-meta-item {
        margin-right: 10px;
        display: flex;
        align-items: center;
    }
    
    .rules-list {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 20px;
    }
    
    .rule-item {
        margin-bottom: 8px;
        font-size: 14px;
        display: flex;
        color: #555;
    }
    
    .rule-number {
        color: #3c67e3;
        font-weight: 600;
        margin-right: 8px;
        width: 20px;
        flex-shrink: 0;
    }
    
    .action-button {
        display: block;
        width: 100%;
        padding: 12px;
        text-align: center;
        border-radius: 8px;
        margin-bottom: 10px;
        font-size: 16px;
        font-weight: 500;
        text-decoration: none;
        transition: all 0.3s ease;
    }
    
    .primary-button {
        background-color: #3c67e3;
        color: white;
        box-shadow: 0 3px 6px rgba(60, 103, 227, 0.2);
    }
    
    .primary-button:hover {
        background-color: #3559c7;
        color: white;
    }
    
    .secondary-button {
        background-color: #f0f4fe;
        color: #3c67e3;
        border: 1px solid #d8e3fc;
    }
    
    .secondary-button:hover {
        background-color: #e6ecfd;
        color: #3c67e3;
    }
    
    .disabled-button {
        background-color: #e9ecef;
        color: #868e96;
        cursor: not-allowed;
    }
    
    .user-attempts {
        margin-bottom: 20px;
    }
    
    .attempt-item {
        background: white;
        border-radius: 10px;
        padding: 12px;
        margin-bottom: 10px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        border: 1px solid #f0f0f0;
    }
    
    .attempt-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
    }
    
    .attempt-date {
        font-size: 12px;
        color: #999;
    }
    
    .attempt-status {
        font-size: 12px;
        padding: 3px 8px;
        border-radius: 12px;
        color: white;
    }
    
    .status-pass {
        background-color: #28a745;
    }
    
    .status-fail {
        background-color: #dc3545;
    }
    
    .attempt-details {
        display: flex;
        align-items: center;
        font-size: 14px;
    }
    
    .attempt-score {
        font-weight: 500;
        color: #333;
        margin-right: 10px;
    }
    
    .attempt-prize {
        margin-top: 8px;
        background-color: #fff8e5;
        padding: 8px;
        border-radius: 6px;
        font-size: 13px;
        color: #d4a017;
    }
    
    .countdown-container {
        margin-bottom: 20px;
        padding: 15px;
        background-color: #f0f4fe;
        border-radius: 10px;
        text-align: center;
    }
    
    .countdown-title {
        font-size: 15px;
        font-weight: 500;
        margin-bottom: 10px;
        color: #3c67e3;
    }
    
    .countdown-timer {
        display: flex;
        justify-content: center;
        gap: 10px;
    }
    
    .countdown-part {
        display: flex;
        flex-direction: column;
        align-items: center;
    }
    
    .countdown-value {
        background-color: #fff;
        width: 45px;
        height: 45px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        font-weight: 600;
        color: #3c67e3;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    
    .countdown-label {
        font-size: 12px;
        color: #3c67e3;
        margin-top: 5px;
    }
    
    .countdown-separator {
        font-size: 20px;
        font-weight: 600;
        color: #3c67e3;
        line-height: 45px;
    }
</style>
@endsection

@section('content')
<div class="quiz-header">
    <div style="width: 30px;">
        <a href="{{ route('quiz.index') }}" class="quiz-back-button">
            <div class="quiz-back-icon"></div>
        </a>
    </div>
    <h1>问答详情</h1>
    <div style="width: 30px;"></div>
</div>

<div class="quiz-content quiz-fade-in">
    @if(session('success'))
    <div class="alert alert-success" style="background-color: #e7f3ee; color: #1a7348; border-left: 4px solid #28a745; border-radius: 8px; padding: 12px 15px; margin-bottom: 15px; font-size: 15px; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
        <i class="fas fa-check-circle" style="margin-right: 8px;"></i> {{ session('success') }}
    </div>
    @endif

    @if(session('error'))
    <div class="alert alert-danger" style="background-color: #f8eaed; color: #a12a43; border-left: 4px solid #dc3545; border-radius: 8px; padding: 12px 15px; margin-bottom: 15px; font-size: 15px; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
        <i class="fas fa-exclamation-circle" style="margin-right: 8px;"></i> {{ session('error') }}
    </div>
    @endif

    <!-- 问答活动封面 -->
    <div class="quiz-cover">
        <img src="{{ $quiz->cover_image ? asset('storage/'.$quiz->cover_image) : asset('images/default-quiz.jpg') }}" alt="{{ $quiz->title }}">
    </div>
    
    <!-- 问答活动标题和信息 -->
    <h1 class="quiz-title">{{ $quiz->title }}</h1>
    
    <div class="quiz-meta">
        <div class="quiz-meta-item">
            <i class="far fa-calendar-alt quiz-meta-icon"></i>
            {{ $quiz->start_time->format('Y-m-d H:i') }} ~ {{ $quiz->end_time->format('Y-m-d H:i') }}
        </div>
        <div class="quiz-meta-item">
            <i class="fas fa-users quiz-meta-icon"></i>
            {{ $quiz->attempts()->where('is_completed', true)->count() }}人参与
        </div>
        <div class="quiz-meta-item">
            <i class="fas fa-trophy quiz-meta-icon"></i>
            及格分数：{{ $quiz->pass_score }}
        </div>
    </div>
    
    <!-- 活动状态和倒计时 -->
    @if(!$quiz->isOngoing() && $quiz->start_time->isFuture())
        <div class="countdown-container">
            <div class="countdown-title">距离活动开始还剩</div>
            <div class="countdown-timer" id="startCountdown" data-target="{{ $quiz->start_time->timestamp }}">
                <div class="countdown-part">
                    <div class="countdown-value" id="days">0</div>
                    <div class="countdown-label">天</div>
                </div>
                <div class="countdown-separator">:</div>
                <div class="countdown-part">
                    <div class="countdown-value" id="hours">0</div>
                    <div class="countdown-label">时</div>
                </div>
                <div class="countdown-separator">:</div>
                <div class="countdown-part">
                    <div class="countdown-value" id="minutes">0</div>
                    <div class="countdown-label">分</div>
                </div>
                <div class="countdown-separator">:</div>
                <div class="countdown-part">
                    <div class="countdown-value" id="seconds">0</div>
                    <div class="countdown-label">秒</div>
                </div>
            </div>
        </div>
    @elseif($quiz->isOngoing())
        <div class="countdown-container">
            <div class="countdown-title">距离活动结束还剩</div>
            <div class="countdown-timer" id="endCountdown" data-target="{{ $quiz->end_time->timestamp }}">
                <div class="countdown-part">
                    <div class="countdown-value" id="days">0</div>
                    <div class="countdown-label">天</div>
                </div>
                <div class="countdown-separator">:</div>
                <div class="countdown-part">
                    <div class="countdown-value" id="hours">0</div>
                    <div class="countdown-label">时</div>
                </div>
                <div class="countdown-separator">:</div>
                <div class="countdown-part">
                    <div class="countdown-value" id="minutes">0</div>
                    <div class="countdown-label">分</div>
                </div>
                <div class="countdown-separator">:</div>
                <div class="countdown-part">
                    <div class="countdown-value" id="seconds">0</div>
                    <div class="countdown-label">秒</div>
                </div>
            </div>
        </div>
    @endif
    
    <!-- 问答活动描述 -->
    <div class="quiz-description">
        {{ $quiz->description }}
    </div>
    
    <!-- 奖品列表 -->
    @if($quiz->prizes->isNotEmpty())
        <h2 class="section-title">
            <i class="fas fa-gift section-title-icon"></i> 活动奖品
        </h2>
        
        <div class="prize-list">
            @foreach($quiz->prizes as $prize)
            <div class="prize-item">
                <div class="prize-image">
                    <img src="{{ $prize->image ? asset('storage/'.$prize->image) : asset('images/default-prize.jpg') }}" alt="{{ $prize->name }}">
                </div>
                <div class="prize-info">
                    <h3 class="prize-name">{{ $prize->name }}</h3>
                    @if($prize->description)
                    <p class="prize-description">{{ Str::limit($prize->description, 50) }}</p>
                    @endif
                    <div class="prize-meta">
                        <div class="prize-meta-item">
                            <i class="fas fa-medal" style="margin-right: 3px; color: #f0ad4e;"></i> 
                            最低分数: {{ $prize->min_score }}
                        </div>
                        <div class="prize-meta-item">
                            <i class="fas fa-cubes" style="margin-right: 3px; color: #5bc0de;"></i> 
                            剩余: {{ $prize->remainingQuantity() }}/{{ $prize->quantity }}
                        </div>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    @endif
    
    <!-- 活动规则 -->
    <h2 class="section-title">
        <i class="fas fa-info-circle section-title-icon"></i> 活动规则
    </h2>
    
    <div class="rules-list">
        <div class="rule-item">
            <div class="rule-number">1.</div>
            <div>参与问答活动需要登录账号，活动期间每人最多可参与{{ $quiz->max_attempts }}次</div>
        </div>
        <div class="rule-item">
            <div class="rule-number">2.</div>
            <div>答题过程中请勿关闭页面或刷新页面，否则本次答题将无效</div>
        </div>
        <div class="rule-item">
            <div class="rule-number">3.</div>
            <div>得分达到{{ $quiz->pass_score }}分或以上可以获得奖品，奖品数量有限，先到先得</div>
        </div>
        <div class="rule-item">
            <div class="rule-number">4.</div>
            <div>获得奖品后请及时填写联系方式和收货地址，逾期未填写将视为放弃奖品</div>
        </div>
        <div class="rule-item">
            <div class="rule-number">5.</div>
            <div>本活动最终解释权归平台所有</div>
        </div>
    </div>
    
    <!-- 我的参与记录 -->
    @if(Auth::check() && $userAttempts->isNotEmpty())
        <h2 class="section-title">
            <i class="fas fa-history section-title-icon"></i> 我的参与记录
        </h2>
        
        <div class="user-attempts">
            @foreach($userAttempts as $attempt)
            <div class="attempt-item">
                <div class="attempt-header">
                    <div class="attempt-date">{{ $attempt->completed_at->format('Y-m-d H:i') }}</div>
                    <div class="attempt-status {{ ($attempt->correct_count / $attempt->total_questions) * 100 >= $quiz->pass_score ? 'status-pass' : 'status-fail' }}">
                        {{ ($attempt->correct_count / $attempt->total_questions) * 100 >= $quiz->pass_score ? '答题及格' : '答题未及格' }}
                    </div>
                </div>
                <div class="attempt-details">
                    <div class="attempt-score">得分: {{ $attempt->score }}/{{ $attempt->total_questions }}</div>
                    <div>正确: {{ $attempt->correct_count }}/{{ $attempt->total_questions }}</div>
                </div>
                
                @if($attempt->has_won_prize && $attempt->prizeWinner)
                <div class="attempt-prize">
                    <i class="fas fa-crown" style="margin-right: 5px;"></i>
                    获得奖品: {{ $attempt->prizeWinner->prize->name }}
                    @if($attempt->prizeWinner->status === 'unclaimed')
                    <a href="{{ route('quiz.claim_prize', ['id' => $quiz->id, 'winner_id' => $attempt->prizeWinner->id]) }}" style="float: right; color: #d4a017; text-decoration: underline;">去领取</a>
                    @else
                    <span style="float: right;">
                        @if($attempt->prizeWinner->status == 'pending')
                            处理中
                        @elseif($attempt->prizeWinner->status == 'claimed')
                            已领取
                        @elseif($attempt->prizeWinner->status == 'shipped')
                            已发货
                        @elseif($attempt->prizeWinner->status == 'delivered')
                            已送达
                        @else
                            {{ $attempt->prizeWinner->status }}
                        @endif
                    </span>
                    @endif
                </div>
                @endif
                
                <div style="margin-top: 10px;">
                    <a href="{{ route('quiz.result', ['id' => $quiz->id, 'attempt_id' => $attempt->id]) }}" class="quiz-btn quiz-btn-outline" style="margin-bottom: 0;">查看详情</a>
                </div>
            </div>
            @endforeach
        </div>
    @endif
    
    <!-- 参与按钮 -->
    <div style="margin-top: 20px;">
        @if(!Auth::check())
            <a href="{{ route('login') }}" class="quiz-btn quiz-btn-primary">登录后参与问答</a>
        @elseif(!$quiz->isOngoing())
            @if($quiz->start_time->isFuture())
                <button class="quiz-btn" style="background-color: #e9ecef; color: #6c757d; cursor: not-allowed;">活动未开始</button>
            @else
                <button class="quiz-btn" style="background-color: #e9ecef; color: #6c757d; cursor: not-allowed;">活动已结束</button>
            @endif
        @elseif(!$canAttempt)
            <button class="quiz-btn" style="background-color: #e9ecef; color: #6c757d; cursor: not-allowed;">已达到最大参与次数</button>
        @elseif(!$quiz->hasAvailablePrizes())
            <button class="quiz-btn" style="background-color: #e9ecef; color: #6c757d; cursor: not-allowed;">奖品已发完</button>
        @else
            <a href="{{ route('quiz.start', $quiz->id) }}" class="quiz-btn quiz-btn-primary quiz-pulse">立即参与</a>
        @endif
        
        <a href="{{ route('quiz.my_prizes') }}" class="quiz-btn quiz-btn-secondary">我的奖品</a>
        
        <!-- 添加底部空间，避免被底部菜单遮挡 -->
        <div class="quiz-bottom-space"></div>
    </div>
</div>

@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 倒计时功能
        function updateCountdown(elementId) {
            const countdownElement = document.getElementById(elementId);
            if (!countdownElement) return;
            
            const targetTimestamp = parseInt(countdownElement.getAttribute('data-target'));
            const now = Math.floor(Date.now() / 1000);
            
            const secondsRemaining = targetTimestamp - now;
            
            if (secondsRemaining <= 0) {
                // 倒计时结束，刷新页面
                location.reload();
                return;
            }
            
            const days = Math.floor(secondsRemaining / 86400);
            const hours = Math.floor((secondsRemaining % 86400) / 3600);
            const minutes = Math.floor((secondsRemaining % 3600) / 60);
            const seconds = secondsRemaining % 60;
            
            countdownElement.querySelector('#days').textContent = days;
            countdownElement.querySelector('#hours').textContent = hours < 10 ? '0' + hours : hours;
            countdownElement.querySelector('#minutes').textContent = minutes < 10 ? '0' + minutes : minutes;
            countdownElement.querySelector('#seconds').textContent = seconds < 10 ? '0' + seconds : seconds;
        }
        
        const startCountdown = document.getElementById('startCountdown');
        const endCountdown = document.getElementById('endCountdown');
        
        if (startCountdown) {
            updateCountdown('startCountdown');
            setInterval(function() { updateCountdown('startCountdown'); }, 1000);
        }
        
        if (endCountdown) {
            updateCountdown('endCountdown');
            setInterval(function() { updateCountdown('endCountdown'); }, 1000);
        }
    });
</script>
@endsection
