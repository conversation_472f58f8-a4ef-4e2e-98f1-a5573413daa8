<?php

namespace App\Http\Controllers\Counselor;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Message;
use App\Models\User;
use App\Models\ConsultationAppointment;
use Carbon\Carbon;
use Illuminate\Support\Facades\Validator;

class MessageController extends Controller
{
    /**
     * 构造函数
     */
    public function __construct()
    {
        $this->middleware('counselor.auth');
    }
    
    /**
     * 显示消息列表
     */
    public function index(Request $request)
    {
        $counselorId = $request->session()->get('counselor_id');
        
        // 获取与咨询师有消息交流的用户ID
        $userIds = Message::where('counselor_id', $counselorId)
            ->select('user_id')
            ->distinct()
            ->pluck('user_id')
            ->toArray();
            
        // 获取与咨询师有预约的用户ID
        $appointmentUserIds = ConsultationAppointment::where('counselor_id', $counselorId)
            ->whereIn('status', [2, 3, 4]) // 只包括已确认、进行中和已完成的预约
            ->select('user_id')
            ->distinct()
            ->pluck('user_id')
            ->toArray();
            
        // 合并用户ID（避免重复）
        $allUserIds = array_unique(array_merge($userIds, $appointmentUserIds));
        
        // 获取这些用户的信息
        $users = User::whereIn('id', $allUserIds)->get();
        
        // 获取每个用户的未读消息数量和最后一条消息
        $unreadCounts = [];
        $lastMessages = [];
        
        foreach ($users as $user) {
            $unreadCounts[$user->id] = Message::where('counselor_id', $counselorId)
                ->where('user_id', $user->id)
                ->where('sender_type', Message::SENDER_USER)
                ->where('is_read', false)
                ->count();
                
            $lastMessage = Message::where('counselor_id', $counselorId)
                ->where('user_id', $user->id)
                ->orderBy('created_at', 'desc')
                ->first();
                
            $lastMessages[$user->id] = $lastMessage;
        }
        
        // 获取每个用户最近的预约
        $appointments = [];
        foreach ($users as $user) {
            $appointment = ConsultationAppointment::where('counselor_id', $counselorId)
                ->where('user_id', $user->id)
                ->whereIn('status', [2, 3]) // 已确认或进行中
                ->orderBy('appointment_time')
                ->first();
                
            $appointments[$user->id] = $appointment;
        }
        
        return view('counselor.messages.index', [
            'users' => $users,
            'unreadCounts' => $unreadCounts,
            'lastMessages' => $lastMessages,
            'appointments' => $appointments
        ]);
    }
    
    /**
     * 显示与特定用户的聊天页面
     */
    public function show(Request $request, $userId)
    {
        $counselorId = $request->session()->get('counselor_id');
        
        // 获取用户信息
        $user = User::findOrFail($userId);
        
        // 获取与该用户关联的预约
        $appointment = ConsultationAppointment::where('counselor_id', $counselorId)
            ->where('user_id', $userId)
            ->whereIn('status', [2, 3, 4]) // 只显示已确认、进行中或已完成的预约
            ->orderBy('appointment_time', 'desc')
            ->first();
        
        // 获取聊天记录
        $messages = Message::where('counselor_id', $counselorId)
            ->where('user_id', $userId)
            ->orderBy('created_at', 'desc')
            ->paginate(30);
            
        // 将未读消息标记为已读
        Message::where('counselor_id', $counselorId)
            ->where('user_id', $userId)
            ->where('sender_type', Message::SENDER_USER)
            ->where('is_read', false)
            ->update(['is_read' => true, 'read_at' => now()]);
            
        return view('counselor.messages.chat', [
            'user' => $user,
            'messages' => $messages,
            'appointment' => $appointment
        ]);
    }
    
    /**
     * 发送消息
     */
    public function send(Request $request, $userId)
    {
        $counselorId = $request->session()->get('counselor_id');
        
        $validator = Validator::make($request->all(), [
            'content' => 'required|string|max:2000',
            'appointment_id' => 'nullable|exists:consultation_appointments,id',
        ]);
        
        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }
        
        // 验证用户是否存在
        $user = User::findOrFail($userId);
        
        // 如果提供了预约ID，验证是否有效
        if ($request->appointment_id) {
            $appointment = ConsultationAppointment::where('id', $request->appointment_id)
                ->where('user_id', $userId)
                ->where('counselor_id', $counselorId)
                ->whereIn('status', [2, 3, 4])
                ->first();
                
            if (!$appointment) {
                return response()->json([
                    'success' => false,
                    'message' => '无效的预约ID'
                ], 400);
            }
        }
        
        // 创建消息
        $message = new Message();
        $message->user_id = $userId;
        $message->counselor_id = $counselorId;
        $message->appointment_id = $request->appointment_id;
        $message->sender_type = Message::SENDER_COUNSELOR;
        $message->content = $request->content;
        $message->is_read = false;
        $message->save();
        
        // 返回新消息
        return response()->json([
            'success' => true,
            'message' => $message,
            'formatted_time' => Carbon::parse($message->created_at)->format('H:i')
        ]);
    }
    
    /**
     * 获取未读消息数
     */
    public function getUnreadCount(Request $request)
    {
        $counselorId = $request->session()->get('counselor_id');
        
        $unreadCount = Message::where('counselor_id', $counselorId)
            ->where('sender_type', Message::SENDER_USER)
            ->where('is_read', false)
            ->count();
            
        return response()->json([
            'unread_count' => $unreadCount
        ]);
    }
    
    /**
     * 检查是否有新消息
     */
    public function checkNewMessages(Request $request, $userId)
    {
        $counselorId = $request->session()->get('counselor_id');
        
        // 获取客户端发送的最后看到的ID，或使用会话存储
        $lastKnownId = $request->input('last_id', 0);
        
        if (empty($lastKnownId)) {
            // 如果没有提供或提供的倒0，尝试使用会话存储
            $lastMessageIdKey = 'counselor_last_checked_id_'.$counselorId.'_'.$userId;
            $lastKnownId = $request->session()->get($lastMessageIdKey, 0);
        }
        
        // 如果还是没有ID，查找第一条消息
        if (empty($lastKnownId)) {
            $firstMessage = Message::where('user_id', $userId)
                ->where('counselor_id', $counselorId)
                ->orderBy('id')
                ->first();
                
            if ($firstMessage) {
                // 如果有消息，则从第一条消息的ID开始
                $lastKnownId = $firstMessage->id - 1; // 前一条，这样可以获取到第一条消息
            }
        }
        
        // 查询新消息 - 只获取用户发送的新消息
        $newMessages = Message::where('user_id', $userId)
            ->where('counselor_id', $counselorId)
            ->where('sender_type', Message::SENDER_USER)
            ->where('id', '>', $lastKnownId)
            ->orderBy('id')
            ->get();
            
        // 更新最后看到的消息ID(保存到会话)
        if ($newMessages->isNotEmpty()) {
            $lastNewId = $newMessages->last()->id;
            $lastMessageIdKey = 'counselor_last_checked_id_'.$counselorId.'_'.$userId;
            $request->session()->put($lastMessageIdKey, $lastNewId);
        }
            
        // 构建返回数据
        $formattedMessages = [];
        foreach ($newMessages as $message) {
            $formattedMessages[] = [
                'id' => $message->id,
                'content' => $message->content,
                'created_at' => $message->created_at->format('Y-m-d H:i:s'),
                'formatted_time' => Carbon::parse($message->created_at)->format('H:i'),
                'is_read' => $message->is_read,
                'sender_type' => $message->sender_type
            ];
            
            // 标记为已读
            if (!$message->is_read) {
                $message->is_read = true;
                $message->read_at = now();
                $message->save();
            }
        }
        
        return response()->json([
            'has_new' => count($newMessages) > 0,
            'messages' => $formattedMessages,
            'debug' => [
                'last_known_id' => $lastKnownId,
                'new_messages_count' => count($newMessages),
                'first_message_id' => $newMessages->isNotEmpty() ? $newMessages->first()->id : null,
                'last_message_id' => $newMessages->isNotEmpty() ? $newMessages->last()->id : null
            ]
        ]);
    }
}
