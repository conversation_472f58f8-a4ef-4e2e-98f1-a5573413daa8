<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Counselor;
use App\Models\CounselorSchedule;
use App\Models\ConsultationAppointment;
use App\Models\OfflineCourse;
use App\Models\AiConsultationRecord;
use App\Models\AiConsultationSetting;
use App\Models\CourseRegistration;
use App\Services\AiConsultationRecommendationService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Validator;

class ConsultationController extends Controller
{
    private AiConsultationRecommendationService $recommendationService;

    public function __construct(AiConsultationRecommendationService $recommendationService)
    {
        $this->recommendationService = $recommendationService;
    }

    /**
     * 显示咨询师列表
     */
    public function counselors(Request $request)
    {
        $query = Counselor::query();

        // 筛选专业领域
        if ($request->has('expertise') && !empty($request->expertise)) {
            $query->where('expertise', 'like', '%' . $request->expertise . '%');
        }

        // 确保只显示有效的咨询师
        $query->where('is_active', 1);

        // 分页并获取咨询师列表
        $counselors = $query->paginate(10);

        return view('pages.consultation.counselors', [
            'counselors' => $counselors
        ]);
    }

    /**
     * 咨询师详情和预约页面
     */
    public function counselorDetail($id)
    {
        $counselor = Counselor::findOrFail($id);
        $completedAppointments = ConsultationAppointment::where(['counselor_id'=>$id,'status'=>4])->count();

        // 只能查看活跃的咨询师
        if (!$counselor->is_active) {
            return redirect()->route('consultation.counselors')
                ->with('error', '该咨询师暂不提供服务');
        }

        // 获取未来7天的日期
        $dates = [];
        $startDate = Carbon::today();
        for ($i = 0; $i < 7; $i++) {
            $date = $startDate->copy()->addDays($i);

            // 获取当天的可用排班
            $schedules = $counselor->schedules()
                ->where('date', $date->format('Y-m-d'))
                ->get();

            $dates[] = [
                'date' => $date->format('Y-m-d'),
                'day' => $date->format('m-d'),
                'weekday' => $this->getChineseWeekday($date),
                'schedules' => $schedules
            ];
        }

        return view('pages.consultation.counselor_detail', [
            'counselor' => $counselor,
            'dates' => $dates,
            'completedAppointments' => $completedAppointments
        ]);
    }

    /**
     * 创建咨询预约
     */
    public function createAppointment(Request $request)
    {
        // 验证输入
        $validated = $request->validate([
            'counselor_id' => 'required|exists:counselors,id',
            'schedule_id' => 'required|exists:counselor_schedules,id',
            'consultation_type' => 'required|in:1,2,3',
            'issue_description' => 'required|string|min:10|max:500',
        ]);

        // 检查时间段是否可用
        $schedule = CounselorSchedule::findOrFail($validated['schedule_id']);
        if (!$schedule->is_available) {
            return back()->with('error', '该时间段已不可预约');
        }

        // 检查咨询师是否支持该咨询类型
        $counselor = Counselor::findOrFail($validated['counselor_id']);
        if (
            ($validated['consultation_type'] == 1 && !$counselor->support_text) ||
            ($validated['consultation_type'] == 2 && !$counselor->support_voice) ||
            ($validated['consultation_type'] == 3 && !$counselor->support_video)
        ) {
            return back()->with('error', '该咨询师不支持所选咨询方式');
        }

        try {
            // 创建预约
            $appointment = new ConsultationAppointment();
            $appointment->user_id = Auth::id();
            $appointment->counselor_id = $validated['counselor_id'];
            $appointment->schedule_id = $validated['schedule_id'];
            $appointment->consultation_type = $validated['consultation_type'];
            $appointment->issue_description = $validated['issue_description'];
            // 使用时间字符串而非对象，避免日期数据类型转换问题
            $timeString = date('H:i:s', strtotime($schedule->start_time));
            $dateString = date('Y-m-d', strtotime($schedule->date));
            $appointment->appointment_time = $dateString . ' ' . $timeString;
            $appointment->duration = 50; // 默认50分钟
            $appointment->price = $counselor->price;
            $appointment->status = ConsultationAppointment::STATUS_PENDING;
            $appointment->save();

            // 将时间段标记为不可用
            $schedule->is_available = false;
            $schedule->save();

            return redirect()->route('consultation.my_appointments')
                ->with('success', '预约申请已提交，请等待咨询师确认');
        } catch (\Exception $e) {
            Log::error('预约创建失败: ' . $e->getMessage());
            return back()->with('error', '预约失败，请稍后再试');
        }
    }

    /**
     * 显示用户的所有预约
     */
    public function myAppointments()
    {
        $appointments = ConsultationAppointment::with('counselor')
            ->where('user_id', Auth::id())
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('pages.consultation.my_appointments', [
            'appointments' => $appointments
        ]);
    }

    /**
     * 取消预约
     */
    public function cancelAppointment($id)
    {
        $appointment = ConsultationAppointment::where('id', $id)
            ->where('user_id', Auth::id())
            ->firstOrFail();

        // 只能取消待确认或已确认且未开始的预约
        if (!$appointment->canCancel()) {
            return back()->with('error', '该预约无法取消');
        }

        try {
            // 将预约状态改为已取消
            $appointment->status = ConsultationAppointment::STATUS_CANCELLED;
            $appointment->cancelled_at = Carbon::now();
            $appointment->save();

            // 如果有绑定的排班，将其恢复为可预约
            if ($appointment->schedule_id) {
                $schedule = CounselorSchedule::find($appointment->schedule_id);
                if ($schedule) {
                    $schedule->is_available = true;
                    $schedule->save();
                }
            }

            return back()->with('success', '预约已成功取消');
        } catch (\Exception $e) {
            Log::error('取消预约失败: ' . $e->getMessage());
            return back()->with('error', '取消预约失败，请稍后再试');
        }
    }

    /**
     * 开始咨询会话
     */
    public function startConsultation($id)
    {
        $appointment = ConsultationAppointment::where('id', $id)
            ->where('user_id', Auth::id())
            ->firstOrFail();

        // 只能开始已确认的预约
        if ($appointment->status != ConsultationAppointment::STATUS_CONFIRMED) {
            return back()->with('error', '该预约尚未确认，无法开始咨询');
        }

        // 检查预约时间是否到了
        $appointmentTime = Carbon::parse($appointment->appointment_time);
        $now = Carbon::now();
        $timeDiff = $now->diffInMinutes($appointmentTime, false);

        // 提前10分钟可以进入咨询室
        if ($timeDiff > 10) {
            return back()->with('error', '距离预约时间还有' . $timeDiff . '分钟，请在预约时间前10分钟进入');
        }

        // 如果预约已过期
        if ($timeDiff < -60) {
            return back()->with('error', '该预约已过期');
        }

        // 更新预约状态为进行中
        $appointment->status = ConsultationAppointment::STATUS_ONGOING;
        $appointment->save();

        // 根据咨询类型返回不同的咨询界面
        switch ($appointment->consultation_type) {
            case 1: // 文字咨询
                return view('pages.consultation.text_consultation', ['appointment' => $appointment]);
            case 2: // 语音咨询
                return view('pages.consultation.voice_consultation', ['appointment' => $appointment]);
            case 3: // 视频咨询
                return view('pages.consultation.video_consultation', ['appointment' => $appointment]);
            default:
                return back()->with('error', '未知的咨询类型');
        }
    }

    /**
     * 显示AI咨询界面
     */
    public function aiConsultation()
    {
        return view('pages.consultation.ai_consultation');
    }

    /**
     * 处理AI咨询请求
     */
    public function aiConsultationSend(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'message' => 'required|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // 获取活跃的AI设置，按ID升序排序并返回ID最小的一条
            $aiSetting = AiConsultationSetting::where('is_active', true)
                ->orderBy('id', 'asc')
                ->first();

            if (!$aiSetting) {
                return response()->json([
                    'success' => false,
                    'response' => '系统尚未配置AI咨询服务，请联系管理员。'
                ]);
            }

            // 调用AI服务获取回复
            $aiResponse = $this->callAiService($aiSetting, $request->message);
            
            // 获取相关内容推荐
            $recommendations = $this->recommendationService->getRecommendations($request->message);
            
            // 将推荐内容添加到AI回复中
            $recommendationText = $this->recommendationService->formatRecommendationsText($recommendations);
            $finalResponse = $aiResponse . $recommendationText;
            
            try {
                // 记录用户问题和AI回复
                $record = new AiConsultationRecord();
                $record->user_id = Auth::check() ? Auth::id() : null;
                
                // 根据实际表结构使用正确的字段名
                $record->user_query = $request->message;  // 使用user_query字段而非question
                $record->ai_response = $finalResponse;  // 保存包含推荐的完整回复
                $record->recommended_resources = $recommendations; // 保存推荐资源的结构化数据
                $record->save();
            } catch (\Exception $dbEx) {
                // 记录数据库错误但不影响用户体验
                Log::error('保存AI对话记录失败: ' . $dbEx->getMessage());
                // 继续处理，不中断响应
            }

            return response()->json([
                'success' => true,
                'response' => $finalResponse
            ]);
        } catch (\Exception $e) {
            Log::error('AI咨询失败: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
                'user_message' => $request->message
            ]);

            return response()->json([
                'success' => false,
                'response' => '很抱歉，服务器处理请求时出现错误，请稍后再试。'
            ], 500);
        }
    }

    /**
     * 调用实际的AI服务API
     *
     * @param AiConsultationSetting $setting AI设置
     * @param string $userMessage 用户消息
     * @return string AI响应内容
     * @throws \Exception 异常
     */
    private function callAiService(AiConsultationSetting $setting, string $userMessage)
    {
        $maxRetries = 2; // 最多重试次数
        $retryDelay = 1000; // 重试间隔（毫秒）
        $timeout = 60; // 超时时间增加到60秒
        
        $attempt = 0;
        $lastException = null;
        
        while ($attempt <= $maxRetries) {
            try {
                // 获取原始API密钥（绕过安全处理）
                $apiKey = \DB::table('ai_consultation_settings')
                    ->where('id', $setting->id)
                    ->value('api_key');

                // API调用参数
                $headers = [
                    'Content-Type' => 'application/json',
                    'Authorization' => 'Bearer ' . $apiKey
                ];

                $messages = [
                    ['role' => 'system', 'content' => $setting->system_prompt],
                    ['role' => 'user', 'content' => $userMessage]
                ];

                $payload = [
                    'model' => $setting->model,
                    'messages' => $messages,
                    'max_tokens' => $setting->max_tokens,
                    'temperature' => $setting->temperature,
                    'stream' => false
                ];

                // 记录调用日志和重试次数
                Log::info('AI咨询API调用', [
                    'provider' => $setting->provider,
                    'model' => $setting->model,
                    'api_url' => $setting->api_url,
                    'user_message_length' => strlen($userMessage),
                    'attempt' => $attempt + 1,
                    'timeout' => $timeout
                ]);

                // 发送HTTP请求
                $response = Http::withHeaders($headers)
                    ->timeout($timeout) // 增加超时时间
                    ->post($setting->api_url, $payload);

                if (!$response->successful()) {
                    $errorMsg = 'AI服务响应错误: ' . $response->status() . ' ' . $response->body();
                    Log::warning($errorMsg, ['attempt' => $attempt + 1]);
                    throw new \Exception($errorMsg);
                }

                $responseData = $response->json();

                // 提取AI回复内容（根据DeepSeek/OpenAI的响应结构）
                if (isset($responseData['choices'][0]['message']['content'])) {
                    $aiResponse = $responseData['choices'][0]['message']['content'];
                    
                    // 记录成功调用
                    Log::info('AI服务响应成功', [
                        'response_length' => strlen($aiResponse),
                        'attempt' => $attempt + 1
                    ]);
                    
                    return $aiResponse;
                } else {
                    throw new \Exception('无法解析AI响应数据: ' . json_encode($responseData));
                }
                
            } catch (\Exception $e) {
                $lastException = $e;
                Log::warning('AI服务调用失败，准备重试', [
                    'attempt' => $attempt + 1,
                    'max_retries' => $maxRetries,
                    'error' => $e->getMessage()
                ]);
                
                $attempt++;
                
                // 如果还有重试次数，等待等待一段时间后再重试
                if ($attempt <= $maxRetries) {
                    usleep($retryDelay * 1000); // 转换为微秒
                    // 每次重试都增加延迟
                    $retryDelay *= 2;
                }
            }
        }
        
        // 所有重试都失败后，记录最终错误并回退到预设响应
        Log::error('AI服务调用失败，重试耗尽', [
            'final_error' => $lastException ? $lastException->getMessage() : '未知错误',
            'total_attempts' => $maxRetries + 1
        ]);
        
        // 回退到模拟响应
        return $this->getAiResponse($userMessage);
    }

    /**
     * 显示线下课程列表
     */
    public function offlineCourses(Request $request)
    {
        $categories = OfflineCourse::getCategories();
        $selected   = $request->input('category');

        $query = OfflineCourse::with('lecturerlx')
            ->where('start_time', '>=', Carbon::now())
            ->where('status', OfflineCourse::STATUS_PUBLISHED);

        if ($selected && isset($categories[$selected])) {
            $query->where('category', $selected);
        }

        $courses = $query
            ->orderBy('start_time', 'asc')
            ->paginate(10)
            ->appends(['category' => $selected]);

        return view('pages.consultation.offline_courses', compact('courses', 'categories', 'selected'));
    }

    /**
     * 显示线下课程详情
     */
    public function offlineCourseDetail($id)
    {
        $course = OfflineCourse::with('lecturer')->findOrFail($id);

        // 检查课程是否活跃
        if ($course->status !== OfflineCourse::STATUS_PUBLISHED) {
            return redirect()->route('consultation.offline_courses')
                ->with('error', '该课程已下架');
        }

        // 检查是否已达到人数上限
        $isFullyBooked = $course->current_participants >= $course->max_participants;

        // 检查当前用户是否已报名
        $userRegistered = false;
        if (Auth::check()) {
            $userRegistered = CourseRegistration::where('user_id', Auth::id())
                ->where('course_id', $course->id)
                ->where('status','!=','0')
                ->exists();
        }

        return view('pages.consultation.offline_course_detail', [
            'course' => $course,
            'isFullyBooked' => $isFullyBooked,
            'userRegistered' => $userRegistered
        ]);
    }

    /**
     * 报名参加线下课程
     */
    public function registerCourse(Request $request)
    {
        $user = Auth::user();
    
        // 1. 验证规则
        $rules = [
            'course_id'    => 'required|exists:offline_courses,id',
            'participants' => 'required|integer|min:1',
        ];
        // 如果用户 enterprise 为空，则必须填写 department
        if (empty($user->enterprise)) {
            $rules['department'] = 'required|string|max:255';
        }
    
        // 2. 手动校验
        $validator = Validator::make($request->all(), $rules);
        if ($validator->fails()) {
            // 校验失败统一返回 JSON 错误
            return response()->json([
                'success' => false,
                'errors'  => $validator->errors(),
            ], 422);
        }
    
        $data       = $validator->validated();
        $department = $data['department'] ?? $user->enterprise;
        $num        = $data['participants'];
    
        // 3. 加载课程并做业务校验
        $course = OfflineCourse::find($data['course_id']);
        if (! $course || $course->status !== OfflineCourse::STATUS_PUBLISHED) {
            return response()->json([
                'success' => false,
                'message' => '该课程已下架',
            ], 400);
        }
    
        // 重复报名检查
        if (CourseRegistration::where('user_id', $user->id)
                ->where('course_id', $course->id)
                ->where('status','!=',0)
                ->exists()) {
            return response()->json([
                'success' => false,
                'message' => '您已经报名过该课程',
            ], 400);
        }
    
        // 名额检查
        if ($course->current_participants + $num > $course->max_participants) {
            return response()->json([
                'success' => false,
                'message' => '报名人数超出剩余名额',
            ], 400);
        }
    
        try {
            // 4. 写入报名记录
            $reg = new CourseRegistration();
            $reg->user_id            = $user->id;
            $reg->course_id          = $course->id;
            $reg->registration_code  = CourseRegistration::generateCode();
            $reg->status             = CourseRegistration::STATUS_REGISTERED;
            $reg->price              = $course->price;
            $reg->participants_count = $num;
            $reg->department         = $department;
            $reg->payment_status     = $course->price <= 0
                ? CourseRegistration::PAYMENT_PAID
                : CourseRegistration::PAYMENT_UNPAID;
            $reg->save();
    
            // 更新课程当前参与人数
            $course->increment('current_participants', $num);
    
            // 报名成功：保持原逻辑，重定向到“我的课程”并携带成功信息
            return redirect()->route('consultation.my_courses')
                ->with('success', '课程报名成功');
        } catch (\Exception $e) {
            Log::error('课程报名失败: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => '报名失败，请稍后再试',
            ], 500);
        }
    }


    /**
     * 显示用户的课程报名记录
     */
    public function myCourses()
    {
        $registrations = CourseRegistration::with('course')
            ->where('user_id', Auth::id())
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('pages.consultation.my_courses', [
            'registrations' => $registrations
        ]);
    }

    /**
     * 取消课程报名
     */
    public function cancelCourseRegistration($id)
    {
        $registration = CourseRegistration::where('id', $id)
            ->where('user_id', Auth::id())
            ->firstOrFail();

        // 检查课程是否已开始
        $course = $registration->course;
        if (Carbon::now() >= Carbon::parse($course->start_time)) {
            return back()->with('error', '课程已开始，无法取消报名');
        }

        try {
            // 将状态更改为已取消
            $registration->status = CourseRegistration::STATUS_CANCELLED;
            $registration->save();

            // 减少课程的当前参与人数
            $course->decrement('current_participants',$registration->participants_count);

            return back()->with('success', '课程报名已取消');
        } catch (\Exception $e) {
            Log::error('取消课程报名失败: ' . $e->getMessage());
            return back()->with('error', '取消报名失败，请稍后再试');
        }
    }

    /**
     * 获取中文星期几
     */
    private function getChineseWeekday(Carbon $date)
    {
        $weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
        return $weekdays[$date->dayOfWeek];
    }

    /**
     * 获取AI回复（模拟） - 仅在实际API调用失败时作为后备
     */
    private function getAiResponse($message)
    {
        // 这里只是一个简单的模拟，在实际API调用失败时作为备用方案
        $responses = [
            '感谢您分享您的感受。这种情况确实可能会让人感到困扰，您能进一步描述一下具体的情境吗？',
            '我理解您现在的心情。面对这样的情况，您可以尝试深吸气放松一下，给自己一些空间来整理思绪。',
            '您提到的问题很常见，许多人都有类似的经历。您是否尝试过与朋友或家人交流这个问题？',
            '听起来您正在经历一些挑战。请记住，寻求帮助是勇气的表现，而不是软弱。您已经迈出了重要的一步。',
            '您的感受是完全有效的。有时候我们需要给自己一些时间和空间来处理复杂的情绪。',
            '我注意到您可能正在经历一些压力。您可以尝试一些简单的放松技巧，比如冠想或轻度运动，这可能对缓解压力有所帮助。',
            '每个人都有自己的应对方式。您过去是如何处理类似情况的？那些策略是否有效？',
            '感谢您的信任。虽然我无法提供专业的心理治疗，但我很乐意倾听并提供一些支持。您是否考虑过咨询专业的心理咨询师？'
        ];

        // 增加一些延迟以模拟真实AI处理时间
        usleep(random_int(500000, 1500000)); // 0.5-1.5秒延迟

        return $responses[array_rand($responses)];
    }

}
