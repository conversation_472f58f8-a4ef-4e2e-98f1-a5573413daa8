<?php

namespace App\Admin\Controllers;

use App\Models\QuizActivity;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class QuizActivityController extends AdminController
{
    protected $title = '有奖问答活动';

    protected function grid()
    {
        return Grid::make(new QuizActivity(), function (Grid $grid) {
            $grid->column('id', 'ID')->sortable();
            $grid->column('title', '活动标题');
            $grid->column('start_time', '开始时间')->sortable();
            $grid->column('end_time', '结束时间')->sortable();
            $grid->column('is_active', '是否激活')->switch();
            
            $grid->column('status', '状态')->display(function () {
                if (!$this->is_active) {
                    return '<span class="label bg-gray">未激活</span>';
                }
                
                if (now()->lt($this->start_time)) {
                    return '<span class="label bg-yellow">即将开始</span>';
                }
                
                if (now()->between($this->start_time, $this->end_time)) {
                    return '<span class="label bg-green">进行中</span>';
                }
                
                return '<span class="label bg-red">已结束</span>';
            });
            
            $grid->column('attempts_count', '参与人数')->display(function () {
                return $this->attempts()->where('is_completed', true)->count();
            });
            
            $grid->column('prize_count', '奖品数量')->display(function () {
                return $this->prizes()->count();
            });
            
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id', 'ID');
                $filter->like('title', '活动标题');
                $filter->between('start_time', '开始时间')->datetime();
                $filter->between('end_time', '结束时间')->datetime();
                $filter->equal('is_active', '是否激活')->select([
                    0 => '未激活',
                    1 => '已激活',
                ]);
            });
            
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableView();
                $actions->append('<a href="' . admin_url('quiz-questions?quiz_activity_id=' . $actions->row->id) . '" class="btn btn-primary btn-sm" style="margin-right:5px">管理问题</a>');
                $actions->append('<a href="' . admin_url('quiz-prizes?quiz_activity_id=' . $actions->row->id) . '" class="btn btn-success btn-sm">管理奖品</a>');
            });
        });
    }

    protected function form()
    {
        return Form::make(new QuizActivity(), function (Form $form) {
            $form->display('id', 'ID');
            
            $form->text('title', '活动标题')
                ->required()
                ->rules('required|max:100');
                
            $form->textarea('description', '活动描述')
                ->rows(5);
                
            $form->image('cover_image', '封面图片')
                ->autoUpload()
                ->uniqueName()
                ->help('建议尺寸: 1200 x 630 像素');
                
            $form->datetime('start_time', '开始时间')
                ->required()
                ->rules('required|date');
                
            $form->datetime('end_time', '结束时间')
                ->required()
                ->rules('required|date|after:start_time');
                
            $form->number('pass_score', '及格分数')
                ->min(0)
                ->max(100)
                ->default(60)
                ->help('百分比分数，例如60表示60%的题目答对才能获得奖品');
                
            $form->number('max_attempts', '允许参与次数')
                ->min(1)
                ->default(1);
                
            $form->switch('is_active', '是否激活')
                ->default(true);
                
            $form->switch('show_answers_after_submit', '提交后显示答案')
                ->default(false);
                
            $form->display('created_at', '创建时间');
            $form->display('updated_at', '更新时间');
            
            $form->disableViewButton();
            
            // 保存前检查开始时间和结束时间
            $form->saving(function (Form $form) {
                if ($form->start_time && $form->end_time) {
                    if (strtotime($form->start_time) >= strtotime($form->end_time)) {
                        return $form->error('结束时间必须晚于开始时间');
                    }
                }
            });
        });
    }
}
