<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class VideoAuditLog extends Model
{
    use HasFactory,HasDateTimeFormatter;

    protected $fillable = [
        'video_id',
        'admin_id',
        'status',
        'reason'
    ];

    // 状态常量
    const STATUS_PENDING = 1;   // 待审核
    const STATUS_APPROVED = 2;  // 通过
    const STATUS_REJECTED = 3;  // 拒绝

    // 关联视频
    public function video()
    {
        return $this->belongsTo(Video::class, 'video_id');
    }

    // 关联管理员
    public function admin()
    {
        return $this->belongsTo(Admin::class, 'admin_id');
    }

    // 获取状态文本
    public function getStatusTextAttribute()
    {
        $statusMap = [
            self::STATUS_PENDING => '待审核',
            self::STATUS_APPROVED => '已通过',
            self::STATUS_REJECTED => '已拒绝'
        ];

        return $statusMap[$this->status] ?? '未知状态';
    }
}
