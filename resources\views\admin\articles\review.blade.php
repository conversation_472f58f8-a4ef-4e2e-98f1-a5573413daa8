<div class="card">
    <div class="card-header">
        <h4 class="card-title">文章审核</h4>
    </div>
    <div class="card-body">
        <div class="row mb-4">
            <div class="col-md-12">
                <h3>{{ $article->title }}</h3>
                <div class="text-muted">
                    <span>分类: {{ $article->category ? $article->category->name : '-' }}</span> | 
                    <span>作者: {{ $article->author ? $article->author->name : '-' }}</span> | 
                    <span>创建时间: {{ $article->created_at->format('Y-m-d H:i:s') }}</span>
                </div>
            </div>
        </div>
        
        @if($article->image)
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">文章主图</h5>
                    </div>
                    <div class="card-body text-center">
                        <img src="{{ Storage::disk(config('admin.upload.disk'))->url($article->image) }}" alt="{{ $article->title }}" class="img-fluid" style="max-height: 300px;">
                    </div>
                </div>
            </div>
        </div>
        @endif
        
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">文章内容</h5>
                    </div>
                    <div class="card-body">
                        @if($article->summary)
                        <div class="article-summary mb-4">
                            <blockquote class="blockquote">
                                <p class="mb-0"><strong>摘要：</strong>{{ $article->summary }}</p>
                            </blockquote>
                            <hr>
                        </div>
                        @endif
                        
                        <div class="article-content">
                            {!! $article->content !!}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">审核操作</h5>
                    </div>
                    <div class="card-body">
                        <form id="reviewForm" action="{{ admin_url('articles/'.$article->id.'/handle-review') }}" method="POST" pjax-container>
                            {{ csrf_field() }}
                            
                            <div class="form-group">
                                <label for="status">审核结果</label>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="custom-control custom-radio">
                                            <input type="radio" id="approved" name="status" value="approved" class="custom-control-input" checked>
                                            <label class="custom-control-label" for="approved">通过</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="custom-control custom-radio">
                                            <input type="radio" id="rejected" name="status" value="rejected" class="custom-control-input">
                                            <label class="custom-control-label" for="rejected">拒绝</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="comments">审核意见</label>
                                <textarea id="comments" name="comments" class="form-control" rows="4"></textarea>
                            </div>
                            
                            <div class="form-group">
                                <button type="button" id="submitReview" class="btn btn-primary">提交审核</button>
                                <a href="{{ admin_url('articles') }}" class="btn btn-default">返回列表</a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    $(function() {
        // 提交审核
        $('#submitReview').on('click', function() {
            var status = $('input[name="status"]:checked').val();
            var comments = $('#comments').val();
            
            if (!comments) {
                Dcat.error('请填写审核意见');
                return;
            }
            
            // 使用Swal直接调用，避免Dcat.confirm的封装问题
            Swal.fire({
                title: '确认操作',
                text: '确认要执行此审核操作吗？',
                type: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                showLoaderOnConfirm: true,
                preConfirm: function() {
                    return new Promise(function(resolve, reject) {
                        $.ajax({
                            url: '{{ admin_url("articles/".$article->id."/handle-review") }}',
                            type: 'POST',
                            dataType: 'json',
                            data: {
                                _token: '{{ csrf_token() }}',
                                status: status,
                                comments: comments
                            },
                            success: function(data) {
                                console.log('请求成功:', data);
                                resolve(data);
                            },
                            error: function(xhr) {
                                console.log('请求失败:', xhr);
                                reject(xhr);
                            }
                        });
                    });
                },
                allowOutsideClick: false
            }).then(function(result) {
                if (result.value) {
                    var data = result.value;
                    if (data.status) {
                        Dcat.success(data.message || '审核成功');
                        setTimeout(function() {
                            location.href = '{{ admin_url("articles") }}';
                        }, 1500);
                    } else {
                        Dcat.error(data.message || '操作失败');
                    }
                }
            }).catch(function(error) {
                var message = '';
                if (error.responseJSON && error.responseJSON.message) {
                    message = error.responseJSON.message;
                } else {
                    message = '服务器错误，请稍后再试';
                }
                Dcat.error(message);
            });
        });
    });
</script>
