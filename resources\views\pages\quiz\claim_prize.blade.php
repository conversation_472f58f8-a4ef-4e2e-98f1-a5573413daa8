@extends('layouts.app')

@section('title', '奖品领取')

@section('custom-styles')
<style>
    .header-container {
        background: linear-gradient(135deg, #FFC107, #FF9800);
        color: white;
        padding: 15px;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 1000;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 50px;
    }
    
    .content-container {
        margin-top: 65px;
        padding: 15px;
        padding-bottom: 70px;
    }
    
    .prize-card {
        background: white;
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        border: 1px solid #f0f0f0;
    }
    
    .prize-icon {
        width: 70px;
        height: 70px;
        border-radius: 50%;
        background: linear-gradient(135deg, #FFC107, #FF9800);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 15px;
        font-size: 28px;
        color: white;
        box-shadow: 0 4px 10px rgba(255, 152, 0, 0.2);
    }
    
    .prize-title {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 5px;
        color: #333;
        text-align: center;
    }
    
    .prize-quiz {
        font-size: 14px;
        color: #666;
        text-align: center;
        margin-bottom: 15px;
    }
    
    .prize-description {
        font-size: 14px;
        color: #666;
        background-color: #fff9e6;
        border-radius: 8px;
        padding: 12px;
        margin-bottom: 20px;
        border-left: 3px solid #FFC107;
    }
    
    .form-title {
        font-size: 17px;
        font-weight: 500;
        margin-bottom: 15px;
        color: #333;
        display: flex;
        align-items: center;
    }
    
    .form-title-icon {
        margin-right: 8px;
        color: #FF9800;
    }
    
    .form-group {
        margin-bottom: 15px;
    }
    
    .form-label {
        display: block;
        font-size: 14px;
        font-weight: 500;
        color: #495057;
        margin-bottom: 5px;
    }
    
    .form-control {
        display: block;
        width: 100%;
        padding: 12px;
        font-size: 15px;
        line-height: 1.5;
        color: #495057;
        background-color: #fff;
        background-clip: padding-box;
        border: 1px solid #ced4da;
        border-radius: 8px;
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }
    
    .form-control:focus {
        border-color: #FF9800;
        outline: 0;
        box-shadow: 0 0 0 0.2rem rgba(255, 152, 0, 0.25);
    }
    
    .form-text {
        display: block;
        margin-top: 5px;
        font-size: 12px;
        color: #6c757d;
    }
    
    .action-button {
        display: block;
        width: 100%;
        padding: 12px;
        text-align: center;
        border-radius: 8px;
        margin-bottom: 10px;
        font-size: 16px;
        font-weight: 500;
        text-decoration: none;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
    }
    
    .primary-button {
        background-color: #FF9800;
        color: white;
        box-shadow: 0 3px 6px rgba(255, 152, 0, 0.2);
    }
    
    .primary-button:hover {
        background-color: #F57C00;
        color: white;
    }
    
    .secondary-button {
        background-color: #f8f9fa;
        color: #495057;
        border: 1px solid #ced4da;
    }
    
    .secondary-button:hover {
        background-color: #e9ecef;
        color: #495057;
    }
    
    .info-box {
        background-color: #e7f3ff;
        border-radius: 8px;
        padding: 12px;
        margin-bottom: 20px;
        font-size: 14px;
        color: #0c63e4;
        border-left: 3px solid #0d6efd;
    }
    
    .info-title {
        font-weight: 600;
        margin-bottom: 5px;
    }
    
    .info-item {
        display: flex;
        margin-bottom: 8px;
    }
    
    .info-label {
        flex-basis: 80px;
        flex-shrink: 0;
        font-weight: 500;
    }
    
    .info-value {
        flex-grow: 1;
    }
</style>
@endsection

@section('content')
<div class="header-container">
    <a href="{{ route('quiz.result', ['id' => $winner->attempt->quiz_activity_id, 'attempt_id' => $winner->attempt_id]) }}" class="back-button" style="display: flex; align-items: center; justify-content: center; width: 30px; height: 30px;">
        <div class="back-icon" style="border-top: 2px solid white; border-left: 2px solid white; width: 12px; height: 12px; transform: rotate(-45deg);"></div>
    </a>
    <h1 style="font-size: 18px; font-weight: 500; margin: 0 auto; text-align: center; flex-grow: 1; text-shadow: 0 1px 2px rgba(0,0,0,0.1);">奖品领取</h1>
    <div style="width: 30px; visibility: hidden;">
        <div class="back-icon" style="border-top: 2px solid white; border-left: 2px solid white; width: 12px; height: 12px;"></div>
    </div>
</div>

<div class="content-container">
    <!-- 奖品信息 -->
    <div class="prize-card">
        <div class="prize-icon">
            <i class="fas fa-trophy"></i>
        </div>
        
        <h2 class="prize-title">{{ $winner->prize->name }}</h2>
        <div class="prize-quiz">{{ $winner->attempt->quizActivity->title }}</div>
        
        <div class="prize-description">
            {{ $winner->prize->description }}
        </div>
        
        <div class="info-box">
            <div class="info-title">奖品详情：</div>
            
            <div class="info-item">
                <div class="info-label">奖品类型：</div>
                <div class="info-value">
                    {{ $winner->prize->prize_type == 'physical' ? '实物奖品' : ($winner->prize->prize_type == 'coupon' ? '优惠券' : '虚拟奖品') }}
                </div>
            </div>
            
            @if($winner->prize->prize_type == 'physical')
            <div class="info-item">
                <div class="info-label">领取方式：</div>
                <div class="info-value">快递配送</div>
            </div>
            @elseif($winner->prize->prize_type === 'coupon')
            <div class="info-item">
                <div class="info-label">领取方式：</div>
                <div class="info-value">系统自动发送优惠券码</div>
            </div>
            @else
            <div class="info-item">
                <div class="info-label">领取方式：</div>
                <div class="info-value">系统自动发放</div>
            </div>
            @endif
            
            <div class="info-item">
                <div class="info-label">获奖日期：</div>
                <div class="info-value">{{ $winner->created_at->format('Y-m-d H:i') }}</div>
            </div>
        </div>
    </div>
    
    <!-- 领取表单 -->
    <h2 class="form-title">
        <i class="fas fa-file-alt form-title-icon"></i> 填写领取信息
    </h2>
    
    <form action="{{ route('quiz.submit_claim', ['id' => $winner->attempt->quiz_activity_id, 'winner_id' => $winner->id]) }}" method="POST">
        @csrf
        
        <!-- 实物奖品需要填写收货地址 -->
        @if($winner->prize->prize_type == 'physical')
        <div class="form-group">
            <label for="name" class="form-label">联系人姓名</label>
            <input type="text" id="name" name="name" class="form-control" value="{{ old('name', $winner->winner_name) }}" required>
            @error('name')
                <span class="form-text text-danger">{{ $message }}</span>
            @enderror
        </div>
        
        <div class="form-group">
            <label for="phone" class="form-label">联系电话</label>
            <input type="tel" id="phone" name="phone" class="form-control" value="{{ old('phone', $winner->contact_info) }}" required>
            <span class="form-text">请填写正确的手机号码，方便快递员联系</span>
            @error('phone')
                <span class="form-text text-danger">{{ $message }}</span>
            @enderror
        </div>
        
        <div class="form-group">
            <label for="address" class="form-label">收货地址</label>
            <textarea id="address" name="address" class="form-control" rows="3" required>{{ old('address', $winner->shipping_address) }}</textarea>
            <span class="form-text">请填写详细的收货地址，包含省市区街道门牌号等信息</span>
            @error('address')
                <span class="form-text text-danger">{{ $message }}</span>
            @enderror
        </div>
        
        <div class="form-group">
            <label for="notes" class="form-label">备注信息</label>
            <textarea id="notes" name="notes" class="form-control" rows="2">{{ old('notes', $winner->admin_notes) }}</textarea>
            <span class="form-text">如有特殊需求，请在此处填写</span>
            @error('notes')
                <span class="form-text text-danger">{{ $message }}</span>
            @enderror
        </div>
        
        @elseif($winner->prize->prize_type == 'coupon')
        <!-- 优惠券需要填写姓名、电话和邮箱 -->
        <div class="form-group">
            <label for="name" class="form-label">姓名</label>
            <input type="text" id="name" name="name" class="form-control" value="{{ old('name', $winner->winner_name) }}" required>
            @error('name')
                <span class="form-text text-danger">{{ $message }}</span>
            @enderror
        </div>
        
        <div class="form-group">
            <label for="phone" class="form-label">手机号码</label>
            <input type="tel" id="phone" name="phone" class="form-control" value="{{ old('phone') }}" required>
            <span class="form-text">用于收取优惠券信息</span>
            @error('phone')
                <span class="form-text text-danger">{{ $message }}</span>
            @enderror
        </div>
        
        <div class="form-group">
            <label for="email" class="form-label">电子邮箱</label>
            <input type="email" id="email" name="email" class="form-control" value="{{ old('email') }}" required>
            <span class="form-text">优惠券码将发送到此邮箱</span>
            @error('email')
                <span class="form-text text-danger">{{ $message }}</span>
            @enderror
        </div>
        
        <div class="form-group">
            <label for="phone" class="form-label">联系电话</label>
            <input type="tel" id="phone" name="phone" class="form-control" value="{{ old('phone', $winner->phone) }}">
            <span class="form-text">选填，方便我们与您取得联系</span>
            @error('phone')
                <span class="form-text text-danger">{{ $message }}</span>
            @enderror
        </div>
        @endif
        
        <div style="margin-top: 20px;">
            <button type="submit" class="action-button primary-button">确认领取</button>
            <a href="{{ route('quiz.result', ['id' => $winner->attempt->quiz_activity_id, 'attempt_id' => $winner->attempt_id]) }}" class="action-button secondary-button">返回</a>
        </div>
    </form>
</div>
@endsection
