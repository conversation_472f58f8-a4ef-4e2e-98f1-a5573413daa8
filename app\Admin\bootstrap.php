<?php

use Dcat\Admin\Admin;
use Dcat\Admin\Grid;
use Dcat\Admin\Form;
use Dcat\Admin\Grid\Filter;
use Dcat\Admin\Show;

/**
 * Dcat-admin - admin builder based on Lara<PERSON>.
 * <AUTHOR> <https://github.com/jqhph>
 *
 * Bootstraper for Admin.
 *
 * Here you can remove builtin form field:
 * Dcat\Admin\Form::forget(['map', 'editor']);
 *
 * Or extend custom form field:
 * Dcat\Admin\Form::extend('php', PHPEditor::class);
 *
 * Or require js and css assets:
 * Admin::css('/packages/prettydocs/css/styles.css');
 * Admin::js('/packages/prettydocs/js/main.js');
 *
 */

// 添加视频上传功能到富文本编辑器
use Dcat\Admin\Form\Field\Editor;

// 全局加载 Select2 插件，支持 nested hasMany 中的 select2
Admin::css('/vendor/dcat-admin/plugins/select/select2.min.css');
Admin::js('/vendor/dcat-admin/plugins/select/select2.full.min.js');

// 设置图片和文件上传服务器地址
Admin::booting(function () {
    // 设置图片上传服务器地址
    config(['admin.upload.server' => admin_url('upload/image')]);
    // 设置文件上传服务器地址
    config(['admin.upload.file_server' => admin_url('upload/file')]);
});

Form\Field\Editor::resolving(function (Form\Field\Editor $editor) {
    $editor->options([
        'file_picker_types' => 'media',
        'file_picker_callback' => \Dcat\Admin\Support\JavaScript::make(<<<JS
            function file_picker_callback (callback, value, meta) {
                // 设置上传地址为原富文本框图片文件上传地址
                var upurl = opts.images_upload_url;
                var filetype = '';
                // 处理媒体类型文件能选择的文件类型
                if (meta.filetype == 'media') {
                    filetype = 'video/*';
                }
                //模拟出一个input用于添加本地文件
                var input = document.createElement('input');
                input.setAttribute('type', 'file');
                input.setAttribute('accept', filetype);
                // 模拟点击file input
                input.click();
                input.onchange = function() {
                    var file = this.files[0];
                    var xhr, formData;
                    xhr = new XMLHttpRequest();
                    xhr.withCredentials = false;
                    xhr.open('POST', upurl);
                    xhr.onload = function() {
                        var json;
                        if (xhr.status != 200) {
                            failure('HTTP Error: ' + xhr.status);
                            return;
                        }
                        json = JSON.parse(xhr.responseText);
                        if (!json || typeof json.location != 'string') {
                            failure('Invalid JSON: ' + xhr.responseText);
                            return;
                        }
                        callback(json.location);
                    };
                    formData = new FormData();
                    formData.append('file', file, file.name );
                    xhr.send(formData);
                };
            }
        JS
        ),
    ]);
});

// 全局设置文件上传大小限制
Admin::booting(function () {
    // 设置默认的文件大小限制为1024MB
    Form::resolving(function ($field) {
        if ($field instanceof Form\Field\File || 
            $field instanceof Form\Field\Image || 
            $field instanceof Form\Field\MultipleFile || 
            $field instanceof Form\Field\MultipleImage) {
            
            // 设置单个文件大小限制为1024MB (1073741824 bytes)
            $field->options(['fileSingleSizeLimit' => 1073741824]);
            // 设置总文件大小限制为10GB
            $field->options(['fileSizeLimit' => 10737418240]);
        }
    });
});
