<?php

namespace App\Services;

use App\Models\ContentIndex;
use App\Services\AlibabaVectorService;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Exception;

/**
 * 内容搜索服务
 * 提供基于向量的语义搜索功能
 */
class ContentSearchService
{
    /**
     * 向量服务实例
     */
    private AlibabaVectorService $vectorService;

    /**
     * 默认搜索配置
     */
    private array $defaultOptions = [
        'limit' => 10,
        'similarity_threshold' => 0.7,
        'include_metadata' => true,
        'types' => [], // 空数组表示搜索所有类型
    ];

    /**
     * 构造函数
     */
    public function __construct(AlibabaVectorService $vectorService)
    {
        $this->vectorService = $vectorService;
    }

    /**
     * 语义搜索
     *
     * @param string $query 搜索查询
     * @param array $options 搜索选项
     * @return array 搜索结果
     */
    public function search(string $query, array $options = []): array
    {
        try {
            // 合并默认选项
            $options = array_merge($this->defaultOptions, $options);
            
            // 生成查询向量
            $queryVector = $this->generateQueryVector($query);
            
            if (empty($queryVector)) {
                Log::warning('查询向量生成失败', ['query' => $query]);
                return [];
            }

            // 执行向量搜索
            $vectorResults = $this->performVectorSearch($queryVector, $options);
            
            // 处理搜索结果
            $results = $this->processSearchResults($vectorResults, $options);
            
            Log::info('语义搜索完成', [
                'query' => $query,
                'results_count' => count($results),
                'options' => $options,
            ]);

            return $results;

        } catch (Exception $e) {
            Log::error('语义搜索失败', [
                'query' => $query,
                'error' => $e->getMessage(),
                'options' => $options,
            ]);
            
            // 降级到传统搜索
            return $this->fallbackSearch($query, $options);
        }
    }

    /**
     * 批量搜索
     *
     * @param array $queries 查询数组
     * @param array $options 搜索选项
     * @return array 搜索结果数组
     */
    public function batchSearch(array $queries, array $options = []): array
    {
        $results = [];
        
        foreach ($queries as $index => $query) {
            try {
                $results[$index] = $this->search($query, $options);
            } catch (Exception $e) {
                Log::error('批量搜索单项失败', [
                    'query_index' => $index,
                    'query' => $query,
                    'error' => $e->getMessage(),
                ]);
                $results[$index] = [];
            }
        }
        
        return $results;
    }

    /**
     * 相似内容推荐
     *
     * @param int $contentId 内容ID
     * @param string $contentType 内容类型
     * @param array $options 推荐选项
     * @return array 推荐结果
     */
    public function recommendSimilar(int $contentId, string $contentType, array $options = []): array
    {
        try {
            // 获取内容索引
            $contentIndex = ContentIndex::where('indexable_id', $contentId)
                ->where('indexable_type', $this->getModelClass($contentType))
                ->where('vector_generated', true)
                ->first();

            if (!$contentIndex || !$contentIndex->vector) {
                Log::warning('内容向量不存在', [
                    'content_id' => $contentId,
                    'content_type' => $contentType,
                ]);
                return [];
            }

            // 使用内容向量进行搜索
            $queryVector = $contentIndex->getVectorData();
            
            // 排除自身
            $options = array_merge($this->defaultOptions, $options);
            $vectorResults = $this->performVectorSearch($queryVector, $options);
            
            // 过滤掉自身
            $vectorResults = array_filter($vectorResults, function ($result) use ($contentId, $contentType) {
                return !($result['fields']['indexable_id'] == $contentId && 
                        $result['fields']['type'] == $contentType);
            });

            $results = $this->processSearchResults($vectorResults, $options);

            Log::info('相似内容推荐完成', [
                'content_id' => $contentId,
                'content_type' => $contentType,
                'results_count' => count($results),
            ]);

            return $results;

        } catch (Exception $e) {
            Log::error('相似内容推荐失败', [
                'content_id' => $contentId,
                'content_type' => $contentType,
                'error' => $e->getMessage(),
            ]);
            return [];
        }
    }

    /**
     * 生成查询向量
     *
     * @param string $query 查询文本
     * @return array 向量数据
     */
    private function generateQueryVector(string $query): array
    {
        $cacheKey = 'query_vector:' . md5($query);
        
        // 尝试从缓存获取
        if ($cached = Cache::get($cacheKey)) {
            return $cached;
        }

        try {
            $vector = $this->vectorService->generateTextVector($query);
            
            // 缓存查询向量（1小时）
            Cache::put($cacheKey, $vector, 3600);
            
            return $vector;
            
        } catch (Exception $e) {
            Log::error('查询向量生成失败', [
                'query' => $query,
                'error' => $e->getMessage(),
            ]);
            return [];
        }
    }

    /**
     * 执行向量搜索
     *
     * @param array $queryVector 查询向量
     * @param array $options 搜索选项
     * @return array 搜索结果
     */
    private function performVectorSearch(array $queryVector, array $options): array
    {
        try {
            // 构建过滤条件
            $filter = [];
            
            // 按类型过滤
            if (!empty($options['types'])) {
                $filter['type'] = ['$in' => $options['types']];
            }
            
            // 只搜索已激活的内容
            $filter['is_active'] = true;

            // 调用DashVector查询API
            $results = $this->vectorService->queryDocs(
                $queryVector,
                $options['limit'],
                $filter,
                $options['include_metadata']
            );

            return $results;

        } catch (Exception $e) {
            Log::error('向量搜索执行失败', [
                'error' => $e->getMessage(),
                'options' => $options,
            ]);
            return [];
        }
    }

    /**
     * 处理搜索结果
     *
     * @param array $vectorResults DashVector搜索结果
     * @param array $options 搜索选项
     * @return array 处理后的结果
     */
    private function processSearchResults(array $vectorResults, array $options): array
    {
        $results = [];
        
        foreach ($vectorResults as $result) {
            // 检查相似度阈值
            $similarity = $result['score'] ?? 0;
            if ($similarity < $options['similarity_threshold']) {
                continue;
            }

            $fields = $result['fields'] ?? [];
            
            $resultItem = [
                'id' => $result['id'],
                'type' => $fields['type'] ?? '',
                'title' => $fields['title'] ?? '',
                'summary' => $fields['summary'] ?? '',
                'image' => $fields['image'] ?? '',
                'url' => $fields['url'] ?? '',
                'similarity' => round($similarity, 4),
                'created_at' => $fields['created_at'] ?? null,
                'updated_at' => $fields['updated_at'] ?? null,
            ];

            // 包含元数据
            if ($options['include_metadata'] && isset($fields['metadata'])) {
                $resultItem['metadata'] = $fields['metadata'];
            }

            // 包含关联模型信息
            if (isset($fields['indexable_type']) && isset($fields['indexable_id'])) {
                $resultItem['model'] = [
                    'type' => $fields['indexable_type'],
                    'id' => $fields['indexable_id'],
                ];
            }

            $results[] = $resultItem;
        }

        return $results;
    }

    /**
     * 降级搜索（当向量搜索失败时）
     *
     * @param string $query 搜索查询
     * @param array $options 搜索选项
     * @return array 搜索结果
     */
    private function fallbackSearch(string $query, array $options): array
    {
        try {
            $queryBuilder = ContentIndex::query()
                ->where('is_active', true)
                ->where(function ($q) use ($query) {
                    $q->where('title', 'like', "%{$query}%")
                      ->orWhere('summary', 'like', "%{$query}%");
                });

            // 按类型过滤
            if (!empty($options['types'])) {
                $queryBuilder->whereIn('type', $options['types']);
            }

            $results = $queryBuilder
                ->orderBy('relevance_score', 'desc')
                ->orderBy('view_count', 'desc')
                ->limit($options['limit'])
                ->get()
                ->map(function ($item) {
                    return [
                        'id' => $item->id,
                        'type' => $item->type,
                        'title' => $item->title,
                        'summary' => $item->summary,
                        'image' => $item->image,
                        'url' => $item->url,
                        'similarity' => 0.5, // 默认相似度
                        'created_at' => $item->created_at,
                        'updated_at' => $item->updated_at,
                        'model' => [
                            'type' => $item->indexable_type,
                            'id' => $item->indexable_id,
                        ],
                    ];
                })
                ->toArray();

            Log::info('降级搜索完成', [
                'query' => $query,
                'results_count' => count($results),
            ]);

            return $results;

        } catch (Exception $e) {
            Log::error('降级搜索失败', [
                'query' => $query,
                'error' => $e->getMessage(),
            ]);
            return [];
        }
    }

    /**
     * 获取模型类名
     *
     * @param string $type 内容类型
     * @return string 模型类名
     */
    private function getModelClass(string $type): string
    {
        return match ($type) {
            'article' => 'App\\Models\\Article',
            'video' => 'App\\Models\\Video',
            'course_lesson' => 'App\\Models\\CourseLesson',
            default => '',
        };
    }

    /**
     * 获取搜索统计信息
     *
     * @return array 统计信息
     */
    public function getSearchStats(): array
    {
        try {
            $stats = [
                'total_indexed' => ContentIndex::where('vector_generated', true)->count(),
                'by_type' => ContentIndex::where('vector_generated', true)
                    ->selectRaw('type, count(*) as count')
                    ->groupBy('type')
                    ->pluck('count', 'type')
                    ->toArray(),
                'vector_service_health' => $this->vectorService->healthCheck(),
            ];

            // 获取DashVector统计信息
            try {
                $vectorStats = $this->vectorService->getCollectionStats();
                $stats['vector_service_stats'] = $vectorStats;
            } catch (Exception $e) {
                $stats['vector_service_stats'] = null;
                Log::warning('获取向量服务统计信息失败', [
                    'error' => $e->getMessage(),
                ]);
            }

            return $stats;

        } catch (Exception $e) {
            Log::error('获取搜索统计信息失败', [
                'error' => $e->getMessage(),
            ]);
            return [];
        }
    }

    /**
     * 清理搜索缓存
     *
     * @return bool
     */
    public function clearSearchCache(): bool
    {
        try {
            // 清理查询向量缓存
            Cache::flush(); // 简单粗暴的方式，实际应该更精确
            
            Log::info('搜索缓存清理完成');
            return true;
            
        } catch (Exception $e) {
            Log::error('搜索缓存清理失败', [
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }
} 