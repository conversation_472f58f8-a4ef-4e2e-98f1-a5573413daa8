@extends('layouts.app')

@section('title', '心理咨询师 - 心理咨询平台')

@section('custom-styles')
<style>
    /* 覆盖主要容器样式 */
    body .container {
        max-width: 500px;
        padding: 0;
        margin: 0 auto;
        overflow-x: hidden;
        height: auto;
        min-height: 100vh;
    }
    
    /* 页面标题样式 */
    .module-header {
        background: linear-gradient(45deg, #5b7cef, #3c67e3);
        background-size: cover;
        background-position: center;
        color: white;
        padding: 15px;
        position: relative;
        text-align: center;
        height: 56px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 10px rgba(60, 103, 227, 0.15);
    }
    
    .module-header::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: radial-gradient(circle at top right, rgba(255,255,255,0.1), transparent 70%);
        z-index: 1;
    }
    
    .module-title {
        font-size: 18px;
        font-weight: 600;
        position: relative;
        z-index: 2;
        color: white;
        text-shadow: 0 1px 2px rgba(0,0,0,0.2);
        letter-spacing: 0.5px;
    }
    
    .back-button {
        position: absolute;
        left: 15px;
        top: 50%;
        transform: translateY(-50%);
        z-index: 10;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: rgba(255,255,255,0.2);
        border-radius: 50%;
    }
    
    .back-icon {
        width: 16px;
        height: 16px;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z'/%3E%3C/svg%3E");
        background-repeat: no-repeat;
        background-size: contain;
    }
    
    /* 内容模块容器 */
    .module-section {
        width: 100%;
    }
    
    /* AI咨询卡片样式 */
    .ai-consult-card {
        margin: 15px;
        border-radius: 10px;
        background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
        padding: 15px;
        color: white;
        display: flex;
        align-items: center;
        box-shadow: 0 5px 15px rgba(37, 117, 252, 0.2);
        overflow: hidden;
        position: relative;
    }
    
    .ai-consult-card::after {
        content: '';
        position: absolute;
        top: -20px;
        right: -20px;
        width: 100px;
        height: 100px;
        border-radius: 50%;
        background: rgba(255,255,255,0.1);
        z-index: 0;
    }
    
    .ai-icon {
        flex-shrink: 0;
        width: 42px;
        height: 42px;
        background-color: rgba(255,255,255,0.2);
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        position: relative;
        z-index: 1;
    }
    
    .ai-icon img {
        width: 24px;
        height: 24px;
    }
    
    .ai-content {
        flex-grow: 1;
        position: relative;
        z-index: 1;
    }
    
    .ai-title {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 4px;
    }
    
    .ai-desc {
        font-size: 12px;
        opacity: 0.9;
    }
    
    .ai-arrow {
        flex-shrink: 0;
        width: 20px;
        height: 20px;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z'/%3E%3C/svg%3E");
        background-repeat: no-repeat;
        background-size: contain;
        position: relative;
        z-index: 1;
    }
    
    /* 咨询师列表样式 */
    .counselor-list {
        padding: 0 15px;
        display: flex;
        flex-direction: column;
        width: 100%;
    }
    
    .counselor-item {
        width: 100%;
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        overflow: hidden;
        display: flex;
        margin-bottom: 15px;
        transition: transform 0.2s, box-shadow 0.2s;
    }
    
    .counselor-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    }
    
    .counselor-avatar {
        width: 100px;
        height: 120px;
        flex-shrink: 0;
        position: relative;
    }
    
    .counselor-avatar img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    
    .counselor-info {
        padding: 12px;
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
    }
    
    .counselor-name {
        font-size: 15px;
        font-weight: 600;
        color: #333;
        margin-bottom: 2px;
    }
    
    .counselor-title {
        font-size: 12px;
        color: #666;
        margin-bottom: 6px;
    }
    
    .counselor-expertise {
        font-size: 12px;
        color: #555;
        margin-bottom: 8px;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        line-height: 1.4;
    }
    
    .consultation-type {
        display: flex;
        flex-wrap: wrap;
        gap: 5px;
        margin-top: 5px;
    }
    
    .type-badge {
        background-color: #f5f5f5;
        color: #666;
        border-radius: 4px;
        padding: 2px 6px;
        font-size: 11px;
    }
    
    .counselor-bottom {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 8px;
    }
    
    .counselor-price {
        color: #ff5722;
        font-weight: 600;
        font-size: 14px;
    }
    
    .counselor-button {
        background-color: #4CAF50;
        color: white;
        border: none;
        padding: 5px 12px;
        border-radius: 15px;
        font-size: 12px;
        font-weight: 500;
        text-decoration: none;
        transition: background-color 0.2s;
    }
    
    .counselor-button:hover {
        background-color: #3d9140;
    }
    
    .no-counselors {
        text-align: center;
        padding: 25px 20px;
        color: #666;
        background-color: #f9f9f9;
        border-radius: 8px;
        margin: 15px;
        font-size: 14px;
    }
    
    .pagination-container {
        display: flex;
        justify-content: center;
        margin: 20px 0 30px;
    }
    
    .consultation-type {
        display: flex;
        gap: 5px;
        flex-wrap: wrap;
        margin-top: 5px;
    }
    
    .type-badge {
        background-color: #f5f5f5;
        color: #666;
        border-radius: 4px;
        padding: 2px 6px;
        font-size: 11px;
    }
</style>
@endsection

@section('content')
<!-- 顶部标题区域 -->
<div class="module-header">
    <a href="{{ route('home') }}" class="back-button">
        <div class="back-icon"></div>
    </a>
    <div class="module-title">心理咨询师</div>
</div>

<!-- 内容容器 -->
<div class="module-section">
    <!-- AI咨询入口 -->
    <a href="{{ route('consultation.ai_consultation') }}" style="text-decoration: none;">
        <div class="ai-consult-card">
            <div class="ai-icon">
                <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M21 11.5a8.38 8.38 0 0 1-.9 3.8 8.5 8.5 0 0 1-7.6 4.7 8.38 8.38 0 0 1-3.8-.9L3 21l1.9-5.7a8.38 8.38 0 0 1-.9-3.8 8.5 8.5 0 0 1 4.7-7.6 8.38 8.38 0 0 1 3.8-.9h.5a8.48 8.48 0 0 1 8 8v.5z'/%3E%3C/svg%3E" alt="AI咨询">
            </div>
            <div class="ai-content">
                <div class="ai-title">AI智能心理咨询</div>
                <div class="ai-desc">即时定制化咨询，24小时随时可用</div>
            </div>
            <div class="ai-arrow"></div>
        </div>
    </a>
    
    <a href="{{ route('message_consultation.my_consultations') }}" style="text-decoration: none;">
        <div class="ai-consult-card">
            <div class="ai-icon">
                <img
                  src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M20 4H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z'/%3E%3C/svg%3E"
                  alt="留言咨询"
                >
            </div>
            <div class="ai-content">
                <div class="ai-title">留言咨询</div>
                <div class="ai-desc">无人值班可留言咨询，24小时内回复</div>
            </div>
            <div class="ai-arrow"></div>
        </div>
    </a>

    
    <!-- 咨询师列表 -->
    <div class="counselor-list">
        @if(count($counselors) > 0)
            @foreach($counselors as $counselor)
            <div class="counselor-item" onclick="window.location.href='{{ route("consultation.counselor_detail", ["id" => $counselor->id]) }}'" style="cursor: pointer;">
                <div class="counselor-avatar">
                    <img src="{{ $counselor->avatar ? asset('storage/'.$counselor->avatar) : asset('images/default-avatar.jpg') }}" alt="{{ $counselor->name }}">
                </div>
                <div class="counselor-info">
                    <div>
                        <div class="counselor-name">{{ $counselor->name }}</div>
                        <div class="counselor-title">{{ $counselor->title ?: '心理咨询师' }}</div>
                        <div class="counselor-expertise">{!! Str::limit(strip_tags($counselor->expertise), 60) ?: '擅长心理咨询与辅导' !!}</div>
                        @if($counselor->support_text || $counselor->support_voice || $counselor->support_video)
                        <div class="consultation-type">
                            @if($counselor->support_text)
                                <span class="type-badge">文字咨询</span>
                            @endif
                            @if($counselor->support_voice)
                                <span class="type-badge">语音咨询</span>
                            @endif
                            @if($counselor->support_video)
                                <span class="type-badge">视频咨询</span>
                            @endif
                        </div>
                    @endif

                    </div>
                    <div class="counselor-bottom">
                        <!-- <div class="counselor-price">
                            {{ $counselor->price > 0 ? '¥'.$counselor->price.'/次' : '价格面议' }}
                        </div> -->
                        <a href="{{ route('consultation.counselor_detail', ['id' => $counselor->id]) }}" class="counselor-button">咨询详情</a>
                    </div>
                </div>
            </div>
            @endforeach
        @else
            <div class="no-counselors">
                <p>暂无可咨询的心理医师</p>
            </div>
        @endif
    </div>
    
    <!-- 分页 -->
    @if(count($counselors) > 0)
    <div class="pagination-container">
        {{ $counselors->appends(request()->query())->links() }}
    </div>
    @endif
</div>
@endsection
