# 心理健康平台 Web 接口文档

## 项目概述
基于Laravel框架开发的心理健康服务平台Web接口文档，包含用户认证、心理咨询、评估测试、知识学习等功能模块。

## 基础信息
- **框架**: Laravel
- **协议**: HTTP/HTTPS
- **认证方式**: Session认证 + 中间件
- **数据格式**: JSON/HTML

## 中间件说明
- `guest`: 仅未登录用户可访问
- `auth`: 需要用户登录
- `counselor.auth`: 需要咨询师登录

---

## 1. 认证相关接口

### 1.1 用户登录页面
- **URL**: `/login`
- **方法**: GET
- **中间件**: guest
- **控制器**: AuthController@showLogin
- **描述**: 显示用户登录页面

### 1.2 密码登录
- **URL**: `/login/password`
- **方法**: POST
- **中间件**: guest
- **控制器**: AuthController@loginWithPassword
- **参数**: 
  - `email`: 邮箱/手机号
  - `password`: 密码
- **描述**: 使用密码登录

### 1.3 验证码登录
- **URL**: `/login/code`
- **方法**: POST
- **中间件**: guest
- **控制器**: AuthController@loginWithCode
- **参数**:
  - `phone`: 手机号
  - `code`: 验证码
- **描述**: 使用短信验证码登录

### 1.4 发送登录验证码
- **URL**: `/login/send-code`
- **方法**: POST
- **中间件**: guest
- **控制器**: AuthController@sendLoginCode
- **参数**:
  - `phone`: 手机号
- **描述**: 发送登录短信验证码

### 1.5 用户注册页面
- **URL**: `/register`
- **方法**: GET
- **中间件**: guest
- **控制器**: AuthController@showRegister
- **描述**: 显示用户注册页面

### 1.6 用户注册
- **URL**: `/register`
- **方法**: POST
- **中间件**: guest
- **控制器**: AuthController@register
- **参数**:
  - `name`: 用户名
  - `phone`: 手机号
  - `code`: 验证码
  - `password`: 密码
- **描述**: 用户注册

### 1.7 发送注册验证码
- **URL**: `/register/send-code`
- **方法**: POST
- **中间件**: guest
- **控制器**: AuthController@sendRegisterCode
- **参数**:
  - `phone`: 手机号
- **描述**: 发送注册短信验证码

### 1.8 用户登出
- **URL**: `/logout`
- **方法**: POST
- **控制器**: AuthController@logout
- **描述**: 用户登出

### 1.9 保存跳转URL
- **URL**: `/save-intended-url`
- **方法**: POST
- **参数**:
  - `intended_url`: 目标URL
- **响应**: `{"success": true}`
- **描述**: 保存用户登录后要跳转的URL

---

## 2. 首页接口

### 2.1 首页
- **URL**: `/`
- **方法**: GET
- **控制器**: HomeController@index
- **路由名**: home
- **描述**: 平台首页

---

## 3. 心理减压模块

### 3.1 心理减压首页
- **URL**: `/stress-relief/`
- **方法**: GET
- **路由名**: stress_relief.index
- **描述**: 心理减压功能首页

### 3.2 睡眠调整
- **URL**: `/stress-relief/sleep`
- **方法**: GET
- **路由名**: stress_relief.sleep
- **描述**: 睡眠调整功能页面

### 3.3 呼吸训练
- **URL**: `/stress-relief/breathing`
- **方法**: GET
- **路由名**: stress_relief.breathing
- **描述**: 呼吸训练功能页面

### 3.4 音乐疗愈
- **URL**: `/stress-relief/music`
- **方法**: GET
- **路由名**: stress_relief.music
- **描述**: 音乐疗愈功能页面

### 3.5 正念冥想
- **URL**: `/stress-relief/meditation`
- **方法**: GET
- **路由名**: stress_relief.meditation
- **描述**: 正念冥想功能页面

### 3.6 大脑按摩
- **URL**: `/stress-relief/brain-massage`
- **方法**: GET
- **路由名**: stress_relief.brain_massage
- **描述**: 大脑按摩功能页面

### 3.7 影视欣赏
- **URL**: `/stress-relief/video`
- **方法**: GET
- **路由名**: stress_relief.video
- **描述**: 影视欣赏功能页面

---

## 4. 咨询师系统接口

### 4.1 咨询师登录页面
- **URL**: `/counselor/login`
- **方法**: GET
- **控制器**: Counselor\AuthController@showLogin
- **路由名**: counselor.login
- **描述**: 咨询师登录页面

### 4.2 咨询师登录
- **URL**: `/counselor/login`
- **方法**: POST
- **控制器**: Counselor\AuthController@login
- **路由名**: counselor.login.post
- **参数**:
  - `email`: 邮箱
  - `password`: 密码
- **描述**: 咨询师登录

### 4.3 咨询师登出
- **URL**: `/counselor/logout`
- **方法**: POST
- **控制器**: Counselor\AuthController@logout
- **路由名**: counselor.logout
- **描述**: 咨询师登出

### 4.4 忘记密码页面
- **URL**: `/counselor/forgot-password`
- **方法**: GET
- **控制器**: Counselor\ProfileController@showForgotPassword
- **路由名**: counselor.forgot_password
- **描述**: 咨询师忘记密码页面

### 4.5 发送重置密码验证码
- **URL**: `/counselor/forgot-password/send-code`
- **方法**: POST
- **控制器**: Counselor\ProfileController@sendResetCode
- **路由名**: counselor.forgot_password.send_code
- **参数**:
  - `phone`: 手机号
- **描述**: 发送密码重置验证码

### 4.6 重置密码
- **URL**: `/counselor/forgot-password/reset`
- **方法**: POST
- **控制器**: Counselor\ProfileController@resetPassword
- **路由名**: counselor.forgot_password.reset
- **参数**:
  - `phone`: 手机号
  - `code`: 验证码
  - `password`: 新密码
- **描述**: 重置咨询师密码

### 4.7 咨询师仪表盘
- **URL**: `/counselor/`
- **方法**: GET
- **中间件**: counselor.auth
- **控制器**: Counselor\DashboardController@index
- **路由名**: counselor.dashboard
- **描述**: 咨询师工作台首页

### 4.8 咨询师个人资料页面
- **URL**: `/counselor/profile`
- **方法**: GET
- **中间件**: counselor.auth
- **控制器**: Counselor\ProfileController@show
- **路由名**: counselor.profile
- **描述**: 查看咨询师个人资料

### 4.9 更新咨询师个人资料
- **URL**: `/counselor/profile`
- **方法**: POST
- **中间件**: counselor.auth
- **控制器**: Counselor\ProfileController@update
- **路由名**: counselor.profile.update
- **参数**:
  - `name`: 姓名
  - `phone`: 手机号
  - `bio`: 个人简介
  - 其他资料字段
- **描述**: 更新咨询师个人资料

### 4.10 预约管理列表
- **URL**: `/counselor/appointments`
- **方法**: GET
- **中间件**: counselor.auth
- **控制器**: Counselor\AppointmentController@index
- **路由名**: counselor.appointments
- **描述**: 查看咨询师的预约列表

### 4.11 确认预约
- **URL**: `/counselor/appointments/{id}/confirm`
- **方法**: POST
- **中间件**: counselor.auth
- **控制器**: Counselor\AppointmentController@confirm
- **路由名**: counselor.appointments.confirm
- **参数**:
  - `id`: 预约ID
- **描述**: 确认用户预约

### 4.12 拒绝预约
- **URL**: `/counselor/appointments/{id}/reject`
- **方法**: POST
- **中间件**: counselor.auth
- **控制器**: Counselor\AppointmentController@reject
- **路由名**: counselor.appointments.reject
- **参数**:
  - `id`: 预约ID
  - `reason`: 拒绝原因
- **描述**: 拒绝用户预约

### 4.13 开始咨询
- **URL**: `/counselor/appointments/{id}/start`
- **方法**: POST
- **中间件**: counselor.auth
- **控制器**: Counselor\AppointmentController@start
- **路由名**: counselor.appointments.start
- **参数**:
  - `id`: 预约ID
- **描述**: 开始咨询会话

### 4.14 完成咨询
- **URL**: `/counselor/appointments/{id}/complete`
- **方法**: POST
- **中间件**: counselor.auth
- **控制器**: Counselor\AppointmentController@complete
- **路由名**: counselor.appointments.complete
- **参数**:
  - `id`: 预约ID
  - `summary`: 咨询总结
- **描述**: 完成咨询会话

### 4.15 取消预约
- **URL**: `/counselor/appointments/{id}/cancel`
- **方法**: POST
- **中间件**: counselor.auth
- **控制器**: Counselor\AppointmentController@cancel
- **路由名**: counselor.appointments.cancel
- **参数**:
  - `id`: 预约ID
  - `reason`: 取消原因
- **描述**: 取消预约

### 4.16 消息管理列表
- **URL**: `/counselor/messages`
- **方法**: GET
- **中间件**: counselor.auth
- **控制器**: Counselor\MessageController@index
- **路由名**: counselor.messages
- **描述**: 查看咨询师的消息列表

### 4.17 查看与用户的对话
- **URL**: `/counselor/messages/{userId}`
- **方法**: GET
- **中间件**: counselor.auth
- **控制器**: Counselor\MessageController@show
- **路由名**: counselor.messages.show
- **参数**:
  - `userId`: 用户ID
- **描述**: 查看与特定用户的对话

### 4.18 发送消息给用户
- **URL**: `/counselor/messages/{userId}/send`
- **方法**: POST
- **中间件**: counselor.auth
- **控制器**: Counselor\MessageController@send
- **路由名**: counselor.messages.send
- **参数**:
  - `userId`: 用户ID
  - `message`: 消息内容
- **描述**: 向用户发送消息

### 4.19 获取未读消息数
- **URL**: `/counselor/messages/unread/count`
- **方法**: GET
- **中间件**: counselor.auth
- **控制器**: Counselor\MessageController@getUnreadCount
- **路由名**: counselor.messages.unread
- **响应**: `{"unread_count": 5}`
- **描述**: 获取咨询师未读消息数量

### 4.20 检查新消息
- **URL**: `/counselor/messages/{userId}/check-new`
- **方法**: GET
- **中间件**: counselor.auth
- **控制器**: Counselor\MessageController@checkNewMessages
- **路由名**: counselor.messages.check-new
- **参数**:
  - `userId`: 用户ID
- **响应**: `{"has_new": true, "messages": [...]}`
- **描述**: 检查与特定用户是否有新消息

---

## 5. 基础页面接口

### 5.1 课程页
- **URL**: `/courses`
- **方法**: GET
- **路由名**: courses
- **描述**: 课程列表页面

### 5.2 学习页
- **URL**: `/study`
- **方法**: GET
- **控制器**: HomeController@study
- **路由名**: study
- **描述**: 用户学习进度页面

### 5.3 个人页
- **URL**: `/my`
- **方法**: GET
- **路由名**: my
- **描述**: 用户个人中心页面

---

## 6. 用户个人管理接口

### 6.1 个人资料页面
- **URL**: `/user/profile`
- **方法**: GET
- **中间件**: auth
- **控制器**: HomeController@profile
- **路由名**: user.profile
- **描述**: 查看个人资料

### 6.2 更新个人资料
- **URL**: `/user/profile/update`
- **方法**: POST
- **中间件**: auth
- **控制器**: HomeController@updateProfile
- **路由名**: user.profile.update
- **参数**:
  - `name`: 姓名
  - `phone`: 手机号
  - `avatar`: 头像
  - 其他个人信息
- **描述**: 更新个人资料

### 6.3 账号管理页面
- **URL**: `/user/account`
- **方法**: GET
- **中间件**: auth
- **控制器**: HomeController@account
- **路由名**: user.account
- **描述**: 账号管理页面

### 6.4 修改密码
- **URL**: `/user/change-password`
- **方法**: POST
- **中间件**: auth
- **控制器**: HomeController@changePassword
- **路由名**: user.change_password
- **参数**:
  - `current_password`: 当前密码
  - `new_password`: 新密码
  - `new_password_confirmation`: 确认新密码
- **描述**: 修改用户密码

### 6.5 设置页面
- **URL**: `/user/settings`
- **方法**: GET
- **中间件**: auth
- **控制器**: HomeController@settings
- **路由名**: user.settings
- **描述**: 用户设置页面

### 6.6 更新设置
- **URL**: `/user/settings/update`
- **方法**: POST
- **中间件**: auth
- **控制器**: HomeController@updateSettings
- **路由名**: user.settings.update
- **参数**:
  - 各种设置选项
- **描述**: 更新用户设置

---

## 7. 帮助中心接口

### 7.1 帮助中心
- **URL**: `/help`
- **方法**: GET
- **控制器**: HomeController@help
- **路由名**: help
- **描述**: 帮助中心页面

### 7.2 联系我们页面
- **URL**: `/contact`
- **方法**: GET
- **控制器**: HomeController@contact
- **路由名**: contact
- **描述**: 联系我们页面

### 7.3 提交联系表单
- **URL**: `/contact/submit`
- **方法**: POST
- **控制器**: HomeController@contactSubmit
- **路由名**: contact.submit
- **参数**:
  - `name`: 姓名
  - `email`: 邮箱
  - `subject`: 主题
  - `message`: 留言内容
- **描述**: 提交联系表单

### 7.4 关于我们
- **URL**: `/about`
- **方法**: GET
- **控制器**: HomeController@about
- **路由名**: about
- **描述**: 关于我们页面

---

## 8. 心理科普模块（知识库）

### 8.1 心理文章列表
- **URL**: `/knowledge/articles`
- **方法**: GET
- **控制器**: KnowledgeController@articles
- **路由名**: knowledge.articles
- **参数**:
  - `category`: 分类ID（可选）
  - `tag`: 标签ID（可选）
  - `page`: 页码（可选）
- **描述**: 获取心理文章列表

### 8.2 心理文章详情
- **URL**: `/knowledge/articles/{id}`
- **方法**: GET
- **控制器**: KnowledgeController@articleDetail
- **路由名**: knowledge.article_detail
- **参数**:
  - `id`: 文章ID
- **描述**: 查看文章详情

### 8.3 搜索文章
- **URL**: `/knowledge/search`
- **方法**: GET
- **控制器**: KnowledgeController@search
- **路由名**: knowledge.search
- **参数**:
  - `q`: 搜索关键词
  - `type`: 搜索类型（article/video/course）
- **描述**: 搜索知识库内容

### 8.4 心理课堂列表
- **URL**: `/knowledge/courses`
- **方法**: GET
- **控制器**: KnowledgeController@courses
- **路由名**: knowledge.courses
- **参数**:
  - `category`: 分类ID（可选）
  - `page`: 页码（可选）
- **描述**: 获取心理课程列表

### 8.5 心理课堂详情
- **URL**: `/knowledge/courses/{id}`
- **方法**: GET
- **控制器**: KnowledgeController@courseDetail
- **路由名**: knowledge.course_detail
- **参数**:
  - `id`: 课程ID
- **描述**: 查看课程详情

### 8.6 更新课程进度
- **URL**: `/knowledge/course_progress/{id}`
- **方法**: POST
- **控制器**: KnowledgeController@updateCourseProgress
- **路由名**: knowledge.course_progress
- **参数**:
  - `id`: 课程ID
  - `progress`: 进度百分比
- **描述**: 更新用户课程学习进度

### 8.7 重置课程进度
- **URL**: `/knowledge/course_progress/{id}/reset`
- **方法**: POST
- **控制器**: KnowledgeController@resetCourseProgress
- **路由名**: knowledge.reset_course_progress
- **参数**:
  - `id`: 课程ID
- **描述**: 重置用户课程学习进度

### 8.8 心理视频列表
- **URL**: `/knowledge/videos`
- **方法**: GET
- **控制器**: KnowledgeController@videos
- **路由名**: knowledge.videos
- **参数**:
  - `category`: 分类ID（可选）
  - `tag`: 标签ID（可选）
  - `page`: 页码（可选）
- **描述**: 获取心理视频列表

### 8.9 心理视频详情
- **URL**: `/knowledge/videos/{id}`
- **方法**: GET
- **控制器**: KnowledgeController@videoDetail
- **路由名**: knowledge.video_detail
- **参数**:
  - `id`: 视频ID
- **描述**: 查看视频详情

---

## 9. 心理测评模块

### 9.1 心理测评首页
- **URL**: `/assessment/`
- **方法**: GET
- **控制器**: AssessmentController@index
- **路由名**: assessment.index
- **描述**: 心理测评首页，显示可用问卷

### 9.2 我的测评记录
- **URL**: `/assessment/my-records`
- **方法**: GET
- **中间件**: auth
- **控制器**: AssessmentController@myRecords
- **路由名**: assessment.my-records
- **描述**: 查看用户的测评历史记录

### 9.3 测评结果详情
- **URL**: `/assessment/result/{id}`
- **方法**: GET
- **控制器**: AssessmentController@result
- **路由名**: assessment.result
- **参数**:
  - `id`: 测评结果ID
- **描述**: 查看测评结果详情

### 9.4 问卷详情
- **URL**: `/assessment/{id}`
- **方法**: GET
- **控制器**: AssessmentController@show
- **路由名**: assessment.show
- **参数**:
  - `id`: 问卷ID
- **描述**: 查看问卷详情和介绍

### 9.5 开始测评
- **URL**: `/assessment/{id}/start`
- **方法**: GET
- **控制器**: AssessmentController@start
- **路由名**: assessment.start
- **参数**:
  - `id`: 问卷ID
- **描述**: 开始进行心理测评

### 9.6 提交测评答案
- **URL**: `/assessment/submit`
- **方法**: POST
- **中间件**: auth
- **控制器**: AssessmentController@submit
- **路由名**: assessment.submit
- **参数**:
  - `questionnaire_id`: 问卷ID
  - `answers`: 答案数组
- **描述**: 提交测评答案

### 9.7 检查AI分析状态
- **URL**: `/assessment/ai-analysis-status/{responseId}`
- **方法**: GET
- **中间件**: auth
- **控制器**: AssessmentController@checkAiAnalysisStatus
- **路由名**: assessment.ai_analysis_status
- **参数**:
  - `responseId`: 测评回复ID
- **响应**: `{"status": "completed", "analysis": "..."}`
- **描述**: 检查AI分析是否完成

### 9.8 生成二维码
- **URL**: `/assessment/qr-code/{responseId}`
- **方法**: GET
- **中间件**: auth
- **控制器**: AssessmentController@generateQrCode
- **路由名**: assessment.generate_qr_code
- **参数**:
  - `responseId`: 测评回复ID
- **描述**: 生成测评结果分享二维码

### 9.9 保存截图
- **URL**: `/assessment/screenshot/{responseId}`
- **方法**: POST
- **中间件**: auth
- **控制器**: AssessmentController@saveScreenshot
- **路由名**: assessment.save_screenshot
- **参数**:
  - `responseId`: 测评回复ID
  - `screenshot`: 截图文件
- **描述**: 保存测评结果截图

### 9.10 趣味测评首页
- **URL**: `/assessment/fun`
- **方法**: GET
- **路由名**: assessment.fun_tests
- **描述**: 趣味测评首页

### 9.11 趣味测评详情
- **URL**: `/assessment/fun/{id}`
- **方法**: GET
- **路由名**: assessment.fun_test_detail
- **参数**:
  - `id`: 趣味测评ID
- **描述**: 查看趣味测评详情

### 9.12 提交趣味测评
- **URL**: `/assessment/fun/{id}/submit`
- **方法**: POST
- **路由名**: assessment.fun_test_submit
- **参数**:
  - `id`: 趣味测评ID
  - `answers`: 答案数组
- **描述**: 提交趣味测评答案

### 9.13 趣味测评结果
- **URL**: `/assessment/fun/{id}/result`
- **方法**: GET
- **路由名**: assessment.fun_test_result
- **参数**:
  - `id`: 趣味测评ID
- **描述**: 查看趣味测评结果

---

## 10. 心理咨询模块

### 10.1 咨询师列表
- **URL**: `/consultation/counselors`
- **方法**: GET
- **控制器**: ConsultationController@counselors
- **路由名**: consultation.counselors
- **参数**:
  - `specialty`: 专业领域（可选）
  - `page`: 页码（可选）
- **描述**: 获取咨询师列表

### 10.2 咨询师详情
- **URL**: `/consultation/counselor/{id}`
- **方法**: GET
- **控制器**: ConsultationController@counselorDetail
- **路由名**: consultation.counselor_detail
- **参数**:
  - `id`: 咨询师ID
- **描述**: 查看咨询师详细信息

### 10.3 AI心理咨询页面
- **URL**: `/consultation/ai-consultation`
- **方法**: GET
- **控制器**: ConsultationController@aiConsultation
- **路由名**: consultation.ai_consultation
- **描述**: AI心理咨询对话页面

### 10.4 发送AI咨询消息
- **URL**: `/consultation/ai-consultation/send`
- **方法**: POST
- **控制器**: ConsultationController@aiConsultationSend
- **路由名**: consultation.ai_consultation_send
- **参数**:
  - `message`: 用户消息
- **响应**: `{"reply": "AI回复内容"}`
- **描述**: 向AI发送咨询消息并获取回复

### 10.5 线下课程列表
- **URL**: `/consultation/offline-courses`
- **方法**: GET
- **控制器**: ConsultationController@offlineCourses
- **路由名**: consultation.offline_courses
- **参数**:
  - `category`: 课程分类（可选）
  - `page`: 页码（可选）
- **描述**: 获取线下课程列表

### 10.6 线下课程详情
- **URL**: `/consultation/offline-course/{id}`
- **方法**: GET
- **中间件**: auth
- **控制器**: ConsultationController@offlineCourseDetail
- **路由名**: consultation.offline_course_detail
- **参数**:
  - `id`: 课程ID
- **描述**: 查看线下课程详情

### 10.7 创建预约
- **URL**: `/consultation/create-appointment`
- **方法**: POST
- **中间件**: auth
- **控制器**: ConsultationController@createAppointment
- **路由名**: consultation.create_appointment
- **参数**:
  - `counselor_id`: 咨询师ID
  - `appointment_date`: 预约日期
  - `appointment_time`: 预约时间
  - `consultation_type`: 咨询类型
  - `description`: 问题描述
- **描述**: 创建咨询预约

### 10.8 我的预约
- **URL**: `/consultation/my-appointments`
- **方法**: GET
- **中间件**: auth
- **控制器**: ConsultationController@myAppointments
- **路由名**: consultation.my_appointments
- **描述**: 查看用户的预约列表

### 10.9 取消预约
- **URL**: `/consultation/cancel-appointment/{id}`
- **方法**: POST
- **中间件**: auth
- **控制器**: ConsultationController@cancelAppointment
- **路由名**: consultation.cancel_appointment
- **参数**:
  - `id`: 预约ID
  - `reason`: 取消原因
- **描述**: 取消咨询预约

### 10.10 开始咨询
- **URL**: `/consultation/start-consultation/{id}`
- **方法**: GET
- **中间件**: auth
- **控制器**: ConsultationController@startConsultation
- **路由名**: consultation.start_consultation
- **参数**:
  - `id`: 预约ID
- **描述**: 开始咨询会话

### 10.11 课程报名
- **URL**: `/consultation/register-course`
- **方法**: POST
- **中间件**: auth
- **控制器**: ConsultationController@registerCourse
- **路由名**: consultation.register_course
- **参数**:
  - `course_id`: 课程ID
  - `contact_info`: 联系信息
- **描述**: 报名线下课程

### 10.12 我的课程
- **URL**: `/consultation/my-courses`
- **方法**: GET
- **中间件**: auth
- **控制器**: ConsultationController@myCourses
- **路由名**: consultation.my_courses
- **描述**: 查看用户报名的课程

### 10.13 取消课程报名
- **URL**: `/consultation/cancel-course-registration/{id}`
- **方法**: POST
- **中间件**: auth
- **控制器**: ConsultationController@cancelCourseRegistration
- **路由名**: consultation.cancel_course_registration
- **参数**:
  - `id`: 报名记录ID
  - `reason`: 取消原因
- **描述**: 取消课程报名

---

## 11. 新闻资讯模块

### 11.1 新闻首页
- **URL**: `/news/`
- **方法**: GET
- **控制器**: NewsController@index
- **路由名**: news.index
- **参数**:
  - `category`: 分类ID（可选）
  - `page`: 页码（可选）
- **描述**: 获取新闻资讯列表

### 11.2 新闻详情
- **URL**: `/news/{slug}`
- **方法**: GET
- **控制器**: NewsController@show
- **路由名**: news.show
- **参数**:
  - `slug`: 新闻标识符
- **描述**: 查看新闻详情

### 11.3 推荐新闻API
- **URL**: `/news/api/recommended`
- **方法**: GET
- **控制器**: NewsController@recommended
- **路由名**: news.recommended
- **响应**: JSON格式的推荐新闻列表
- **描述**: 获取推荐新闻（API接口）

### 11.4 记录分享API
- **URL**: `/news/api/{id}/share`
- **方法**: POST
- **控制器**: NewsController@recordShare
- **路由名**: news.share
- **参数**:
  - `id`: 新闻ID
  - `platform`: 分享平台
- **响应**: `{"success": true}`
- **描述**: 记录新闻分享行为

---

## 12. 心理援助热线

### 12.1 热线首页
- **URL**: `/hotline`
- **方法**: GET
- **控制器**: HotlineController@index
- **路由名**: hotline
- **描述**: 心理援助热线页面

### 12.2 记录热线使用
- **URL**: `/hotline/record-usage`
- **方法**: POST
- **控制器**: HotlineController@recordUsage
- **路由名**: hotline.record_usage
- **参数**:
  - `hotline_id`: 热线ID
  - `usage_type`: 使用类型
- **描述**: 记录用户使用热线的行为

---

## 13. 线上直播模块

### 13.1 直播首页
- **URL**: `/live/`
- **方法**: GET
- **控制器**: LiveShowController@index
- **路由名**: live.index
- **参数**:
  - `status`: 直播状态（可选）
  - `page`: 页码（可选）
- **描述**: 获取直播列表

### 13.2 直播详情
- **URL**: `/live/{id}`
- **方法**: GET
- **控制器**: LiveShowController@show
- **路由名**: live.show
- **参数**:
  - `id`: 直播ID
- **描述**: 查看直播详情页面

### 13.3 直播跳转
- **URL**: `/live/{id}/redirect`
- **方法**: POST
- **控制器**: LiveShowController@redirect
- **路由名**: live.redirect
- **参数**:
  - `id`: 直播ID
- **描述**: 跳转到直播间（记录访问）

---

## 14. 有奖问答模块

### 14.1 问答活动列表
- **URL**: `/quiz/`
- **方法**: GET
- **控制器**: QuizController@index
- **路由名**: quiz.index
- **参数**:
  - `status`: 活动状态（可选）
  - `page`: 页码（可选）
- **描述**: 获取问答活动列表

### 14.2 我的奖品
- **URL**: `/quiz/my-prizes`
- **方法**: GET
- **控制器**: QuizController@myPrizes
- **路由名**: quiz.my_prizes
- **描述**: 查看用户获得的奖品

### 14.3 问答活动详情
- **URL**: `/quiz/{id}`
- **方法**: GET
- **控制器**: QuizController@show
- **路由名**: quiz.show
- **参数**:
  - `id`: 活动ID
- **描述**: 查看问答活动详情

### 14.4 开始答题
- **URL**: `/quiz/{id}/start`
- **方法**: GET
- **控制器**: QuizController@start
- **路由名**: quiz.start
- **参数**:
  - `id`: 活动ID
- **描述**: 开始参与问答活动

### 14.5 答题页面
- **URL**: `/quiz/{id}/attempt/{attempt_id}`
- **方法**: GET
- **控制器**: QuizController@attempt
- **路由名**: quiz.attempt
- **参数**:
  - `id`: 活动ID
  - `attempt_id`: 答题记录ID
- **描述**: 显示答题页面

### 14.6 提交答案
- **URL**: `/quiz/{id}/submit/{attempt_id}`
- **方法**: POST
- **控制器**: QuizController@submit
- **路由名**: quiz.submit
- **参数**:
  - `id`: 活动ID
  - `attempt_id`: 答题记录ID
  - `answers`: 答案数组
- **描述**: 提交问答答案

### 14.7 查看结果
- **URL**: `/quiz/{id}/result/{attempt_id}`
- **方法**: GET
- **控制器**: QuizController@result
- **路由名**: quiz.result
- **参数**:
  - `id`: 活动ID
  - `attempt_id`: 答题记录ID
- **描述**: 查看答题结果

### 14.8 领取奖品页面
- **URL**: `/quiz/{id}/claim_prize/{winner_id}`
- **方法**: GET
- **控制器**: QuizController@claimPrize
- **路由名**: quiz.claim_prize
- **参数**:
  - `id`: 活动ID
  - `winner_id`: 中奖记录ID
- **描述**: 显示奖品领取页面

### 14.9 提交奖品领取信息
- **URL**: `/quiz/{id}/submit_claim/{winner_id}`
- **方法**: POST
- **控制器**: QuizController@submitClaim
- **路由名**: quiz.submit_claim
- **参数**:
  - `id`: 活动ID
  - `winner_id`: 中奖记录ID
  - `name`: 收件人姓名
  - `phone`: 联系电话
  - `address`: 收货地址
- **描述**: 提交奖品领取信息

---

## 15. 消息中心模块

### 15.1 消息列表
- **URL**: `/messages/`
- **方法**: GET
- **中间件**: auth
- **控制器**: MessageController@index
- **路由名**: messages.index
- **描述**: 查看用户的消息/会话列表

### 15.2 与咨询师的聊天页面
- **URL**: `/messages/{counselorId}`
- **方法**: GET
- **中间件**: auth
- **控制器**: MessageController@show
- **路由名**: messages.show
- **参数**:
  - `counselorId`: 咨询师ID
- **描述**: 查看与特定咨询师的聊天记录

### 15.3 发送消息
- **URL**: `/messages/{counselorId}/send`
- **方法**: POST
- **中间件**: auth
- **控制器**: MessageController@send
- **路由名**: messages.send
- **参数**:
  - `counselorId`: 咨询师ID
  - `message`: 消息内容
  - `type`: 消息类型（text/image/file）
- **响应**: `{"success": true, "message": {...}}`
- **描述**: 向咨询师发送消息

### 15.4 获取未读消息数
- **URL**: `/messages/unread-count`
- **方法**: GET
- **中间件**: auth
- **控制器**: MessageController@getUnreadCount
- **路由名**: messages.unread_count
- **响应**: `{"unread_count": 3}`
- **描述**: 获取用户未读消息总数

### 15.5 检查新消息
- **URL**: `/messages/{counselorId}/check-new`
- **方法**: GET
- **中间件**: auth
- **控制器**: MessageController@checkNewMessages
- **路由名**: messages.check-new
- **参数**:
  - `counselorId`: 咨询师ID
  - `last_message_id`: 最后一条消息ID（可选）
- **响应**: `{"has_new": true, "messages": [...]}`
- **描述**: 检查与特定咨询师是否有新消息

---

## 16. 留言咨询模块

### 16.1 留言咨询首页
- **URL**: `/message-consultation/`
- **方法**: GET
- **控制器**: ConsultationMessageController@index
- **路由名**: message_consultation.index
- **描述**: 留言咨询首页

### 16.2 提交留言
- **URL**: `/message-consultation/submit`
- **方法**: POST
- **控制器**: ConsultationMessageController@store
- **路由名**: message_consultation.submit
- **参数**:
  - `name`: 姓名
  - `phone`: 联系电话
  - `email`: 邮箱（可选）
  - `subject`: 咨询主题
  - `content`: 咨询内容
- **描述**: 提交留言咨询

### 16.3 我的留言列表
- **URL**: `/message-consultation/my-consultations`
- **方法**: GET
- **中间件**: auth
- **控制器**: ConsultationMessageController@myConsultations
- **路由名**: message_consultation.my_consultations
- **描述**: 查看用户的留言咨询记录

### 16.4 回复留言
- **URL**: `/message-consultation/{consultation}/reply`
- **方法**: POST
- **中间件**: auth
- **控制器**: ConsultationMessageController@reply
- **路由名**: message_consultation.reply
- **参数**:
  - `consultation`: 咨询记录ID
  - `content`: 回复内容
- **描述**: 回复留言咨询

---

## 接口响应格式

### 成功响应
```json
{
    "success": true,
    "data": {...},
    "message": "操作成功"
}
```

### 错误响应
```json
{
    "success": false,
    "error": "错误信息",
    "code": 400
}
```

### 分页响应
```json
{
    "data": [...],
    "current_page": 1,
    "last_page": 10,
    "per_page": 15,
    "total": 150
}
```

---

## 状态码说明

- **200**: 请求成功
- **201**: 创建成功
- **400**: 请求参数错误
- **401**: 未认证
- **403**: 权限不足
- **404**: 资源不存在
- **422**: 表单验证失败
- **500**: 服务器内部错误

---

## 开发团队

- **后端开发**: 刘博涛
- **前端开发**: 王岩
- **UI设计**: 李永盛
- **商务对接**: 曹健鹏

---

*本文档基于 routes/web.php 文件生成，涵盖了心理健康平台的所有Web接口。*
