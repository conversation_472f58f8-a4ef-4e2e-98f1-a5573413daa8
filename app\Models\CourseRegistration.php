<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Dcat\Admin\Traits\HasDateTimeFormatter;

class CourseRegistration extends Model
{
    use HasFactory, SoftDeletes, HasDateTimeFormatter;

    protected $fillable = [
        'user_id',
        'course_id',
        'registration_code',
        'status',
        'department',
        'participants_count',
        'participants_info',
        'remarks',
        'price',
        'payment_status'
    ];

    protected $casts = [
        'participants_info' => 'array',
    ];

    // 状态常量
    const STATUS_CANCELLED = 0;   // 已取消
    const STATUS_PENDING = 1;     // 已驳回
    const STATUS_CONFIRMED = 2;   // 已确认
    const STATUS_REGISTERED = 3;  // 已报名

    // 支付状态常量
    const PAYMENT_UNPAID = 0;    // 未支付
    const PAYMENT_PAID = 1;      // 已支付
    const PAYMENT_REFUNDED = 2;  // 已退款

    // 状态映射
    public static function getStatusMap()
    {
        return [
            self::STATUS_CANCELLED => '已取消',
            self::STATUS_PENDING => '已驳回',
            self::STATUS_CONFIRMED => '已确认',
            self::STATUS_REGISTERED => '已报名',
        ];
    }

    // 支付状态映射
    public static function getPaymentStatusMap()
    {
        return [
            self::PAYMENT_UNPAID => '未支付',
            self::PAYMENT_PAID => '已支付',
            self::PAYMENT_REFUNDED => '已退款',
        ];
    }

    // 获取状态文本
    public function getStatusTextAttribute()
    {
        return self::getStatusMap()[$this->status] ?? '未知';
    }

    // 获取支付状态文本
    public function getPaymentStatusTextAttribute()
    {
        return self::getPaymentStatusMap()[$this->payment_status] ?? '未知';
    }

    // 与用户的关联
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    // 与课程的关联
    public function course()
    {
        return $this->belongsTo(OfflineCourse::class, 'course_id');
    }

    // 生成唯一报名编号
    public static function generateCode()
    {
        $prefix = 'CR';
        $date = date('ymd');
        $random = mt_rand(1000, 9999);
        
        $code = $prefix . $date . $random;
        
        // 确保唯一性
        while (self::where('registration_code', $code)->exists()) {
            $random = mt_rand(1000, 9999);
            $code = $prefix . $date . $random;
        }
        
        return $code;
    }

    // 检查是否可以取消
    public function canCancel()
    {
        if (!$this->course) {
            return false;
        }
        
        return in_array($this->status, [self::STATUS_PENDING, self::STATUS_CONFIRMED, self::STATUS_REGISTERED]) && 
               $this->course->start_time->gt(now()->addHours(24));
    }
}
