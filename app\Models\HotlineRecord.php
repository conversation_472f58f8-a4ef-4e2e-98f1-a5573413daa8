<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Dcat\Admin\Traits\HasDateTimeFormatter;

class HotlineRecord extends Model
{
    use HasFactory, HasDateTimeFormatter;

    protected $fillable = [
        'hotline_id',
        'user_id',
        'action',
        'ip_address',
        'user_agent',
    ];

    /**
     * 获取操作类型映射
     */
    public static function getActionMap()
    {
        return [
            'call' => '电话咨询',
            'chat' => '在线聊天',
        ];
    }

    /**
     * 获取操作类型文本
     */
    public function getActionTextAttribute()
    {
        return self::getActionMap()[$this->action] ?? '未知';
    }

    /**
     * 关联热线
     */
    public function hotline()
    {
        return $this->belongsTo(Hotline::class);
    }

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
