<?php

namespace App\Services;

use App\Models\ContentIndex;
use App\Contracts\HasContentIndex;
use App\Services\AlibabaVectorService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Exception;

/**
 * 内容索引服务
 * 
 * 负责管理内容的向量索引，包括创建、更新、删除和同步
 */
class ContentIndexService
{
    /**
     * 向量服务实例
     */
    private AlibabaVectorService $vectorService;

    /**
     * 构造函数
     */
    public function __construct(AlibabaVectorService $vectorService)
    {
        $this->vectorService = $vectorService;
    }

    /**
     * 为内容创建或更新索引
     *
     * @param mixed $model 实现了HasContentIndex接口的模型
     * @return bool 是否成功
     */
    public function indexContent($model): bool
    {
        // 检查模型是否实现了HasContentIndex接口
        if (!$this->implementsHasContentIndex($model)) {
            throw new Exception('模型必须实现HasContentIndex接口');
        }
        
        try {
            DB::beginTransaction();

            // 获取或创建内容索引记录
            $contentIndex = $this->getOrCreateContentIndex($model);
            
            // 更新基本信息
            $this->updateContentIndexData($contentIndex, $model);
            
            // 生成并存储向量
            $success = $this->generateAndStoreVector($contentIndex, $model);
            
            if ($success) {
                DB::commit();
                Log::info('内容索引创建/更新成功', [
                    'model_type' => get_class($model),
                    'model_id' => $model->id,
                    'index_id' => $contentIndex->id,
                ]);
                return true;
            } else {
                DB::rollBack();
                return false;
            }

        } catch (Exception $e) {
            DB::rollBack();
            Log::error('内容索引创建/更新失败', [
                'model_type' => get_class($model),
                'model_id' => $model->id,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * 批量索引内容
     *
     * @param array $models 模型数组
     * @return array 处理结果统计
     */
    public function batchIndexContent(array $models): array
    {
        $stats = [
            'total' => count($models),
            'success' => 0,
            'failed' => 0,
            'errors' => [],
        ];

        $batchData = [];
        
        foreach ($models as $model) {
            try {
                if (!$this->implementsHasContentIndex($model)) {
                    throw new Exception('模型必须实现HasContentIndex接口');
                }

                // 准备批量数据
                $contentIndex = $this->getOrCreateContentIndex($model);
                $this->updateContentIndexData($contentIndex, $model);
                
                // 生成向量
                $content = $model->getContentForVector();
                if (empty($content)) {
                    throw new Exception('内容为空，无法生成向量');
                }

                $vector = $this->vectorService->generateTextVector($content);
                if (empty($vector)) {
                    throw new Exception('向量生成失败');
                }

                // 准备DashVector文档数据
                $docId = $this->generateDocumentId($contentIndex);
                $fields = $this->prepareDocumentFields($contentIndex, $model);
                
                $batchData[] = [
                    'id' => $docId,
                    'vector' => $vector,
                    'fields' => $fields,
                    'content_index' => $contentIndex,
                ];

                $stats['success']++;

            } catch (Exception $e) {
                $stats['failed']++;
                $stats['errors'][] = [
                    'model_type' => get_class($model),
                    'model_id' => $model->id ?? 'unknown',
                    'error' => $e->getMessage(),
                ];
                
                Log::error('批量索引单项失败', [
                    'model_type' => get_class($model),
                    'model_id' => $model->id ?? 'unknown',
                    'error' => $e->getMessage(),
                ]);
            }
        }

        // 批量插入到DashVector
        if (!empty($batchData)) {
            try {
                $docs = array_map(function ($item) {
                    return [
                        'id' => $item['id'],
                        'vector' => $item['vector'],
                        'fields' => $item['fields'],
                    ];
                }, $batchData);

                $this->vectorService->insertDocsBatch($docs);

                // 更新数据库记录
                foreach ($batchData as $item) {
                    $contentIndex = $item['content_index'];
                    $contentIndex->setVectorData($item['vector']);
                    $contentIndex->save();
                }

                Log::info('批量内容索引完成', [
                    'total' => $stats['total'],
                    'success' => $stats['success'],
                    'failed' => $stats['failed'],
                ]);

            } catch (Exception $e) {
                Log::error('批量向量插入失败', [
                    'error' => $e->getMessage(),
                    'batch_size' => count($batchData),
                ]);
                
                // 更新失败统计
                $stats['failed'] += $stats['success'];
                $stats['success'] = 0;
            }
        }

        return $stats;
    }

    /**
     * 删除内容索引
     *
     * @param mixed $model 模型实例
     * @return bool 是否成功
     */
    public function removeContentIndex($model): bool
    {
        // 检查模型是否实现了HasContentIndex接口
        if (!$this->implementsHasContentIndex($model)) {
            throw new Exception('模型必须实现HasContentIndex接口');
        }
        
        try {
            $contentIndex = ContentIndex::where('indexable_type', get_class($model))
                ->where('indexable_id', $model->id)
                ->first();

            if (!$contentIndex) {
                Log::info('内容索引不存在，无需删除', [
                    'model_type' => get_class($model),
                    'model_id' => $model->id,
                ]);
                return true;
            }

            DB::beginTransaction();

            // 从DashVector删除
            if ($contentIndex->vector_generated) {
                $docId = $this->generateDocumentId($contentIndex);
                $this->vectorService->deleteDocs([$docId]);
            }

            // 从数据库删除
            $contentIndex->delete();

            DB::commit();

            Log::info('内容索引删除成功', [
                'model_type' => get_class($model),
                'model_id' => $model->id,
                'index_id' => $contentIndex->id,
            ]);

            return true;

        } catch (Exception $e) {
            DB::rollBack();
            Log::error('内容索引删除失败', [
                'model_type' => get_class($model),
                'model_id' => $model->id,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * 检查模型是否实现了HasContentIndex接口
     *
     * @param mixed $model
     * @return bool
     */
    private function implementsHasContentIndex($model): bool
    {
        // 使用多种方式检查接口实现
        return $model instanceof HasContentIndex || 
               in_array(HasContentIndex::class, class_implements($model)) ||
               method_exists($model, 'getContentIndexType');
    }

    /**
     * 重建所有索引
     *
     * @param string|null $modelType 指定模型类型，null表示所有类型
     * @return array 重建结果统计
     */
    public function rebuildAllIndexes(?string $modelType = null): array
    {
        $stats = [
            'total' => 0,
            'success' => 0,
            'failed' => 0,
            'errors' => [],
        ];

        try {
            // 初始化DashVector Collection
            $this->vectorService->initializeCollection();

            // 获取需要重建的内容
            $query = ContentIndex::query();
            if ($modelType) {
                $query->where('indexable_type', $modelType);
            }

            $contentIndexes = $query->with('indexable')->get();
            $stats['total'] = $contentIndexes->count();

            Log::info('开始重建索引', [
                'total_count' => $stats['total'],
                'model_type' => $modelType,
            ]);

            // 分批处理
            $batchSize = 50;
            $batches = $contentIndexes->chunk($batchSize);

            foreach ($batches as $batch) {
                $models = $batch->map(function ($contentIndex) {
                    return $contentIndex->indexable;
                })->filter()->values()->toArray();

                $batchResult = $this->batchIndexContent($models);
                
                $stats['success'] += $batchResult['success'];
                $stats['failed'] += $batchResult['failed'];
                $stats['errors'] = array_merge($stats['errors'], $batchResult['errors']);
            }

            Log::info('索引重建完成', $stats);

        } catch (Exception $e) {
            Log::error('索引重建失败', [
                'error' => $e->getMessage(),
                'model_type' => $modelType,
            ]);
            $stats['errors'][] = [
                'error' => $e->getMessage(),
                'context' => 'rebuild_all_indexes',
            ];
        }

        return $stats;
    }

    /**
     * 同步向量到DashVector
     *
     * @return array 同步结果统计
     */
    public function syncVectorsToCloud(): array
    {
        $stats = [
            'total' => 0,
            'synced' => 0,
            'failed' => 0,
            'errors' => [],
        ];

        try {
            // 获取已生成向量但可能未同步的内容
            $contentIndexes = ContentIndex::where('vector_generated', true)
                ->whereNotNull('vector')
                ->get();

            $stats['total'] = $contentIndexes->count();

            if ($stats['total'] === 0) {
                Log::info('没有需要同步的向量');
                return $stats;
            }

            Log::info('开始同步向量到DashVector', [
                'total_count' => $stats['total'],
            ]);

            // 准备批量数据
            $batchData = [];
            foreach ($contentIndexes as $contentIndex) {
                try {
                    $docId = $this->generateDocumentId($contentIndex);
                    $vector = $contentIndex->getVectorData();
                    $fields = $this->prepareDocumentFields($contentIndex);

                    $batchData[] = [
                        'id' => $docId,
                        'vector' => $vector,
                        'fields' => $fields,
                    ];

                    $stats['synced']++;

                } catch (Exception $e) {
                    $stats['failed']++;
                    $stats['errors'][] = [
                        'content_index_id' => $contentIndex->id,
                        'error' => $e->getMessage(),
                    ];
                }
            }

            // 批量插入到DashVector
            if (!empty($batchData)) {
                $this->vectorService->insertDocsBatch($batchData);
            }

            Log::info('向量同步完成', $stats);

        } catch (Exception $e) {
            Log::error('向量同步失败', [
                'error' => $e->getMessage(),
            ]);
            $stats['errors'][] = [
                'error' => $e->getMessage(),
                'context' => 'sync_vectors_to_cloud',
            ];
        }

        return $stats;
    }

    /**
     * 获取或创建内容索引记录
     *
     * @param mixed $model 模型实例
     * @return ContentIndex 内容索引实例
     */
    private function getOrCreateContentIndex($model): ContentIndex
    {
        return ContentIndex::firstOrCreate(
            [
                'indexable_type' => get_class($model),
                'indexable_id' => $model->id,
            ],
            [
                'type' => $this->getContentType($model),
                'title' => $this->extractTitle($model),
                'summary' => $this->extractSummary($model),
                'image' => $this->extractImage($model),
                'url' => $this->generateUrl($model),
                'metadata' => $this->extractMetadata($model),
                'is_active' => true,
                'view_count' => 0,
                'relevance_score' => 0,
            ]
        );
    }

    /**
     * 更新内容索引数据
     *
     * @param ContentIndex $contentIndex 内容索引实例
     * @param mixed $model 模型实例
     * @return void
     */
    private function updateContentIndexData(ContentIndex $contentIndex, $model): void
    {
        $contentIndex->title = $this->extractTitle($model);
        $contentIndex->summary = $this->extractSummary($model);
        $contentIndex->image = $this->extractImage($model);
        $contentIndex->url = $this->generateUrl($model);
        $contentIndex->metadata = $this->extractMetadata($model);
        $contentIndex->save();
    }

    /**
     * 生成并存储向量
     *
     * @param ContentIndex $contentIndex 内容索引实例
     * @param mixed $model 模型实例
     * @return bool 是否成功
     */
    private function generateAndStoreVector(ContentIndex $contentIndex, $model): bool
    {
        try {
            // 获取内容文本
            $content = $model->getContentForVector();
            if (empty($content)) {
                Log::warning('内容为空，跳过向量生成', [
                    'model_type' => get_class($model),
                    'model_id' => $model->id,
                ]);
                return false;
            }

            // 生成向量
            $vector = $this->vectorService->generateTextVector($content);
            if (empty($vector)) {
                Log::error('向量生成失败', [
                    'model_type' => get_class($model),
                    'model_id' => $model->id,
                ]);
                return false;
            }

            // 存储到数据库
            $contentIndex->setVectorData($vector);
            $contentIndex->save();

            // 存储到DashVector
            $docId = $this->generateDocumentId($contentIndex);
            $fields = $this->prepareDocumentFields($contentIndex, $model);
            
            $this->vectorService->insertDoc($docId, $vector, $fields);

            return true;

        } catch (Exception $e) {
            Log::error('向量生成和存储失败', [
                'model_type' => get_class($model),
                'model_id' => $model->id,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * 生成DashVector文档ID
     *
     * @param ContentIndex $contentIndex 内容索引实例
     * @return string 文档ID
     */
    private function generateDocumentId(ContentIndex $contentIndex): string
    {
        return "content_{$contentIndex->id}";
    }

    /**
     * 准备DashVector文档字段
     *
     * @param ContentIndex $contentIndex 内容索引实例
     * @param mixed|null $model 模型实例（可选）
     * @return array 文档字段
     */
    private function prepareDocumentFields(ContentIndex $contentIndex, $model = null): array
    {
        $fields = [
            'indexable_type' => $contentIndex->indexable_type,
            'indexable_id' => (int) $contentIndex->indexable_id,
            'type' => $contentIndex->type,
            'title' => $contentIndex->title ?? '',
            'summary' => $contentIndex->summary ?? '',
            'image' => $contentIndex->image ?? '',
            'url' => $contentIndex->url ?? '',
            'is_active' => (bool) $contentIndex->is_active,
            'view_count' => (int) $contentIndex->view_count,
            'relevance_score' => (float) $contentIndex->relevance_score,
            'created_at' => $contentIndex->created_at?->toISOString() ?? '',
            'updated_at' => $contentIndex->updated_at?->toISOString() ?? '',
        ];

        // 处理元数据，将复杂类型转换为字符串
        if ($contentIndex->metadata && is_array($contentIndex->metadata)) {
            foreach ($contentIndex->metadata as $key => $value) {
                $fieldKey = "metadata_{$key}";
                
                // 将复杂类型转换为DashVector支持的基本类型
                if (is_bool($value)) {
                    $fields[$fieldKey] = $value;
                } elseif (is_numeric($value)) {
                    $fields[$fieldKey] = is_float($value) ? (float) $value : (int) $value;
                } elseif (is_string($value)) {
                    $fields[$fieldKey] = $value;
                } elseif (is_array($value)) {
                    // 数组转换为JSON字符串
                    $fields[$fieldKey] = json_encode($value, JSON_UNESCAPED_UNICODE);
                } elseif (is_object($value)) {
                    // 对象转换为JSON字符串
                    $fields[$fieldKey] = json_encode($value, JSON_UNESCAPED_UNICODE);
                } elseif ($value !== null) {
                    // 其他类型转换为字符串
                    $fields[$fieldKey] = (string) $value;
                }
            }
        }

        // 确保所有字段值都不为null，DashVector不支持null值
        return array_filter($fields, function($value) {
            return $value !== null && $value !== '';
        });
    }

    /**
     * 获取内容类型
     *
     * @param mixed $model 模型实例
     * @return string 内容类型
     */
    private function getContentType($model): string
    {
        return $model->getContentIndexType();
    }

    /**
     * 提取内容标题
     *
     * @param mixed $model 模型实例
     * @return string 标题
     */
    private function extractTitle($model): string
    {
        return $model->getContentIndexTitle();
    }

    /**
     * 提取内容摘要
     *
     * @param mixed $model 模型实例
     * @return string|null 摘要
     */
    private function extractSummary($model): ?string
    {
        $summary = $model->getContentIndexSummary();
        
        // 如果没有摘要，尝试从内容中生成
        if (empty($summary) && method_exists($model, 'getContentForVector')) {
            $content = $model->getContentForVector();
            if (!empty($content)) {
                // 移除HTML标签并截取前200字符作为摘要
                $cleanContent = strip_tags($content);
                $cleanContent = preg_replace('/\s+/', ' ', $cleanContent);
                $summary = mb_substr(trim($cleanContent), 0, 200);
                if (mb_strlen($cleanContent) > 200) {
                    $summary .= '...';
                }
            }
        }
        
        return $summary;
    }

    /**
     * 提取内容图片
     *
     * @param mixed $model 模型实例
     * @return string|null 图片URL
     */
    private function extractImage($model): ?string
    {
        return $model->image ?? null;
    }

    /**
     * 生成内容URL
     *
     * @param mixed $model 模型实例
     * @return string|null URL
     */
    private function generateUrl($model): ?string
    {
        try {
            return $model->getContentIndexUrl();
        } catch (Exception $e) {
            Log::warning('生成内容URL失败', [
                'model_type' => get_class($model),
                'model_id' => $model->id,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * 提取内容元数据
     *
     * @param mixed $model 模型实例
     * @return array|null 元数据
     */
    private function extractMetadata($model): ?array
    {
        try {
            $metadata = $model->getContentIndexMetadata();
            return !empty($metadata) ? $metadata : null;
        } catch (Exception $e) {
            Log::warning('提取内容元数据失败', [
                'model_type' => get_class($model),
                'model_id' => $model->id,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * 获取索引统计信息
     *
     * @return array 统计信息
     */
    public function getIndexStats(): array
    {
        try {
            $stats = [
                'total_content' => ContentIndex::count(),
                'with_vectors' => ContentIndex::where('vector_generated', true)->count(),
                'by_type' => ContentIndex::selectRaw('type, count(*) as count, sum(case when vector_generated then 1 else 0 end) as with_vectors')
                    ->groupBy('type')
                    ->get()
                    ->mapWithKeys(function ($item) {
                        return [$item->type => [
                            'total' => $item->count,
                            'with_vectors' => $item->with_vectors,
                            'coverage' => $item->count > 0 ? round(($item->with_vectors / $item->count) * 100, 2) : 0,
                        ]];
                    })
                    ->toArray(),
                'last_indexed' => ContentIndex::where('vector_generated', true)->max('vector_generated_at'),
                'vector_service_health' => $this->vectorService->healthCheck(),
            ];

            // 获取DashVector统计信息
            try {
                $vectorStats = $this->vectorService->getCollectionStats();
                $stats['dashvector_stats'] = $vectorStats;
            } catch (Exception $e) {
                $stats['dashvector_stats'] = null;
                Log::warning('获取DashVector统计信息失败', [
                    'error' => $e->getMessage(),
                ]);
            }

            return $stats;

        } catch (Exception $e) {
            Log::error('获取索引统计信息失败', [
                'error' => $e->getMessage(),
            ]);
            return [];
        }
    }
} 