<?php

namespace App\Facades;

use Illuminate\Support\Facades\Facade;
use App\Services\Sms\SmsService;

/**
 * 短信服务门面类
 * 
 * @method static array sendVerificationCode(string $mobile, int $type = 1, bool $useCache = true)
 * @method static bool verifyCode(string $mobile, string $code, int $type = 1, bool $deleteIfValid = true)
 * @method static array sendNotification(string $mobile, array $params, string $templateCode)
 * @method static string getProviderName()
 * @method static \App\Services\Sms\SmsService useProvider(string $provider)
 * @method static \App\Services\Sms\SmsService setCodeLength(int $length)
 * @method static \App\Services\Sms\SmsService setCodeExpiration(int $minutes)
 * 
 * @see \App\Services\Sms\SmsService
 */
class Sms extends Facade
{
    /**
     * 获取组件的注册名称
     *
     * @return string
     */
    protected static function getFacadeAccessor()
    {
        return 'sms';
    }
}
