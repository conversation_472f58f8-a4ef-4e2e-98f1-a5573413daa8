<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Dcat\Admin\Traits\HasDateTimeFormatter;

/**
 * 心理测评选项模型
 */
class AssessmentOption extends Model
{
    use HasDateTimeFormatter;

    // 批量赋值字段
    protected $fillable = ['question_id','content','score_value'];

    /**
     * 该选项所属题目
     */
    public function question(): BelongsTo
    {
        return $this->belongsTo(AssessmentQuestion::class);
    }
}
