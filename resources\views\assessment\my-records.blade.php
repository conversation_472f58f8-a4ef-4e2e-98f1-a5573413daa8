@extends('layouts.app')

@section('title', '我的测评记录')

@section('custom-styles')
<link rel="stylesheet" href="{{ asset('css/assessment.css') }}">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
<!-- 自定义分页样式 -->
<style>
.pagination-wrapper {
    text-align: center;
    padding: 15px 0;
}
.pagination-wrapper a,
.pagination-wrapper span {
    display: inline-block;
    margin: 0 4px;
    padding: 8px 12px;
    font-size: 14px;
    color: #667eea;
    text-decoration: none;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 6px;
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: background 0.2s, color 0.2s;
}
.pagination-wrapper a:hover {
    background: #667eea;
    color: #fff;
}
.pagination-wrapper .current-page {
    background: #764ba2;
    color: #fff;
    cursor: default;
}
.pagination-wrapper .disabled {
    color: #bbb;
    cursor: not-allowed;
    background: rgba(255, 255, 255, 0.7);
}
@media (max-width: 480px) {
    .pagination-wrapper a,
    .pagination-wrapper span {
        padding: 6px 10px;
        font-size: 13px;
    }
}
</style>
@endsection

@section('content')
<div class="records-container">
    <!-- 页面头部 -->
    <div class="assessment-header">
        <div class="header-content">
            <button class="back-btn" onclick="history.back()">
                <i class="fas fa-arrow-left"></i>
            </button>
            <h1 class="header-title">我的测评记录</h1>
            <div class="header-action"></div>
        </div>
    </div>

    <!-- 统计信息 -->
    <div class="stats-section">
        <div class="stats-grid">
            <div class="stat-card">
                <span class="stat-number">{{ $records->count() }}</span>
                <span class="stat-label">完成测评</span>
            </div>
            <div class="stat-card">
                <span class="stat-number">{{ $records->pluck('questionnaire.domain')->unique()->count() }}</span>
                <span class="stat-label">涉及领域</span>
            </div>
            <div class="stat-card">
                <span class="stat-number">{{ $records->avg('total_score') ? number_format($records->avg('total_score'), 1) : 0 }}</span>
                <span class="stat-label">平均得分</span>
            </div>
        </div>
    </div>

    <!-- 记录列表 -->
    <div class="questionnaire-list">
        @if($records->count() > 0)
            @foreach($records as $record)
                <div class="questionnaire-card">
                    <div class="card-header" style="height: 100px;">
                        <div class="card-icon">
                            <i class="{{ App\Http\Controllers\AssessmentController::getDomainIcon($record->questionnaire->domain) }}"></i>
                        </div>
                        <div class="domain-badge">{{ App\Http\Controllers\AssessmentController::getDomainName($record->questionnaire->domain) }}</div>
                        
                        <!-- 分数显示 -->
                        <div style="position: absolute; bottom: 10px; right: 15px; background: rgba(255,255,255,0.9); padding: 5px 10px; border-radius: 15px; font-size: 12px; font-weight: 600; color: #667eea;">
                            {{ $record->total_score ?? 0 }} 分
                        </div>
                    </div>
                    
                    <div class="card-content">
                        <h3 class="card-title">{{ $record->questionnaire->title }}</h3>
                        
                        <div class="card-meta" style="margin-bottom: 15px;">
                            <div class="meta-item">
                                <i class="fas fa-calendar"></i>
                                <span>{{ $record->created_at->format('Y-m-d') }}</span>
                            </div>
                            <div class="meta-item">
                                <i class="fas fa-clock"></i>
                                <span>{{ $record->created_at->format('H:i') }}</span>
                            </div>
                        </div>

                        <!-- 结果等级 -->
                        @if($record->analysis && $record->analysis->level_name)
                        <div style="background: rgba(102, 126, 234, 0.1); padding: 10px 15px; border-radius: 10px; margin-bottom: 15px;">
                            <div style="font-size: 14px; color: #667eea; font-weight: 600;">
                                <i class="fas fa-award" style="margin-right: 5px;"></i>
                                测评等级：{{ $record->analysis->level_name }}
                            </div>
                        </div>
                        @endif

                        <!-- 简要描述 -->
                        @if($record->analysis && $record->analysis->description)
                        <p class="card-description">
                            {{ Str::limit($record->analysis->description, 100) }}
                        </p>
                        @endif
                        
                        <div class="card-footer">
                            <button class="start-btn" onclick="viewResult({{ $record->id }})" style="background: #28a745;">
                                <i class="fas fa-eye"></i> 查看详情
                            </button>
                            <button class="start-btn" onclick="retakeAssessment({{ $record->questionnaire_id }})" style="background: #6c757d; margin-left: 10px;">
                                <i class="fas fa-redo"></i> 重新测评
                            </button>
                        </div>
                    </div>
                </div>
            @endforeach
        @else
            <!-- 空状态 -->
            <div class="questionnaire-card">
                <div class="card-content" style="text-align: center; padding: 40px 20px;">
                    <i class="fas fa-clipboard-list" style="font-size: 48px; color: #ccc; margin-bottom: 20px;"></i>
                    <h3 style="color: #666; margin-bottom: 15px;">暂无测评记录</h3>
                    <p style="color: #999; margin-bottom: 25px;">开始您的第一次心理测评，了解自己的心理状态</p>
                    <button class="start-btn" onclick="goToAssessmentList()">
                        <i class="fas fa-plus"></i> 开始测评
                    </button>
                </div>
            </div>
        @endif
    </div>

    <!-- 自定义分页按钮 -->
    @if($records->hasPages())
    <div class="pagination-wrapper">
        {{-- “上一页” --}}
        @if($records->onFirstPage())
            <span class="disabled">&laquo; 上一页</span>
        @else
            <a href="{{ url()->current() }}?page={{ $records->currentPage() - 1 }}">&laquo; 上一页</a>
        @endif

        {{-- 中间页码 --}}
        @php
            $start = max($records->currentPage() - 2, 1);
            $end = min($records->currentPage() + 2, $records->lastPage());
        @endphp

        {{-- 如果起始页大于 1，则显示 1 和 “...” --}}
        @if($start > 1)
            <a href="{{ url()->current() }}?page=1">1</a>
            @if($start > 2)
                <span class="disabled">…</span>
            @endif
        @endif

        {{-- 循环显示从 $start 到 $end 的页码 --}}
        @for($i = $start; $i <= $end; $i++)
            @if($i == $records->currentPage())
                <span class="current-page">{{ $i }}</span>
            @else
                <a href="{{ url()->current() }}?page={{ $i }}">{{ $i }}</a>
            @endif
        @endfor

        {{-- 如果结束页小于最后一页，则显示 “...” 和 最后一页 --}}
        @if($end < $records->lastPage())
            @if($end < $records->lastPage() - 1)
                <span class="disabled">…</span>
            @endif
            <a href="{{ url()->current() }}?page={{ $records->lastPage() }}">{{ $records->lastPage() }}</a>
        @endif

        {{-- “下一页” --}}
        @if($records->hasMorePages())
            <a href="{{ url()->current() }}?page={{ $records->currentPage() + 1 }}">下一页 &raquo;</a>
        @else
            <span class="disabled">下一页 &raquo;</span>
        @endif
    </div>
    @endif
</div>
@endsection

@section('scripts')
<script>
// 查看结果详情
function viewResult(responseId) {
    window.location.href = `/assessment/result/${responseId}`;
}

// 重新测评
function retakeAssessment(questionnaireId) {
    if (confirm('确定要重新进行此测评吗？')) {
        window.location.href = `/assessment/${questionnaireId}/start`;
    }
}

// 返回测评列表
function goToAssessmentList() {
    window.location.href = '/assessment';
}

// 页面加载动画
document.addEventListener('DOMContentLoaded', function() {
    // 统计数字动画
    const statNumbers = document.querySelectorAll('.stat-number');
    statNumbers.forEach(number => {
        const text = number.textContent;
        if (!isNaN(text)) {
            const finalValue = parseFloat(text);
            let currentValue = 0;
            const increment = finalValue / 30;
            
            const timer = setInterval(() => {
                currentValue += increment;
                if (currentValue >= finalValue) {
                    currentValue = finalValue;
                    clearInterval(timer);
                }
                
                if (text.includes('.')) {
                    number.textContent = currentValue.toFixed(1);
                } else {
                    number.textContent = Math.floor(currentValue);
                }
            }, 50);
        }
    });

    // 卡片交错动画
    const cards = document.querySelectorAll('.questionnaire-card');
    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
    });
});

// 添加触摸反馈
document.querySelectorAll('.start-btn').forEach(btn => {
    btn.addEventListener('touchstart', function() {
        this.style.transform = 'scale(0.95)';
    });
    
    btn.addEventListener('touchend', function() {
        this.style.transform = '';
    });
});

// 记录筛选功能（可选）
function filterRecords(domain) {
    const cards = document.querySelectorAll('.questionnaire-card');
    cards.forEach(card => {
        const cardDomain = card.querySelector('.domain-badge').textContent;
        if (domain === 'all' || cardDomain === domain) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
}

// 导出记录功能（可选）
function exportRecords() {
    alert('导出功能开发中...');
}
</script>
@endsection
