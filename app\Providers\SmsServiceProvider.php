<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Services\Sms\SmsService;

class SmsServiceProvider extends ServiceProvider
{
    /**
     * 注册服务
     *
     * @return void
     */
    public function register()
    {
        // 注册单例
        $this->app->singleton('sms', function ($app) {
            return new SmsService();
        });
    }

    /**
     * 引导服务
     *
     * @return void
     */
    public function boot()
    {
        // 配置文件发布
        $this->publishes([
            __DIR__.'/../../config/sms.php' => config_path('sms.php'),
        ], 'config');
    }
}
