<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Dcat\Admin\Traits\HasDateTimeFormatter;

class CourseLessonProgress extends Model
{
    use HasFactory, HasDateTimeFormatter;
    
    protected $table = 'course_lesson_progress';

    protected $fillable = [
        'user_id',
        'course_lesson_id',
        'progress',
        'current_position',
        'last_access_time',
        'is_completed',
        'notes'
    ];

    protected $casts = [
        'last_access_time' => 'datetime',
        'is_completed' => 'boolean',
    ];

    // 与用户的关联
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    // 与课程的关联
    public function lesson()
    {
        return $this->belongsTo(CourseLesson::class, 'course_lesson_id');
    }

    // 更新学习进度
    public static function updateProgress($userId, $lessonId, $position, $progress = null, $completed = false)
    {
        // 记录调试信息
        \Log::info('Updating progress', [
            'userId' => $userId,
            'lessonId' => $lessonId,
            'position' => $position,
            'progress' => $progress,
            'completed' => $completed
        ]);
        
        // 重新学习情况 - 强制删除记录并创建全新的记录
        if ($progress === 0 && !$completed) {
            \Log::info('FORCE DELETE progress record and create new one');
            
            // 先删除现有记录
            self::where('user_id', $userId)
                ->where('course_lesson_id', $lessonId)
                ->delete();
                
            // 创建新记录
            $record = new self([
                'user_id' => $userId,
                'course_lesson_id' => $lessonId,
                'current_position' => 0,
                'progress' => 0,
                'is_completed' => false,
                'last_access_time' => now()
            ]);
            
            $record->save();
            return $record;
        }
        
        // 正常学习进度更新
        $record = self::firstOrNew([
            'user_id' => $userId,
            'course_lesson_id' => $lessonId
        ]);
        
        $record->current_position = $position;
        
        if ($progress !== null) {
            $record->progress = $progress;
        }
        
        if ($completed) {
            $record->is_completed = true;
            $record->progress = 100;
        }
        
        $record->last_access_time = now();
        $record->save();
        
        return $record;
    }
}
