<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\SmsCode;
use App\Facades\Sms;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;
use Carbon\Carbon;

class AuthController extends Controller
{
    /**
     * 显示登录页面
     */
    public function showLogin()
    {
        return view('auth.login');
    }

    /**
     * 显示注册页面
     */
    public function showRegister()
    {
        return view('auth.register');
    }

    /**
     * 处理手机号密码登录
     */
    public function loginWithPassword(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'phone' => 'required|string|regex:/^1[3-9]\d{9}$/',
            'password' => 'required|string|min:6',
        ], [
            'phone.required' => '请输入手机号',
            'phone.regex' => '手机号格式不正确',
            'password.required' => '请输入密码',
            'password.min' => '密码不能少于6位',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput($request->except('password'));
        }

        $credentials = [
            'phone' => $request->phone,
            'password' => $request->password,
        ];

        // 尝试登录
        if (Auth::attempt($credentials, $request->filled('remember'))) {
            $user = Auth::user();
            $user->last_login_at = Carbon::now();
            $user->last_login_ip = $request->ip();
            $user->save();

            $request->session()->regenerate();
            
            // 获取用户原本想要访问的URL
            $intendedUrl = session()->pull('url.intended', route('home'));
            return redirect($intendedUrl);
        }

        // 如果手机号不存在，检查是否通过邮箱能找到此用户
        $user = User::where('email', $request->phone . '@temp.com')->first();
        if ($user && Hash::check($request->password, $user->password)) {
            Auth::login($user, $request->filled('remember'));
            $user->last_login_at = Carbon::now();
            $user->last_login_ip = $request->ip();
            $user->save();

            $request->session()->regenerate();
            
            // 获取用户原本想要访问的URL
            $intendedUrl = session()->pull('url.intended', route('home'));
            return redirect($intendedUrl);
        }

        return redirect()->back()
            ->withErrors(['phone' => '手机号或密码错误'])
            ->withInput($request->except('password'));
    }

    /**
     * 发送登录验证码
     */
    public function sendLoginCode(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'phone' => 'required|string|regex:/^1[3-9]\d{9}$/',
        ], [
            'phone.required' => '请输入手机号',
            'phone.regex' => '手机号格式不正确',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first('phone'),
            ]);
        }

        // 检查发送频率限制
        $rateLimitKey = 'sms:ratelimit:' . $request->phone;
        if (Cache::has($rateLimitKey)) {
            $remainingTime = Cache::get($rateLimitKey);
            return response()->json([
                'success' => false,
                'message' => '请稍后再试，验证码已发送 (' . $remainingTime . 's)',
            ]);
        }

        try {
            // 发送验证码
            $result = Sms::sendVerificationCode($request->phone, SmsCode::TYPE_LOGIN);
            
            // 设置频率限制
            Cache::put($rateLimitKey, 60, now()->addMinutes(1));
            
            // 日志记录
            Log::channel('sms')->info('登录验证码发送', [
                'phone' => $request->phone,
                'success' => $result['success'],
                'provider' => Sms::getProviderName(),
            ]);

            return response()->json([
                'success' => $result['success'],
                'message' => $result['success'] ? '验证码已发送' : $result['message'],
                'debug_code' => config('app.debug') && isset($result['code']) ? $result['code'] : null,
            ]);
        } catch (\Exception $e) {
            Log::error('验证码发送异常: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => '验证码发送失败，请稍后再试',
            ]);
        }
    }

    /**
     * 处理手机号验证码登录
     */
    public function loginWithCode(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'phone' => 'required|string|regex:/^1[3-9]\d{9}$/',
            'code' => 'required|digits:6',
        ], [
            'phone.required' => '请输入手机号',
            'phone.regex' => '手机号格式不正确',
            'code.required' => '请输入验证码',
            'code.digits' => '验证码必须是6位数字',
        ]);
    
        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput($request->except('code'));
        }
    
        // 验证码验证
        if (!Sms::verifyCode($request->phone, $request->code, SmsCode::TYPE_LOGIN)) {
            return redirect()->back()
                ->withErrors(['code' => '验证码错误或已过期'])
                ->withInput($request->except('code'));
        }
        
        // 记录验证码验证成功日志
        Log::channel('sms')->info('验证码验证成功', [
            'phone' => $request->phone,
            'action' => 'login'
        ]);
    
        // 查找或创建用户
        $user = User::where('phone', $request->phone)->first();
        
        if (!$user) {
            // 如果是新用户，创建用户记录
            $user = User::create([
                'name' => substr($request->phone, 0, 3) . '****' . substr($request->phone, -4),
                'phone' => $request->phone,
                'email' => $request->phone . '@temp.com',
                'phone_verified' => true,
                'password' => Hash::make(Str::random(16)),
            ]);
        }
    
        Auth::login($user, $request->filled('remember'));
    
        // 更新登录信息
        $user->last_login_at = Carbon::now();
        $user->last_login_ip = $request->ip();
        $user->phone_verified = true;
        $user->save();
    
        $request->session()->regenerate();
    
        // ✅ 获取原始跳转 URL（并加上端口，如果没带）
        $intendedUrl = session()->pull('url.intended', route('home'));
    
        // 自动修正端口
        $parsed = parse_url($intendedUrl);
        $host = $parsed['host'] ?? '';
        $scheme = $parsed['scheme'] ?? 'http';
        $path = $parsed['path'] ?? '/';
        $query = isset($parsed['query']) ? '?' . $parsed['query'] : '';
        $port = $parsed['port'] ?? null;
    
        // 如果 URL 没有带端口，并且 APP_URL 设置了端口，就补上
        if (!$port) {
            $appUrl = parse_url(config('app.url'));
            if (isset($appUrl['port'])) {
                $intendedUrl = "{$scheme}://{$host}:{$appUrl['port']}{$path}{$query}";
            }
        }
    
        return redirect()->to($intendedUrl);
    }


    /**
     * 处理注册请求
     */
    public function register(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:50',
            'phone' => 'required|string|regex:/^1[3-9]\d{9}$/|unique:users,phone',
            'code' => 'required|digits:6',
            'password' => 'required|string|min:6|confirmed',
            'password_confirmation' => 'required',
        ], [
            'name.required' => '请输入姓名',
            'name.max' => '姓名不能超过50个字符',
            'phone.required' => '请输入手机号',
            'phone.regex' => '手机号格式不正确',
            'phone.unique' => '该手机号已被注册',
            'code.required' => '请输入验证码',
            'code.digits' => '验证码必须是6位数字',
            'password.required' => '请设置密码',
            'password.min' => '密码不能少于6位',
            'password.confirmed' => '两次密码输入不一致',
            'password_confirmation.required' => '请确认密码',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput($request->except(['password', 'password_confirmation', 'code']));
        }

        // 验证码验证
        if (!Sms::verifyCode($request->phone, $request->code, SmsCode::TYPE_REGISTER)) {
            return redirect()->back()
                ->withErrors(['code' => '验证码错误或已过期'])
                ->withInput($request->except(['password', 'password_confirmation', 'code']));
        }
        
        // 记录验证码验证成功日志
        Log::channel('sms')->info('注册验证码验证成功', [
            'phone' => $request->phone,
            'action' => 'register'
        ]);

        // 创建用户
        $user = User::create([
            'name' => $request->name,
            'phone' => $request->phone,
            'email' => $request->has('email') ? $request->email : $request->phone . '@temp.com',
            'phone_verified' => true,
            'password' => Hash::make($request->password),
            'last_login_at' => Carbon::now(),
            'last_login_ip' => $request->ip(),
        ]);

        // 自动登录
        Auth::login($user);
        $request->session()->regenerate();

        return redirect()->route('home')->with('success', '注册成功，欢迎加入我们！');
    }

    /**
     * 发送注册验证码
     */
    public function sendRegisterCode(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'phone' => 'required|string|regex:/^1[3-9]\d{9}$/|unique:users,phone',
        ], [
            'phone.required' => '请输入手机号',
            'phone.regex' => '手机号格式不正确',
            'phone.unique' => '该手机号已被注册',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => $validator->errors()->first('phone'),
            ]);
        }

        // 检查发送频率限制
        $rateLimitKey = 'sms:ratelimit:' . $request->phone;
        if (Cache::has($rateLimitKey)) {
            $remainingTime = Cache::get($rateLimitKey);
            return response()->json([
                'success' => false,
                'message' => '请稍后再试，验证码已发送 (' . $remainingTime . 's)',
            ]);
        }

        try {
            // 发送验证码
            $result = Sms::sendVerificationCode($request->phone, SmsCode::TYPE_REGISTER);
            
            // 设置频率限制
            Cache::put($rateLimitKey, 60, now()->addMinutes(1));
            
            // 日志记录
            Log::channel('sms')->info('注册验证码发送', [
                'phone' => $request->phone,
                'success' => $result['success'],
                'provider' => Sms::getProviderName(),
            ]);

            return response()->json([
                'success' => $result['success'],
                'message' => $result['success'] ? '验证码已发送' : $result['message'],
                'debug_code' => config('app.debug') && isset($result['code']) ? $result['code'] : null,
            ]);
        } catch (\Exception $e) {
            Log::error('验证码发送异常: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => '验证码发送失败，请稍后再试',
            ]);
        }
    }

    /**
     * 登出
     */
    public function logout(Request $request)
    {
        Auth::logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();
        
        return redirect()->route('home');
    }
}
