<?php

namespace App\Services\Sms;

/**
 * 短信服务接口
 */
interface SmsServiceInterface
{
    /**
     * 发送验证码短信
     *
     * @param string $mobile 手机号
     * @param string $code 验证码
     * @param int $type 验证码类型 (1:注册, 2:登录, 3:修改密码, 4:绑定手机)
     * @return array 返回发送结果 ['success' => bool, 'message' => string]
     */
    public function sendVerificationCode(string $mobile, string $code, int $type = 1): array;
    
    /**
     * 发送通知短信
     *
     * @param string $mobile 手机号
     * @param array $params 短信参数
     * @param string $templateCode 短信模板代码
     * @return array 返回发送结果 ['success' => bool, 'message' => string]
     */
    public function sendNotification(string $mobile, array $params, string $templateCode): array;

    /**
     * 获取服务商名称
     * 
     * @return string
     */
    public function getProviderName(): string;
}
