@extends('layouts.app')

@section('title', $questionnaire->title)

@section('custom-styles')
    <link rel="stylesheet" href="{{ asset('css/assessment.css') }}">
@endsection

@section('content')
<div class="container assessment-detail my-4">
    <h2>{{ $questionnaire->title }}</h2>
    <p class="mb-3">{{ $questionnaire->description }}</p>
    <form action="{{ route('assessments.submit') }}" method="post">
        @csrf
        <input type="hidden" name="questionnaire_id" value="{{ $questionnaire->id }}">

        @foreach($questionnaire->questions as $index => $question)
        <div class="card mb-3">
            <div class="card-body">
                <h5 class="card-title">Q{{ $index + 1 }}. {{ $question->content }}</h5>
                <input type="hidden" name="answers[{{ $index }}][question_id]" value="{{ $question->id }}">

                @foreach($question->options as $option)
                <div class="form-check">
                    <input class="form-check-input" type="radio" 
                        name="answers[{{ $index }}][option_id]" 
                        id="opt{{ $option->id }}" 
                        value="{{ $option->id }}">
                    <label class="form-check-label" for="opt{{ $option->id }}">
                        {{ $option->content }}
                    </label>
                </div>
                @endforeach
            </div>
        </div>
        @endforeach

        <button type="submit" class="btn btn-success">提交测评</button>
    </form>
</div>
@endsection

@section('scripts')
    <script src="{{ asset('js/assessment.js') }}"></script>
@endsection
