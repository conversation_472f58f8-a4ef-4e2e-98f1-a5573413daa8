@extends('layouts.app')

@section('title', '心理援助热线 - 心理咨询平台')

@section('custom-styles')
<style>
    .hotline-container {
        padding: 15px;
        max-width: 100%;
    }
    
    .hotline-header {
        background: linear-gradient(135deg, #5b7cef 0%, #3c67e3 100%);
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 20px;
        color: white;
        position: relative;
        overflow: hidden;
        box-shadow: 0 4px 10px rgba(60, 103, 227, 0.1);
    }
    
    .hotline-header h1 {
        font-size: 22px;
        font-weight: 600;
        margin: 0 0 5px 0;
    }
    
    .hotline-header p {
        font-size: 14px;
        margin: 0;
        opacity: 0.9;
        line-height: 1.5;
    }
    
    .hotline-pattern {
        position: absolute;
        top: 0;
        right: 0;
        width: 100%;
        height: 100%;
        opacity: 0.1;
        background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiPjxkZWZzPjxwYXR0ZXJuIGlkPSJwYXR0ZXJuIiB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHBhdHRlcm5Vbml0cz0idXNlclNwYWNlT25Vc2UiIHBhdHRlcm5UcmFuc2Zvcm09InJvdGF0ZSgxMzUpIj48cmVjdCBpZD0icGF0dGVybi1iZyIgd2lkdGg9IjQwMCUiIGhlaWdodD0iNDAwJSIgZmlsbD0icmdiYSgyNTUsMjU1LDI1NSwwLjEpIj48L3JlY3Q+PHBhdGggZmlsbD0icmdiYSgyNTUsMjU1LDI1NSwwLjA1KSIgZD0iTTAgMGg0MHY0MEgweiI+PC9wYXRoPjwvcGF0dGVybj48L2RlZnM+PHJlY3QgZmlsbD0idXJsKCNwYXR0ZXJuKSIgaGVpZ2h0PSIxMDAlIiB3aWR0aD0iMTAwJSI+PC9yZWN0Pjwvc3ZnPg==');
    }
    
    .hotline-list {
        margin-bottom: 60px;
    }
    
    .hotline-item {
        background-color: white;
        border-radius: 10px;
        margin-bottom: 15px;
        padding: 15px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        border: 1px solid rgba(0, 0, 0, 0.05);
        display: flex;
        align-items: center;
        position: relative;
    }
    
    .hotline-icon {
        width: 50px;
        height: 50px;
        border-radius: 25px;
        background-color: #f0f7ff;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        flex-shrink: 0;
    }
    
    .hotline-icon img {
        width: 24px;
        height: 24px;
    }
    
    .hotline-info {
        flex: 1;
    }
    
    .hotline-name {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 3px;
        color: #333;
    }
    
    .hotline-description {
        font-size: 13px;
        color: #666;
        margin-bottom: 5px;
    }
    
    .hotline-status {
        display: inline-block;
        font-size: 11px;
        padding: 2px 8px;
        border-radius: 10px;
        margin-right: 6px;
    }
    
    .status-active {
        background-color: #e9f7ef;
        color: #27ae60;
    }
    
    .status-busy {
        background-color: #fef5e9;
        color: #f39c12;
    }
    
    .status-closed {
        background-color: #f9ebeb;
        color: #e74c3c;
    }
    
    .hotline-hours {
        font-size: 12px;
        color: #999;
    }
    
    .call-btn {
        background-color: #4e9cff;
        width: 36px;
        height: 36px;
        border-radius: 18px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        box-shadow: 0 2px 5px rgba(78, 156, 255, 0.2);
        border: none;
    }
    
    .call-btn img {
        width: 18px;
        height: 18px;
    }
    
    .pagination {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 20px;
        gap: 5px;
    }
    
    .pagination-item {
        width: 36px;
        height: 36px;
        border-radius: 18px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f5f5f5;
        color: #666;
        font-size: 14px;
        text-decoration: none;
    }
    
    .pagination-item.active {
        background-color: #4e9cff;
        color: white;
    }
    
    .pagination-item.disabled {
        opacity: 0.5;
        pointer-events: none;
    }
    
    /* 热线弹窗样式 */
    .hotline-modal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 1000;
        align-items: center;
        justify-content: center;
        backdrop-filter: blur(5px);
        -webkit-backdrop-filter: blur(5px);
    }
    
    .modal-content {
        width: 85%;
        max-width: 320px;
        background-color: white;
        border-radius: 16px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        animation: modalFadeIn 0.3s;
    }
    
    @keyframes modalFadeIn {
        from { opacity: 0; transform: scale(0.95); }
        to { opacity: 1; transform: scale(1); }
    }
    
    .modal-header {
        background: linear-gradient(135deg, #5b7cef 0%, #3c67e3 100%);
        padding: 20px;
        color: white;
        text-align: center;
        position: relative;
    }
    
    .modal-close {
        position: absolute;
        top: 12px;
        right: 12px;
        width: 22px;
        height: 22px;
        opacity: 0.9;
        cursor: pointer;
        background-color: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 5px;
    }
    
    .modal-title {
        font-size: 18px;
        font-weight: 600;
        margin: 0 0 5px 0;
    }
    
    .modal-subtitle {
        font-size: 13px;
        opacity: 0.9;
        margin: 0;
    }
    
    .modal-body {
        padding: 20px;
    }
    
    .hotline-number {
        font-size: 24px;
        font-weight: 600;
        color: #333;
        margin-bottom: 20px;
        text-align: center;
    }
    
    .modal-buttons {
        display: flex;
        gap: 10px;
    }
    
    .modal-btn {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 5px;
        padding: 12px 0;
        border-radius: 12px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        border: none;
        transition: transform 0.2s, opacity 0.2s, background-color 0.2s;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    }
    
    .modal-btn:active {
        transform: scale(0.98);
        opacity: 0.9;
    }
    
    .btn-call {
        background-color: #4e9cff;
        color: white;
    }
    
    .btn-call:hover {
        background-color: #4290f0;
    }
    
    .btn-chat {
        background-color: #f5f5f5;
        color: #333;
    }
    
    .btn-chat:hover {
        background-color: #efefef;
    }
    
    .modal-separator {
        margin: 15px 0;
        height: 1px;
        background-color: #eee;
    }
    
    .modal-footer {
        padding: 0 20px 20px;
        font-size: 12px;
        color: #999;
        text-align: center;
    }
    
    /* 开发中提示 */
    .dev-popup {
        position: fixed;
        bottom: 30%;
        left: 50%;
        transform: translateX(-50%);
        background-color: rgba(50, 50, 50, 0.85);
        color: white;
        padding: 12px 20px;
        border-radius: 12px;
        font-size: 14px;
        z-index: 2000;
        opacity: 0;
        visibility: hidden;
        max-width: 85%;
        text-align: center;
        transition: opacity 0.25s, visibility 0.25s, transform 0.25s;
        transform: translate(-50%, 20px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        backdrop-filter: blur(5px);
        -webkit-backdrop-filter: blur(5px);
    }
    
    .dev-popup.show {
        opacity: 1;
        visibility: visible;
        transform: translate(-50%, 0);
    }
    
    .dev-badge {
        display: inline-block;
        background-color: #ff9500;
        color: white;
        font-size: 10px;
        font-weight: 600;
        padding: 2px 8px;
        border-radius: 10px;
        margin-left: 5px;
        vertical-align: middle;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }
    
    @keyframes fadeInOut {
        0% { opacity: 0; transform: translate(-50%, 20px); }
        100% { opacity: 1; transform: translate(-50%, 0); }
    }
</style>
@endsection

@section('content')
<div class="hotline-container">
    <div class="hotline-header">
        <div class="hotline-pattern"></div>
        <h1>心理援助热线</h1>
        <p>专业心理咨询师为您提供心理支持和帮助</p>
    </div>
    
    <div class="hotline-list">
        @if(count($hotlines) > 0)
            @foreach($hotlines as $hotline)
            <div class="hotline-item" data-id="{{ $hotline->id }}" data-number="{{ $hotline->phone_number }}" data-name="{{ $hotline->name }}">
                <div class="hotline-icon">
                    <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%234e9cff'%3E%3Cpath d='M20 15.5c-1.25 0-2.45-.2-3.57-.57-.35-.11-.74-.03-1.02.24l-2.2 2.2c-2.83-1.44-5.15-3.75-6.59-6.59l2.2-2.21c.28-.26.36-.65.25-1C8.7 6.45 8.5 5.25 8.5 4c0-.55-.45-1-1-1H4c-.55 0-1 .45-1 1 0 9.39 7.61 17 17 17 .55 0 1-.45 1-1v-3.5c0-.55-.45-1-1-1z'/%3E%3C/svg%3E" alt="电话">
                </div>
                <div class="hotline-info">
                    <div class="hotline-name">{{ $hotline->name }}</div>
                    <div class="hotline-description">{{ $hotline->description }}</div>
                    <div>
                        @if($hotline->status === 'active')
                            <span class="hotline-status status-active">在线</span>
                        @elseif($hotline->status === 'busy')
                            <span class="hotline-status status-busy">繁忙</span>
                        @else
                            <span class="hotline-status status-closed">关闭</span>
                        @endif
                        <span class="hotline-hours">{{ $hotline->service_hours }}</span>
                    </div>
                </div>
                <button class="call-btn" onclick="showHotlineModal('{{ $hotline->id }}', '{{ $hotline->phone_number }}', '{{ $hotline->name }}')">
                    <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M20 15.5c-1.25 0-2.45-.2-3.57-.57-.35-.11-.74-.03-1.02.24l-2.2 2.2c-2.83-1.44-5.15-3.75-6.59-6.59l2.2-2.21c.28-.26.36-.65.25-1C8.7 6.45 8.5 5.25 8.5 4c0-.55-.45-1-1-1H4c-.55 0-1 .45-1 1 0 9.39 7.61 17 17 17 .55 0 1-.45 1-1v-3.5c0-.55-.45-1-1-1z'/%3E%3C/svg%3E" alt="拨打">
                </button>
            </div>
            @endforeach
        @else
            <div class="hotline-item" style="justify-content: center; padding: 30px 15px;">
                <p style="margin: 0; color: #999; text-align: center;">暂无可用的心理援助热线</p>
            </div>
        @endif
    </div>
    
    <!-- 分页控件 -->
    {{ $hotlines->links('components.pagination') }}
</div>

<!-- 心理热线弹窗 -->
<div class="hotline-modal" id="hotlineModal">
    <div class="modal-content">
        <div class="modal-header">
            <h2 class="modal-title">心理援助热线</h2>
            <p class="modal-subtitle">专业咨询师为您提供帮助</p>
            <div class="modal-close" onclick="closeHotlineModal()">
                <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z'/%3E%3C/svg%3E" alt="关闭">
            </div>
        </div>
        <div class="modal-body">
            <div class="hotline-number" id="modalPhoneNumber"></div>
            <div class="modal-buttons">
                <button class="modal-btn btn-call" id="callButton">
                    <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white' width='18' height='18'%3E%3Cpath d='M20 15.5c-1.25 0-2.45-.2-3.57-.57-.35-.11-.74-.03-1.02.24l-2.2 2.2c-2.83-1.44-5.15-3.75-6.59-6.59l2.2-2.21c.28-.26.36-.65.25-1C8.7 6.45 8.5 5.25 8.5 4c0-.55-.45-1-1-1H4c-.55 0-1 .45-1 1 0 9.39 7.61 17 17 17 .55 0 1-.45 1-1v-3.5c0-.55-.45-1-1-1z'/%3E%3C/svg%3E" alt="拨打">
                    立即拨打
                </button>
                <button class="modal-btn btn-chat" id="chatButton" style="opacity: 0.85; background-color: #f8f8f8; border: 1px dashed #ddd; color: #888;">
                    <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23888' width='18' height='18'%3E%3Cpath d='M20 2H4c-1.1 0-1.99.9-1.99 2L2 22l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zM6 9h12v2H6V9zm8 5H6v-2h8v2zm4-6H6V6h12v2z'/%3E%3C/svg%3E" alt="聊天">
                    在线援助 <span class="dev-badge">开发中</span>
                </button>
            </div>
            <div class="modal-separator"></div>
            <div class="modal-footer">
                所有通话内容严格保密，请放心咨询
            </div>
        </div>
    </div>
</div>

<!-- 开发中提示 -->
<div class="dev-popup" id="devPopup">
    <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' width='18' height='18' fill='%23ffffff'%3E%3Cpath d='M11 7h2v2h-2zm0 4h2v6h-2zm1-9C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z'/%3E%3C/svg%3E" style="vertical-align: middle; margin-right: 8px;" alt="提示">
    在线援助功能正在开发中，敬请期待
</div>
@endsection

@section('scripts')
<script>
    // 弹窗相关函数
    function showHotlineModal(id, phoneNumber, name) {
        document.getElementById('modalPhoneNumber').textContent = phoneNumber;
        
        // 设置拨打按钮事件
        document.getElementById('callButton').onclick = function() {
            window.location.href = 'tel:' + phoneNumber;
            
            // 记录拨打记录
            fetch('{{ route("hotline.record_usage") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                body: JSON.stringify({
                    hotline_id: id,
                    action: 'call'
                })
            });
        };
        
        // 设置在线援助按钮事件（开发中提示）
        document.getElementById('chatButton').onclick = function() {
            const devPopup = document.getElementById('devPopup');
            devPopup.classList.add('show');
            
            setTimeout(function() {
                devPopup.classList.remove('show');
            }, 2500);
        };
        
        // 显示弹窗
        document.getElementById('hotlineModal').style.display = 'flex';
    }
    
    function closeHotlineModal() {
        document.getElementById('hotlineModal').style.display = 'none';
    }
    
    // 点击弹窗外部关闭弹窗
    window.onclick = function(event) {
        const modal = document.getElementById('hotlineModal');
        if (event.target === modal) {
            closeHotlineModal();
        }
    };
</script>
@endsection
