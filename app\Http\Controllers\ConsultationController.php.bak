<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Counselor;
use App\Models\CounselorSchedule;
use App\Models\ConsultationAppointment;
use App\Models\OfflineCourse;
use App\Models\AiConsultationRecord;
use App\Models\AiConsultationSetting;
use App\Models\CourseRegistration;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Illuminate\Support\Facades\Validator;

class ConsultationController extends Controller
{
    /**
     * 显示咨询师列表
     */
    public function counselors(Request $request)
    {
        $query = Counselor::query();
        
        // 筛选专业领域
        if ($request->has('expertise') && !empty($request->expertise)) {
            $query->where('expertise', 'like', '%' . $request->expertise . '%');
        }
        
        // 确保只显示有效的咨询师
        $query->where('is_active', 1);
            $date = Carbon::parse($timeFilter)->format('Y-m-d');
            $query->whereHas('schedules', function($q) use ($date) {
                $q->where('date', $date)
                  ->where('is_available', true);
            });
        }
        
        // 获取咨询师列表
        $counselors = $query->orderBy('sort_order', 'asc')
                           ->paginate(10);
        
        return view('pages.consultation.counselors', compact('counselors'));
    }
    
    /**
     * 显示咨询师详情页面
     */
    public function counselorDetail($id)
    {
        $counselor = Counselor::findOrFail($id);
        
        // 获取未来7天的日期
        $dates = collect();
        $today = Carbon::now();
        
        for ($i = 0; $i < 7; $i++) {
            $date = $today->copy()->addDays($i);
            $schedules = $counselor->schedules()
                ->where('date', $date->format('Y-m-d'))
                ->where('is_available', true)
                ->orderBy('start_time')
                ->get();
                
            $dates->push([
                'date' => $date->format('Y-m-d'),
                'day' => $date->format('m-d'),
                'weekday' => $this->getChineseWeekday($date),
                'schedules' => $schedules
            ]);
        }
        
        return view('pages.consultation.counselor_detail', compact('counselor', 'dates'));
    }
    
    /**
     * 创建咨询预约
     */
    public function createAppointment(Request $request)
    {
        // 验证输入
        $validated = $request->validate([
            'counselor_id' => 'required|exists:counselors,id',
            'schedule_id' => 'required|exists:counselor_schedules,id',
            'consultation_type' => 'required|in:1,2,3',
            'issue_description' => 'required|string|max:1000',
        ]);
        
        // 检查排班是否可用
        $schedule = CounselorSchedule::findOrFail($validated['schedule_id']);
        
        if (!$schedule->is_available || $schedule->isBooked()) {
            return back()->with('error', '该时间段已被预约，请选择其他时间');
        }
        
        // 检查咨询师是否支持所选咨询方式
        $counselor = Counselor::findOrFail($validated['counselor_id']);
        
        if ($validated['consultation_type'] == ConsultationAppointment::TYPE_TEXT && !$counselor->support_text) {
            return back()->with('error', '该咨询师不支持文字咨询');
        } elseif ($validated['consultation_type'] == ConsultationAppointment::TYPE_VOICE && !$counselor->support_voice) {
            return back()->with('error', '该咨询师不支持语音咨询');
        } elseif ($validated['consultation_type'] == ConsultationAppointment::TYPE_VIDEO && !$counselor->support_video) {
            return back()->with('error', '该咨询师不支持视频咨询');
        }
        
        // 创建预约
        $appointment = new ConsultationAppointment();
        $appointment->user_id = Auth::id();
        $appointment->counselor_id = $validated['counselor_id'];
        $appointment->schedule_id = $validated['schedule_id'];
        $appointment->consultation_type = $validated['consultation_type'];
        $appointment->appointment_time = Carbon::parse($schedule->date . ' ' . $schedule->start_time);
        $appointment->status = ConsultationAppointment::STATUS_PENDING;
        $appointment->issue_description = $validated['issue_description'];
        $appointment->duration = 60; // 默认60分钟
        $appointment->price = $counselor->price;
        $appointment->payment_status = ConsultationAppointment::PAYMENT_UNPAID;
        $appointment->save();
        
        // 标记排班为不可用
        $schedule->is_available = false;
        $schedule->save();
        
        // 发送预约确认通知
        // TODO: 实现通知逻辑
        
        return redirect()->route('consultation.my_appointments')
                        ->with('success', '预约申请已提交，请等待咨询师确认');
    }
    
    /**
     * 显示我的预约列表
     */
    public function myAppointments()
    {
        $appointments = ConsultationAppointment::with(['counselor', 'schedule'])
            ->where('user_id', Auth::id())
            ->orderBy('created_at', 'desc')
            ->paginate(10);
            
        return view('pages.consultation.my_appointments', compact('appointments'));
    }
    
    /**
     * 取消预约
     */
    public function cancelAppointment($id)
    {
        $appointment = ConsultationAppointment::where('user_id', Auth::id())
            ->findOrFail($id);
            
        if (!$appointment->canCancel()) {
            return back()->with('error', '该预约已无法取消');
        }
        
        $appointment->status = ConsultationAppointment::STATUS_CANCELLED;
        $appointment->save();
        
        // 恢复排班可用状态
        if ($appointment->schedule) {
            $appointment->schedule->is_available = true;
            $appointment->schedule->save();
        }
        
        return back()->with('success', '预约已取消');
    }
    
    /**
     * 显示线下课程列表
     */
    public function courses(Request $request)
    {
        // 获取筛选参数
        $typeFilter = $request->input('type', '');
        $timeFilter = $request->input('time', '');
        $locationFilter = $request->input('location', '');
        
        // 构建查询
        $query = OfflineCourse::where('status', OfflineCourse::STATUS_PUBLISHED)
                             ->where('registration_deadline', '>=', now());
        
        // 按类型筛选
        if (!empty($typeFilter)) {
            $query->where('title', 'like', '%' . $typeFilter . '%');
        }
        
        // 按时间筛选
        if (!empty($timeFilter)) {
            $date = Carbon::parse($timeFilter)->format('Y-m-d');
            $query->whereDate('start_time', $date);
        }
        
        // 按地点筛选
        if (!empty($locationFilter)) {
            $query->where('location', 'like', '%' . $locationFilter . '%');
        }
        
        // 获取课程列表
        $courses = $query->orderBy('start_time', 'asc')
                        ->paginate(10);
        
        return view('pages.consultation.courses', compact('courses'));
    }
    
    /**
     * 显示课程详情
     */
    public function courseDetail($id)
    {
        $course = OfflineCourse::with('lecturer')->findOrFail($id);
        
        return view('pages.consultation.course_detail', compact('course'));
    }
    
    /**
     * 创建课程报名
     */
    public function registerCourse(Request $request)
    {
        // 验证输入
        $validated = $request->validate([
            'course_id' => 'required|exists:offline_courses,id',
            'department' => 'nullable|string|max:100',
            'participants_count' => 'required|integer|min:1|max:50',
            'participants_info' => 'nullable|array',
            'remarks' => 'nullable|string|max:500',
        ]);
        
        // 检查课程是否可报名
        $course = OfflineCourse::findOrFail($validated['course_id']);
        
        if (!$course->isRegistrationOpen()) {
            return back()->with('error', '该课程已无法报名');
        }
        
        // 检查剩余名额
        if ($course->available_slots < $validated['participants_count']) {
            return back()->with('error', '剩余名额不足，当前剩余：' . $course->available_slots);
        }
        
        // 创建报名记录
        $registration = new CourseRegistration();
        $registration->user_id = Auth::id();
        $registration->course_id = $validated['course_id'];
        $registration->registration_code = CourseRegistration::generateCode();
        $registration->status = CourseRegistration::STATUS_PENDING;
        $registration->department = $validated['department'];
        $registration->participants_count = $validated['participants_count'];
        $registration->participants_info = $validated['participants_info'];
        $registration->remarks = $validated['remarks'];
        $registration->price = $course->price * $validated['participants_count'];
        $registration->payment_status = CourseRegistration::PAYMENT_UNPAID;
        $registration->save();
        
        // 更新课程报名人数
        $course->current_participants += $validated['participants_count'];
        $course->save();
        
        // 发送报名确认通知
        // TODO: 实现通知逻辑
        
        return redirect()->route('consultation.my_registrations')
                        ->with('success', '报名申请已提交，请尽快完成支付');
    }
    
    /**
     * 显示我的课程报名列表
     */
    public function myRegistrations()
    {
        $registrations = CourseRegistration::with('course')
            ->where('user_id', Auth::id())
            ->orderBy('created_at', 'desc')
            ->paginate(10);
            
        return view('pages.consultation.my_registrations', compact('registrations'));
    }
    
    /**
     * 取消课程报名
     */
    public function cancelRegistration($id)
    {
        $registration = CourseRegistration::where('user_id', Auth::id())
            ->findOrFail($id);
            
        if (!$registration->canCancel()) {
            return back()->with('error', '该报名已无法取消');
        }
        
        $registration->status = CourseRegistration::STATUS_CANCELLED;
        $registration->save();
        
        // 恢复课程名额
        if ($registration->course) {
            $registration->course->current_participants -= $registration->participants_count;
            $registration->course->save();
        }
        
        return back()->with('success', '报名已取消');
    }
    
    /**
     * 进入咨询会话
     */
    public function startConsultation($id)
    {
        $appointment = ConsultationAppointment::where('user_id', Auth::id())
            ->with(['counselor', 'schedule'])
            ->findOrFail($id);
            
        if (!$appointment->canStart()) {
            return back()->with('error', '当前无法进入咨询会话');
        }
        
        // 更新预约状态
        $appointment->status = ConsultationAppointment::STATUS_ONGOING;
        $appointment->save();
        
        // 生成会议链接
        if (empty($appointment->meeting_url)) {
            // 这里简化处理，实际应该集成第三方会议系统
            $appointment->meeting_url = route('consultation.room', ['id' => $appointment->id]);
            $appointment->meeting_password = mt_rand(100000, 999999);
            $appointment->save();
        }
        
        return redirect()->to($appointment->meeting_url);
    }
    
    /**
     * 显示咨询会话室
     */
    public function consultationRoom($id)
    {
        $appointment = ConsultationAppointment::findOrFail($id);
        
        // 检查权限
        if ($appointment->user_id != Auth::id() && $appointment->counselor->id != Auth::user()->id) {
            abort(403, '您没有权限访问此咨询会话');
        }
        
        // 检查状态
        if ($appointment->status != ConsultationAppointment::STATUS_ONGOING) {
            return redirect()->route('consultation.my_appointments')
                           ->with('error', '该咨询会话不在进行中状态');
        }
        
        // 获取AI设置
        $aiSettings = AiConsultationSetting::getActiveSettings();
        
        return view('pages.consultation.room', compact('appointment', 'aiSettings'));
    }
    
    /**
     * AI辅助咨询API
     */
    public function aiConsult(Request $request)
    {
        // 验证输入
        $validated = $request->validate([
            'user_query' => 'required|string',
            'appointment_id' => 'nullable|exists:consultation_appointments,id',
        ]);
        
        // 获取AI设置
        $aiSettings = AiConsultationSetting::getActiveSettings();
        
        if (!$aiSettings) {
            return response()->json(['error' => 'AI咨询服务暂时不可用'], 503);
        }
        
        try {
            // 调用DeepSeek API
            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
                'Authorization' => 'Bearer ' . $aiSettings->api_key,
            ])->post($aiSettings->api_url, [
                'model' => $aiSettings->model,
                'messages' => [
                    ['role' => 'system', 'content' => $aiSettings->system_prompt],
                    ['role' => 'user', 'content' => $validated['user_query']],
                ],
                'max_tokens' => $aiSettings->max_tokens,
                'temperature' => $aiSettings->temperature,
            ]);
            
            if ($response->successful()) {
                $responseData = $response->json();
                $aiResponse = $responseData['choices'][0]['message']['content'] ?? '';
                
                // 生成推荐资源
                $recommendedResources = $this->generateRecommendedResources($validated['user_query']);
                
                // 保存记录
                $record = new AiConsultationRecord();
                $record->user_id = Auth::id();
                $record->appointment_id = $validated['appointment_id'];
                $record->user_query = $validated['user_query'];
                $record->ai_response = $aiResponse;
                $record->recommended_resources = $recommendedResources;
                $record->save();
                
                return response()->json([
                    'ai_response' => $aiResponse,
                    'recommended_resources' => $recommendedResources
                ]);
            } else {
                Log::error('AI API Error: ' . $response->body());
                return response()->json(['error' => 'AI服务暂时不可用'], 503);
            }
        } catch (\Exception $e) {
            Log::error('AI Consultation Error: ' . $e->getMessage());
            return response()->json(['error' => '服务器错误，请稍后再试'], 500);
        }
    }
    
    /**
     * 生成推荐资源
     */
    private function generateRecommendedResources($query)
    {
        // 在实际应用中，这里可以使用更复杂的算法或搜索引擎来匹配相关资源
        // 这里简化实现，根据关键词匹配
        
        $keywords = ['焦虑', '抑郁', '压力', '情绪', '失眠', '人际关系', '亲子', '工作'];
        $matchedKeywords = [];
        
        foreach ($keywords as $keyword) {
            if (strpos($query, $keyword) !== false) {
                $matchedKeywords[] = $keyword;
            }
        }
        
        $resources = [];
        
        // 根据匹配的关键词推荐文章
        if (!empty($matchedKeywords)) {
            // 这里应该查询数据库获取相关文章和视频
            // 这里简化处理，返回假数据
            $resources = [
                [
                    'type' => 'article',
                    'title' => '如何有效应对' . implode('和', $matchedKeywords),
                    'url' => route('knowledge.articles')
                ],
                [
                    'type' => 'video',
                    'title' => '专家详解' . $matchedKeywords[0] . '的成因与解决方案',
                    'url' => route('knowledge.videos')
                ]
            ];
        }
        
        return $resources;
    }
    
    /**
     * 获取中文星期几
     */
    private function getChineseWeekday($date)
    {
        $weekMap = [
            0 => '周日',
            1 => '周一',
            2 => '周二',
            3 => '周三',
            4 => '周四',
            5 => '周五',
            6 => '周六',
        ];
        
        return $weekMap[$date->dayOfWeek];
    }
}
