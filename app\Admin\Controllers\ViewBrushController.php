<?php

namespace App\Admin\Controllers;

use Dcat\Admin\Layout\Content;
use Dcat\Admin\Form;
use Illuminate\Routing\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Dcat\Admin\Http\JsonResponse;

class ViewBrushController extends Controller
{
    public function index(Content $content)
    {
        return $content
            ->header('心理科普浏览量管理')
            ->body($this->form());
    }

    public function handle(Request $request)
    {
        \Log::info('进入 handle()', $request->all());
        $table = $request->input('table');
        $idsInput = $request->input('ids');
        $views = intval($request->input('views'));
        $mode = $request->input('mode');
         \Log::info("参数：table=$table, ids=$idsInput, mode=$mode, views=$views");

        if (!in_array($table, ['videos', 'course_lessons', 'articles','news'])) {
            return JsonResponse::make()->error('数据表不存在！');
        }

        // 解析 id 列表或范围
        $ids = [];
        if (preg_match('/^\d+$/', $idsInput)) {
            $ids[] = intval($idsInput);
        } elseif (preg_match('/^(\d+)-(\d+)$/', $idsInput, $m)) {
            $ids = range(intval($m[1]), intval($m[2]));
        } else {
            return JsonResponse::make()->error('ID 格式不正确，应为 5 或 5-10');
        }

        foreach ($ids as $id) {
            if (!DB::table($table)->where('id', $id)->exists()) {
                \Log::warning("ID $id 不存在，跳过");
                continue;
            }

            if ($mode === 'increment') {
                if($table == 'news'){
                   DB::table($table)->where('id', $id)->increment('view_count', $views); 
                }else{
                    DB::table($table)->where('id', $id)->increment('views', $views);
                }
                
                \Log::info("ID $id 增加 $views");
            } else {
                if($table == 'news'){
                    DB::table($table)->where('id', $id)->update(['view_count' => $views]);
                }else{
                    DB::table($table)->where('id', $id)->update(['views' => $views]);
                }
                \Log::info("ID $id 设置为 $views");
            }
        }

        return JsonResponse::make()->success('执行成功！');
    }

    protected function form()
    {
        return Form::make([], function (Form $form) {
            $form->action(admin_url('view-brush')); // ✅ 表单提交地址
        
            $form->select('table', '选择心里科普类型')->options([
                'videos' => '心理视频',
                'course_lessons' => '心理课堂',
                'articles' => '心理文章',
                'news' => '新闻资讯',
            ])->required();
        
            $form->text('ids', 'ID 或 ID 范围')
                ->help('例：5 或 5-10（缺失自动跳过）')->required();
        
            $form->radio('mode', '操作方式')
                ->options(['increment' => '增加', 'set' => '设置为'])->default('increment')->required();
        
            $form->number('views', '浏览量')->min(0)->required();
        
            $form->footer(function ($footer) {
                $footer->disableReset();
                $footer->disableViewCheck();
                $footer->disableEditingCheck();
                $footer->disableCreatingCheck();
                
            });
            $form->tools(function (Form\Tools $tools) {
                  $tools->disableDelete();
                  $tools->disableView();
                  $tools->disableList();
             });
        });
    }
}
