<?php

namespace App\Admin\Controllers;

use App\Models\LiveShow;
use App\Models\Counselor;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Illuminate\Support\Facades\Storage;
use Z<PERSON>\QrReader;

class LiveShowController extends AdminController
{
    protected $title = '直播管理';
    
    protected function grid()
    {
        return Grid::make(LiveShow::with(['counselor']), function (Grid $grid) {
            $grid->column('id')->sortable();
            
            $grid->column('cover_image', '封面')->image('', 60, 40);
            
            $grid->column('title', '直播标题')->width(200)->display(function ($title) {
                return "<strong>{$title}</strong>";
            });
            
            $grid->column('counselor.name', '主播')->display(function ($name) {
                return $name ?: '<span style="color: #999;">未指定</span>';
            });
            
            $grid->column('scheduled_at', '预定时间')->display(function ($time) {
                return $time ? $time->format('m月d日 H:i') : '<span style="color: #999;">待定</span>';
            });
            $grid->column('ended_at', '结束时间')->display(function ($time) {
                return $time ? $time->format('m月d日 H:i') : '<span style="color: #999;">手动控制</span>';
            });
            
            $grid->column('status', '状态')->using([
                'upcoming' => '即将开始',
                'live' => '直播中',
                'ended' => '已结束'
            ])->label([
                'upcoming' => 'warning',
                'live' => 'danger',
                'ended' => 'default'
            ]);
            
            // 显示解析出的链接状态
            $grid->column('live_url', '直播链接')->display(function ($url) {
                if (empty($url)) {
                    return '<span style="color: #999;">未设置</span>';
                }
                return '<span style="color: #28a745;">已设置</span>';
            });
            
            $grid->column('is_featured', '首页推荐')->switch();
            $grid->column('is_active', '启用状态')->switch();
            $grid->column('sort_order', '排序')->editable();
            
            $grid->column('created_at', '创建时间');
            
            // 筛选器
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('counselor_id', '主播')->select(
                    Counselor::where('is_active', 1)->pluck('name', 'id')
                );
                $filter->equal('status', '状态')->select([
                    'upcoming' => '即将开始',
                    'live' => '直播中',
                    'ended' => '已结束'
                ]);
                $filter->equal('is_featured', '首页推荐')->select([
                    1 => '是',
                    0 => '否'
                ]);
                $filter->between('scheduled_at', '预定时间')->datetime();
            });
            
            // 排序
            $grid->model()->orderBy('sort_order', 'desc')->orderBy('scheduled_at', 'asc');
            
            // 行操作
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                // 根据状态显示不同操作
                if ($this->canWatch()) {
                    $actions->append('<a href="#" onclick="openLivePreview(\'' . $this->live_url . '\')" class="btn btn-xs btn-success">预览</a>');
                }
            });
            
        });
    }
    
    protected function form()
    {
        return Form::make(LiveShow::class, function (Form $form) {
            
            $form->text('title', '直播标题')
                ->required()
                ->rules('max:100')
                ->help('建议不超过20个字符，突出重点');
                
            $form->textarea('description', '直播描述')
                ->rows(4)
                ->help('详细介绍本次直播的内容和亮点');
                
            $form->image('cover_image', '封面图片')
                ->uniqueName()
                ->help('建议尺寸：16:9，如375x210像素，支持jpg、png格式');
                
            $form->select('counselor_id', '主播咨询师')
                ->options(Counselor::where('is_active', 1)->pluck('name', 'id'))
                ->help('选择负责本次直播的咨询师');
                
            // 二维码上传，会自动解析链接
            $form->image('qr_code_image', '微信视频号预约二维码')
                ->uniqueName()
                ->help('上传微信视频号的预约二维码图片')->autoUpload();
                
            // 显示解析出的链接（只读）
            $form->text('live_url', '直播链接')->help('直播的地址');
                
            $form->datetime('scheduled_at', '预定直播时间')
                ->help('设置直播的预定开始时间');
            $form->datetime('ended_at', '预定结束时间')
                ->help('设置后将根据时间自动维护状态：开始前=即将开始，进行中=直播中，结束后=已结束。留空则手动维护状态。');
                    
                
            $form->select('status', '直播状态')
                ->options([
                    'upcoming' => '即将开始',
                    'live' => '直播中',
                    'ended' => '已结束'
                ])
                ->default('upcoming')
                ->help('如果设置了结束时间，状态将自动维护。如果没有结束时间，请手动维护状态。');
                
            $form->textarea('notice', '直播公告')
                ->rows(3)
                ->help('显示在直播卡片上的特别提醒或公告信息');
                
            $form->switch('is_featured', '首页推荐')
                ->default(false)
                ->help('开启后在直播结束前将在首页显示');
                
            $form->switch('is_active', '启用状态')
                ->default(true)
                ->help('关闭后前端将不显示此直播');
                
            $form->number('sort_order', '排序权重')
                ->default(0)
                ->help('数值越大越靠前显示');
                
            
        });
    }
    
    protected function detail($id)
    {
        return Show::make($id, LiveShow::with(['counselor']), function (Show $show) {
            $show->field('id', 'ID');
            $show->field('title', '直播标题');
            $show->field('description', '直播描述');
            $show->field('cover_image', '封面图片')->image();
            $show->field('counselor.name', '主播咨询师');
            $show->field('live_url', '直播链接')->link();
            $show->field('qr_code_image', '预约二维码')->image();
            $show->field('scheduled_at', '预定时间');
             $show->field('ended_at', '结束时间')->as(function ($value) {
                return $value ? $value->format('Y-m-d H:i:s') : '手动控制状态';
            });
            $show->field('status_text', '状态');
            $show->field('notice', '直播公告');
            $show->field('is_featured', '首页推荐')->as(function ($value) {
                return $value ? '是' : '否';
            });
            $show->field('is_active', '启用状态')->as(function ($value) {
                return $value ? '是' : '否';
            });
            $show->field('sort_order', '排序权重');
            $show->field('created_at', '创建时间');
            $show->field('updated_at', '更新时间');
        });
    }
    
    /**
     * 解析二维码文件中的URL
     */
    private function parseQRCodeFromFile($imagePath)
    {
        try {
            // 方法1: 使用简单的正则匹配（适用于包含明显URL的二维码）
            $content = $this->parseQRCodeSimple($imagePath);
            if ($content) {
                return $content;
            }
            
            // 方法2: 如果安装了二维码解析库，使用库解析
            if (class_exists('\Zxing\QrReader')) {
                $qrcode = new \Zxing\QrReader($imagePath);
                $text = $qrcode->text();
                if ($text && $this->isWechatChannelsUrl($text)) {
                    return $text;
                }
            }
            
            return null;
        } catch (\Exception $e) {
            \Log::error('二维码解析失败: ' . $e->getMessage());
            return null;
        }
    }
    
    /**
     * 简单的二维码解析方法（基于图像处理的简化版本）
     */
    private function parseQRCodeSimple($imagePath)
    {
        // 这里可以添加更复杂的图像处理逻辑
        // 目前先返回null，让用户手动输入
        return null;
    }
    
    /**
     * 检查是否是微信视频号链接
     */
    private function isWechatChannelsUrl($url)
    {
        return strpos($url, 'channels.weixin.qq.com') !== false || 
               strpos($url, 'wxch.lol') !== false ||
               strpos($url, 'weixin://') === 0;
    }

    protected function parseQrCode($qrImagePath)
    {
        try {
            $qrcode = new QrReader($qrImagePath);
            $text = $qrcode->text(); // 返回解码后的文本
            return $text;
        } catch (\Exception $e) {
            \Log::error('QR Code parsing error: ' . $e->getMessage());
            return null;
        }
    }
} 