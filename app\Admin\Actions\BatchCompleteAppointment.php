<?php

namespace App\Admin\Actions;

use App\Models\ConsultationAppointment;
use Dcat\Admin\Grid\BatchAction;
use Illuminate\Database\Eloquent\Collection;

class BatchCompleteAppointment extends BatchAction
{
    // 确认弹窗信息
    public function confirm()
    {
        return '您确定要将所选预约标记为已完成吗？';
    }

    // 处理请求
    public function handle(Collection $collection)
    {
        $successCount = 0;
        foreach ($collection as $model) {
            if ($model->status == 2 || $model->status == 3) { // 只处理已确认或进行中的预约
                $model->status = 4; // 已完成
                $model->save();
                $successCount++;
            }
        }

        return $this->response()
            ->success("成功标记 {$successCount} 条预约为已完成")
            ->refresh();
    }
}
