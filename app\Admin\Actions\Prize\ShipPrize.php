<?php

namespace App\Admin\Actions\Prize;

use Dcat\Admin\Grid\RowAction;
use Illuminate\Http\Request;
use Dcat\Admin\Admin;

class ShipPrize extends RowAction
{
    protected $title = '发货';
    
    public function handle(Request $request)
    {
        // 获取当前行数据的ID
        $id = $this->getKey();
        
        // 查询数据库
        $model = \App\Models\QuizPrizeWinner::find($id);
        
        if (!$model) {
            return $this->response()->error('找不到对应的奖品记录');
        }
        
        // 只有处理中或已申领状态才能标记为已发货
        if (!in_array($model->status, ['pending', 'claimed'])) {
            return $this->response()->error('只有处理中或已申领的奖品才能标记为发货');
        }
        
        // 更新模型
        $model->status = 'shipped';
        $model->admin_notes = ($model->admin_notes ? $model->admin_notes . "\n" : '') . 
                              Admin::user()->name . ' 于 ' . now() . ' 标记为已发货';
        $model->save();
        
        return $this->response()
                    ->success('奖品已标记为已发货')
                    ->refresh();
    }
    
    /**
     * 确认消息
     */
    public function confirm()
    {
        return ['发货确认', '您确定要将该奖品标记为已发货吗？'];
    }
}
