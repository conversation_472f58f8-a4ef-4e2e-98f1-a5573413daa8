<?php
namespace App\Admin\Controllers;

use App\Models\AssessmentAiSetting;
use Dcat\Admin\Grid;
use Dcat\Admin\Form;
use Dcat\Admin\Show;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Dcat\Admin\Http\Controllers\AdminController;

class AssessmentAiSettingsController extends AdminController
{
    protected $title = '心理测评AI设置';

    protected function grid(): Grid
    {
        return Grid::make(new AssessmentAiSetting(), function (Grid $grid) {
            $grid->column('id', '编号')->sortable();
            $grid->column('api_key', 'API Key')->display(function ($apiKey) {
                return '****'.substr($apiKey, -4);
            });
            $grid->column('endpoint', '接口地址');
            $grid->column('model', '模型ID');
            $grid->column('system_prompt', '系统提示词')->limit(30);
            $grid->column('user_prompt_template', '用户提示词')->limit(30);
            $grid->column('temperature', '温度');
            $grid->column('max_tokens', '最大Token');
            $grid->column('created_at', '创建时间');
            $grid->column('updated_at', '更新时间');
            $grid->column('is_active', '启用状态')->bool();
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('is_active', '启用状态')->radio([1 => '启用', 0 => '禁用']);
            });

            $grid->actions(function ($actions) {
                $id = $actions->getKey();
                $url = admin_url("assessment-ai-settings/{$id}/test-connection");
                $actions->append("<a href=\"javascript:;\" onclick=\"Dcat.NP.start(); $.ajax({type:'POST', url:'{$url}', headers:{'X-CSRF-TOKEN': $('meta[name=csrf-token]').attr('content')}, success:function(){Dcat.NP.done(); Dcat.success('连接成功');}, error:function(xhr){Dcat.NP.done(); Dcat.error('连接失败：'+xhr.status);} });\" class=\"btn btn-sm btn-warning\">测试连接</a>");
            });
        });
    }

    protected function detail($id): Show
    {
        return Show::make($id, new AssessmentAiSetting(), function (Show $show) {
            $show->field('id', '编号');
            $show->field('api_key', 'API Key')->as(function ($apiKey) {
                return '****'.substr($apiKey, -4);
            });
            $show->field('endpoint', '接口地址');
            $show->field('model', '模型ID');
            $show->field('system_prompt', '系统提示词');
            $show->field('user_prompt_template', '用户提示词');
            $show->field('temperature', '温度');
            $show->field('max_tokens', '最大Token');
            $show->field('is_active', '启用状态')->using([1 => '启用', 0 => '禁用']);
            $show->field('created_at', '创建时间');
            $show->field('updated_at', '更新时间');
        });
    }

    protected function form(): Form
    {
        return Form::make(new AssessmentAiSetting(), function (Form $form) {
            $form->display('id', '编号');
            $form->password('api_key', 'API Key')->required()->default('');
            $form->text('endpoint', '接口地址')->required();
            $form->text('model', '模型ID')->required();
            $form->textarea('system_prompt', '系统提示词')->required();
            $form->textarea('user_prompt_template', '用户提示词')->required();
            $form->decimal('temperature', '温度')->default(0.7)->required();
            $form->number('max_tokens', '最大Token')->default(512)->required();
            $form->switch('is_active', '启用状态')->default(true);

            $form->tools(function (Form\Tools $tools) use ($form) {
                if ($id = $form->getKey()) {
                    $url = admin_url("assessment-ai-settings/{$id}/test-connection");
                    $tools->append("<a href=\"javascript:;\" onclick=\"Dcat.NP.start(); $.ajax({type:'POST', url:'{$url}', headers:{'X-CSRF-TOKEN': $('meta[name=csrf-token]').attr('content')}, success:function(){Dcat.NP.done(); Dcat.success('连接成功');}, error:function(xhr){Dcat.NP.done(); Dcat.error('连接失败：'+xhr.status);} });\" class=\"btn btn-warning\">测试连接</a>");
                }
            });

            $form->saving(function (Form $form) {
                if (! $form->api_key) {
                    $form->ignore(['api_key']);
                }
            });

            $form->display('created_at', '创建时间');
            $form->display('updated_at', '更新时间');
        });
    }

    public function testConnection($id, Request $request)
    {
        $setting = AssessmentAiSetting::findOrFail($id);
        try {
            $apiResponse = Http::withHeaders([
                'Authorization' => 'Bearer '.$setting->api_key,
                'Content-Type' => 'application/json',
            ])->post($setting->endpoint, [
                'model' => $setting->model,
                'messages' => [
                    ['role' => 'system', 'content' => $setting->system_prompt],
                    ['role' => 'user', 'content' => '测试连接'],
                ],
                'temperature' => $setting->temperature,
                'max_tokens' => 1,
            ]);

            if ($apiResponse->successful()) {
                return response()->json(['message' => '连接成功'], 200);
            }

            return response()->json([
                'message' => '连接失败：'.$apiResponse->status()
            ], $apiResponse->status());
        } catch (\Exception $e) {
            return response()->json([
                'message' => '连接异常：'.$e->getMessage()
            ], 500);
        }
    }
}
