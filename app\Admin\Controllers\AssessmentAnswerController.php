<?php
namespace App\Admin\Controllers;

use App\Models\AssessmentAnswer;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Form;
use Dcat\Admin\Http\Controllers\AdminController;

class AssessmentAnswerController extends AdminController
{
    protected $title = '心理测评答案';

    protected function grid(): Grid
    {
        return Grid::make(new AssessmentAnswer(), function (Grid $grid) {
            $grid->column('id', '编号')->sortable();
            $grid->column('response.id', '答卷ID');
            $grid->column('question.content', '题目内容');
            $grid->column('option.content', '选项内容');
            $grid->column('custom_value', '自定义答案');
            $grid->column('created_at', '创建时间');
            $grid->column('updated_at', '更新时间');

            // 禁用新增，编辑，删除，仅保留查看
            $grid->disableCreateButton();
            $grid->actions(function ($actions) {
                $actions->disableEdit();
                $actions->disableDelete();
            });

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('response_id', '答卷ID');
            });
        });
    }

    protected function detail($id): Show
    {
        return Show::make($id, new AssessmentAnswer(), function (Show $show) {
            $show->field('id', '编号');
            $show->field('response.id', '答卷ID');
            $show->field('question.content', '题目内容');
            $show->field('option.content', '选项内容');
            $show->field('custom_value', '自定义答案');
            $show->field('created_at', '创建时间');
            $show->field('updated_at', '更新时间');
        });
    }

    protected function form(): Form
    {
        return Form::make(new AssessmentAnswer(), function (Form $form) {
            $form->display('id', '编号');
            $form->display('response.id', '答卷ID');
            $form->display('question.content', '题目内容');
            $form->display('option.content', '选项内容');
            $form->display('custom_value', '自定义答案');
            $form->display('created_at', '创建时间');
            $form->display('updated_at', '更新时间');

            // 全部禁止操作
            $form->disableCreateButton();
            $form->disableEditButton();
            $form->disableDeleteButton();
            $form->disableSubmitButton();
        });
    }
}
