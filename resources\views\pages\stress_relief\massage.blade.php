@extends('layouts.app')

@section('title', '大脑按摩 - 心理减压')

@section('content')
<div class="massage-container">
  <!-- 页面头部 -->
  <div class="page-header">
    <div class="container">
      <div class="header-content">
        <div class="breadcrumb">
          <a href="{{ route('stress_relief.index') }}">心理减压</a>
          <span class="divider">/</span>
          <span class="current">大脑按摩</span>
        </div>
        <h1 class="page-title">大脑按摩</h1>
        <p class="page-subtitle">ASMR，为你打造一个治愈专注的空间</p>
      </div>
    </div>
  </div>

  <!-- 技术卡片 -->
  <div class="massage-techniques">
    <div class="container">
      <div class="programs-grid">
        <div class="technique-card" onclick="startTechnique('visualization')">
          <div class="technique-icon"><i class="fas fa-eye"></i></div>
          <h3 class="technique-title">视觉想象</h3>
          <p class="technique-desc">通过想象美好画面舒缓大脑</p>
        </div>
        <div class="technique-card" onclick="startTechnique('breathing')">
          <div class="technique-icon"><i class="fas fa-wind"></i></div>
          <h3 class="technique-title">呼吸练习</h3>
          <p class="technique-desc">深呼吸训练，平静内心</p>
        </div>
        <div class="technique-card" onclick="startTechnique('memory')">
          <div class="technique-icon"><i class="fas fa-brain"></i></div>
          <h3 class="technique-title">记忆训练</h3>
          <p class="technique-desc">增强记忆力的认知训练</p>
        </div>
        <div class="technique-card" onclick="startTechnique('focus')">
          <div class="technique-icon"><i class="fas fa-bullseye"></i></div>
          <h3 class="technique-title">专注练习</h3>
          <p class="technique-desc">提升注意力集中能力</p>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- 弹窗 -->
<div id="technique-modal" class="technique-modal" style="display:none;">
  <div class="modal-overlay"></div>
  <div class="modal-content">
    <div class="modal-header">
      <h3 id="technique-title">大脑按摩</h3>
      <button class="close-btn" onclick="closeTechniqueModal()"><i class="fas fa-times"></i></button>
    </div>
    <div class="modal-body">
      <div class="technique-visual" id="technique-visual"></div>
    </div>
  </div>
</div>

<style>
.massage-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    /* 自然处理安全区域 */
    padding-top: calc(env(safe-area-inset-top, 0px) + 20px);
    padding-bottom: calc(env(safe-area-inset-bottom, 0px) + 80px);
}

/* 覆盖全局容器样式 */
.massage-container .container {
    max-width: none;
    width: 100%;
    padding: 0 15px;
    background-color: transparent !important;
    box-sizing: border-box;
}

/* 页面头部 - 自然设计 */
.page-header {
    padding: 6rem 0 2rem;
    color: white;
    position: relative;
    background: transparent;
}

/* 装饰性背景 */
.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 100%;
    background: radial-gradient(ellipse at top, rgba(255,255,255,0.1) 0%, transparent 50%);
    pointer-events: none;
}

.header-content {
    position: relative;
    z-index: 1;
    text-align: center;
}

.breadcrumb {
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.breadcrumb a {
    color: rgba(255,255,255,0.8);
    text-decoration: none;
}

.divider {
    margin: 0 0.5rem;
    opacity: 0.6;
}

.page-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 0 2px 10px rgba(0,0,0,0.3);
}

.page-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
}

.massage-techniques {
    padding: 3rem 0 5rem;
    width: 100%;
    box-sizing: border-box;
}

.programs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
    box-sizing: border-box;
    width: 100%;
}

.technique-card {
    background: white;
    border-radius: 20px;
    padding: 2.5rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    text-align: center;
}

.technique-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
}

.technique-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: white;
}

.technique-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.technique-desc {
    color: #7f8c8d;
    line-height: 1.6;
    font-size: 1rem;
}

.technique-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.9);
    backdrop-filter: blur(10px);
}

.modal-content {
    position: relative;
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    border-radius: 20px;
    width: 90%;
    max-width: 600px;
    overflow: hidden;
    color: white;
}

.modal-header {
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.modal-header h3 {
    margin: 0;
    font-size: 1.3rem;
}

.close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.3s ease;
}

.close-btn:hover {
    background: rgba(255,255,255,0.2);
}

.modal-body {
    padding: 2rem;
    text-align: center;
}

.technique-visual {
    height: 200px;
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.breathing-circle {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0.1) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    font-size: 1.2rem;
    font-weight: 600;
    transition: transform 0.3s ease;
}

.visualization-scene {
    text-align: center;
    opacity: 0.9;
}

.visualization-scene h4 {
    margin-bottom: 1rem;
    font-size: 1.3rem;
    color: #ffd700;
}

.visualization-scene p {
    line-height: 1.8;
    font-size: 1rem;
    opacity: 0.9;
}

.memory-game {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10px;
    margin: 0 auto;
    max-width: 300px;
}

.memory-card {
    aspect-ratio: 1;
    background: rgba(255,255,255,0.2);
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.memory-card:hover, .memory-card.active {
    background: rgba(255,255,255,0.5);
    transform: scale(1.1);
}

.focus-target {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: radial-gradient(circle, white 20%, transparent 20%);
    margin: 0 auto;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.technique-controls {
    margin-top: 2rem;
}

.control-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem 5rem;
    background: rgba(255,255,255,0.2);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.control-btn:hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

@media (max-width: 768px) {
    .massage-container {
        padding-top: calc(env(safe-area-inset-top, 0px) + 15px);
        padding-bottom: calc(env(safe-area-inset-bottom, 0px) + 80px);
    }
    
    .programs-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        padding: 0 10px;
    }
    
    .modal-content {
        width: 95%;
        margin: 1rem;
    }
}

@media (max-width: 500px) {
    .massage-container {
        padding-top: calc(env(safe-area-inset-top, 0px) + 10px);
        padding-bottom: calc(env(safe-area-inset-bottom, 0px) + 80px);
        width: 100%;
        overflow-x: hidden;
    }
    
    .massage-container .container {
        max-width: 100% !important;
        width: 100% !important;
        padding: 0 10px !important;
        margin: 0 !important;
        background-color: transparent !important;
        box-sizing: border-box !important;
    }
    
    .page-header {
        padding: 1rem 0 1.5rem;
        width: 100%;
        box-sizing: border-box;
    }
    
    .page-title {
        font-size: 2rem;
    }
    
    .page-subtitle {
        font-size: 1rem;
    }
    
    .massage-techniques {
        padding: 1.5rem 0 2rem;
        width: 100%;
        box-sizing: border-box;
    }
    
    .programs-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
        padding: 0 !important;
        margin: 0 !important;
        width: 100% !important;
        max-width: 100% !important;
        box-sizing: border-box !important;
    }
    
    .technique-card {
        padding: 1.5rem 1rem;
        transition: transform 0.2s ease;
        width: 100%;
        box-sizing: border-box;
    }
    
    .technique-card:hover {
        transform: translateY(-3px);
    }
    
    .technique-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
        margin-bottom: 1rem;
    }
    
    .technique-title {
        font-size: 1rem;
        margin-bottom: 0.8rem;
    }
    
    .technique-desc {
        font-size: 0.75rem;
        line-height: 1.4;
    }
    /* 呼吸动画：平滑放大收缩 */
    @keyframes breathe {
      0%,100% { transform: scale(1); }
      50%    { transform: scale(1.3); }
    }
    .breathing-circle.animate {
      animation: breathe 16s infinite ease-in-out;
    }
    .technique-controls {
      display: none !important;
    }
    /* 只针对记忆训练区做样式覆盖，避免与其他公共CSS冲突 */
    #technique-modal .memory-game {
      display: grid !important;
      grid-template-columns: repeat(4, 1fr) !important;
      gap: 8px !important;
      margin: 0 auto !important;
      padding: 1rem !important;
      background: rgba(255, 255, 255, 0.1) !important;
      border-radius: 10px !important;
      max-width: 320px !important;
    }
    #technique-modal .memory-card {
      aspect-ratio: 1 !important;
      background: rgba(255, 255, 255, 0.2) !important;
      border-radius: 6px !important;
      cursor: pointer !important;
      transition: background 0.2s ease, transform 0.2s ease !important;
    }
    #technique-modal .memory-card.active {
      background: rgba(255, 255, 255, 0.5) !important;
      transform: scale(1.05) !important;
    }
    #technique-modal .memory-card:hover {
      background: rgba(255, 255, 255, 0.4) !important;
      transform: scale(1.05) !important;
    }
}
</style>


<script>
document.addEventListener('DOMContentLoaded', () => {
  const techniques = {
    visualization: {
      title: '视觉想象',
      render: () => `
        <div class="visualization-scene">
          <h4>宁静的海滩</h4>
          <p>想象你正躺在温暖的沙滩上，海浪轻柔地拍打着岸边。阳光洒落，带来宁静与舒适。</p>
        </div>`
    },
    breathing: {
      title: '呼吸练习',
      render: () => `<div class="breathing-circle" id="breathing-circle">准备呼吸</div>`
    },
    memory: {
      title: '数字记忆训练',
      render: () => `
        <div id="memory-train">
          <div id="memory-display" style="font-size:2rem; margin-bottom:1rem;"></div>
          <div id="memory-input" style="display:none;">
            <input id="memory-answer" type="text" placeholder="请输入刚才的数字" style="padding:0.5rem; font-size:1rem; width:80%;"/>
            <button id="memory-submit" class="control-btn" style="margin-left:0.5rem;">提交</button>
          </div>
        </div>`
    },
    focus: {
      title: '专注练习',
      render: () => `
        <div class="focus-target"></div>
        <p style="margin-top:1rem;">专注观察中心的白点，保持注意力集中。</p>`
    }
  };

  let breathingInterval, memorySequence = [];

  // 打开模式
  window.startTechnique = type => {
    const t = techniques[type];
    document.getElementById('technique-title').textContent = t.title;
    document.getElementById('technique-visual').innerHTML = t.render();
    document.getElementById('technique-modal').style.display = 'flex';
    stopAll();
    if (type === 'breathing')  startBreathing();
    if (type === 'memory')    startNumericMemory();
  };

  // 关闭
  window.closeTechniqueModal = () => {
    document.getElementById('technique-modal').style.display = 'none';
    stopAll();
  };

  function stopAll() {
    clearInterval(breathingInterval);
    breathingInterval = null;
  }

  // 呼吸练习：4s吸/4s呼
  function startBreathing() {
    const circle = document.getElementById('breathing-circle');
    if (!circle) return;
    let phase = 0;
    const labels = ['吸气 4秒','呼气 4秒'];
    update();
    breathingInterval = setInterval(update, 4000);
    function update() {
      circle.textContent = labels[phase];
      circle.className = 'breathing-circle ' + (phase===0?'inhale':'exhale');
      phase = 1 - phase;
    }
  }

  // 数字记忆训练
  function startNumericMemory() {
    const display = document.getElementById('memory-display');
    const inputWrap = document.getElementById('memory-input');
    const answerInput = document.getElementById('memory-answer');
    const submitBtn = document.getElementById('memory-submit');

    // 生成 5 位随机数字
    memorySequence = Array.from({length:5}, () => Math.floor(Math.random()*10)).join('');
    display.textContent = memorySequence;
    inputWrap.style.display = 'none';
    answerInput.value = '';

    // 展示 5 秒后切换到输入
    setTimeout(() => {
      display.textContent = '';
      inputWrap.style.display = 'block';
      answerInput.focus();
    }, 5000);

    // 提交校验
    submitBtn.onclick = () => {
      const ans = answerInput.value.trim();
      if (ans === memorySequence) {
        alert('🎉 回答正确！你记住了所有数字。');
        stopAll();
      } else {
        alert('❌ 回答错误，请重试。');
        answerInput.value = '';
        answerInput.focus();
      }
    };
  }

  // 绑定关闭按钮
  document.querySelector('#technique-modal .close-btn')
    .addEventListener('click', closeTechniqueModal);
});
</script>
@endsection 