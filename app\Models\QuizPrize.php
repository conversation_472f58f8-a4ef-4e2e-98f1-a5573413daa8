<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class QuizPrize extends Model
{
    use HasFactory, HasDateTimeFormatter;

    protected $fillable = [
        'quiz_activity_id',
        'name',
        'description',
        'image',
        'quantity',
        'prize_type',
        'min_score',
    ];

    // 关联问答活动
    public function quizActivity()
    {
        return $this->belongsTo(QuizActivity::class);
    }

    // 关联获奖记录
    public function winners()
    {
        return $this->hasMany(QuizPrizeWinner::class, 'prize_id');
    }

    // 检查奖品是否还有库存
    public function hasStock()
    {
        return $this->quantity > $this->winners()->count();
    }

    // 获取剩余数量
    public function remainingQuantity()
    {
        return $this->quantity - $this->winners()->count();
    }
}
