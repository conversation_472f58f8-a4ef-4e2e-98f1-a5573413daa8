# 心理健康平台数据库设计文档

## 数据库概述

### 基本信息
- **数据库名称**: `psy_oneself_icu`
- **数据库版本**: MySQL 5.7.44
- **字符集**: utf8mb4
- **排序规则**: utf8mb4_unicode_ci
- **表总数**: 68个
- **导出时间**: 2025-07-25 16:27:44

### 设计特点
- 采用Laravel框架的数据库迁移管理
- 支持多语言字符集（utf8mb4）
- 完整的外键约束和索引优化
- 模块化的表结构设计
- 支持软删除和时间戳记录

---

## 数据库架构图

```mermaid
graph TB
    subgraph "用户管理模块"
        Users[users 用户表]
        AdminUsers[admin_users 管理员表]
        SmsCode[sms_codes 短信验证码]
    end
    
    subgraph "权限管理模块"
        AdminRoles[admin_roles 角色表]
        AdminPermissions[admin_permissions 权限表]
        AdminMenu[admin_menu 菜单表]
        AdminRoleUsers[admin_role_users 用户角色关联]
        AdminRolePermissions[admin_role_permissions 角色权限关联]
    end
    
    subgraph "内容管理模块"
        Articles[articles 文章表]
        ArticleCategories[article_categories 文章分类]
        ArticleTags[article_tags 文章标签]
        Videos[videos 视频表]
        VideoCategories[video_categories 视频分类]
        News[news 新闻表]
        NewsCategories[news_categories 新闻分类]
    end
    
    subgraph "咨询管理模块"
        Counselors[counselors 咨询师表]
        CounselorSchedules[counselor_schedules 咨询师排班]
        ConsultationAppointments[consultation_appointments 咨询预约]
        Messages[messages 消息表]
        Consultations[consultations 咨询记录]
    end
    
    subgraph "评估测试模块"
        AssessmentQuestionnaires[assessment_questionnaires 问卷表]
        AssessmentQuestions[assessment_questions 问题表]
        AssessmentOptions[assessment_options 选项表]
        AssessmentResponses[assessment_responses 答卷表]
        AssessmentAnswers[assessment_answers 答案表]
        AssessmentAnalyses[assessment_analyses 分析结果]
    end
    
    subgraph "AI服务模块"
        AiConsultationSettings[ai_consultation_settings AI咨询设置]
        AiConsultationRecords[ai_consultation_records AI咨询记录]
        AssessmentAiSettings[assessment_ai_settings 评估AI设置]
    end
    
    subgraph "课程管理模块"
        CourseLessons[course_lessons 在线课程]
        CourseLessonCategories[course_lesson_categories 课程分类]
        OfflineCourses[offline_courses 线下课程]
        CourseRegistrations[course_registrations 课程报名]
    end
    
    subgraph "活动管理模块"
        QuizActivities[quiz_activities 答题活动]
        QuizQuestions[quiz_questions 答题问题]
        QuizPrizes[quiz_prizes 奖品表]
        QuizAttempts[quiz_attempts 答题记录]
        LiveShows[live_shows 直播表]
    end
    
    subgraph "系统管理模块"
        SystemSettings[system_settings 系统设置]
        Hotlines[hotlines 热线表]
        ContentIndices[content_indices 内容索引]
        Migrations[migrations 迁移记录]
    end
    
    Users --> ConsultationAppointments
    Users --> AssessmentResponses
    Users --> QuizAttempts
    Users --> Messages
    Counselors --> ConsultationAppointments
    Counselors --> Messages
    AssessmentQuestionnaires --> AssessmentQuestions
    AssessmentQuestions --> AssessmentOptions
    AssessmentResponses --> AssessmentAnswers
    AssessmentResponses --> AssessmentAnalyses
    Articles --> ArticleCategories
    Videos --> VideoCategories
    QuizActivities --> QuizQuestions
    QuizActivities --> QuizPrizes
```

---

## 表结构详细说明

### 1. 用户管理模块

#### 1.1 users - 用户表
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint(20) unsigned | - | NO | AUTO_INCREMENT | 主键ID |
| name | varchar | 191 | NO | - | 用户姓名 |
| email | varchar | 191 | NO | - | 邮箱地址 |
| phone | varchar | 20 | YES | NULL | 手机号码 |
| phone_verified | tinyint(1) | - | NO | 0 | 手机号是否验证 |
| avatar | varchar | 191 | YES | NULL | 头像URL |
| email_verified_at | timestamp | - | YES | NULL | 邮箱验证时间 |
| password | varchar | 191 | NO | - | 密码哈希 |
| remember_token | varchar | 100 | YES | NULL | 记住登录令牌 |
| last_login_at | timestamp | - | YES | NULL | 最后登录时间 |
| last_login_ip | varchar | 45 | YES | NULL | 最后登录IP |
| is_contact | tinyint(1) | - | NO | 0 | 是否是联络人 |
| enterprise | varchar | 255 | YES | NULL | 单位名称 |
| created_at | timestamp | - | YES | NULL | 创建时间 |
| updated_at | timestamp | - | YES | NULL | 更新时间 |

**索引:**
- PRIMARY KEY (`id`)
- UNIQUE KEY `users_email_unique` (`email`)
- KEY `users_phone_index` (`phone`)

#### 1.2 admin_users - 管理员用户表
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint(20) unsigned | - | NO | AUTO_INCREMENT | 主键ID |
| username | varchar | 120 | NO | - | 用户名 |
| password | varchar | 80 | NO | - | 密码哈希 |
| name | varchar | 191 | NO | - | 显示名称 |
| avatar | varchar | 191 | YES | NULL | 头像URL |
| remember_token | varchar | 100 | YES | NULL | 记住登录令牌 |
| created_at | timestamp | - | YES | NULL | 创建时间 |
| updated_at | timestamp | - | YES | NULL | 更新时间 |

**索引:**
- PRIMARY KEY (`id`)
- UNIQUE KEY `admin_users_username_unique` (`username`)

#### 1.3 sms_codes - 短信验证码表
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint(20) unsigned | - | NO | AUTO_INCREMENT | 主键ID |
| phone | varchar | 20 | NO | - | 手机号码 |
| code | varchar | 10 | NO | - | 验证码 |
| type | varchar | 20 | NO | login | 验证码类型 |
| expires_at | timestamp | - | NO | - | 过期时间 |
| used | tinyint(1) | - | NO | 0 | 是否已使用 |
| created_at | timestamp | - | YES | NULL | 创建时间 |
| updated_at | timestamp | - | YES | NULL | 更新时间 |

**索引:**
- PRIMARY KEY (`id`)
- KEY `sms_codes_phone_index` (`phone`)

### 2. 权限管理模块

#### 2.1 admin_roles - 管理员角色表
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint(20) unsigned | - | NO | AUTO_INCREMENT | 主键ID |
| name | varchar | 50 | NO | - | 角色名称 |
| slug | varchar | 50 | NO | - | 角色标识 |
| created_at | timestamp | - | YES | NULL | 创建时间 |
| updated_at | timestamp | - | YES | NULL | 更新时间 |

**索引:**
- PRIMARY KEY (`id`)
- UNIQUE KEY `admin_roles_slug_unique` (`slug`)

#### 2.2 admin_permissions - 管理员权限表
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint(20) unsigned | - | NO | AUTO_INCREMENT | 主键ID |
| name | varchar | 50 | NO | - | 权限名称 |
| slug | varchar | 50 | NO | - | 权限标识 |
| http_method | varchar | 191 | YES | NULL | HTTP方法 |
| http_path | text | - | YES | NULL | HTTP路径 |
| order | int(11) | - | NO | 0 | 排序 |
| parent_id | bigint(20) | - | NO | 0 | 父级权限ID |
| created_at | timestamp | - | YES | NULL | 创建时间 |
| updated_at | timestamp | - | YES | NULL | 更新时间 |

**索引:**
- PRIMARY KEY (`id`)
- UNIQUE KEY `admin_permissions_slug_unique` (`slug`)

#### 2.3 admin_menu - 管理员菜单表
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint(20) unsigned | - | NO | AUTO_INCREMENT | 主键ID |
| parent_id | bigint(20) | - | NO | 0 | 父级菜单ID |
| order | int(11) | - | NO | 0 | 排序 |
| title | varchar | 50 | NO | - | 菜单标题 |
| icon | varchar | 50 | YES | NULL | 菜单图标 |
| uri | varchar | 50 | YES | NULL | 菜单URI |
| extension | varchar | 50 | NO | '' | 扩展标识 |
| show | tinyint(4) | - | NO | 1 | 是否显示 |
| created_at | timestamp | - | YES | NULL | 创建时间 |
| updated_at | timestamp | - | YES | NULL | 更新时间 |

**索引:**
- PRIMARY KEY (`id`)

### 3. 内容管理模块

#### 3.1 articles - 文章表
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint(20) unsigned | - | NO | AUTO_INCREMENT | 主键ID |
| title | varchar | 191 | NO | - | 文章标题 |
| content | longtext | - | NO | - | 文章内容 |
| summary | varchar | 500 | YES | NULL | 文章摘要 |
| image | varchar | 191 | YES | NULL | 封面图片 |
| category_id | bigint(20) unsigned | - | NO | - | 分类ID |
| author_id | bigint(20) unsigned | - | YES | NULL | 作者ID |
| is_recommended | tinyint(1) | - | NO | 0 | 是否推荐 |
| views | int(11) | - | NO | 0 | 浏览次数 |
| status | enum | - | NO | draft | 状态 |
| publish_time | timestamp | - | YES | NULL | 发布时间 |
| source | varchar | 100 | YES | NULL | 文章来源 |
| created_at | timestamp | - | YES | NULL | 创建时间 |
| updated_at | timestamp | - | YES | NULL | 更新时间 |

**状态枚举值:** draft(草稿), pending(待审核), published(已发布), rejected(已拒绝)

**索引:**
- PRIMARY KEY (`id`)
- KEY `articles_category_id_foreign` (`category_id`)
- KEY `articles_author_id_foreign` (`author_id`)
- KEY `articles_status_publish_time_index` (`status`, `publish_time`)

**外键约束:**
- FOREIGN KEY (`category_id`) REFERENCES `article_categories` (`id`) ON DELETE CASCADE
- FOREIGN KEY (`author_id`) REFERENCES `users` (`id`) ON DELETE SET NULL

#### 3.2 article_categories - 文章分类表
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint(20) unsigned | - | NO | AUTO_INCREMENT | 主键ID |
| name | varchar | 50 | NO | - | 分类名称 |
| description | varchar | 255 | YES | NULL | 分类描述 |
| sort_order | int(11) | - | NO | 0 | 排序顺序 |
| is_active | tinyint(1) | - | NO | 1 | 是否启用 |
| created_at | timestamp | - | YES | NULL | 创建时间 |
| updated_at | timestamp | - | YES | NULL | 更新时间 |

**索引:**
- PRIMARY KEY (`id`)

#### 3.3 videos - 视频表
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint(20) unsigned | - | NO | AUTO_INCREMENT | 主键ID |
| title | varchar | 191 | NO | - | 视频标题 |
| description | text | - | YES | NULL | 视频简短描述 |
| content | longtext | - | YES | NULL | 视频详细内容 |
| image | varchar | 191 | YES | NULL | 视频封面图片 |
| video_url | varchar | 191 | YES | NULL | 视频URL |
| video_duration | varchar | 20 | YES | NULL | 视频时长 |
| category_id | int(10) unsigned | - | YES | NULL | 分类ID |
| author_id | int(10) unsigned | - | YES | NULL | 作者ID |
| views | int(10) unsigned | - | NO | 0 | 观看次数 |
| is_recommended | tinyint(1) | - | NO | 0 | 是否推荐 |
| status | tinyint(4) | - | NO | 0 | 状态 |
| publish_time | timestamp | - | YES | NULL | 发布时间 |
| created_at | timestamp | - | YES | NULL | 创建时间 |
| updated_at | timestamp | - | YES | NULL | 更新时间 |

**状态值:** 0=草稿，1=待审核，2=已发布，3=已拒绝

**索引:**
- PRIMARY KEY (`id`)
- KEY `videos_category_id_foreign` (`category_id`)

### 4. 咨询管理模块

#### 4.1 counselors - 咨询师表
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint(20) unsigned | - | NO | AUTO_INCREMENT | 主键ID |
| name | varchar | 100 | NO | - | 咨询师姓名 |
| email | varchar | 191 | NO | - | 邮箱地址 |
| phone | varchar | 20 | YES | NULL | 手机号码 |
| password | varchar | 191 | YES | NULL | 登录密码 |
| avatar | varchar | 191 | YES | NULL | 头像URL |
| bio | text | - | YES | NULL | 个人简介 |
| specialties | text | - | YES | NULL | 专业领域 |
| qualifications | text | - | YES | NULL | 资质证书 |
| experience_years | int(11) | - | NO | 0 | 从业年限 |
| hourly_rate | decimal(8,2) | - | NO | 0.00 | 小时费率 |
| is_active | tinyint(1) | - | NO | 1 | 是否启用 |
| is_verified | tinyint(1) | - | NO | 0 | 是否认证 |
| created_at | timestamp | - | YES | NULL | 创建时间 |
| updated_at | timestamp | - | YES | NULL | 更新时间 |

**索引:**
- PRIMARY KEY (`id`)
- UNIQUE KEY `counselors_email_unique` (`email`)

#### 4.2 consultation_appointments - 咨询预约表
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint(20) unsigned | - | NO | AUTO_INCREMENT | 主键ID |
| user_id | bigint(20) unsigned | - | NO | - | 用户ID |
| counselor_id | bigint(20) unsigned | - | NO | - | 咨询师ID |
| appointment_date | date | - | NO | - | 预约日期 |
| appointment_time | time | - | NO | - | 预约时间 |
| duration | int(11) | - | NO | 60 | 咨询时长(分钟) |
| consultation_type | varchar | 50 | NO | - | 咨询类型 |
| description | text | - | YES | NULL | 问题描述 |
| status | varchar | 20 | NO | pending | 预约状态 |
| confirmed_at | timestamp | - | YES | NULL | 确认时间 |
| started_at | timestamp | - | YES | NULL | 开始时间 |
| completed_at | timestamp | - | YES | NULL | 完成时间 |
| cancelled_at | timestamp | - | YES | NULL | 取消时间 |
| cancellation_reason | text | - | YES | NULL | 取消原因 |
| created_at | timestamp | - | YES | NULL | 创建时间 |
| updated_at | timestamp | - | YES | NULL | 更新时间 |

**状态值:** pending(待确认), confirmed(已确认), started(进行中), completed(已完成), cancelled(已取消)

**索引:**
- PRIMARY KEY (`id`)
- KEY `consultation_appointments_user_id_foreign` (`user_id`)
- KEY `consultation_appointments_counselor_id_foreign` (`counselor_id`)

**外键约束:**
- FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
- FOREIGN KEY (`counselor_id`) REFERENCES `counselors` (`id`) ON DELETE CASCADE

#### 4.3 messages - 消息表
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint(20) unsigned | - | NO | AUTO_INCREMENT | 主键ID |
| user_id | bigint(20) unsigned | - | NO | - | 用户ID |
| counselor_id | bigint(20) unsigned | - | NO | - | 咨询师ID |
| appointment_id | bigint(20) unsigned | - | YES | NULL | 预约ID |
| sender_type | varchar | 191 | NO | - | 发送者类型 |
| content | text | - | NO | - | 消息内容 |
| is_read | tinyint(1) | - | NO | 0 | 是否已读 |
| read_at | timestamp | - | YES | NULL | 阅读时间 |
| attachment | varchar | 191 | YES | NULL | 附件 |
| created_at | timestamp | - | YES | NULL | 创建时间 |
| updated_at | timestamp | - | YES | NULL | 更新时间 |

**发送者类型:** user(用户), counselor(咨询师)

**索引:**
- PRIMARY KEY (`id`)
- KEY `messages_user_id_counselor_id_index` (`user_id`, `counselor_id`)
- KEY `messages_appointment_id_index` (`appointment_id`)
- KEY `messages_sender_type_is_read_index` (`sender_type`, `is_read`)

### 5. 评估测试模块

#### 5.1 assessment_questionnaires - 评估问卷表
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint(20) unsigned | - | NO | AUTO_INCREMENT | 主键ID |
| title | varchar | 191 | NO | - | 问卷标题 |
| description | text | - | YES | NULL | 问卷描述 |
| domain | varchar | 191 | NO | - | 测评领域 |
| question_count | int(11) | - | NO | 0 | 题目数量 |
| est_duration | int(11) | - | NO | 0 | 预计时长(分钟) |
| cover_image | varchar | 191 | YES | NULL | 封面图片URL |
| is_active | tinyint(1) | - | NO | 1 | 是否激活 |
| created_at | timestamp | - | YES | NULL | 创建时间 |
| updated_at | timestamp | - | YES | NULL | 更新时间 |

**索引:**
- PRIMARY KEY (`id`)

#### 5.2 assessment_questions - 评估问题表
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint(20) unsigned | - | NO | AUTO_INCREMENT | 主键ID |
| questionnaire_id | bigint(20) unsigned | - | NO | - | 所属问卷ID |
| type | enum | - | NO | - | 题目类型 |
| content | text | - | NO | - | 题干内容 |
| sort_order | int(11) | - | NO | 0 | 排序 |
| created_at | timestamp | - | YES | NULL | 创建时间 |
| updated_at | timestamp | - | YES | NULL | 更新时间 |

**题目类型:** single(单选), multiple(多选), scale(量表)

**索引:**
- PRIMARY KEY (`id`)
- KEY `assessment_questions_questionnaire_id_foreign` (`questionnaire_id`)

**外键约束:**
- FOREIGN KEY (`questionnaire_id`) REFERENCES `assessment_questionnaires` (`id`) ON DELETE CASCADE

#### 5.3 assessment_responses - 评估答卷表
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint(20) unsigned | - | NO | AUTO_INCREMENT | 主键ID |
| user_id | bigint(20) unsigned | - | NO | - | 用户ID |
| questionnaire_id | bigint(20) unsigned | - | NO | - | 问卷ID |
| submitted_at | timestamp | - | YES | NULL | 提交时间 |
| total_score | int(11) | - | NO | 0 | 总分 |
| created_at | timestamp | - | YES | NULL | 创建时间 |
| updated_at | timestamp | - | YES | NULL | 更新时间 |

**索引:**
- PRIMARY KEY (`id`)
- KEY `assessment_responses_user_id_foreign` (`user_id`)
- KEY `assessment_responses_questionnaire_id_foreign` (`questionnaire_id`)

**外键约束:**
- FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
- FOREIGN KEY (`questionnaire_id`) REFERENCES `assessment_questionnaires` (`id`) ON DELETE CASCADE

#### 5.4 assessment_analyses - 评估分析结果表
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint(20) unsigned | - | NO | AUTO_INCREMENT | 主键ID |
| response_id | bigint(20) unsigned | - | NO | - | 所属答卷ID |
| overall_score | int(11) | - | NO | 0 | 总分 |
| level_name | varchar | 191 | YES | NULL | 等级名称 |
| description | text | - | YES | NULL | 结果描述 |
| suggestions | text | - | YES | NULL | 建议 |
| detailed_analysis | text | - | YES | NULL | 详细分析 |
| ai_analysis | longtext | - | YES | NULL | AI分析结果 |
| detail_json | json | - | NO | - | 分域得分JSON |
| created_at | timestamp | - | YES | NULL | 创建时间 |
| updated_at | timestamp | - | YES | NULL | 更新时间 |

**索引:**
- PRIMARY KEY (`id`)
- KEY `assessment_analyses_response_id_foreign` (`response_id`)

**外键约束:**
- FOREIGN KEY (`response_id`) REFERENCES `assessment_responses` (`id`) ON DELETE CASCADE

### 6. AI服务模块

#### 6.1 ai_consultation_settings - AI咨询设置表
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint(20) unsigned | - | NO | AUTO_INCREMENT | 主键ID |
| provider | varchar | 50 | NO | deepseek | AI提供商 |
| api_key | varchar | 191 | YES | NULL | API密钥 |
| api_url | varchar | 191 | YES | NULL | API地址 |
| model | varchar | 100 | YES | NULL | 模型名称 |
| max_tokens | int(11) | - | NO | 2000 | 最大token数 |
| temperature | double(8,2) | - | NO | 0.70 | 温度参数 |
| system_prompt | text | - | YES | NULL | 系统提示词 |
| is_active | tinyint(1) | - | NO | 1 | 是否启用 |
| created_at | timestamp | - | YES | NULL | 创建时间 |
| updated_at | timestamp | - | YES | NULL | 更新时间 |

**索引:**
- PRIMARY KEY (`id`)

#### 6.2 ai_consultation_records - AI咨询记录表
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint(20) unsigned | - | NO | AUTO_INCREMENT | 主键ID |
| user_id | bigint(20) unsigned | - | YES | NULL | 用户ID |
| appointment_id | bigint(20) unsigned | - | YES | NULL | 预约ID |
| user_query | text | - | NO | - | 用户问题 |
| ai_response | text | - | NO | - | AI回复 |
| recommended_resources | text | - | YES | NULL | 推荐资源JSON |
| created_at | timestamp | - | YES | NULL | 创建时间 |
| updated_at | timestamp | - | YES | NULL | 更新时间 |

**索引:**
- PRIMARY KEY (`id`)
- KEY `ai_consultation_records_user_id_foreign` (`user_id`)
- KEY `ai_consultation_records_appointment_id_foreign` (`appointment_id`)

**外键约束:**
- FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
- FOREIGN KEY (`appointment_id`) REFERENCES `consultation_appointments` (`id`) ON DELETE SET NULL

### 7. 课程管理模块

#### 7.1 course_lessons - 在线课程表
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint(20) unsigned | - | NO | AUTO_INCREMENT | 主键ID |
| title | varchar | 191 | NO | - | 课程标题 |
| description | text | - | YES | NULL | 课程描述 |
| content | longtext | - | YES | NULL | 课程内容 |
| cover_image | varchar | 191 | YES | NULL | 封面图片 |
| video_url | varchar | 191 | YES | NULL | 视频URL |
| duration | int(11) | - | NO | 0 | 课程时长(分钟) |
| category_id | bigint(20) unsigned | - | YES | NULL | 分类ID |
| instructor_id | bigint(20) unsigned | - | YES | NULL | 讲师ID |
| difficulty_level | tinyint(4) | - | NO | 1 | 难度等级 |
| is_free | tinyint(1) | - | NO | 1 | 是否免费 |
| price | decimal(8,2) | - | NO | 0.00 | 课程价格 |
| is_published | tinyint(1) | - | NO | 0 | 是否发布 |
| sort_order | int(11) | - | NO | 0 | 排序 |
| view_count | int(11) | - | NO | 0 | 观看次数 |
| created_at | timestamp | - | YES | NULL | 创建时间 |
| updated_at | timestamp | - | YES | NULL | 更新时间 |

**索引:**
- PRIMARY KEY (`id`)
- KEY `course_lessons_category_id_foreign` (`category_id`)

#### 7.2 offline_courses - 线下课程表
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint(20) unsigned | - | NO | AUTO_INCREMENT | 主键ID |
| title | varchar | 100 | NO | - | 课程标题 |
| image | varchar | 191 | YES | NULL | 课程图片 |
| lecturer_id | bigint(20) | - | YES | NULL | 讲师ID |
| description | text | - | YES | NULL | 课程描述 |
| outline | text | - | YES | NULL | 课程大纲 |
| location | varchar | 200 | YES | NULL | 上课地点 |
| start_time | datetime | - | NO | - | 开始时间 |
| end_time | datetime | - | NO | - | 结束时间 |
| registration_deadline | datetime | - | NO | - | 报名截止时间 |
| max_participants | int(11) | - | NO | 30 | 最大参与人数 |
| current_participants | int(11) | - | NO | 0 | 当前参与人数 |
| price | decimal(10,2) | - | NO | 0.00 | 课程价格 |
| status | tinyint(4) | - | NO | 1 | 状态 |
| created_at | timestamp | - | YES | NULL | 创建时间 |
| updated_at | timestamp | - | YES | NULL | 更新时间 |

**状态值:** 0=待发布，1=已发布，2=已结束

**索引:**
- PRIMARY KEY (`id`)

### 8. 活动管理模块

#### 8.1 quiz_activities - 答题活动表
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint(20) unsigned | - | NO | AUTO_INCREMENT | 主键ID |
| title | varchar | 100 | NO | - | 活动标题 |
| description | text | - | YES | NULL | 活动描述 |
| cover_image | varchar | 191 | YES | NULL | 封面图片 |
| start_time | datetime | - | NO | - | 开始时间 |
| end_time | datetime | - | NO | - | 结束时间 |
| pass_score | int(11) | - | NO | 60 | 及格分数 |
| max_attempts | int(11) | - | NO | 1 | 最大尝试次数 |
| is_active | tinyint(1) | - | NO | 1 | 是否启用 |
| show_answers_after_submit | tinyint(1) | - | NO | 0 | 提交后显示答案 |
| created_at | timestamp | - | YES | NULL | 创建时间 |
| updated_at | timestamp | - | YES | NULL | 更新时间 |
| deleted_at | timestamp | - | YES | NULL | 删除时间 |

**索引:**
- PRIMARY KEY (`id`)

#### 8.2 quiz_attempts - 答题记录表
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint(20) unsigned | - | NO | AUTO_INCREMENT | 主键ID |
| quiz_activity_id | bigint(20) unsigned | - | NO | - | 活动ID |
| user_id | bigint(20) unsigned | - | NO | - | 用户ID |
| score | int(11) | - | NO | 0 | 得分 |
| total_points | int(11) | - | NO | 0 | 总分 |
| correct_count | int(11) | - | NO | 0 | 正确题数 |
| total_questions | int(11) | - | NO | 0 | 总题数 |
| has_won_prize | tinyint(1) | - | NO | 0 | 是否中奖 |
| is_completed | tinyint(1) | - | NO | 0 | 是否完成 |
| completed_at | datetime | - | YES | NULL | 完成时间 |
| created_at | timestamp | - | YES | NULL | 创建时间 |
| updated_at | timestamp | - | YES | NULL | 更新时间 |

**索引:**
- PRIMARY KEY (`id`)
- KEY `quiz_attempts_quiz_activity_id_foreign` (`quiz_activity_id`)
- KEY `quiz_attempts_user_id_foreign` (`user_id`)

**外键约束:**
- FOREIGN KEY (`quiz_activity_id`) REFERENCES `quiz_activities` (`id`) ON DELETE CASCADE
- FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE

#### 8.3 live_shows - 直播表
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint(20) unsigned | - | NO | AUTO_INCREMENT | 主键ID |
| title | varchar | 191 | NO | - | 直播标题 |
| description | text | - | YES | NULL | 直播描述 |
| cover_image | varchar | 500 | YES | NULL | 封面图片 |
| live_url | varchar | 1000 | YES | NULL | 直播链接 |
| qr_code_image | varchar | 500 | YES | NULL | 预约二维码 |
| counselor_id | bigint(20) unsigned | - | YES | NULL | 主播咨询师ID |
| scheduled_at | timestamp | - | YES | NULL | 预定直播时间 |
| ended_at | datetime | - | YES | NULL | 结束时间 |
| status | enum | - | NO | upcoming | 状态 |
| is_featured | tinyint(1) | - | NO | 0 | 是否首页推荐 |
| is_active | tinyint(1) | - | NO | 1 | 是否启用 |
| sort_order | int(11) | - | NO | 0 | 排序权重 |
| notice | text | - | YES | NULL | 直播公告 |
| created_at | timestamp | - | YES | NULL | 创建时间 |
| updated_at | timestamp | - | YES | NULL | 更新时间 |

**状态枚举值:** upcoming(即将开始), live(直播中), ended(已结束)

**索引:**
- PRIMARY KEY (`id`)
- KEY `live_shows_counselor_id_foreign` (`counselor_id`)

### 9. 系统管理模块

#### 9.1 system_settings - 系统设置表
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint(20) unsigned | - | NO | AUTO_INCREMENT | 主键ID |
| key | varchar | 191 | NO | - | 配置键 |
| value | text | - | YES | NULL | 配置值 |
| label | varchar | 255 | YES | NULL | 配置项说明 |
| created_at | timestamp | - | YES | NULL | 创建时间 |
| updated_at | timestamp | - | YES | NULL | 更新时间 |

**索引:**
- PRIMARY KEY (`id`)
- UNIQUE KEY `uniq_system_settings_key` (`key`)

#### 9.2 hotlines - 热线表
| 字段名 | 类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | bigint(20) unsigned | - | NO | AUTO_INCREMENT | 主键ID |
| name | varchar | 100 | NO | - | 热线名称 |
| phone | varchar | 20 | NO | - | 热线电话 |
| description | text | - | YES | NULL | 热线描述 |
| service_hours | varchar | 100 | YES | NULL | 服务时间 |
| is_active | tinyint(1) | - | NO | 1 | 是否启用 |
| sort_order | int(11) | - | NO | 0 | 排序 |
| created_at | timestamp | - | YES | NULL | 创建时间 |
| updated_at | timestamp | - | YES | NULL | 更新时间 |

**索引:**
- PRIMARY KEY (`id`)

---

## 数据库关系图

### 核心实体关系
```mermaid
erDiagram
    users ||--o{ consultation_appointments : "用户预约"
    counselors ||--o{ consultation_appointments : "咨询师接单"
    users ||--o{ assessment_responses : "用户答题"
    assessment_questionnaires ||--o{ assessment_questions : "问卷包含问题"
    assessment_questions ||--o{ assessment_options : "问题包含选项"
    assessment_responses ||--o{ assessment_answers : "答卷包含答案"
    assessment_responses ||--|| assessment_analyses : "答卷生成分析"
    users ||--o{ messages : "用户发消息"
    counselors ||--o{ messages : "咨询师发消息"
    users ||--o{ quiz_attempts : "用户参与答题"
    quiz_activities ||--o{ quiz_attempts : "活动包含记录"
    articles ||--|| article_categories : "文章属于分类"
    videos ||--|| video_categories : "视频属于分类"
    news ||--|| news_categories : "新闻属于分类"
```

---

## 索引优化策略

### 1. 主要查询索引
- **用户相关查询**: `users.email`, `users.phone`
- **时间范围查询**: `created_at`, `updated_at`, `scheduled_at`
- **状态筛选**: `status`, `is_active`, `is_published`
- **关联查询**: 所有外键字段都建立了索引

### 2. 复合索引
- `messages(user_id, counselor_id)` - 消息会话查询
- `messages(sender_type, is_read)` - 未读消息统计
- `articles(status, publish_time)` - 已发布文章按时间排序
- `assessment_answers(response_id, question_id)` - 答卷答案查询

### 3. 唯一索引
- 用户邮箱、管理员用户名等关键字段
- 多对多关联表的组合唯一索引

---

## 数据库设计特色

### 1. 模块化设计
- 按功能模块划分表结构
- 清晰的命名规范
- 完整的外键约束关系

### 2. 扩展性设计
- JSON字段存储灵活数据
- 预留扩展字段
- 软删除支持

### 3. 性能优化
- 合理的索引设计
- 适当的字段类型选择
- 查询优化考虑

### 4. 数据完整性
- 外键约束保证数据一致性
- 枚举类型限制数据范围
- 非空约束保证数据质量

---

## 开发团队

- **数据库设计**: 刘博涛
- **前端对接**: 王岩
- **UI优化**: 李永盛
- **系统配置**: 曹健鹏

---

*本文档基于 psy_oneself_icu_2025-07-25_16-27-44_mysql_data_2hAI7.sql 文件生成，涵盖了心理健康平台的完整数据库设计。*
