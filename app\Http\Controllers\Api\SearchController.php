<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\ContentSearchService;
use App\Models\ContentIndex;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;
use Exception;

/**
 * 搜索API控制器
 */
class SearchController extends Controller
{
    protected ContentSearchService $searchService;

    public function __construct(ContentSearchService $searchService)
    {
        $this->searchService = $searchService;
    }

    /**
     * 执行内容搜索
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function search(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'q' => 'required|string|min:1|max:500',
                'type' => 'nullable|string|in:article,video,course_lesson',
                'types' => 'nullable|array',
                'types.*' => 'string|in:article,video,course_lesson',
                'limit' => 'nullable|integer|min:1|max:100',
                'similarity_threshold' => 'nullable|numeric|min:0|max:1',
                'include_metadata' => 'nullable|boolean',
                'page' => 'nullable|integer|min:1',
                'per_page' => 'nullable|integer|min:1|max:50',
            ]);

            $query = $validated['q'];
            $options = [];

            // 处理类型过滤
            if (isset($validated['type'])) {
                $options['types'] = [$validated['type']];
            } elseif (isset($validated['types'])) {
                $options['types'] = $validated['types'];
            }

            // 其他选项
            if (isset($validated['limit'])) {
                $options['limit'] = $validated['limit'];
            }
            if (isset($validated['similarity_threshold'])) {
                $options['similarity_threshold'] = $validated['similarity_threshold'];
            }
            if (isset($validated['include_metadata'])) {
                $options['include_metadata'] = $validated['include_metadata'];
            }

            // 分页搜索
            if (isset($validated['page'])) {
                $page = $validated['page'];
                $perPage = $validated['per_page'] ?? 15;
                
                $results = $this->searchService->searchPaginated($query, $page, $perPage, $options);
                
                return response()->json([
                    'success' => true,
                    'data' => [
                        'query' => $query,
                        'results' => $results->items(),
                        'pagination' => [
                            'current_page' => $results->currentPage(),
                            'per_page' => $results->perPage(),
                            'total' => $results->total(),
                            'last_page' => $results->lastPage(),
                            'has_more' => $results->hasMorePages(),
                        ],
                        'options' => $options,
                    ],
                ]);
            }

            // 普通搜索
            $searchResult = $this->searchService->search($query, $options);

            return response()->json([
                'success' => true,
                'data' => $searchResult,
            ]);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => '请求参数验证失败',
                'errors' => $e->errors(),
            ], 422);

        } catch (Exception $e) {
            \Log::error('搜索API错误', [
                'query' => $request->get('q'),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'success' => false,
                'message' => '搜索服务暂时不可用，请稍后重试',
            ], 500);
        }
    }

    /**
     * 获取相似内容
     *
     * @param Request $request
     * @param string $type 内容类型
     * @param int $id 内容ID
     * @return JsonResponse
     */
    public function similar(Request $request, string $type, int $id): JsonResponse
    {
        try {
            $validated = $request->validate([
                'limit' => 'nullable|integer|min:1|max:50',
                'similarity_threshold' => 'nullable|numeric|min:0|max:1',
                'types' => 'nullable|array',
                'types.*' => 'string|in:article,video,course_lesson',
                'include_metadata' => 'nullable|boolean',
            ]);

            // 验证类型
            if (!in_array($type, ['article', 'video', 'course_lesson'])) {
                return response()->json([
                    'success' => false,
                    'message' => '不支持的内容类型',
                ], 400);
            }

            // 查找内容索引
            $contentIndex = ContentIndex::where('type', $type)
                ->where('indexable_id', $id)
                ->first();

            if (!$contentIndex) {
                return response()->json([
                    'success' => false,
                    'message' => '内容未找到或未被索引',
                ], 404);
            }

            $options = [];
            if (isset($validated['limit'])) {
                $options['limit'] = $validated['limit'];
            }
            if (isset($validated['similarity_threshold'])) {
                $options['similarity_threshold'] = $validated['similarity_threshold'];
            }
            if (isset($validated['types'])) {
                $options['types'] = $validated['types'];
            }
            if (isset($validated['include_metadata'])) {
                $options['include_metadata'] = $validated['include_metadata'];
            }

            $similarContent = $this->searchService->findSimilar($contentIndex, $options);

            return response()->json([
                'success' => true,
                'data' => [
                    'reference' => [
                        'id' => $contentIndex->id,
                        'type' => $contentIndex->type,
                        'title' => $contentIndex->title,
                    ],
                    'similar_content' => $similarContent,
                    'total' => count($similarContent),
                    'options' => $options,
                ],
            ]);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => '请求参数验证失败',
                'errors' => $e->errors(),
            ], 422);

        } catch (Exception $e) {
            \Log::error('相似内容API错误', [
                'type' => $type,
                'id' => $id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => '服务暂时不可用，请稍后重试',
            ], 500);
        }
    }

    /**
     * 获取搜索建议
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function suggestions(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'q' => 'required|string|min:1|max:100',
                'limit' => 'nullable|integer|min:1|max:20',
                'type' => 'nullable|string|in:article,video,course_lesson',
            ]);

            $query = $validated['q'];
            $limit = $validated['limit'] ?? 10;
            $type = $validated['type'] ?? null;

            // 基于标题的快速建议
            $queryBuilder = ContentIndex::query()
                ->select('id', 'type', 'title', 'url')
                ->where('title', 'like', "%{$query}%");

            if ($type) {
                $queryBuilder->where('type', $type);
            }

            $suggestions = $queryBuilder
                ->orderBy('title')
                ->limit($limit)
                ->get()
                ->map(function ($item) {
                    return [
                        'id' => $item->id,
                        'type' => $item->type,
                        'title' => $item->title,
                        'url' => $item->url,
                    ];
                });

            return response()->json([
                'success' => true,
                'data' => [
                    'query' => $query,
                    'suggestions' => $suggestions,
                    'total' => $suggestions->count(),
                ],
            ]);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => '请求参数验证失败',
                'errors' => $e->errors(),
            ], 422);

        } catch (Exception $e) {
            \Log::error('搜索建议API错误', [
                'query' => $request->get('q'),
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => '服务暂时不可用，请稍后重试',
            ], 500);
        }
    }

    /**
     * 获取搜索统计信息
     *
     * @return JsonResponse
     */
    public function stats(): JsonResponse
    {
        try {
            $stats = $this->searchService->getSearchStats();

            return response()->json([
                'success' => true,
                'data' => $stats,
            ]);

        } catch (Exception $e) {
            \Log::error('搜索统计API错误', [
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => '服务暂时不可用，请稍后重试',
            ], 500);
        }
    }

    /**
     * 清除搜索缓存
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function clearCache(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'pattern' => 'nullable|string|max:100',
            ]);

            $pattern = $validated['pattern'] ?? null;
            $this->searchService->clearCache($pattern);

            return response()->json([
                'success' => true,
                'message' => '缓存清除成功',
                'data' => [
                    'pattern' => $pattern,
                    'cleared_at' => now()->toISOString(),
                ],
            ]);

        } catch (ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => '请求参数验证失败',
                'errors' => $e->errors(),
            ], 422);

        } catch (Exception $e) {
            \Log::error('清除缓存API错误', [
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => '服务暂时不可用，请稍后重试',
            ], 500);
        }
    }
}