<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Dcat\Admin\Traits\HasDateTimeFormatter;

class LiveShow extends Model
{
    use HasFactory, HasDateTimeFormatter;
    protected $table = 'live_shows';
    
    const STATUS_UPCOMING = 'upcoming';
    const STATUS_LIVE = 'live';
    const STATUS_ENDED = 'ended';
    
    protected $fillable = [
        'title',
        'description',
        'cover_image',
        'live_url',
        'qr_code_image',
        'counselor_id',
        'scheduled_at',
        'ended_at',
        'status',
        'is_featured',
        'is_active',
        'sort_order',
        'notice'
    ];
    
    protected $casts = [
        'scheduled_at' => 'datetime',
        'ended_at' => 'datetime',
        'is_featured' => 'boolean',
        'is_active' => 'boolean'
    ];
    
    /**
     * 模型启动时的事件监听
     */
    protected static function boot()
    {
        parent::boot();
        
        // 保存前自动更新状态
        static::saving(function ($liveShow) {
            if ($liveShow->shouldAutoMaintainStatus()) {
                $liveShow->status = $liveShow->calculateStatus();
            }
        });
    }
    
    // 关联关系
    public function counselor(): BelongsTo
    {
        return $this->belongsTo(Counselor::class);
    }
    
    // 状态判断方法
    public function isUpcoming(): bool
    {
        return $this->status === self::STATUS_UPCOMING;
    }
    
    public function isLive(): bool
    {
        return $this->status === self::STATUS_LIVE;
    }
    
    public function isEnded(): bool
    {
        return $this->status === self::STATUS_ENDED;
    }
    
    // 获取状态文本
    public function getStatusTextAttribute(): string
    {
        return match($this->status) {
            self::STATUS_UPCOMING => '即将开始',
            self::STATUS_LIVE => '直播中',
            self::STATUS_ENDED => '已结束',
            default => '未知状态',
        };
    }
    
    // 获取状态颜色
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            self::STATUS_UPCOMING => '#ffa502',
            self::STATUS_LIVE => '#ff4757',
            self::STATUS_ENDED => '#747d8c',
            default => '#ddd',
        };
    }
    
    // 查询作用域
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
    
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }
    
    public function scopeUpcoming($query)
    {
        return $query->where('status', self::STATUS_UPCOMING);
    }
    
    public function scopeLive($query)
    {
        return $query->where('status', self::STATUS_LIVE);
    }
    
    // 获取格式化的直播时间
    public function getFormattedScheduledAtAttribute(): string
    {
        if (!$this->scheduled_at) {
            return '待定';
        }
        
        return $this->scheduled_at->format('m月d日 H:i');
    }
    
    // 获取格式化的结束时间
    public function getFormattedEndedAtAttribute(): string
    {
        if (!$this->ended_at) {
            return '手动控制';
        }
        
        return $this->ended_at->format('m月d日 H:i');
    }
    
    /**
     * 自动计算并更新状态
     * 如果设置了结束时间，则根据时间自动判断状态
     * 如果没有设置结束时间，则保持手动维护的状态
     */
    public function updateStatusAutomatically(): void
    {
        // 如果没有设置结束时间，则不自动更新状态
        if (!$this->ended_at) {
            return;
        }
        
        $now = now();
        $newStatus = $this->calculateStatus($now);
        
        // 只有状态发生变化时才更新
        if ($this->status !== $newStatus) {
            $this->update(['status' => $newStatus]);
        }
    }
    
    /**
     * 根据当前时间计算状态
     */
    public function calculateStatus(\DateTime $currentTime = null): string
    {
        $currentTime = $currentTime ?? now();
        
        // 如果没有设置结束时间，返回当前状态（手动维护）
        if (!$this->ended_at) {
            return $this->status;
        }
        
        // 如果没有设置开始时间，无法自动判断
        if (!$this->scheduled_at) {
            return $this->status;
        }
        
        // 根据时间自动判断状态
        if ($currentTime < $this->scheduled_at) {
            return self::STATUS_UPCOMING;
        } elseif ($currentTime >= $this->scheduled_at && $currentTime <= $this->ended_at) {
            return self::STATUS_LIVE;
        } else {
            return self::STATUS_ENDED;
        }
    }
    
    /**
     * 检查是否应该自动维护状态
     */
    public function shouldAutoMaintainStatus(): bool
    {
        return !is_null($this->ended_at) && !is_null($this->scheduled_at);
    }
    
     /**
     * 批量更新状态（用于定时任务）
     */
    public static function updateAllStatuses(): int
    {
        $updatedCount = 0;
        
        // 只处理设置了结束时间且状态不是ended的直播
        $liveShows = self::whereNotNull('ended_at')
            ->whereNotNull('scheduled_at')
            ->where('is_active', true)
            ->where('status', '!=', 'ended') // 排除已结束的直播
            ->get();
        
        foreach ($liveShows as $liveShow) {
            $oldStatus = $liveShow->status;
            $newStatus = $liveShow->calculateStatus();
            
            if ($oldStatus !== $newStatus) {
                $liveShow->update(['status' => $newStatus]);
                $updatedCount++;
                
                // 记录状态变化日志（可选）
                \Log::info("LiveShow #{$liveShow->id} status changed from {$oldStatus} to {$newStatus}");
            }
        }
        
        return $updatedCount;
    }
    
    // 检查是否可以观看
    public function canWatch(): bool
    {
        return $this->is_active && !empty($this->live_url) && $this->status !== self::STATUS_ENDED;
    }
} 