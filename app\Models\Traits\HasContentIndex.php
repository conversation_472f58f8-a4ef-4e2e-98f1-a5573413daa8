<?php

namespace App\Models\Traits;

use App\Models\ContentIndex;
use App\Services\ContentIndexService;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Support\Facades\Log;

/**
 * 内容索引 Trait
 * 
 * 为模型提供内容索引功能，自动同步索引数据
 */
trait HasContentIndex
{
    /**
     * 模型启动时注册观察者
     *
     * @return void
     */
    protected static function bootHasContentIndex(): void
    {
        // 模型保存后同步索引
        static::saved(function ($model) {
            if ($model->shouldBeIndexed()) {
                $model->updateContentIndex();
            } else {
                $model->removeContentIndex();
            }
        });

        // 模型删除后移除索引
        static::deleted(function ($model) {
            $model->removeContentIndex();
        });
    }

    /**
     * 与内容索引的多态关联
     *
     * @return MorphOne
     */
    public function contentIndex(): MorphOne
    {
        return $this->morphOne(ContentIndex::class, 'indexable');
    }

    /**
     * 更新内容索引
     *
     * @return bool
     */
    public function updateContentIndex(): bool
    {
        try {
            $contentIndexService = app(ContentIndexService::class);
            return $contentIndexService->indexContent($this);
        } catch (\Exception $e) {
            Log::error('更新内容索引失败', [
                'model' => get_class($this),
                'id' => $this->getKey(),
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * 移除内容索引
     *
     * @return bool
     */
    public function removeContentIndex(): bool
    {
        try {
            $contentIndexService = app(ContentIndexService::class);
            return $contentIndexService->removeContentIndex($this);
        } catch (\Exception $e) {
            Log::error('移除内容索引失败', [
                'model' => get_class($this),
                'id' => $this->getKey(),
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * 强制重新生成向量
     *
     * @return bool
     */
    public function regenerateVector(): bool
    {
        try {
            // 先移除现有索引
            $this->removeContentIndex();
            
            // 重新创建索引
            return $this->updateContentIndex();
        } catch (\Exception $e) {
            Log::error('重新生成向量失败', [
                'model' => get_class($this),
                'id' => $this->getKey(),
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * 检查是否有内容索引
     *
     * @return bool
     */
    public function hasContentIndex(): bool
    {
        return $this->contentIndex()->exists();
    }

    /**
     * 获取内容索引
     *
     * @return ContentIndex|null
     */
    public function getContentIndex(): ?ContentIndex
    {
        return $this->contentIndex;
    }

    /**
     * 获取向量数据
     *
     * @return array|null
     */
    public function getVectorData(): ?array
    {
        $contentIndex = $this->contentIndex;
        return $contentIndex?->vectorArray;
    }

    /**
     * 检查是否有向量
     *
     * @return bool
     */
    public function hasVector(): bool
    {
        $contentIndex = $this->contentIndex;
        return $contentIndex?->hasVector ?? false;
    }

    /**
     * 获取相关性评分
     *
     * @return float
     */
    public function getRelevanceScore(): float
    {
        $contentIndex = $this->contentIndex;
        return $contentIndex?->relevance_score ?? 0.0;
    }

    /**
     * 更新相关性评分
     *
     * @param float $score
     * @return bool
     */
    public function updateRelevanceScore(float $score): bool
    {
        $contentIndex = $this->contentIndex;
        return $contentIndex?->updateRelevanceScore($score) ?? false;
    }

    /**
     * 增加查看次数
     *
     * @param int $count
     * @return bool
     */
    public function incrementViewCount(int $count = 1): bool
    {
        $contentIndex = $this->contentIndex;
        return $contentIndex?->incrementViewCount($count) ?? false;
    }
} 