@extends('layouts.app')

@section('title', '与 ' . $counselor->name . ' 的对话 - 心理咨询平台')

@section('custom-styles')
<style>
    body {
        background-color: #f8f9fa;
    }
    
    .container {
        max-width: 900px;
        margin: 0 auto;
        padding-bottom: 80px !important; /* 为底部菜单栏预留空间 */
    }
    
    .page-header {
        display: flex;
        align-items: center;
        background: linear-gradient(135deg, #5b7cef 0%, #3c67e3 100%);
        color: white;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        border-radius: 0 0 10px 10px;
        padding: 12px 15px;
        margin-bottom: 15px;
    }
    
    .back-button {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 30px;
        height: 30px;
    }
    
    .back-icon {
        border-top: 2px solid white;
        border-left: 2px solid white;
        width: 12px;
        height: 12px;
        transform: rotate(-45deg);
    }
    
    .page-header h1 {
        font-size: 18px;
        font-weight: 500;
        margin: 0 auto;
        text-align: center;
        flex-grow: 1;
        margin-left: -30px;
        text-shadow: 0 1px 2px rgba(0,0,0,0.1);
    }
    
    .chat-container {
        padding: 10px 15px;
        height: calc(100vh - 180px);
        display: flex;
        flex-direction: column;
    }
    
    .messages-container {
        flex-grow: 1;
        overflow-y: auto;
        padding: 0 5px;
        display: flex;
        flex-direction: column; /* 改为正向排列，保持消息正序显示 */
    }
    
    .message {
        display: flex;
        margin-bottom: 15px;
        max-width: 80%;
    }
    
    .message.sent {
        align-self: flex-end;
        flex-direction: row-reverse;
    }
    
    .message.received {
        align-self: flex-start;
    }
    
    .avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        overflow: hidden;
        margin: 0 10px;
        flex-shrink: 0;
        align-self: flex-end;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        border: 2px solid #fff;
    }
    
    .avatar img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    
    .message-content {
        padding: 12px 15px;
        border-radius: 18px;
        position: relative;
    }
    
    .sent .message-content {
        background-color: #3c67e3;
        color: white;
        border-bottom-right-radius: 5px;
    }
    
    .received .message-content {
        background-color: #e9e9eb;
        color: #333;
        border-bottom-left-radius: 5px;
    }
    
    .message-time {
        font-size: 11px;
        margin-top: 5px;
        text-align: right;
        opacity: 0.7;
    }
    
    .input-container {
        margin-top: 15px;
        position: relative;
        display: flex;
        align-items: center;
        background: white;
        border-radius: 24px;
        padding: 8px 15px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    }
    
    #message-input {
        flex-grow: 1;
        border: none;
        outline: none;
        font-size: 15px;
        padding: 8px 10px;
        max-height: 120px;
        resize: none;
        font-family: inherit;
    }
    
    #send-button {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        background-color: #3c67e3;
        display: flex;
        align-items: center;
        justify-content: center;
        border: none;
        outline: none;
        cursor: pointer;
        transition: background-color 0.2s ease;
        margin-left: 10px;
        flex-shrink: 0;
    }
    
    #send-button:disabled {
        background-color: #ccc;
        cursor: not-allowed;
    }
    
    #send-button svg {
        width: 18px;
        height: 18px;
        fill: white;
    }
    
    .input-container button svg path {
        fill: white;
    }
    
    .consultation-completed-alert {
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 10px;
        padding: 15px;
        margin: 15px 0;
        text-align: center;
        font-size: 15px;
        color: #555;
        display: flex;
        flex-direction: column;
        align-items: center;
        box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    }
    
    .consultation-completed-alert i {
        font-size: 20px;
        color: #28a745;
        margin-right: 5px;
    }
    
    form.disabled .input-container {
        opacity: 0.7;
        background-color: #f2f2f2;
    }
    
    .date-divider {
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 20px 0;
        color: #999;
        font-size: 13px;
    }
    
    .date-divider:before,
    .date-divider:after {
        content: "";
        flex-grow: 1;
        background-color: #eee;
        height: 1px;
        margin: 0 10px;
    }
    
    .appointment-info {
        background-color: #f0f3ff;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 15px;
        display: flex;
        align-items: center;
        color: #333;
        border: 1px solid #e0e7ff;
    }
    
    .appointment-icon {
        margin-right: 12px;
        width: 24px;
        height: 24px;
        color: #3c67e3;
    }
    
    .appointment-details {
        flex-grow: 1;
        font-size: 14px;
    }
    
    .appointment-date {
        font-weight: 600;
        margin-bottom: 4px;
    }
    
    .appointment-type {
        color: #666;
    }
    
    .no-messages {
        text-align: center;
        padding: 30px;
        color: #666;
        border-radius: 12px;
        margin: 50px 0;
    }
    
    .loading-indicator {
        text-align: center;
        padding: 20px;
        color: #999;
    }
    
    .new-message-alert {
        position: fixed;
        bottom: 90px;
        left: 50%;
        transform: translateX(-50%);
        background-color: #3c67e3;
        color: white;
        padding: 10px 20px;
        border-radius: 20px;
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
        font-size: 14px;
        z-index: 100;
        cursor: pointer;
        animation: bounce 1s infinite alternate;
    }
    
    @keyframes bounce {
        from { transform: translateX(-50%) translateY(0); }
        to { transform: translateX(-50%) translateY(-10px); }
    }
</style>
@endsection

@section('content')
<div class="page-header">
    <a href="{{ route('messages.index') }}" class="back-button">
        <div class="back-icon"></div>
    </a>
    <h1>{{ $counselor->name }}</h1>
    <div style="width: 30px;"></div>
</div>

<div class="container">
    <div class="chat-container">
        @if($appointments->isNotEmpty())
            <div class="appointment-info">
                <svg class="appointment-icon" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M19 4h-1V2h-2v2H8V2H6v2H5c-1.11 0-1.99.9-1.99 2L3 20c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 16H5V10h14v10zM9 14H7v-2h2v2zm4 0h-2v-2h2v2zm4 0h-2v-2h2v2zm-8 4H7v-2h2v2zm4 0h-2v-2h2v2zm4 0h-2v-2h2v2z"/>
                </svg>
                <div class="appointment-details">
                    <div class="appointment-date">
                        预约时间：{{ \Carbon\Carbon::parse($appointments->first()->appointment_time)->format('Y-m-d H:i') }}
                    </div>
                    <div class="appointment-type">
                        @if($appointments->first()->consultation_type == 1)
                            咨询方式：文字咨询
                        @elseif($appointments->first()->consultation_type == 2)
                            咨询方式：语音咨询
                        @elseif($appointments->first()->consultation_type == 3)
                            咨询方式：视频咨询
                        @endif
                    </div>
                </div>
            </div>
        @endif
        
        <div class="messages-container" id="messages-container">
            @if($messages->isNotEmpty())
                @php
                    $currentDate = null;
                    // 将消息按时间正序排列（从旧到新）
                    $sortedMessages = $messages->sortBy('created_at');
                @endphp
                
                @foreach($sortedMessages as $message)
                    @php
                        $messageDate = \Carbon\Carbon::parse($message->created_at)->format('Y-m-d');
                        $showDateDivider = $currentDate !== $messageDate;
                        $currentDate = $messageDate;
                    @endphp
                    
                    @if($showDateDivider)
                        <div class="date-divider">
                            @if(\Carbon\Carbon::parse($message->created_at)->isToday())
                                今天
                            @elseif(\Carbon\Carbon::parse($message->created_at)->isYesterday())
                                昨天
                            @else
                                {{ \Carbon\Carbon::parse($message->created_at)->format('Y年m月d日') }}
                            @endif
                        </div>
                    @endif
                    
                    <div class="message {{ $message->isSentByUser() ? 'sent' : 'received' }}">
                        @if($message->isSentByCounselor())
                            <div class="avatar">
                                <img src="{{ $counselor->avatar ? asset('storage/'.$counselor->avatar) : asset('images/default-avatar.jpg') }}" alt="{{ $counselor->name }}">
                            </div>
                        @endif
                        
                        <div class="message-content">
                            {{ $message->content }}
                            <div class="message-time">
                                {{ \Carbon\Carbon::parse($message->created_at)->format('H:i') }}
                            </div>
                        </div>
                        
                        @if($message->isSentByUser())
                            <div class="avatar">
                                <img src="{{ auth()->user()->avatar ? asset('storage/'.auth()->user()->avatar) : asset('images/default-avatar.jpg') }}" alt="{{ auth()->user()->name }}">
                            </div>
                        @endif
                    </div>
                @endforeach
            @else
                <div class="no-messages">
                    <p>开始与{{ $counselor->name }}咨询师的对话吧</p>
                </div>
            @endif
        </div>
        
        @php
            $latestAppointment = $appointments->isNotEmpty() ? $appointments->first() : null;
            $isCompleted = $latestAppointment && $latestAppointment->status == \App\Models\ConsultationAppointment::STATUS_COMPLETED;
        @endphp

        @if($isCompleted)
        <div class="consultation-completed-alert">
            <i class="bi bi-check-circle"></i> 此次咨询已完成，如需继续请创建新预约
            <a href="{{ route('consultation.counselor_detail', $counselor->id) }}" class="btn btn-sm btn-primary mt-2">预约新咨询</a>
        </div>
        @endif

        <form id="message-form" data-counselor-id="{{ $counselor->id }}" data-appointment-id="{{ $latestAppointment ? $latestAppointment->id : '' }}" {{ $isCompleted ? 'class=disabled' : '' }}>
            <div class="input-container">
                <textarea id="message-input" placeholder="{{ $isCompleted ? '此次咨询已完成，无法发送新消息' : '输入消息...' }}" rows="1" maxlength="1000" {{ $isCompleted ? 'disabled' : '' }}></textarea>
                <button id="send-button" type="submit" disabled {{ $isCompleted ? 'style="opacity: 0.5;"' : '' }}>
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                        <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                    </svg>
                </button>
            </div>
        </form>
    </div>
</div>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const messageForm = document.getElementById('message-form');
        const messageInput = document.getElementById('message-input');
        const sendButton = document.getElementById('send-button');
        const messagesContainer = document.getElementById('messages-container');
        const counselorId = messageForm.getAttribute('data-counselor-id');
        const appointmentId = messageForm.getAttribute('data-appointment-id');
        let lastCheckedTime = new Date().toISOString();
        
        // 自动调整文本域高度
        messageInput.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = (this.scrollHeight) + 'px';
            
            // 启用/禁用发送按钮
            sendButton.disabled = !this.value.trim();
        });
        
        // 提交表单
        messageForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // 检查表单是否被禁用（咨询已完成）
            if (messageForm.classList.contains('disabled')) {
                return;
            }
            
            const content = messageInput.value.trim();
            if (!content) return;
            
            // 禁用发送按钮，防止重复发送
            sendButton.disabled = true;
            
            // 创建FormData对象
            const formData = new FormData();
            formData.append('content', content);
            if (appointmentId) {
                formData.append('appointment_id', appointmentId);
            }
            formData.append('_token', '{{ csrf_token() }}');
            
            // 添加临时消息到UI
            const now = new Date();
            const hours = now.getHours().toString().padStart(2, '0');
            const minutes = now.getMinutes().toString().padStart(2, '0');
            const timeString = `${hours}:${minutes}`;
            
            const messageElement = document.createElement('div');
            messageElement.className = 'message sent';
            messageElement.innerHTML = `
                <div class="message-content">
                    ${content}
                    <div class="message-time">${timeString}</div>
                </div>
                <div class="avatar">
                    <img src="{{ auth()->user()->avatar ? asset('storage/'.auth()->user()->avatar) : asset('images/default-avatar.jpg') }}" alt="{{ auth()->user()->name }}">
                </div>
            `;
            
            // 正确添加到容器底部（适配正序显示）
            messagesContainer.appendChild(messageElement);
            // 发送后自动滚动到底部
            scrollToBottom();
            
            // 清空输入框
            messageInput.value = '';
            messageInput.style.height = 'auto';
            
            // 发送消息到服务器
            fetch(`/messages/${counselorId}/send`, {
                method: 'POST',
                body: formData,
                credentials: 'same-origin'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('消息发送成功:', data);
                    
                    // 更新最后消息ID
                    const lastMessageIdKey = 'last_message_id_' + {{ auth()->id() }} + '_' + counselorId;
                    sessionStorage.setItem(lastMessageIdKey, data.message.id);
                    
                    // 将消息添加到正确位置（替换临时消息）
                    messageElement.querySelector('.message-time').innerText = data.formatted_time;
                } else {
                    console.error('消息发送失败:', data);
                    alert('消息发送失败，请重试');
                    // 如果发送失败，移除临时消息
                    messagesContainer.removeChild(messageElement);
                }
                sendButton.disabled = false;
            })
            .catch(error => {
                console.error('发送消息出错:', error);
                alert('发送消息出错，请检查网络连接后重试');
                sendButton.disabled = false;
            });
        });
        
        // 自动聚焦输入框
        messageInput.focus();
        
        // 自动滚动到底部 - 适配正序显示
        scrollToBottom();
        
        // 判断是否接近底部
        function isNearBottom() {
            const threshold = 100; // 距离底部的阈值（像素）
            return (messagesContainer.scrollHeight - messagesContainer.scrollTop - messagesContainer.clientHeight) < threshold;
        }
        
        // 滚动到底部
        function scrollToBottom() {
            messagesContainer.scrollTop = messagesContainer.scrollHeight - messagesContainer.clientHeight;
        }
        
        // 显示新消息提醒
        function showNewMessageAlert() {
            if (!document.getElementById('new-message-alert')) {
                const alert = document.createElement('div');
                alert.id = 'new-message-alert';
                alert.className = 'new-message-alert';
                alert.textContent = '收到新消息，点击查看';
                alert.addEventListener('click', function() {
                    scrollToBottom();
                    this.remove();
                });
                document.body.appendChild(alert);
            }
        }
        
        // 检查新消息
        function checkNewMessages() {
            fetch(`/messages/${counselorId}/check-new`)
                .then(response => response.json())
                .then(data => {
                    if (data.has_new && data.messages.length > 0) {
                        // 创建文档片段，一次性添加所有消息
                        const fragment = document.createDocumentFragment();
                        const wasNearBottom = isNearBottom();
                        
                        data.messages.forEach(message => {
                            const messageHtml = `
                                <div class="message received">
                                    <div class="avatar">
                                        <img src="{{ $counselor->avatar ? asset('storage/'.$counselor->avatar) : asset('images/default-avatar.jpg') }}" alt="{{ $counselor->name }}">
                                    </div>
                                    <div class="message-content">
                                        ${message.content}
                                        <div class="message-time">${message.formatted_time}</div>
                                    </div>
                                </div>
                            `;
                            const messageElement = document.createElement('div');
                            messageElement.innerHTML = messageHtml;
                            fragment.appendChild(messageElement);
                        });
                        
                        // 将所有消息添加到容器
                        messagesContainer.appendChild(fragment);
                        
                        // 如果用户正在查看聊天底部，自动滚动到底部
                        if (wasNearBottom) {
                            scrollToBottom();
                        } else {
                            // 显示新消息提示
                            showNewMessageAlert();
                        }
                    }
                })
                .catch(error => console.error('检查新消息出错:', error));
        }
        
        // 监听滚动事件，隐藏提醒
        messagesContainer.addEventListener('scroll', function() {
            if (isNearBottom()) {
                const alert = document.getElementById('new-message-alert');
                if (alert) alert.remove();
            }
        });
        
        // 页面加载后立即检查一次
        checkNewMessages();
        // 设置更频繁的检测间隔（每 1.5 秒）
        setInterval(checkNewMessages, 1500);
    });
</script>
@endsection
