<?php
namespace App\Admin\Controllers;

use App\Models\AssessmentAnalysis;
use App\Models\AssessmentResponse;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Form;
use Dcat\Admin\Http\Controllers\AdminController;

class AssessmentAnalysisController extends AdminController
{
    protected $title = '心理测评分析';

    protected function grid(): Grid
    {
        return Grid::make(new AssessmentAnalysis(), function (Grid $grid) {
            $grid->column('id', '编号')->sortable();
            $grid->column('response.id', '答卷ID');
            $grid->column('overall_score', '总分');
            $grid->column('created_at', '创建时间');
            $grid->column('updated_at', '更新时间');

            // 列过滤
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id', '编号');
                $filter->equal('response_id', '答卷ID')->select(AssessmentResponse::pluck('id', 'id'));
                $filter->between('created_at', '创建时间')->datetime();
                $filter->between('updated_at', '更新时间')->datetime();
            });

            $grid->disableCreateButton();
            $grid->disableActions();
        });
    }

    protected function form(): Form
    {
        return Form::make(new AssessmentAnalysis(), function (Form $form) {
            $form->display('id');
            $form->display('response.id', '答卷ID');
            $form->display('overall_score', '总分');
            $form->textarea('detail_json', '详细分析');
            $form->display('created_at');
            $form->display('updated_at');

            $form->disableCreateButton();
            $form->disableDeleteButton();
            $form->disableSubmitButton();
        });
    }

    /**
     * 详情视图
     */
    protected function detail($id): Show
    {
        return Show::make($id, new AssessmentAnalysis(), function (Show $show) {
            $show->field('id', '编号');
            $show->field('response.id', '答卷ID');
            $show->field('overall_score', '总分');
            $show->field('detail_json', '详细分析')->json();
            $show->field('created_at', '创建时间');
            $show->field('updated_at', '更新时间');
        });
    }
}
