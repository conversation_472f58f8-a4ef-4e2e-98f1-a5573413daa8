<?php

namespace App\Admin\Controllers;

use App\Models\NewsTag;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class NewsTagController extends AdminController
{
    /**
     * 页面标题
     *
     * @return string
     */
    protected function title()
    {
        return '新闻标签管理';
    }

    /**
     * 列表页面
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new NewsTag(), function (Grid $grid) {
            $grid->column('id', 'ID')->sortable();
            $grid->column('name', '标签名称');
            $grid->column('slug', 'URL别名');
            $grid->column('created_at', '创建时间')->sortable();
            
            // 显示关联的新闻数量
            $grid->column('news_count', '关联新闻数')->display(function () {
                return $this->news()->count();
            });
            
            // 快速搜索
            $grid->quickSearch(['name', 'slug']);
            
            // 快速创建
            $grid->quickCreate(function (Grid\Tools\QuickCreate $create) {
                $create->text('name', '标签名称')->required();
                $create->text('slug', 'URL别名')->required();
            });
            
            // 表格过滤器
            $grid->filter(function (Grid\Filter $filter) {
                $filter->panel();
                $filter->like('name', '标签名称');
                $filter->like('slug', 'URL别名');
            });
        });
    }

    /**
     * 详情页面
     *
     * @param mixed $id
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new NewsTag(), function (Show $show) {
            $show->field('id', 'ID');
            $show->field('name', '标签名称');
            $show->field('slug', 'URL别名');
            $show->field('created_at', '创建时间');
            $show->field('updated_at', '更新时间');
            
            // 显示关联的新闻
            $show->relation('news', '关联的新闻', function ($model) {
                $grid = new Grid(new \App\Models\News());
                $grid->model()->whereHas('tags', function ($query) use ($model) {
                    $query->where('news_tags.id', $model->id);
                });
                
                $grid->column('id', 'ID');
                $grid->column('title', '标题');
                $grid->column('published_at', '发布时间');
                
                $grid->disableCreateButton();
                $grid->disableActions();
                
                return $grid;
            });
        });
    }

    /**
     * 表单页面
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new NewsTag(), function (Form $form) {
            $form->display('id', 'ID');
            
            $form->text('name', '标签名称')
                ->required()
                ->rules('required|max:50|unique:news_tags,name,'.$form->getKey());
                
            $form->text('slug', 'URL别名')
                ->required()
                ->rules('required|max:50|unique:news_tags,slug,'.$form->getKey());
                
            $form->display('created_at', '创建时间');
            $form->display('updated_at', '更新时间');
        });
    }
}
