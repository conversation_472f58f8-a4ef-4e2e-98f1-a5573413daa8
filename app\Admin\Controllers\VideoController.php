<?php

namespace App\Admin\Controllers;

use App\Models\Video;
use App\Models\VideoCategory;
use App\Models\VideoTag;
use App\Models\VideoAuditLog;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Admin;
use Dcat\Admin\Layout\Content;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class VideoController extends AdminController
{
    /**
     * 页面标题
     *
     * @return string
     */
    protected function title()
    {
        return '视频管理';
    }
    
    /**
     * 表格构建
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(Video::with(['category', 'author', 'tags']), function (Grid $grid) {
            
            $grid->column('id', 'ID')->sortable();
            $grid->column('title', '视频标题');
            $grid->column('image', '封面图片')->image('', 50, 50);
            $grid->column('video_url', '视频链接')->display(function($url) {
                // 截取链接，使其不会太长
                $shortUrl = mb_strlen($url) > 30 ? mb_substr($url, 0, 30) . '...' : $url;
                return "<a href='{$url}' target='_blank' title='{$url}'>{$shortUrl}</a>";
            })->sortable();
            $grid->column('video_duration', '视频时长');
            $grid->column('category.name', '分类')->display(function ($name) {
                if (!$name) return '-';
                return "<span class='badge badge-primary'>$name</span>";
            });
            
            // 标签显示
            $grid->column('tags', '标签')->display(function ($tags) {
                if (!$tags) return '-';
                
                return collect($tags)->map(function ($tag) {
                    return "<span class='badge badge-info mr-1'>{$tag['name']}</span>";
                })->implode(' ');
            });
            
            $grid->column('author.name', '作者')->display(function ($name) {
                return $name ?: '-';
            });
            $grid->column('views', '观看次数')->sortable();
            $grid->column('is_recommended', '是否推荐')->switch();
            
            // 状态显示
            $grid->column('status', '状态')->using(Video::getStatusMap())->dot([
                Video::STATUS_DRAFT => 'gray',
                Video::STATUS_PENDING => 'orange',
                Video::STATUS_PUBLISHED => 'green',
                Video::STATUS_REJECTED => 'red',
            ]);
            
            $grid->column('publish_time', '发布时间')->sortable();
            $grid->column('created_at', '创建时间')->sortable();
            
            // 快速搜索
            $grid->quickSearch('title', 'description');
            
            // 筛选器
            $grid->filter(function (Grid\Filter $filter) {
                $filter->panel();
                
                $filter->equal('id', 'ID');
                $filter->like('title', '标题');
                $filter->like('description', '描述');
                $filter->equal('category_id', '分类')->select(
                    VideoCategory::pluck('name', 'id')
                );
                $filter->whereBetween('created_at', function ($query) {
                    $start = $this->input['start'] ?? null;
                    $end = $this->input['end'] ?? null;
                    
                    if ($start && $end) {
                        $query->whereBetween('created_at', [$start, $end]);
                    }
                })->datetime()->width(6, 3);
                
                $filter->equal('is_recommended', '是否推荐')->select([
                    0 => '否',
                    1 => '是',
                ]);
                
                $filter->equal('status', '状态')->select(Video::getStatusMap());
            });
            
            // 操作按钮
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                // 基于权限控制显示/隐藏默认按钮
                if (!Admin::user()->can('video_view')) {
                    $actions->disableView();
                }
                
                if (!Admin::user()->can('video_edit')) {
                    $actions->disableEdit();
                }
                
                if (!Admin::user()->can('video_delete')) {
                    $actions->disableDelete();
                }
                
                // 如果是内容审核角色
                if (Admin::user()->can('video_review')) {
                    // 添加审核按钮 - 只显示待审核的视频
                    if ($actions->row->status == Video::STATUS_PENDING) {
                        $actions->append('<a href="'.admin_url('videos/'.$actions->row->id.'/review').'" class="btn btn-sm btn-warning"><i class="feather icon-check"></i> 审核</a>');
                    }
                }
                
                // 如果是内容编辑角色
                if (Admin::user()->can('video_submit_review')) {
                    // 添加提交审核按钮 - 只显示草稿状态的视频
                    if ($actions->row->status == Video::STATUS_DRAFT) {
                        $actions->append('<a href="javascript:void(0);" class="btn btn-sm btn-success submit-review" data-id="'.$actions->row->id.'"><i class="feather icon-send"></i> 提交审核</a>');
                    }
                }
                
                // 移除某些角色的编辑/删除权限
                if (!Admin::user()->can('video_edit')) {
                    // 非编辑角色不能编辑和删除视频
                    $actions->disableEdit();
                    $actions->disableDelete();
                }
            });
            
            // 移除待审核视频按钮
            $grid->tools(function (Grid\Tools $tools) {
                // 不再添加待审核视频按钮
            });
            
            // 修复JS脚本处理按钮事件
            Admin::script('
            $(function () {
                $(".submit-review").on("click", function () {
                    var id = $(this).data("id");
                    $.ajax({
                        url: "'.admin_url('videos').'/"+id+"/submit-review",
                        type: "POST",
                        data: {_token: Dcat.token},
                        success: function (data) {
                            if (data.status) {
                                Dcat.success(data.message);
                                setTimeout(function(){
                                    Dcat.reload();
                                }, 1500);
                            } else {
                                Dcat.error(data.message);
                            }
                        },
                        error: function (xhr) {
                            Dcat.error("操作失败: " + xhr.statusText);
                        }
                    });
                });
            });
            ');
        });
    }
    
    /**
     * 详情页构建
     *
     * @param mixed $id
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, Video::with(['category', 'author', 'tags', 'auditLogs']), function (Show $show) {
            $show->field('id', 'ID');
            $show->field('title', '视频标题');
            $show->field('description', '视频描述');
            $show->field('content', '视频内容')->unescape()->as(function ($content) {
                return $content ? "<div class='content-preview'>$content</div>" : '-';
            });
            
            $show->field('image', '封面图片')->image();
            
            $show->field('video_url', '视频链接')->link();
            $show->field('video_duration', '视频时长');
            
            $show->field('category.name', '分类')->as(function ($name) {
                return $name ?: '-';
            });
            
            $show->field('tags', '标签')->as(function ($tags) {
                if (!$tags || empty($tags)) {
                    return '-';
                }
                
                return collect($tags)->pluck('name')->implode(', ');
            });
            
            $show->field('author.name', '作者')->as(function ($name) {
                return $name ?: '-';
            });
            
            $show->field('views', '观看次数');
            $show->field('is_recommended', '是否推荐')->as(function ($isRecommended) {
                return $isRecommended ? '是' : '否';
            });
            
            $show->field('status', '状态')->as(function ($status) {
                $statusMap = [
                    0 => '草稿',
                    1 => '待审核',
                    2 => '已发布',
                    3 => '已拒绝',
                ];
                
                return $statusMap[$status] ?? '未知状态';
            });
            
            $show->field('publish_time', '发布时间');
            $show->field('created_at', '创建时间');
            $show->field('updated_at', '更新时间');
            
            // 审核记录
            $show->field('auditLogs', '审核记录')->as(function ($logs) {
                if (!$logs || empty($logs)) {
                    return '暂无审核记录';
                }
                
                $html = '<div class="table-responsive"><table class="table table-bordered">';
                $html .= '<thead><tr><th>审核人</th><th>状态</th><th>意见</th><th>时间</th></tr></thead>';
                $html .= '<tbody>';
                
                foreach ($logs as $log) {
                    $statusMap = [
                        1 => ['待审核', 'orange'],
                        2 => ['通过', 'green'],
                        3 => ['拒绝', 'red'],
                    ];
                    
                    $status = isset($statusMap[$log['status']]) 
                        ? "<span class='badge badge-{$statusMap[$log['status']][1]}'>{$statusMap[$log['status']][0]}</span>" 
                        : '未知状态';
                    
                    $admin = $log['admin'] ? $log['admin']['name'] : '未知';
                    
                    $html .= "<tr>";
                    $html .= "<td>{$admin}</td>";
                    $html .= "<td>{$status}</td>";
                    $html .= "<td>{$log['reason']}</td>";
                    $html .= "<td>{$log['created_at']}</td>";
                    $html .= "</tr>";
                }
                
                $html .= '</tbody></table></div>';
                
                return $html;
            })->unescape();
            
            // 自定义样式
            $show->style('content-preview', <<<CSS
            .content-preview {
                max-height: 300px;
                overflow-y: auto;
                padding: 10px;
                border: 1px solid #eee;
                border-radius: 3px;
            }
            CSS);
        });
    }
    
    /**
     * 表单构建
     *
     * @return Form
     */
    protected function form()
    {
        // 按照文档示例，使用with预加载关联数据
        return Form::make(Video::with('tags'), function (Form $form) {
            $form->display('id', 'ID');
            
            $form->text('title', '视频标题')
                ->required()
                ->rules('required|max:255');
                
            $form->textarea('description', '视频描述')
                ->rows(3)
                ->rules('nullable|max:500');
                
            $form->editor('content', '视频内容')
                ->rules('nullable');
                
            $form->image('image', '封面图片')
                ->autoUpload()
                ->uniqueName()
                ->rules('nullable|image|max:2048')
                ->help('建议尺寸: 16:9, 最大2MB');
                
            $form->url('video_url', '视频链接')
                ->required()
                ->rules('required|url')
                ->help('支持优酷、腾讯视频、哔哩哔哩等平台的分享链接或本地上传视频的链接');
                
            // 添加视频上传按钮，上传后自动填充视频链接字段
            $form->divider('或者上传本地视频');
            
            // 添加一个自定义按钮来处理视频上传
            $form->html('<div class="btn-group pull-left" style="margin-top: 10px;">
                <button type="button" id="upload-video-btn" class="btn btn-primary"><i class="feather icon-upload"></i> 上传视频</button>
            </div>
            <input type="file" id="video-upload-input" style="display:none;" accept=".mp4">
            <div id="video-upload-progress" class="progress" style="display:none; margin-top: 10px;">
                <div class="progress-bar" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
            </div>
            <div id="video-preview-container"></div>');
            
            // 添加脚本实现视频上传与链接字段的联动
            Admin::script(<<<JS
            $(function () {
                // 点击上传按钮时触发文件选择
                $('#upload-video-btn').on('click', function() {
                    $('#video-upload-input').click();
                });
                
                // 当选择文件后开始上传
                $('#video-upload-input').on('change', function() {
                    var file = this.files[0];
                    if (!file) return;
                    
                    // 检查文件类型
                    if (file.type !== 'video/mp4' && !file.name.toLowerCase().endsWith('.mp4')) {
                        Dcat.error('只支持上传MP4格式的视频文件');
                        return;
                    }
                    
                    // 显示进度条
                    $('#video-upload-progress').show();
                    $('.progress-bar').css('width', '0%').attr('aria-valuenow', 0).text('0%');
                    
                    // 创建FormData对象
                    var formData = new FormData();
                    formData.append('file', file);
                    formData.append('_token', $('meta[name="csrf-token"]').attr('content'));
                    
                    // 使用AJAX上传文件
                    $.ajax({
                        url: '/admin/upload/video',
                        type: 'POST',
                        data: formData,
                        processData: false,
                        contentType: false,
                        xhr: function() {
                            var xhr = new window.XMLHttpRequest();
                            xhr.upload.addEventListener("progress", function(evt) {
                                if (evt.lengthComputable) {
                                    var percentComplete = Math.round((evt.loaded / evt.total) * 100);
                                    $('.progress-bar').css('width', percentComplete + '%')
                                        .attr('aria-valuenow', percentComplete)
                                        .text(percentComplete + '%');
                                }
                            }, false);
                            return xhr;
                        },
                        success: function(response) {
                            console.log('Upload success:', response);
                            
                            // 获取视频URL
                            var videoUrl = '';
                            if (response.url) {
                                videoUrl = response.url;
                            } else if (response.location) {
                                videoUrl = response.location;
                            }
                            
                            if (videoUrl) {
                                // 将URL填充到视频链接字段
                                $('input[name="video_url"]').val(videoUrl);
                                
                                // 显示成功消息
                                Dcat.success('视频上传成功，链接已自动填充');
                                
                                // 显示视频预览
                                var previewHtml = '<div class="video-preview mt-2"><video src="' + videoUrl + '" controls style="max-width:100%;max-height:200px;"></video></div>';
                                $('#video-preview-container').html(previewHtml);
                            }
                        },
                        error: function(xhr, status, error) {
                            console.error('Upload error:', error);
                            Dcat.error('视频上传失败: ' + (xhr.responseJSON ? xhr.responseJSON.message : error));
                        },
                        complete: function() {
                            // 重置文件输入框
                            $('#video-upload-input').val('');
                            // 隐藏进度条
                            setTimeout(function() {
                                $('#video-upload-progress').hide();
                            }, 1000);
                        }
                    });
                });
            });
            JS
            );
                
            $form->text('video_duration', '视频时长')
                ->rules('nullable|max:20')
                ->help('格式: MM:SS，例如 05:30');
                
            $form->select('category_id', '分类')
                ->options(VideoCategory::where('is_active', 1)->pluck('name', 'id'))
                ->required()
                ->rules('required|exists:video_categories,id');
                
            $form->multipleSelect('tags', '标签')
                ->options(VideoTag::pluck('name', 'id'))
                ->customFormat(function ($v) {
                    if (is_array($v)) {
                        return array_column($v, 'id');
                    }
                    return $v;
                });
                
            $form->switch('is_recommended', '是否推荐')
                ->default(false);
                
            // 获取当前用户
            $user = Admin::user();
            $isAdmin = $user->isAdministrator();
            
            // 状态显示，不允许编辑
            if ($form->isCreating()) {
                // 新建时隐藏状态字段，默认为草稿
                $form->hidden('status')->default(0);
                $form->display('status_text', '状态')->default('草稿');
            } else {
                // 编辑时显示状态但不允许编辑
                $statusMap = [
                    0 => '草稿',
                    1 => '待审核',
                    2 => '已发布',
                    3 => '已拒绝',
                ];
                
                // 使用隐藏字段保留原值
                $form->hidden('status');
                
                // 显示状态文本
                $form->display('status_text', '状态')->with(function () use ($form, $statusMap) {
                    $status = $form->model()->status;
                    return $statusMap[$status] ?? '未知状态';
                });
            }
            
            // 如果状态为已发布，显示发布时间字段
            if ($form->isEditing() && $form->model()->status == 2) {
                $form->datetime('publish_time', '发布时间')->readOnly();
            }
                
            // 添加作者选择功能
            if ($form->isCreating()) {
                // 新建时使用当前管理员作为作者
                $form->hidden('author_id')->default(Admin::user()->id);
                $form->display('author_name', '作者')->default(Admin::user()->name);
            } else {
                // 编辑模式下显示作者名称
                $form->display('author.name', '作者')->default('未指定');
            }
            
            $form->display('views', '观看次数');
            $form->display('publish_time', '发布时间');
            $form->display('created_at', '创建时间');
            $form->display('updated_at', '更新时间');
            
            // 保存前回调
            $form->saving(function (Form $form) {
                // 如果是新建视频，设置作者ID
                if (!$form->isEditing()) {
                    $form->author_id = Admin::user()->id;
                }
                
                // 如果状态从非发布变为发布，设置发布时间
                if ($form->isEditing()) {
                    $video = Video::find($form->getKey());
                    
                    if ($video && $video->status != Video::STATUS_PUBLISHED && $form->status == Video::STATUS_PUBLISHED) {
                        $form->publish_time = now();
                    }
                } else if ($form->status == Video::STATUS_PUBLISHED) {
                    $form->publish_time = now();
                }
                
                // 非管理员创建的视频默认为草稿状态
                if (!$form->isEditing() && !Admin::user()->isAdministrator() && !Admin::user()->can('video_review')) {
                    $form->status = Video::STATUS_DRAFT;
                }
            });
            
            // 保存后回调
            $form->saved(function (Form $form) {
                // 如果状态为待审核，创建审核记录
                if ($form->status == Video::STATUS_PENDING) {
                    VideoAuditLog::create([
                        'video_id' => $form->getKey(),
                        'admin_id' => Admin::user()->id,
                        'status' => 1, // 待审核状态
                        'reason' => '提交审核'
                    ]);
                }
                
                // 如果状态为已发布，设置发布时间
                if ($form->status == Video::STATUS_PUBLISHED && empty($form->publish_time)) {
                    $video = Video::find($form->getKey());
                    if ($video) {
                        $video->publish_time = now();
                        $video->save();
                    }
                }
            });
        });
    }
    
    /**
     * 提交审核
     */
    public function submitReview($id, Request $request)
    {
        $video = Video::find($id);
        
        if (!$video) {
            return response()->json(['status' => false, 'message' => '视频不存在']);
        }
        
        if ($video->status != Video::STATUS_DRAFT) {
            return response()->json(['status' => false, 'message' => '只有草稿状态的视频才能提交审核']);
        }
        
        DB::beginTransaction();
        
        try {
            // 更新视频状态
            $video->status = Video::STATUS_PENDING;
            $video->save();
            
            // 创建审核记录
            VideoAuditLog::create([
                'video_id' => $video->id,
                'admin_id' => Admin::user()->id,
                'status' => VideoAuditLog::STATUS_PENDING,
                'reason' => '提交审核'
            ]);
            
            DB::commit();
            
            return response()->json(['status' => true, 'message' => '提交审核成功']);
        } catch (\Exception $e) {
            DB::rollBack();
            
            return response()->json(['status' => false, 'message' => '提交失败：' . $e->getMessage()]);
        }
    }
    
    /**
     * 待审核视频列表
     */
    public function pendingReview(Content $content)
    {
        if (!Admin::user()->can('video_review')) {
            admin_error('错误', '您没有审核视频的权限');
            return redirect(admin_url('videos'));
        }
        
        return $content
            ->title('待审核视频')
            ->description('审核视频列表')
            ->body(function (Grid $grid) {
                $grid->model(Video::with(['category', 'author', 'tags']))
                    ->where('status', Video::STATUS_PENDING);
                
                $grid->column('id', 'ID')->sortable();
                $grid->column('title', '视频标题');
                $grid->column('image', '封面图片')->image('', 50, 50);
                $grid->column('category.name', '分类');
                $grid->column('author.name', '提交人');
                $grid->column('created_at', '提交时间')->sortable();
                
                // 操作按钮
                $grid->actions(function (Grid\Displayers\Actions $actions) {
                    $actions->disableDelete();
                    $actions->disableEdit();
                    
                    // 添加审核按钮
                    $actions->append('<a href="' . admin_url('videos/' . $actions->row->id . '/review') . '" class="grid-row-review">审核</a>');
                });
                
                // 禁用创建按钮
                $grid->disableCreateButton();
                
                // 禁用批量操作
                $grid->disableBatchActions();
                
                // 默认排序
                $grid->model()->orderBy('created_at', 'desc');
            });
    }
    
    /**
     * 审核页面
     */
    public function review($id, Content $content)
    {
        if (!Admin::user()->can('video_review')) {
            admin_error('错误', '您没有审核视频的权限');
            return redirect(admin_url('videos'));
        }
        
        // 使用load代替with，确保即使关联不存在也不会报错
        $video = Video::find($id);
        
        if (!$video) {
            admin_error('错误', '视频不存在');
            return redirect(admin_url('videos'));
        }
        
        // 安全加载关联数据
        try {
            $video->load(['category', 'author', 'tags']);
        } catch (\Exception $e) {
            // 忽略加载关联数据时的错误
        }
        
        if ($video->status != Video::STATUS_PENDING) {
            admin_error('错误', '只能审核待审核状态的视频');
            return redirect(admin_url('videos'));
        }
        
        return $content
            ->title('视频审核')
            ->description('审核视频内容')
            ->body(view('admin.videos.review', compact('video')));
    }
    
    /**
     * 处理审核操作
     */
    public function handleReview($id, Request $request)
    {
        if (!Admin::user()->can('video_review')) {
            return response()->json(['status' => false, 'message' => '您没有审核视频的权限']);
        }
        
        $status = $request->input('status');
        $reason = $request->input('reason', '');
        
        $video = Video::find($id);
        
        if (!$video) {
            return response()->json(['status' => false, 'message' => '视频不存在']);
        }
        
        if ($video->status != Video::STATUS_PENDING) {
            return response()->json(['status' => false, 'message' => '只能审核待审核状态的视频']);
        }
        
        DB::beginTransaction();
        
        try {
            // 更新视频状态
            if ($status == 'approved') {
                $video->status = Video::STATUS_PUBLISHED;
                $video->publish_time = now();
            } else {
                $video->status = Video::STATUS_REJECTED;
            }
            
            $video->save();
            
            // 创建审核记录
            VideoAuditLog::create([
                'video_id' => $video->id,
                'admin_id' => Admin::user()->id,
                'status' => $status == 'approved' ? VideoAuditLog::STATUS_APPROVED : VideoAuditLog::STATUS_REJECTED,
                'reason' => $reason ?: ($status == 'approved' ? '审核通过' : '审核拒绝')
            ]);
            
            DB::commit();
            
            return response()->json(['status' => true, 'message' => '审核操作成功']);
        } catch (\Exception $e) {
            DB::rollBack();
            
            return response()->json(['status' => false, 'message' => '审核失败：' . $e->getMessage()]);
        }
    }
}
