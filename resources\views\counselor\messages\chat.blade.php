<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>与 {{ $user->name }} 的对话 - 心理健康平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
            font-family: "SF Pro Text", "SF Pro Icons", "Helvetica Neue", Helvetica, Arial, sans-serif;
        }
        .sidebar {
            position: fixed;
            top: 0;
            bottom: 0;
            left: 0;
            z-index: 100;
            padding: 48px 0 0;
            box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
            background: #fff;
        }
        .sidebar-sticky {
            height: calc(100vh - 48px);
            overflow-x: hidden;
            overflow-y: auto;
        }
        .nav-link {
            color: #333;
            font-weight: 500;
            padding: 15px 25px;
            display: flex;
            align-items: center;
        }
        .nav-link:hover {
            background: rgba(52, 152, 219, 0.1);
        }
        .nav-link.active {
            color: #3498db;
            background: rgba(52, 152, 219, 0.15);
            position: relative;
        }
        .nav-link.active::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: #3498db;
        }
        .nav-icon {
            margin-right: 10px;
            font-size: 20px;
        }
        .badge-notifications {
            background: #e74c3c;
            color: white;
            font-size: 12px;
            padding: 3px 6px;
            border-radius: 10px;
        }
        .navbar {
            background: white;
        }
        .chat-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            display: flex;
            flex-direction: column;
            height: calc(100vh - 150px);
        }
        .chat-header {
            padding: 15px 20px;
            background: linear-gradient(45deg, #3498db, #8e44ad);
            color: white;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .chat-header-left {
            display: flex;
            align-items: center;
        }
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 15px;
        }
        .user-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 50%;
        }
        .user-info h2 {
            font-size: 18px;
            font-weight: 600;
            margin: 0;
        }
        .user-info p {
            font-size: 14px;
            margin: 0;
            opacity: 0.8;
        }
        .chat-body {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            display: flex;
            flex-direction: column; /* 改为正向排列 */
        }
        .message {
            max-width: 75%;
            padding: 10px 15px;
            margin-bottom: 15px;
            border-radius: 18px;
            position: relative;
            word-wrap: break-word;
        }
        .message-user {
            align-self: flex-start;
            background-color: #f1f0f0;
            border-bottom-left-radius: 5px;
        }
        .message-counselor {
            align-self: flex-end;
            background-color: #0084ff;
            color: white;
            border-bottom-right-radius: 5px;
        }
        .message-time {
            font-size: 12px;
            opacity: 0.7;
            margin-top: 5px;
            text-align: right;
        }
        .message-user .message-time {
            color: #999;
        }
        .message-counselor .message-time {
            color: rgba(255, 255, 255, 0.8);
        }
        .message-date {
            text-align: center;
            margin: 20px 0;
            position: relative;
        }
        .message-date span {
            background: #f8f9fa;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 12px;
            color: #999;
            position: relative;
            z-index: 1;
        }
        .message-date::before {
            content: "";
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #e9ecef;
            z-index: 0;
        }
        .chat-footer {
            padding: 15px;
            border-top: 1px solid #e9ecef;
            background: white;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
        }
        
        .message-form {
            display: flex;
            align-items: center;
        }
        
        .message-input {
            flex: 1;
            border: 1px solid #e9ecef;
            border-radius: 20px;
            padding: 10px 20px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.2s;
        }
        
        .message-input:focus {
            border-color: #3498db;
        }
        
        .send-button {
            background: #0084ff;
            color: white;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            font-size: 20px;
            margin-left: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.2s;
        }
        
        .send-button:hover {
            background: #0066cc;
        }
        
        .send-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .appointment-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 15px;
            margin-bottom: 20px;
            border-left: 4px solid #3498db;
        }
        
        .appointment-card h3 {
            font-size: 16px;
            margin-bottom: 10px;
            color: #3498db;
        }
        
        .appointment-card p {
            margin-bottom: 5px;
            font-size: 14px;
            color: #666;
        }
        
        .appointment-card .appointment-time {
            font-weight: 600;
        }
        
        .back-link {
            color: white;
            text-decoration: none;
            display: flex;
            align-items: center;
        }
        
        .back-link:hover {
            color: rgba(255, 255, 255, 0.8);
        }
        
        .back-link i {
            margin-right: 5px;
        }
        
        .appointment-status-card {
            margin: 15px;
            padding: 15px;
            border-radius: 10px;
            display: flex;
            align-items: flex-start;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        
        .appointment-status-card i {
            font-size: 24px;
            margin-right: 15px;
            margin-top: 5px;
        }
        
        .appointment-status-card h4 {
            margin: 0 0 5px 0;
            font-size: 16px;
            font-weight: 600;
        }
        
        .appointment-status-card p {
            margin: 0 0 10px 0;
            font-size: 14px;
            color: #666;
        }
        
        .appointment-status-card.completed {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
        }
        
        .appointment-status-card.completed i {
            color: #28a745;
        }
        
        .appointment-status-card.ongoing {
            background-color: #fff8e1;
            border: 1px solid #ffe082;
        }
        
        .appointment-status-card.ongoing i {
            color: #fb8c00;
        }
        
        .appointment-status-card.confirmed {
            background-color: #e3f2fd;
            border: 1px solid #bbdefb;
        }
        
        .appointment-status-card.confirmed i {
            color: #1976d2;
        }
        
        form.disabled .message-input,
        form.disabled .send-button {
            opacity: 0.7;
            background-color: #f2f2f2;
        }
        .new-message-alert {
            position: fixed;
            bottom: 85px;
            left: 50%;
            transform: translateX(-50%);
            background-color: #3498db;
            color: white;
            padding: 10px 20px;
            border-radius: 20px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.2);
            cursor: pointer;
            z-index: 1000;
            animation: bounceIn 0.5s;
        }
        
        @keyframes bounceIn {
            0% { transform: translateX(-50%) scale(0.8); opacity: 0; }
            50% { transform: translateX(-50%) scale(1.1); opacity: 1; }
            100% { transform: translateX(-50%) scale(1); }
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-light bg-white fixed-top shadow-sm">
        <div class="container-fluid">
            <span class="navbar-brand">心理健康平台 - 咨询师工作台</span>
            <div class="d-flex">
                <div class="dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <span class="me-2">{{ session('counselor_name') }}</span>
                        <img src="{{ session('counselor_avatar') ? asset('storage/'.session('counselor_avatar')) : asset('images/default-avatar.jpg') }}" width="32" height="32" class="rounded-circle">
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                        <li>
                            <a href="{{ route('counselor.profile') }}" class="dropdown-item">
                                <i class="bi bi-person me-2"></i>个人资料
                            </a>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <form method="POST" action="{{ route('counselor.logout') }}">
                                @csrf
                                <button type="submit" class="dropdown-item">
                                    <i class="bi bi-box-arrow-right me-2"></i>退出登录
                                </button>
                            </form>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- 侧边导航 -->
            <nav id="sidebarMenu" class="col-md-3 col-lg-2 d-md-block sidebar">
                <div class="position-sticky sidebar-sticky pt-3">
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link" href="{{ route('counselor.dashboard') }}">
                                <i class="bi bi-house-door nav-icon"></i>
                                首页
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ route('counselor.appointments') }}">
                                <i class="bi bi-calendar-check nav-icon"></i>
                                预约管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="{{ route('counselor.messages') }}">
                                <i class="bi bi-chat-dots nav-icon"></i>
                                消息中心
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- 主要内容 -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 py-4" style="margin-top: 48px;">
                <div class="chat-container">
                    <div class="chat-header">
                        <div class="chat-header-left">
                            <a href="{{ route('counselor.messages') }}" class="back-link">
                                <i class="bi bi-arrow-left"></i>
                            </a>
                            <div class="user-avatar">
                                <img src="{{ $user->avatar ? url('storage/'.$user->avatar) : url('images/default-avatar.jpg') }}" alt="{{ $user->name }}">
                            </div>
                            <div class="user-info">
                                <h2>{{ $user->name }}</h2>
                                <p>
                                    @if($appointment)
                                    预约: {{ \Carbon\Carbon::parse($appointment->appointment_time)->format('Y-m-d H:i') }}
                                    @else
                                    无当前预约
                                    @endif
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="chat-body" id="chat-body">
                        @if($messages->count() > 0)
                            @php
                                $currentDate = null;
                                // 将消息按时间正序排列（从旧到新）
                                $sortedMessages = $messages->sortBy('created_at');
                            @endphp
                            
                            @foreach($sortedMessages as $message)
                                @php
                                    $messageDate = \Carbon\Carbon::parse($message->created_at)->format('Y-m-d');
                                @endphp
                                
                                @if($currentDate != $messageDate)
                                    <div class="message-date">
                                        <span>{{ \Carbon\Carbon::parse($message->created_at)->format('Y年m月d日') }}</span>
                                    </div>
                                    @php
                                        $currentDate = $messageDate;
                                    @endphp
                                @endif
                                
                                <div class="message {{ $message->sender_type == 'user' ? 'message-user' : 'message-counselor' }}">
                                    {{ $message->content }}
                                    <div class="message-time">{{ \Carbon\Carbon::parse($message->created_at)->format('H:i') }}</div>
                                </div>
                            @endforeach
                        @else
                            @if($appointment)
                                <div class="appointment-card">
                                    <h3>预约信息</h3>
                                    <p>用户: <strong>{{ $user->name }}</strong></p>
                                    <p>咨询类型: {{ $appointment->consultation_type }}</p>
                                    <p class="appointment-time">预约时间: {{ \Carbon\Carbon::parse($appointment->appointment_time)->format('Y年m月d日 H:i') }}</p>
                                    <p>状态: 
                                        @if($appointment->status == 1)
                                            待确认
                                        @elseif($appointment->status == 2)
                                            已确认
                                        @elseif($appointment->status == 3)
                                            进行中
                                        @elseif($appointment->status == 4)
                                            已完成
                                        @else
                                            已取消
                                        @endif
                                    </p>
                                </div>
                                <div class="message-date">
                                    <span>今天</span>
                                </div>
                                <div class="message message-counselor">
                                    您好，我是您的咨询师{{ session('counselor_name') }}，我已收到您的预约。有任何问题都可以在这里联系我。
                                    <div class="message-time">{{ \Carbon\Carbon::now()->format('H:i') }}</div>
                                </div>
                            @else
                                <div class="message-date">
                                    <span>今天</span>
                                </div>
                                <div class="message message-counselor">
                                    您好，我是咨询师{{ session('counselor_name') }}，有任何问题都可以在这里联系我。
                                    <div class="message-time">{{ \Carbon\Carbon::now()->format('H:i') }}</div>
                                </div>
                            @endif
                        @endif
                    </div>
                    
                    @if($appointment && $appointment->status == 4)
                    <div class="appointment-status-card completed">
                        <i class="bi bi-check-circle-fill"></i>
                        <div>
                            <h4>此次咨询已完成</h4>
                            <p>用户无法再发送新消息，如需继续服务请让用户创建新预约</p>
                        </div>
                    </div>
                    @elseif($appointment && $appointment->status == 3)
                    <div class="appointment-status-card ongoing">
                        <i class="bi bi-clock-fill"></i>
                        <div>
                            <h4>咨询进行中</h4>
                            <p>您可以随时将此次咨询标记为已完成</p>
                            <form action="{{ route('counselor.appointments.complete', $appointment->id) }}" method="POST" class="d-inline" id="complete-form">
                                @csrf
                                <button type="button" class="btn btn-sm btn-success" id="complete-btn">标记为已完成</button>
                            </form>
                        </div>
                    </div>
                    @elseif($appointment && $appointment->status == 2)
                    <div class="appointment-status-card confirmed">
                        <i class="bi bi-calendar-check-fill"></i>
                        <div>
                            <h4>预约已确认</h4>
                            <p>预约时间: {{ \Carbon\Carbon::parse($appointment->appointment_time)->format('Y年m月d日 H:i') }}</p>
                            <form action="{{ route('counselor.appointments.start', $appointment->id) }}" method="POST" class="d-inline">
                                @csrf
                                <button type="submit" class="btn btn-sm btn-primary">开始咨询</button>
                            </form>
                        </div>
                    </div>
                    @endif

                    <div class="chat-footer">
                        <form class="message-form" id="message-form" {{ $appointment && $appointment->status == 4 ? 'class=disabled' : '' }}>
                            <input type="hidden" name="user_id" id="user_id" value="{{ $user->id }}">
                            @if($appointment)
                            <input type="hidden" name="appointment_id" id="appointment_id" value="{{ $appointment->id }}">
                            @endif
                            <input type="text" class="message-input" id="message-input" 
                                placeholder="{{ $appointment && $appointment->status == 4 ? '此次咨询已完成，无法发送新消息' : '输入消息...' }}" 
                                required {{ $appointment && $appointment->status == 4 ? 'disabled' : '' }}>
                            <button type="submit" class="send-button" id="send-button" {{ $appointment && $appointment->status == 4 ? 'disabled style="opacity: 0.5;"' : '' }}>
                                <i class="bi bi-send"></i>
                            </button>
                        </form>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <script>
        $(document).ready(function() {
            // CSRF 设置
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });
            
            // 元素引用
            const chatBody = document.getElementById('chat-body');
            const messageForm = document.getElementById('message-form');
            const messageInput = document.getElementById('message-input');
            const sendButton = document.getElementById('send-button');
            const userId = $('#user_id').val();
            
            // 调试函数 - 仅输出到控制台
            function debug(message, data) {
                console.log(message, data);
                // 不再显示到UI上
            }
            
            // 检查是否靠近底部的辅助函数
            function isNearBottom() {
                return (chatBody.scrollHeight - chatBody.scrollTop - chatBody.clientHeight) < 100;
            }
            
            // 滚动到底部
            function scrollToBottom() {
                chatBody.scrollTop = chatBody.scrollHeight;
            }
            
            // 页面加载时滚动到底部
            scrollToBottom();
            
            // 添加消息到界面
            function addMessageToUI(content, time, isSelf = true) {
                const messageElement = document.createElement('div');
                messageElement.className = isSelf ? 'message message-counselor' : 'message message-user';
                messageElement.innerHTML = `
                    ${content}
                    <div class="message-time">${time}</div>
                `;
                
                // 添加到底部
                chatBody.appendChild(messageElement);
                
                // 滚动到底部
                scrollToBottom();
                

                return messageElement;
            }
            
            // 显示新消息通知
            function showNewMessageAlert() {
                if (!document.querySelector('.new-message-alert')) {
                    const notification = document.createElement('div');
                    notification.className = 'new-message-alert';
                    notification.textContent = '有新消息';
                    notification.onclick = function() {
                        scrollToBottom();
                        this.remove();
                    };
                    
                    document.querySelector('.chat-container').appendChild(notification);
                    
                    // 5秒后自动移除通知
                    setTimeout(() => notification.remove(), 5000);
                }
            }
            
            // 发送消息处理函数
            messageForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const content = messageInput.value.trim();
                const appointmentId = $('#appointment_id').val();
                
                if (!content) return;
                
                // 禁用发送按钮
                sendButton.disabled = true;
                
                // 获取当前时间格式化
                const now = new Date();
                const timeStr = now.getHours().toString().padStart(2, '0') + ":" + 
                                now.getMinutes().toString().padStart(2, '0');
                
                // 先添加一个临时消息到UI
                const tempMessage = addMessageToUI(content, timeStr, true);
                
                // 清空输入框
                messageInput.value = '';
                
                // 发送到服务器
                $.ajax({
                    url: `/counselor/messages/${userId}/send`,
                    method: 'POST',
                    data: {
                        content: content,
                        appointment_id: appointmentId
                    },
                    success: function(response) {
                        debug('发送消息响应', response);
                        
                        if (response.success) {
                            // 更新临时消息的时间戳为服务器返回的时间
                            const timeElement = tempMessage.querySelector('.message-time');
                            if (timeElement) {
                                timeElement.textContent = response.formatted_time;
                            }
                            
                            // 保存最后发送的消息ID，帮助追踪对话
                            const lastSentIdKey = `counselor_last_sent_id_${userId}`;
                            sessionStorage.setItem(lastSentIdKey, response.message.id);
                        } else {
                            // 发送失败，移除临时消息
                            tempMessage.remove();
                            alert('消息发送失败: ' + (response.message || '未知错误'));
                        }
                    },
                    error: function(error) {
                        debug('发送消息错误', error);
                        // 发送失败，移除临时消息
                        tempMessage.remove();
                        alert('发送消息失败，请检查网络连接后重试');
                    },
                    complete: function() {
                        // 重新启用发送按钮
                        sendButton.disabled = false;
                    }
                });
            });
            
            // 检查新消息函数
            function checkNewMessages() {
                // 使用直接Ajax请求而不依赖于sessionStorage
                $.ajax({
                    url: `/counselor/messages/${userId}/check-new`,
                    method: 'GET',
                    success: function(data) {
                        debug('检查新消息响应', data);
                        
        
                        if (data.has_new && data.messages && data.messages.length > 0) {
                            // 检测是否靠近底部
                            const wasNearBottom = isNearBottom();
                            
                            // 添加所有新消息
                            data.messages.forEach(message => {

                                addMessageToUI(message.content, message.formatted_time, false);
                            });
                            
                            // 如果不在底部，显示新消息通知
                            if (!wasNearBottom) {
                                showNewMessageAlert();
                            } else {
                                // 如果在底部，自动滚动到底部
                                scrollToBottom();
                            }
                        }
                    },
                    error: function(error) {

                        debug('检查新消息错误', error);
                    }
                });
            }
            
            
            // 页面加载后立即检查一次新消息
            checkNewMessages();
            
            // 设置定时检查 - 每1秒检查一次 (更频繁一些)
            const checkInterval = setInterval(checkNewMessages, 1000);
            
            // 确保滚动到底部
            setTimeout(scrollToBottom, 500);
            
            // 添加样式
            const style = document.createElement('style');
            style.textContent = `
                .new-message-alert {
                    position: fixed;
                    bottom: 85px;
                    left: 50%;
                    transform: translateX(-50%);
                    background-color: #3498db;
                    color: white;
                    padding: 10px 20px;
                    border-radius: 20px;
                    box-shadow: 0 3px 10px rgba(0,0,0,0.2);
                    cursor: pointer;
                    z-index: 1000;
                    animation: bounceIn 0.5s;
                }
                
                
                @keyframes bounceIn {
                    0% { transform: translateX(-50%) scale(0.8); opacity: 0; }
                    50% { transform: translateX(-50%) scale(1.1); opacity: 1; }
                    100% { transform: translateX(-50%) scale(1); }
                }
            `;
            document.head.appendChild(style);
        });
    </script>
</body>
</html>
