<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\QuizActivity;
use App\Models\QuizAttempt;
use App\Models\QuizAnswer;
use App\Models\QuizPrizeWinner;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class QuizController extends Controller
{
    // 显示问答活动列表
    public function index()
    {
        $ongoingQuizzes = QuizActivity::where('is_active', true)
            ->where('start_time', '<=', now())
            ->where('end_time', '>=', now())
            ->orderBy('start_time', 'asc')
            ->get();
            
        $upcomingQuizzes = QuizActivity::where('is_active', true)
            ->where('start_time', '>', now())
            ->orderBy('start_time', 'asc')
            ->get();
            
        $pastQuizzes = QuizActivity::where('end_time', '<', now())
            ->orderBy('end_time', 'desc')
            ->limit(5)
            ->get();
            
        return view('pages.quiz.index', [
            'ongoingQuizzes' => $ongoingQuizzes,
            'upcomingQuizzes' => $upcomingQuizzes,
            'pastQuizzes' => $pastQuizzes
        ]);
    }
    
    // 显示问答活动详情
    public function show($id)
    {
        $quiz = QuizActivity::with('prizes')->findOrFail($id);
        $canAttempt = false;
        $userAttempts = collect();
        $hasPrize = false;
        
        if (Auth::check()) {
            $user = Auth::user();
            $canAttempt = $quiz->canUserAttempt($user->id);
            $userAttempts = $quiz->attempts()
                ->where('user_id', $user->id)
                ->where('is_completed', true)
                ->with('prizeWinner.prize')
                ->orderBy('completed_at', 'desc')
                ->get();
                
            $hasPrize = $userAttempts->contains(function ($attempt) {
                return $attempt->has_won_prize && $attempt->prizeWinner;
            });
        }
        
        return view('pages.quiz.show', [
            'quiz' => $quiz,
            'canAttempt' => $canAttempt,
            'userAttempts' => $userAttempts,
            'hasPrize' => $hasPrize
        ]);
    }
    
    // 开始答题
    public function start($id)
    {
        $quiz = QuizActivity::findOrFail($id);
        
        if (!Auth::check()) {
            return redirect()->route('login')
                ->with('error', '请先登录再参与问答');
        }
        
        $user = Auth::user();
        
        if (!$quiz->canUserAttempt($user->id)) {
            return redirect()->route('quiz.show', $quiz->id)
                ->with('error', '您已达到最大尝试次数或问答活动未开始/已结束');
        }
        
        // 创建新的尝试记录
        $attempt = QuizAttempt::create([
            'quiz_activity_id' => $quiz->id,
            'user_id' => $user->id,
            'score' => 0,
            'is_completed' => false,
            'total_questions' => $quiz->questions()->count(),
            'correct_count' => 0,
            'has_won_prize' => false
        ]);
        
        return redirect()->route('quiz.attempt', ['id' => $quiz->id, 'attempt_id' => $attempt->id]);
    }
    
    // 答题页面
    public function attempt($id, $attempt_id)
    {
        $quiz = QuizActivity::with(['questions.options' => function($query) {
            $query->orderBy('order');
        }])->findOrFail($id);
        
        $attempt = QuizAttempt::where('id', $attempt_id)
            ->where('user_id', Auth::id())
            ->where('quiz_activity_id', $quiz->id)
            ->where('is_completed', false)
            ->firstOrFail();
            
        return view('pages.quiz.attempt', [
            'quiz' => $quiz,
            'attempt' => $attempt
        ]);
    }
    
    // 提交答案
    public function submit(Request $request, $id, $attempt_id)
    {
        $quiz = QuizActivity::with(['questions', 'prizes'])->findOrFail($id);
        
        $attempt = QuizAttempt::where('id', $attempt_id)
            ->where('user_id', Auth::id())
            ->where('quiz_activity_id', $quiz->id)
            ->where('is_completed', false)
            ->firstOrFail();
            
        $answers = $request->input('answers', []);
        $correctCount = 0;
        $totalScore = 0;
        
        DB::transaction(function() use ($quiz, $attempt, $answers, &$correctCount, &$totalScore) {
            // 处理每个问题的答案
            foreach ($quiz->questions as $question) {
                $userAnswer = $answers[$question->id] ?? null;
                $isCorrect = false;
                
                // 检查答案是否正确
                if ($userAnswer !== null) {
                    $isCorrect = $question->checkAnswer($userAnswer);
                }
                
                // 记录用户答案
                QuizAnswer::create([
                    'attempt_id' => $attempt->id,
                    'question_id' => $question->id,
                    'user_answer' => $userAnswer,
                    'is_correct' => $isCorrect
                ]);
                
                // 统计正确答案数和得分
                if ($isCorrect) {
                    $correctCount++;
                    $totalScore += $question->points ?? 1;
                }
            }
            
            // 更新尝试记录
            $attempt->score = $totalScore;
            $attempt->correct_count = $correctCount;
            $attempt->total_questions = count($quiz->questions); // 设置总题数字段
            $attempt->is_completed = true;
            $attempt->completed_at = now();
            
            // 使用pass_score作为百分比来计算是否通过
            // 例如pass_score=60表示需要答对60%的题目才能通过
            
            // 计算用户的答题正确率
            $userCorrectPercentage = count($quiz->questions) > 0 
                ? ($correctCount / count($quiz->questions)) * 100 
                : 0;
                
            // 判断是否通过：用户的正确率大于等于要求的pass_score
            $passed = $userCorrectPercentage >= $quiz->pass_score;
            // 不使用 has_passed 字段，改用 score 和 is_completed 字段
            
            // 如果通过了测试，检查是否获得奖品
            if ($passed && $quiz->prizes->count() > 0) {
                $prize = $quiz->assignPrizeToUser($attempt, $passed);
                
                if ($prize) {
                    // 只设置 has_won_prize 字段，该字段在数据库中存在
                    $attempt->has_won_prize = true;
                    
                    // 创建奖品获得记录，正确关联quiz_activity_id
                    $winner = QuizPrizeWinner::create([
                        'user_id' => Auth::id(),
                        'quiz_activity_id' => $quiz->id, // 添加缺失的quiz_activity_id
                        'prize_id' => $prize->id,
                        'attempt_id' => $attempt->id,
                        'status' => 'unclaimed' // 使用unclaimed状态，与claimPrize方法中的条件匹配
                    ]);
                    
                    // 添加调试日志
                    \Log::info("Prize winner created: user_id={$winner->user_id}, quiz_id={$winner->quiz_activity_id}, prize_id={$winner->prize_id}, attempt_id={$winner->attempt_id}");
                }
            }
            
            $attempt->save();
        });
        
        return redirect()->route('quiz.result', ['id' => $quiz->id, 'attempt_id' => $attempt->id]);
    }
    
    // 答题结果页面
    public function result($id, $attempt_id)
    {
        $quiz = QuizActivity::findOrFail($id);
        
        $attempt = QuizAttempt::where('id', $attempt_id)
            ->where('user_id', Auth::id())
            ->where('quiz_activity_id', $quiz->id)
            ->where('is_completed', true)
            ->with(['answers.question.options', 'prizeWinner.prize'])
            ->firstOrFail();
            
        $scorePercentage = $attempt->total_questions > 0 
            ? round(($attempt->correct_count / $attempt->total_questions) * 100) 
            : 0;
            
        // 使用pass_score作为百分比来计算是否通过
        // 例如pass_score=60表示需要答对60%的题目才能通过
        $passed = false;
        
        // 计算用户的答题正确率
        $userCorrectPercentage = $attempt->total_questions > 0 
            ? ($attempt->correct_count / $attempt->total_questions) * 100 
            : 0;
            
        // 判断是否通过：用户的正确率大于等于要求的pass_score
        $passed = $userCorrectPercentage >= $quiz->pass_score;
        
        return view('pages.quiz.result', [
            'quiz' => $quiz,
            'attempt' => $attempt,
            'scorePercentage' => $scorePercentage,
            'passed' => $passed
        ]);
    }
    
    // 填写奖品领取信息
    public function claimPrize($id, $winner_id)
    {
        // 如果是创建新的领取记录
        if ($winner_id == 1) {
            // 找到用户最近完成的答题记录
            $attempt = QuizAttempt::where('user_id', Auth::id())
                ->where('quiz_activity_id', $id)
                ->where('is_completed', true)
                ->orderBy('created_at', 'desc')
                ->first();
                
            if (!$attempt) {
                return redirect()->route('quiz.my_prizes')
                                 ->with('error', '没有找到您的答题记录');
            }
            
            // 检查用户是否已经领取过奖品
            $existingWinner = QuizPrizeWinner::where('user_id', Auth::id())
                ->where('quiz_activity_id', $id)
                ->exists();
                
            if ($existingWinner) {
                return redirect()->route('quiz.my_prizes')
                                 ->with('error', '您已经领取过该问答活动的奖品，不能重复领取');
            }
            
            // 用户必须获得满分才能领取奖品
            if ($attempt->score < 3) { // 确保得分足够才能领取奖品
                return redirect()->route('quiz.result', ['id' => $id, 'attempt_id' => $attempt->id])
                                 ->with('error', '您的得分不够，无法领取奖品');
            }
            
            // 找到对应等级的奖品 - 得分为3时选择min_score=3的奖品
            $quiz = QuizActivity::findOrFail($id);
            
            // 先查询所有奖品并记录到日志
            $allPrizes = $quiz->prizes()->get();
            \Log::info('Available prizes:', $allPrizes->toArray());
            \Log::info('User score for prize selection: ' . $attempt->score);
            
            // 查询用户可以领取的奖品(选择min_score小于等于用户得分的最高级别奖品)
            $prize = $quiz->prizes()
                ->where('min_score', '<=', $attempt->score) // 奖品最小分数要求小于等于用户得分
                ->where('quantity', '>', function($query) {
                    $query->selectRaw('COUNT(*)')
                          ->from('quiz_prize_winners')
                          ->whereColumn('prize_id', 'quiz_prizes.id');
                })
                ->orderBy('min_score', 'desc') // 选择满足条件的最高级别奖品
                ->first();
                
            if ($prize) {
                \Log::info('Selected prize for user:', $prize->toArray());
            } else {
                \Log::info('No prizes available that match the user score criteria');
                return redirect()->route('quiz.result', ['id' => $id, 'attempt_id' => $attempt->id])
                                 ->with('error', '很抱歉，没有可领取的奖品或已经领完');
            }
            
            // 创建奖品获得记录
            $winner = QuizPrizeWinner::create([
                'user_id' => Auth::id(),
                'quiz_activity_id' => $id,
                'prize_id' => $prize->id,
                'attempt_id' => $attempt->id,
                'status' => 'unclaimed'
            ]);
            
            // 更新attempt记录
            $attempt->has_won_prize = true;
            $attempt->save();
            
            // 重新加载关联
            $winner->load(['prize', 'attempt.quizActivity']);
        } else {
            // 如果是通过ID查找现有记录
            $winner = QuizPrizeWinner::where('id', $winner_id)
                ->where('user_id', Auth::id())
                ->where('status', 'unclaimed')
                ->with(['prize', 'attempt.quizActivity'])
                ->firstOrFail();
                
            if ($winner->quiz_activity_id != $id) {
                abort(404, '奖品记录与测试不匹配');
            }
        }
        
        return view('pages.quiz.claim_prize', [
            'winner' => $winner
        ]);
    }
    
    // 提交奖品领取信息
    public function submitClaim(Request $request, $id, $winner_id)
    {
        $winner = QuizPrizeWinner::where('id', $winner_id)
            ->where('user_id', Auth::id())
            ->where('status', 'unclaimed')
            ->with('prize')
            ->firstOrFail();
        
        // 验证获奖记录的问答活动ID是否匹配
        if ($winner->quiz_activity_id != $id) {
            abort(404);
        }
        
        // 根据奖品类型验证不同的字段
        if ($winner->prize->prize_type == 'physical') {
            // 实物奖品需要填写收货地址
            $request->validate([
                'name' => 'required|string|max:50',
                'phone' => 'required|string|max:20',
                'address' => 'required|string|max:200',
                'notes' => 'nullable|string|max:500',
            ]);
            
            // 正确使用数据库字段
            $winner->winner_name = $request->name;
            $winner->contact_info = $request->phone;
            $winner->shipping_address = $request->address;
            $winner->admin_notes = $request->notes;
            $winner->status = 'pending';
        } else if ($winner->prize->prize_type == 'coupon') {
            // 优惠券只需要填写手机和邮箱
            $request->validate([
                'name' => 'required|string|max:50',
                'phone' => 'required|string|max:20',
                'email' => 'required|email|max:100',
            ]);
            
            // 正确使用数据库字段
            $winner->winner_name = $request->name;
            $winner->contact_info = $request->phone . ' / ' . $request->email;
            $winner->status = 'claimed';
            $winner->claimed_at = now();
            
            // 生成优惠券码
            $couponCode = 'CP-' . strtoupper(substr(md5(uniqid(rand(), true)), 0, 8));
            $winner->admin_notes = '优惠券码: ' . $couponCode;
            
            // 这里可以添加发送邮件的逻辑
            // Mail::to($request->email)->send(new VirtualPrizeNotification($winner));
        }
        
        $winner->updated_at = now();
        $winner->save();
        
        return redirect()->route('quiz.my_prizes')
                         ->with('success', '奖品领取信息已提交');
    }
    
    // 我的奖品页面
    public function myPrizes()
    {
        if (!Auth::check()) {
            return redirect()->route('login')->with('error', '请先登录');
        }
        
        $prizeWinners = QuizPrizeWinner::with(['prize', 'attempt.quizActivity'])
                                       ->where('user_id', Auth::id())
                                       ->orderBy('created_at', 'desc')
                                       ->get();
        
        return view('pages.quiz.my_prizes', compact('prizeWinners'));
    }
}
