<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class QuizAnswer extends Model
{
    use HasFactory, HasDateTimeFormatter;

    protected $fillable = [
        'attempt_id',
        'question_id',
        'user_answer',
        'is_correct',
    ];

    protected $casts = [
        'is_correct' => 'boolean',
        'user_answer' => 'json',
    ];

    // 关联尝试记录
    public function attempt()
    {
        return $this->belongsTo(QuizAttempt::class);
    }

    // 关联问题
    public function question()
    {
        return $this->belongsTo(QuizQuestion::class);
    }
}
