<?php

namespace App\Services;

use App\Models\AssessmentAiSetting;
use App\Models\AssessmentResponse;
use App\Models\AssessmentAnswer;
use App\Models\AssessmentQuestion;
use App\Models\AssessmentOption;
use App\Models\AssessmentQuestionnaire;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;

class AiAnalysisService
{
    /**
     * 生成AI分析报告
     *
     * @param AssessmentResponse $response
     * @return string|null
     */
    public function generateAnalysis(AssessmentResponse $response): ?string
    {
        try {
            // 获取AI设置
            $aiSetting = AssessmentAiSetting::where('is_active', true)->first();
            if (!$aiSetting) {
                Log::warning('未找到启用的AI设置');
                return null;
            }

            // 构建分析数据
            $analysisData = $this->buildAnalysisData($response);
            
            // 构建用户提示词
            $userPrompt = $this->buildUserPrompt($aiSetting->user_prompt_template, $analysisData);
            
            // 调用AI API
            $aiResponse = $this->callAiApi($aiSetting, $userPrompt);
            
            return $aiResponse;
            
        } catch (Exception $e) {
            Log::error('AI分析生成失败: ' . $e->getMessage(), [
                'response_id' => $response->id,
                'error' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * 构建分析数据
     *
     * @param AssessmentResponse $response
     * @return array
     */
    private function buildAnalysisData(AssessmentResponse $response): array
    {
        // 加载问卷信息
        $questionnaire = $response->questionnaire;
        
        // 获取用户答案
        $answers = AssessmentAnswer::where('response_id', $response->id)
            ->with(['question', 'option'])
            ->get();
        
        // 构建问题和答案数据
        $questionsAndAnswers = [];
        $scoringDetails = [];
        
        foreach ($answers as $answer) {
            $question = $answer->question;
            $option = $answer->option;
            
            $questionsAndAnswers[] = [
                'question' => $question->question_text,
                'answer' => $option ? $option->option_text : $answer->custom_value,
                'score' => $option ? $option->score_value : 0
            ];
            
            $scoringDetails[] = [
                'question' => $question->question_text,
                'selected_option' => $option ? $option->option_text : $answer->custom_value,
                'score' => $option ? $option->score_value : 0
            ];
        }
        
        return [
            'questionnaire_title' => $questionnaire->title,
            'questionnaire_description' => $questionnaire->description,
            'questionnaire_id' => $questionnaire->id,
            'assessment_date' => $response->submitted_at ? $response->submitted_at->format('Y-m-d H:i:s') : now()->format('Y-m-d H:i:s'),
            'total_score' => $response->total_score,
            'questions_and_answers' => $questionsAndAnswers,
            'scoring_details' => $scoringDetails
        ];
    }

    /**
     * 构建用户提示词
     *
     * @param string $template
     * @param array $data
     * @return string
     */
    private function buildUserPrompt(string $template, array $data): string
    {
        // 格式化问题和答案
        $questionsAndAnswersText = '';
        foreach ($data['questions_and_answers'] as $index => $qa) {
            $questionsAndAnswersText .= sprintf(
                "%d. %s\n   答案：%s\n   得分：%d分\n\n",
                $index + 1,
                $qa['question'],
                $qa['answer'],
                $qa['score']
            );
        }
        
        // 格式化评分详情
        $scoringDetailsText = '';
        $totalQuestions = count($data['scoring_details']);
        
        // 计算实际的最大可能分数
        $maxPossibleScore = 0;
        $questionnaire = AssessmentQuestionnaire::with(['questions.options'])->find($data['questionnaire_id'] ?? 0);
        if ($questionnaire) {
            foreach ($questionnaire->questions as $question) {
                $maxPossibleScore += $question->options->max('score_value');
            }
        } else {
            // 如果无法获取问卷信息，使用传统方法估算
            $maxPossibleScore = $totalQuestions * 5;
        }
        
        $percentage = $maxPossibleScore > 0 ? round(($data['total_score'] / $maxPossibleScore) * 100, 1) : 0;
        
        $scoringDetailsText = sprintf(
            "总题数：%d题\n总得分：%d分\n满分：%d分\n得分率：%s%%\n\n详细得分：\n%s",
            $totalQuestions,
            $data['total_score'],
            $maxPossibleScore,
            $percentage,
            $questionsAndAnswersText
        );
        
        // 替换模板变量
        $userPrompt = str_replace([
            '{questionnaire_title}',
            '{questionnaire_description}',
            '{assessment_date}',
            '{total_score}',
            '{questions_and_answers}',
            '{scoring_details}'
        ], [
            $data['questionnaire_title'],
            $data['questionnaire_description'],
            $data['assessment_date'],
            $data['total_score'],
            $questionsAndAnswersText,
            $scoringDetailsText
        ], $template);
        
        return $userPrompt;
    }

    /**
     * 调用AI API
     *
     * @param AssessmentAiSetting $setting
     * @param string $userPrompt
     * @return string|null
     */
    private function callAiApi(AssessmentAiSetting $setting, string $userPrompt): ?string
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $setting->api_key,
                'Content-Type' => 'application/json',
            ])->timeout(60)->post($setting->endpoint, [
                'model' => $setting->model,
                'messages' => [
                    [
                        'role' => 'system',
                        'content' => $setting->system_prompt
                    ],
                    [
                        'role' => 'user',
                        'content' => $userPrompt
                    ]
                ],
                'temperature' => $setting->temperature,
                'max_tokens' => $setting->max_tokens,
            ]);

            if ($response->successful()) {
                $data = $response->json();
                
                // 根据不同API格式解析响应
                if (isset($data['choices'][0]['message']['content'])) {
                    return $data['choices'][0]['message']['content'];
                } elseif (isset($data['choices'][0]['text'])) {
                    return $data['choices'][0]['text'];
                } else {
                    Log::warning('AI API响应格式异常', ['response' => $data]);
                    return null;
                }
            } else {
                Log::error('AI API调用失败', [
                    'status' => $response->status(),
                    'response' => $response->body()
                ]);
                return null;
            }
            
        } catch (Exception $e) {
            Log::error('AI API调用异常: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * 检查AI分析功能是否可用
     *
     * @return bool
     */
    public function isAvailable(): bool
    {
        $aiSetting = AssessmentAiSetting::where('is_active', true)->first();
        return $aiSetting && !empty($aiSetting->api_key) && !empty($aiSetting->endpoint);
    }
} 