<?php
namespace App\Admin\Controllers;

use App\Models\AssessmentResponse;
use App\Models\AssessmentQuestionnaire;
use App\Models\User;
use App\Models\AssessmentAnswer;
use Dcat\Admin\Grid;
use Dcat\Admin\Form;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class AssessmentResponseController extends AdminController
{
    protected $title = '心理测评答卷';

    /**
     * 资源列表表格
     */
    protected function grid(): Grid
    {
        return Grid::make(new AssessmentResponse(), function (Grid $grid) {
            $grid->column('id', '编号')->sortable();
            $grid->column('user.name', '用户');
            $grid->column('questionnaire.title', '问卷标题');
            $grid->column('submitted_at', '提交时间')->sortable();
            $grid->column('created_at', '创建时间');
            $grid->column('updated_at', '更新时间');

            // 列过滤和搜索
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id', '编号');
                $filter->equal('user_id', '用户')->select(User::pluck('name', 'id'));
                $filter->equal('questionnaire_id', '问卷')->select(AssessmentQuestionnaire::pluck('title', 'id'));
                $filter->between('submitted_at', '提交时间')->datetime();
                $filter->between('created_at', '创建时间')->datetime();
                $filter->between('updated_at', '更新时间')->datetime();
            });

            // 禁止新增，仅禁编辑和删除，保留查看按钮
            $grid->disableCreateButton();
            $grid->disableEditButton();
            $grid->disableDeleteButton();
        });
    }

    /**
     * 详情视图
     */
    protected function detail($id): Show
    {
        return Show::make($id, new AssessmentResponse(), function (Show $show) {
            $show->field('id', '编号');
            $show->field('user.name', '用户');
            $show->field('questionnaire.title', '问卷标题');
            $show->field('submitted_at', '提交时间');
            $show->field('created_at', '创建时间');
            $show->field('updated_at', '更新时间');
            // 答案列表
            $show->answers('答案列表', function (Grid $grid) {
                $grid->column('id', '编号')->sortable();
                $grid->column('question.content', '题目内容');
                $grid->column('option.content', '选项内容');
                $grid->column('custom_value', '自定义答案');
                $grid->column('created_at', '创建时间');
                $grid->column('updated_at', '更新时间');
                $grid->disableCreateButton();
                $grid->disableActions();
            });
        });
    }

    /**
     * 详情表单
     */
    protected function form(): Form
    {
        return Form::make(new AssessmentResponse(), function (Form $form) {
            $form->display('id');
            $form->display('user.name', '用户');
            $form->display('questionnaire.title', '问卷');
            $form->display('submitted_at', '提交时间');
            $form->display('created_at');
            $form->display('updated_at');

            // 禁止新增、编辑、删除
            $form->disableCreateButton();
            $form->disableDeleteButton();
            $form->disableSubmitButton();
        });
    }
}
