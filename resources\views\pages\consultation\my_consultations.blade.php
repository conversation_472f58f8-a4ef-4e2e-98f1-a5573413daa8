@extends('layouts.app')

@section('title', '我的留言')

@section('content')
<div class="consultation-container">
    <!-- 页面标题 -->
    <div class="page-header">
        <div class="header-content">
            <h1>我的留言</h1>
            <p>查看您的留言记录和回复</p>
        </div>
        <a href="{{ route('message_consultation.index') }}" class="new-btn">
            新建留言
        </a>
    </div>

    @if($consultations->count() > 0)
        <!-- 留言列表 -->
        <div class="consultations-list">
            @foreach($consultations as $consultation)
            <div class="consultation-item">
                <!-- 留言头部 -->
                <div class="consultation-header" onclick="toggleConsultation({{ $consultation->id }})">
                    <div class="header-main">
                        <h3 class="subject">{{ $consultation->subject }}</h3>
                        <div class="content-preview">{{ Str::limit($consultation->content, 60) }}</div>
                        <div class="meta-info">
                            <span>{{ $consultation->created_at->format('m-d H:i') }}</span>
                            @if($consultation->replied_at)
                            <span>已回复</span>
                            @endif
                            <span>{{ $consultation->replies->count() }}条回复</span>
                        </div>
                    </div>
                    <div class="header-side">
                        <span class="status-badge status-{{ $consultation->status }}">
                            {{ $consultation->status_text }}
                        </span>
                        <i class="fas fa-chevron-down toggle-icon"></i>
                    </div>
                </div>

                <!-- 留言详情（默认隐藏） -->
                <div class="consultation-detail" id="detail-{{ $consultation->id }}">
                    <!-- 原始留言内容 -->
                    <div class="original-content">
                        <div class="section-title">原始留言：</div>
                        <div class="content-text">{{ $consultation->content }}</div>
                        @if($consultation->phone || $consultation->email)
                        <div class="contact-info">
                            @if($consultation->phone)
                            <span>电话：{{ $consultation->phone }}</span>
                            @endif
                            @if($consultation->email)
                            <span>邮箱：{{ $consultation->email }}</span>
                            @endif
                        </div>
                        @endif
                    </div>

                    <!-- 回复列表 -->
                    @if($consultation->replies->count() > 0)
                    <div class="replies-section">
                        <div class="section-title">回复记录：</div>
                        <div class="replies-list" id="replies-{{ $consultation->id }}">
                            @foreach($consultation->replies as $reply)
                            <div class="reply-item {{ $reply->is_admin_reply ? 'admin-reply' : 'user-reply' }}">
                                <div class="reply-header">
                                    <span class="author">{{ $reply->is_admin_reply ? ($reply->identity??'管理员') : '我' }}</span>
                                    <span class="time">{{ $reply->created_at->format('m-d H:i') }}</span>
                                </div>
                                <div class="reply-content">{{ $reply->content }}</div>
                            </div>
                            @endforeach
                        </div>
                    </div>
                    @endif

                    <!-- 回复表单 -->
                    @if($consultation->status !== 'closed')
                    <div class="reply-form-section">
                        <form class="reply-form" data-consultation-id="{{ $consultation->id }}">
                            @csrf
                            <div class="form-group">
                                <label>添加回复：</label>
                                <textarea name="content" rows="3" placeholder="请输入您的回复..." required></textarea>
                            </div>
                            <div class="form-actions">
                                <button type="submit">发送回复</button>
                            </div>
                        </form>
                    </div>
                    @endif
                </div>
            </div>
            @endforeach
        </div>

        <!-- 分页 -->
        <div class="pagination-wrapper">
            {{ $consultations->links() }}
        </div>
    @else
        <!-- 空状态 -->
        <div class="empty-state">
            <i class="fas fa-comments"></i>
            <h3>暂无留言记录</h3>
            <p>您还没有提交过任何留言</p>
            <a href="{{ route('message_consultation.index') }}" class="create-btn">
                立即留言
            </a>
        </div>
    @endif
</div>

<style>
.consultation-container {
    max-width: 100%;
    margin: 0;
    padding: 15px;
    background: #f5f5f5;
    min-height: 100vh;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.header-content h1 {
    font-size: 20px;
    color: #333;
    margin: 0 0 5px 0;
    font-weight: 600;
}

.header-content p {
    font-size: 14px;
    color: #666;
    margin: 0;
}

.new-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-decoration: none;
    padding: 10px 16px;
    border-radius: 20px;
    font-size: 14px;
    white-space: nowrap;
}

.consultations-list {
    space-y: 15px;
}

.consultation-item {
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    margin-bottom: 15px;
    overflow: hidden;
}

.consultation-header {
    padding: 15px;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.consultation-header:active {
    background: #f8f9fa;
}

.header-main {
    flex: 1;
    min-width: 0;
}

.subject {
    font-size: 16px;
    color: #333;
    margin: 0 0 8px 0;
    font-weight: 600;
    line-height: 1.4;
}

.content-preview {
    color: #666;
    font-size: 14px;
    margin-bottom: 10px;
    line-height: 1.4;
}

.meta-info {
    display: flex;
    gap: 12px;
    font-size: 12px;
    color: #999;
    flex-wrap: wrap;
}

.header-side {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 8px;
    margin-left: 15px;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    white-space: nowrap;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
}

.status-replied {
    background: #d4edda;
    color: #155724;
}

.status-closed {
    background: #f8d7da;
    color: #721c24;
}

.toggle-icon {
    color: #ccc;
    transition: transform 0.3s;
    font-size: 12px;
}

.toggle-icon.rotated {
    transform: rotate(180deg);
}

.consultation-detail {
    display: none;
}

.original-content {
    padding: 15px;
    border-bottom: 1px solid #f0f0f0;
    background: #fafafa;
}

.section-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
    font-size: 14px;
}

.content-text {
    color: #666;
    line-height: 1.6;
    white-space: pre-wrap;
    font-size: 14px;
}

.contact-info {
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid #eee;
    font-size: 12px;
    color: #999;
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.replies-section {
    padding: 15px;
}

.replies-list {
    margin-top: 10px;
}

.reply-item {
    margin-bottom: 12px;
    padding: 12px;
    border-radius: 8px;
    font-size: 14px;
}

.admin-reply {
    background: #e8f4fd;
    border-left: 3px solid #2196F3;
}

.user-reply {
    background: #f0f8ff;
    border-left: 3px solid #667eea;
}

.reply-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 6px;
}

.author {
    font-weight: 600;
    font-size: 13px;
}

.admin-reply .author {
    color: #2196F3;
}

.user-reply .author {
    color: #667eea;
}

.time {
    font-size: 11px;
    color: #999;
}

.reply-content {
    color: #333;
    line-height: 1.5;
    white-space: pre-wrap;
}

.reply-form-section {
    padding: 15px;
    margin-bottom:30px;
    border-top: 1px solid #f0f0f0;
    background: #fafafa;
}

.form-group {
    margin-bottom: 12px;
}

.form-group label {
    display: block;
    margin-bottom: 6px;
    color: #333;
    font-weight: 500;
    font-size: 14px;
}

.form-group textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    resize: vertical;
    box-sizing: border-box;
}

.form-group textarea:focus {
    border-color: #667eea;
    outline: none;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.form-actions {
    text-align: right;
}

.form-actions button {
    background: #667eea;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 16px;
    font-size: 14px;
    cursor: pointer;
}

.form-actions button:active {
    background: #5a6fd8;
}

.form-actions button:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.pagination-wrapper {
    margin-top: 20px;
    text-align: center;
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.empty-state i {
    font-size: 48px;
    color: #ddd;
    margin-bottom: 20px;
}

.empty-state h3 {
    color: #666;
    margin-bottom: 10px;
    font-size: 18px;
}

.empty-state p {
    color: #999;
    margin-bottom: 30px;
    font-size: 14px;
}

.create-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-decoration: none;
    padding: 12px 24px;
    border-radius: 20px;
    font-size: 16px;
    display: inline-block;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .consultation-container {
        padding: 10px;
    }
    
    .page-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .header-content {
        width: 100%;
    }
    
    .consultation-header {
        flex-direction: column;
        gap: 12px;
    }
    
    .header-side {
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        margin-left: 0;
    }
    
    .meta-info {
        justify-content: center;
    }
    
    .contact-info {
        flex-direction: column;
        gap: 8px;
    }
}

@media (max-width: 480px) {
    .subject {
        font-size: 15px;
    }
    
    .content-preview {
        font-size: 13px;
    }
    
    .meta-info {
        font-size: 11px;
        gap: 8px;
    }
}
</style>

<script>
function toggleConsultation(id) {
    const detail = document.getElementById('detail-' + id);
    const icon = detail.previousElementSibling.querySelector('.toggle-icon');
    
    if (detail.style.display === 'none' || detail.style.display === '') {
        detail.style.display = 'block';
        icon.classList.add('rotated');
    } else {
        detail.style.display = 'none';
        icon.classList.remove('rotated');
    }
}

document.addEventListener('DOMContentLoaded', function() {
    // 处理回复表单提交
    document.querySelectorAll('.reply-form').forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const consultationId = this.dataset.consultationId;
            const formData = new FormData(this);
            const submitBtn = this.querySelector('button[type="submit"]');
            const textarea = this.querySelector('textarea');
            
            // 禁用提交按钮
            submitBtn.disabled = true;
            submitBtn.textContent = '发送中...';
            
            fetch(`/message-consultation/${consultationId}/reply`, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 清空表单
                    textarea.value = '';
                    
                    // 添加新回复到列表
                    const repliesList = document.getElementById('replies-' + consultationId);
                    const newReply = document.createElement('div');
                    newReply.className = 'reply-item user-reply';
                    newReply.innerHTML = `
                        <div class="reply-header">
                            <span class="author">我</span>
                            <span class="time">${data.reply.created_at}</span>
                        </div>
                        <div class="reply-content">${data.reply.content}</div>
                    `;
                    repliesList.appendChild(newReply);
                    
                    alert('回复成功');
                } else {
                    alert(data.message || '回复失败，请重试');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('回复失败，请检查网络连接后重试');
            })
            .finally(() => {
                // 恢复提交按钮
                submitBtn.disabled = false;
                submitBtn.textContent = '发送回复';
            });
        });
    });
});
</script>
@endsection 