<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Carbon\Carbon;
use Carbon\CarbonPeriod;
use App\Models\Counselor;
use App\Models\CounselorSchedule;

class GenerateAllCounselorSchedules extends Command
{
    protected $signature = 'generate:all-counselor-schedules
                            {start_date : 开始日期 YYYY-MM-DD}
                            {end_date   : 结束日期 YYYY-MM-DD}
                            {--start_time=00:00 : 当天起始时间 HH:MM}
                            {--end_time=23:59   : 当天结束时间 HH:MM}';

    protected $description = '每天仅生成一条 00:00~23:59 的排班；可用参数覆盖开始/结束时间';

    public function handle(): int
    {
        $startDate = Carbon::parse($this->argument('start_date'))->startOfDay();
        $endDate   = Carbon::parse($this->argument('end_date'))->startOfDay();
        $startTime = $this->option('start_time');   // 默认 00:00
        $endTime   = $this->option('end_time');     // 默认 23:59

        if ($endDate->lt($startDate)) {
            $this->error('结束日期不能早于开始日期。');
            return 1;
        }

        $counselorIds = Counselor::pluck('id')->all();   // 换成自己的 ID 列表也行
        if (!$counselorIds) {
            $this->warn('没有找到任何咨询师。');
            return 0;
        }

        $datePeriod = CarbonPeriod::create($startDate, $endDate);
        $bar = $this->output->createProgressBar(count($counselorIds));
        $bar->start();

        foreach ($counselorIds as $cid) {

            foreach ($datePeriod as $date) {
                $dateString = $date->toDateString();

                // 若已存在当天整段排班就跳过
                $exists = CounselorSchedule::where('counselor_id', $cid)
                    ->where('date',       $dateString)
                    ->where('start_time', $startTime.':00')   // 补秒
                    ->exists();

                if (! $exists) {
                    CounselorSchedule::create([
                        'counselor_id' => $cid,
                        'date'         => $dateString,
                        'start_time'   => $startTime.':00',
                        'end_time'     => $endTime.':59',   // 或 ':00' 看实际需求
                        'is_available' => true,
                    ]);
                }
            }

            $datePeriod->rewind();   // 重置日期指针
            $bar->advance();
        }

        $bar->finish();
        $this->newLine();
        $this->info('排班生成完成！');

        return 0;
    }
}
