<?php

namespace App\Services;

use App\Models\AssessmentResponse;
use App\Models\AssessmentAiSetting;
use Illuminate\Support\Facades\Http;

class AssessmentAiService
{
    /**
     * 调用 DeepSeek AI 进行心理测评分析，保存结果到 assessment_analyses 表
     */
    public function analyze(AssessmentResponse $response): void
    {
        $settings = AssessmentAiSetting::getSettings();
        if (! $settings) {
            return;
        }

        // 准备答题数据
        $answers = $response->answers->map(function ($ans) {
            return [
                'question_id'  => $ans->question_id,
                'option_id'    => $ans->option_id,
                'custom_value' => $ans->custom_value,
            ];
        })->toArray();

        // 构造消息
        $messages = [
            ['role' => 'system', 'content' => $settings->system_prompt],
            ['role' => 'user',   'content' => str_replace('{responses}', json_encode($answers), $settings->user_prompt_template)],
        ];

        // 请求载荷
        $payload = [
            'model'       => $settings->model,
            'messages'    => $messages,
            'temperature' => $settings->temperature,
            'max_tokens'  => $settings->max_tokens,
        ];

        // 发起 HTTP 请求
        $apiResponse = Http::withHeaders([
            'Authorization' => "Bearer {$settings->api_key}"
        ])->post($settings->endpoint, $payload);

        $body = $apiResponse->json();

        // 存储分析结果
        $response->analysis()->create([
            'overall_score' => $this->calcOverallScore($response),
            'detail_json'   => $body['choices'][0]['message']['content'] ?? '',
        ]);
    }

    /**
     * 计算总分
     */
    protected function calcOverallScore(AssessmentResponse $response): int
    {
        return $response->answers->sum(function ($ans) {
            return optional($ans->option)->score_value ?? 0;
        });
    }
}
