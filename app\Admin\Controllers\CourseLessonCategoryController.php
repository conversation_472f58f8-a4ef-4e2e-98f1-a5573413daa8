<?php

namespace App\Admin\Controllers;

use App\Models\CourseLessonCategory;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class CourseLessonCategoryController extends AdminController
{
    /**
     * 设置标题
     *
     * @return string
     */
    protected $title = '心理课堂分类';

    /**
     * 列表页
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new CourseLessonCategory(), function (Grid $grid) {
            $grid->column('id')->sortable();
            $grid->column('name', '分类名称');
            $grid->column('icon', '图标')->image('', 50, 50);
            $grid->column('description', '描述');
            $grid->column('sort', '排序')->sortable();
            $grid->column('is_active', '是否启用')->switch();
            $grid->column('created_at', '创建时间');
            $grid->column('updated_at', '更新时间');

            $grid->filter(function (Grid\Filter $filter) {
                $filter->panel();

                $filter->equal('id');
                $filter->like('name', '分类名称');
                $filter->equal('is_active', '是否启用')->select([
                    1 => '是',
                    0 => '否',
                ]);
            });

            $grid->quickSearch('name');

            // 启用弹窗编辑功能
            $grid->showQuickEditButton();

            // 正确的弹窗实现方式
            $grid->enableDialogCreate();
        });
    }

    /**
     * 详情页
     *
     * @param mixed $id
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new CourseLessonCategory(), function (Show $show) {
            $show->field('id');
            $show->field('name', '分类名称');
            $show->field('icon', '图标')->image();
            $show->field('description', '描述');
            $show->field('sort', '排序');
            $show->field('is_active', '是否启用')->as(function ($value) {
                return $value ? '是' : '否';
            });
            $show->field('created_at', '创建时间');
            $show->field('updated_at', '更新时间');

            $show->lessons('关联课程', function ($model) {
                $grid = new Grid(new \App\Models\CourseLesson());

                $grid->model()->where('category_id', $model->id);

                $grid->column('id')->sortable();
                $grid->column('title', '标题');
                $grid->column('status', '状态')->using(\App\Models\CourseLesson::getStatusMap());
                $grid->column('publish_time', '发布时间');

                $grid->disableCreateButton();
                $grid->disableActions();
                $grid->disableBatchDelete();
                $grid->disableRowSelector();

                return $grid;
            });
        });
    }

    /**
     * 表单页
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new CourseLessonCategory(), function (Form $form) {
            $form->display('id');
            $form->text('name', '分类名称')->required();
            $form->image('icon', '图标')->autoUpload()->uniqueName();
            $form->textarea('description', '描述');
            $form->number('sort', '排序')->default(0)->help('数字越小排序越靠前');
            $form->switch('is_active', '是否启用')->default(true);

            $form->display('created_at', '创建时间');
            $form->display('updated_at', '更新时间');
        });
    }
}
