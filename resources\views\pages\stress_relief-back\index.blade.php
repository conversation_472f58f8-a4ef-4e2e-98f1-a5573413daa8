@extends('layouts.app')

@section('title', '心理减压 - 身心健康平台')

@section('content')
<div class="stress-relief-container">
    <!-- 页面头部 - 自然融合状态栏区域 -->
    <div class="page-header">
        <div class="container">
            <div class="header-content">
                <h1 class="page-title">心理减压</h1>
                <p class="page-subtitle">科学减压方法，释放身心压力，重获内心平静</p>
            </div>
        </div>
    </div>

    <!-- 减压服务模块 -->
    <div class="stress-relief-services">
        <div class="container">
            <div class="services-grid">
                <!-- 睡眠调整 -->
                <div class="service-card" onclick="window.location.href='{{ route('stress_relief.sleep') }}'">
                    <div class="service-icon sleep">
                        <i class="fas fa-moon"></i>
                    </div>
                    <h3 class="service-title">睡眠调整</h3>
                    <p class="service-desc">专业睡眠管理，与您一起入睡</p>
                    <div class="service-stats">
                        <span class="stats-text">累计使用人次 823</span>
                    </div>
                </div>

                <!-- 呼吸训练 -->
                <div class="service-card" onclick="window.location.href='{{ route('stress_relief.breathing') }}'">
                    <div class="service-icon breathing">
                        <i class="fas fa-wind"></i>
                    </div>
                    <h3 class="service-title">呼吸训练</h3>
                    <p class="service-desc">吐纳之间，感受内缓的平静与放松</p>
                    <div class="service-stats">
                        <span class="stats-text">累计使用人次 950</span>
                    </div>
                </div>

                <!-- 音乐疗愈 -->
                <div class="service-card" onclick="window.location.href='{{ route('stress_relief.music') }}'">
                    <div class="service-icon music">
                        <i class="fas fa-music"></i>
                    </div>
                    <h3 class="service-title">音乐疗愈</h3>
                    <p class="service-desc">精选治愈音乐，助力身心健康</p>
                    <div class="service-stats">
                        <span class="stats-text">累计使用人次 597</span>
                    </div>
                </div>

                <!-- 正念冥想 -->
                <div class="service-card" onclick="window.location.href='{{ route('stress_relief.meditation') }}'">
                    <div class="service-icon meditation">
                        <i class="fas fa-lotus-position"></i>
                    </div>
                    <h3 class="service-title">正念冥想</h3>
                    <p class="service-desc">正念内观，清净入耳，宁静入心</p>
                    <div class="service-stats">
                        <span class="stats-text">累计使用人次 596</span>
                    </div>
                </div>

                <!-- 大脑按摩 -->
                <div class="service-card" onclick="window.location.href='{{ route('stress_relief.brain_massage') }}'">
                    <div class="service-icon brain">
                        <i class="fas fa-brain"></i>
                    </div>
                    <h3 class="service-title">大脑按摩</h3>
                    <p class="service-desc">ASMR，为你打造一个治愈专注的空间</p>
                    <div class="service-stats">
                        <span class="stats-text">累计使用人次 524</span>
                    </div>
                </div>

                <!-- 影视欣赏 -->
                <div class="service-card" onclick="window.location.href='{{ route('stress_relief.video') }}'">
                    <div class="service-icon video">
                        <i class="fas fa-video"></i>
                    </div>
                    <h3 class="service-title">影视欣赏</h3>
                    <p class="service-desc">治愈系短片，让你感受心的成长</p>
                    <div class="service-stats">
                        <span class="stats-text">累计使用人次 354</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.stress-relief-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    /* 自然处理安全区域，无需单独占位 */
    padding-top: env(safe-area-inset-top, 0px);
    padding-bottom: env(safe-area-inset-bottom, 60px); /* 底部导航栏高度 */
}

/* 覆盖全局容器样式以适应心理减压页面 */
.stress-relief-container .container {
    max-width: none;
    width: 100%;
    padding: 0 15px;
    background-color: transparent !important;
}

/* 页面头部 - 更自然的设计 */
.page-header {
    padding: 6rem 0 2rem; /* 增加顶部内边距自然处理状态栏区域 */
    text-align: center;
    position: relative;
}

/* 为页面头部添加装饰性背景元素 */
.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 100%;
    background: radial-gradient(ellipse at top, rgba(255,255,255,0.1) 0%, transparent 50%);
    pointer-events: none;
}

.header-content {
    color: white;
    position: relative;
    z-index: 1;
}

.page-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 0 2px 10px rgba(0,0,0,0.3);
}

.page-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
}

/* 服务模块 */
.stress-relief-services {
    padding: 3rem 0 2rem; /* 减少底部内边距 */
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.service-card {
    background: white;
    border-radius: 20px;
    padding: 2.5rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--card-color);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.service-card:hover::before {
    transform: scaleX(1);
}

.service-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.service-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: white;
    position: relative;
}

.service-icon.sleep {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --card-color: #667eea;
}

.service-icon.breathing {
    background: linear-gradient(135deg, #06beb6 0%, #48b1bf 100%);
    --card-color: #06beb6;
}

.service-icon.music {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    --card-color: #ff6b6b;
}

.service-icon.meditation {
    background: linear-gradient(135deg, #a855f7 0%, #8b5cf6 100%);
    --card-color: #a855f7;
}

.service-icon.brain {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --card-color: #f093fb;
}

.service-icon.video {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --card-color: #4facfe;
}

.service-card:nth-child(1) {
    --card-color: #667eea;
}

.service-card:nth-child(2) {
    --card-color: #06beb6;
}

.service-card:nth-child(3) {
    --card-color: #ff6b6b;
}

.service-card:nth-child(4) {
    --card-color: #a855f7;
}

.service-card:nth-child(5) {
    --card-color: #f093fb;
}

.service-card:nth-child(6) {
    --card-color: #4facfe;
}

.service-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.service-desc {
    color: #7f8c8d;
    line-height: 1.6;
    margin-bottom: 2rem;
    font-size: 1rem;
}

.service-stats {
    position: absolute;
    bottom: 1.5rem;
    left: 50%;
    transform: translateX(-50%);
}

.stats-text {
    font-size: 0.85rem;
    color: #bdc3c7;
    font-weight: 500;
}

/* 移动端适配 */
@media (max-width: 500px) {
    .stress-relief-container {
        padding-top: calc(env(safe-area-inset-top, 0px) + 10px);
        padding-bottom: calc(env(safe-area-inset-bottom, 0px) + 60px);
    }
    
    .stress-relief-container .container {
        max-width: 100%;
        width: 100%;
        padding: 0;
        margin: 0;
    }
    
    .page-header {
        padding: 3rem 15px 1.5rem;
    }
    
    .page-title {
        font-size: 2rem;
    }
    
    .page-subtitle {
        font-size: 1rem;
        padding: 0 10px;
    }
    
    .stress-relief-services {
        padding: 1.5rem 15px 1rem;
    }
    
    .services-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
        max-width: none;
        margin: 0;
        margin-top: 15px;
    }
    
    .service-card {
        padding: 1.5rem 1rem;
    }
    
    .service-icon {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
        margin-bottom: 1rem;
    }
    
    .service-title {
        font-size: 1rem;
        margin-bottom: 0.5rem;
    }
    
    .service-desc {
        font-size: 0.75rem;
        margin-bottom: 1.5rem;
        line-height: 1.4;
    }
    
    .service-stats {
        bottom: 0.8rem;
    }
    
    .stats-text {
        font-size: 0.7rem;
    }
}

/* 超小屏幕适配 */
@media (max-width: 360px) {
    .services-grid {
        gap: 10px;
    }
    
    .service-card {
        padding: 1rem 0.8rem;
    }
    
    .service-title {
        font-size: 0.9rem;
    }
    
    .service-desc {
        font-size: 0.7rem;
    }
}
</style>
@endsection 