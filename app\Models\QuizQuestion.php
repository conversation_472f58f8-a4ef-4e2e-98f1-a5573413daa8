<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class QuizQuestion extends Model
{
    use HasFactory, HasDateTimeFormatter;

    protected $fillable = [
        'quiz_activity_id',
        'question_text',
        'question_type',
        'explanation',
        'points',
        'order',
    ];

    // 关联问答活动
    public function quizActivity()
    {
        return $this->belongsTo(QuizActivity::class);
    }

    // 关联选项
    public function options()
    {
        return $this->hasMany(QuizOption::class, 'question_id')->orderBy('order');
    }

    // 获取正确选项
    public function correctOptions()
    {
        return $this->options()->where('is_correct', true)->get();
    }

    // 检查答案是否正确
    public function checkAnswer($userAnswer)
    {
        if ($this->question_type === 'true_false' || $this->question_type === 'single_choice') {
            // 单选题或是非题
            $correctOption = $this->options()->where('is_correct', true)->first();
            return $correctOption && $correctOption->id == $userAnswer;
        } else {
            // 多选题
            $correctOptionIds = $this->options()->where('is_correct', true)->pluck('id')->toArray();
            $userAnswerArray = is_array($userAnswer) ? $userAnswer : [$userAnswer];

            // 所选数量与正确答案数量必须相同，且所选都必须正确
            return count($correctOptionIds) === count($userAnswerArray) &&
                   count(array_diff($correctOptionIds, $userAnswerArray)) === 0;
        }
    }
}
