<script>
Dcat.ready(function () {
    // 提交审核按钮点击事件
    $('.submit-for-review').on('click', function () {
        var videoId = $(this).data('id');
        
        Dcat.confirm('确定要提交此视频进行审核吗？', function () {
            // 提交审核
            $.ajax({
                url: Dcat.helpers.url('videos/' + videoId + '/submit-review'),
                type: 'POST',
                data: {
                    _token: Dcat.token
                },
                success: function (data) {
                    if (data.status) {
                        Dcat.success(data.message);
                        
                        // 延迟跳转
                        setTimeout(function () {
                            location.reload();
                        }, 1500);
                    } else {
                        Dcat.error(data.message);
                    }
                },
                error: function () {
                    Dcat.error('服务器错误，请稍后再试');
                }
            });
        });
    });
});
</script>
