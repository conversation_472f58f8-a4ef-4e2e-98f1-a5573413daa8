@extends('layouts.app')

@section('title', '影视欣赏 - 心理减压')

@section('content')
<div class="entertainment-container">
    <!-- 顶部状态栏占位 -->
    <div class="status-bar-spacer"></div>
    
    <!-- 页面头部 -->
    <div class="page-header">
        <div class="container">
            <div class="header-content">
                <div class="breadcrumb">
                    <a href="{{ route('stress_relief.index') }}">心理减压</a>
                    <span class="divider">/</span>
                    <span class="current">影视欣赏</span>
                </div>
                <h1 class="page-title">影视欣赏</h1>
                <p class="page-subtitle">精彩影片，治愈心灵，放松身心</p>
            </div>
        </div>
    </div>

    <div class="entertainment-categories">
        <div class="container">
            <div class="categories-tabs">
                <button class="tab-btn active" onclick="switchCategory('movies')">电影</button>
                <button class="tab-btn" onclick="switchCategory('documentaries')">纪录片</button>
                <button class="tab-btn" onclick="switchCategory('animations')">动画</button>
                <button class="tab-btn" onclick="switchCategory('shorts')">短片</button>
            </div>

            <div class="content-grid" id="content-grid">
                <!-- 动态内容 -->
            </div>
        </div>
    </div>

    <!-- 底部导航栏占位 -->
    <div class="navbar-spacer"></div>
</div>

<div id="video-modal" class="video-modal" style="display: none;">
    <div class="modal-overlay"></div>
    <div class="modal-content">
        <div class="modal-header">
            <h3 id="video-title">影视欣赏</h3>
            <button class="close-btn" onclick="closeVideoModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body">
            <div class="video-placeholder">
                <div class="play-icon">
                    <i class="fas fa-play"></i>
                </div>
                <p>点击播放影片</p>
            </div>
            <div class="video-info">
                <p id="video-description">影片简介...</p>
                <div class="video-tags" id="video-tags">
                    <!-- 标签 -->
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.entertainment-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    /* 自然处理安全区域 */
    padding-top: calc(env(safe-area-inset-top, 0px) + 20px);
    padding-bottom: calc(env(safe-area-inset-bottom, 0px) + 80px);
}

/* 状态栏和导航栏占位 */
.status-bar-spacer {
    height: env(safe-area-inset-top, 0px);
    background: transparent;
}

.navbar-spacer {
    height: 50px;
    background: transparent;
}

/* 覆盖全局容器样式 */
.entertainment-container .container {
    max-width: none;
    width: 100%;
    padding: 0 15px;
    background-color: transparent !important;
    box-sizing: border-box;
}

.entertainment-content {
    padding: 3rem 0 2rem;
    width: 100%;
    box-sizing: border-box;
}

.entertainment-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
    box-sizing: border-box;
    width: 100%;
}

/* 页面头部 - 自然设计，确保足够空间给卡片悬浮效果 */
.page-header {
    padding: 6rem 0 4rem;
    color: white;
    position: relative;
    background: transparent;
}

/* 装饰性背景 */
.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 100%;
    background: radial-gradient(ellipse at top, rgba(255,255,255,0.1) 0%, transparent 50%);
    pointer-events: none;
}

.header-content {
    position: relative;
    z-index: 1;
    text-align: center;
}

.breadcrumb {
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.breadcrumb a {
    color: rgba(255,255,255,0.8);
    text-decoration: none;
}

.breadcrumb a:hover {
    color: white;
}

.divider {
    margin: 0 0.5rem;
    opacity: 0.6;
}

.current {
    color: white;
}

.page-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 0 2px 10px rgba(0,0,0,0.3);
}

.page-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
}

/* 娱乐分类 */
.entertainment-categories {
    padding: 3rem 0;
}

.categories-tabs {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 3rem;
    padding: 2rem 0;
}

.tab-btn {
    background: rgba(255,255,255,0.2);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.tab-btn:hover, .tab-btn.active {
    background: rgba(255,255,255,0.3);
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.1);
}

.content-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    width: 100% !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    padding: 0 !important;
    box-sizing: border-box;
}

.content-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
    cursor: pointer;
}

.content-card:hover {
    transform: translateY(-5px);
}

.content-poster {
    height: 200px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: white;
    position: relative;
}

.play-icon {
    position: absolute;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: rgba(255,255,255,0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    transition: all 0.3s ease;
}

.content-poster:hover .play-icon {
    background: rgba(255,255,255,0.3);
    transform: scale(1.1);
}

.content-info {
    padding: 1.5rem;
}

.content-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.content-desc {
    color: #7f8c8d;
    line-height: 1.6;
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.content-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.8rem;
    color: #95a5a6;
}

.content-duration {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
}

/* 视频弹窗 */
.video-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.9);
    backdrop-filter: blur(10px);
}

.modal-content {
    position: relative;
    background: linear-gradient(135d, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    width: 90%;
    max-width: 600px;
    overflow: hidden;
    color: white;
}

.modal-header {
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.modal-header h3 {
    margin: 0;
    font-size: 1.3rem;
}

.close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background 0.3s ease;
}

.close-btn:hover {
    background: rgba(255,255,255,0.2);
}

.modal-body {
    padding: 2rem;
}

.video-placeholder {
    width: 100%;
    height: 250px;
    background: rgba(0,0,0,0.3);
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
}

.play-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: rgba(255,255,255,0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    margin-bottom: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.play-icon:hover {
    background: rgba(255,255,255,0.3);
    transform: scale(1.1);
}

#video-description {
    line-height: 1.6;
    margin-bottom: 1rem;
    opacity: 0.9;
}

.video-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.video-tag {
    background: rgba(255,255,255,0.2);
    color: white;
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
}

@media (max-width: 768px) {
    .entertainment-container {
        padding-top: calc(env(safe-area-inset-top, 0px) + 15px);
        padding-bottom: calc(env(safe-area-inset-bottom, 0px) + 80px);
    }
    
    .entertainment-container .container {
        max-width: 100% !important;
        width: 100% !important;
        padding: 0 10px !important;
        margin: 0 !important;
        background-color: transparent !important;
    }
    
    .page-title {
        font-size: 2.5rem;
    }
    
    .categories-tabs {
        flex-wrap: wrap;
        gap: 0.5rem;
    }
    
    .tab-btn {
        font-size: 0.9rem;
        padding: 0.8rem 1.5rem;
    }
    
    .content-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        padding: 0 !important;
        margin: 0 !important;
        width: 100% !important;
    }
    
    .modal-content {
        width: 95%;
        margin: 1rem;
    }
    
    .video-placeholder {
        height: 200px;
    }
}

/* 移动端适配 - 覆盖桌面端样式 */
@media (max-width: 500px) {
    .entertainment-container {
        padding-top: calc(env(safe-area-inset-top, 0px) + 10px);
        padding-bottom: calc(env(safe-area-inset-bottom, 0px) + 80px);
        width: 100%;
        overflow-x: hidden;
    }
    
    /* 针对移动端框架的容器修正 */
    .entertainment-container .container {
        max-width: 100% !important;
        width: 100% !important;
        padding: 0 10px !important;
        margin: 0 !important;
        background-color: transparent !important;
        box-sizing: border-box !important;
    }
    
    .page-header {
        padding: 1rem 0 1.5rem;
        width: 100%;
        box-sizing: border-box;
    }
    
    .page-title {
        font-size: 2rem;
    }
    
    .page-subtitle {
        font-size: 1rem;
    }
    
    .entertainment-content {
        padding: 1.5rem 0 2rem;
        width: 100%;
        box-sizing: border-box;
    }
    
    .entertainment-grid {
        grid-template-columns: 1fr;
        gap: 15px;
        padding: 0 !important;
        margin: 0 !important;
        width: 100% !important;
        max-width: 100% !important;
        box-sizing: border-box !important;
    }
    
    .entertainment-card {
        padding: 1.5rem 1rem;
        margin-bottom: 15px;
        width: 100%;
        box-sizing: border-box;
    }
    
    .card-header {
        margin-bottom: 1rem;
        width: 100%;
        box-sizing: border-box;
    }
    
    .card-icon {
        width: 50px;
        height: 50px;
        font-size: 1.3rem;
        margin-bottom: 1rem;
    }
    
    .card-title {
        font-size: 1.1rem;
        margin-bottom: 0.5rem;
    }
    
    .card-content {
        margin-bottom: 1.5rem;
        width: 100%;
        box-sizing: border-box;
    }
    
    .video-player {
        border-radius: 15px;
        overflow: hidden;
        background: #000;
        height: 200px;
        width: 100%;
        box-sizing: border-box;
    }
    
    .play-btn {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }
    
    .content-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
        padding: 0 !important;
        margin: 0 !important;
        width: 100% !important;
    }
    
    .content-card {
        transition: transform 0.2s ease;
    }
    
    .content-card:hover {
        transform: translateY(-3px);
    }
    
    .content-poster {
        height: 120px;
        font-size: 2rem;
    }
    
    .content-info {
        padding: 1rem 0.8rem;
    }
    
    .content-title {
        font-size: 1rem;
        margin-bottom: 0.3rem;
    }
    
    .content-desc {
        font-size: 0.75rem;
        line-height: 1.4;
        margin-bottom: 0.8rem;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
    
    .content-meta {
        font-size: 0.7rem;
    }
    
    .content-duration {
        padding: 0.2rem 0.6rem;
        font-size: 0.7rem;
    }
    
    .video-placeholder {
        height: 150px;
        margin-bottom: 1rem;
    }
    
    .play-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
    }
    
    .modal-body {
        padding: 1.5rem;
    }
    
    #video-description {
        font-size: 0.9rem;
        margin-bottom: 0.8rem;
    }
    
    .video-tag {
        font-size: 0.7rem;
        padding: 0.2rem 0.6rem;
    }
}

/* 超小屏幕适配 */
@media (max-width: 360px) {
    .content-grid {
        gap: 10px;
    }
    
    .content-poster {
        height: 100px;
        font-size: 1.5rem;
    }
    
    .content-info {
        padding: 0.8rem 0.6rem;
    }
    
    .content-title {
        font-size: 0.9rem;
    }
    
    .content-desc {
        font-size: 0.7rem;
    }
}
</style>

<script>
const contentData = {
    movies: [
        {
            title: '千与千寻',
            desc: '宫崎骏经典作品，讲述少女千寻在神秘世界的冒险',
            icon: 'fas fa-film',
            duration: '125分钟',
            year: '2001',
            tags: ['治愈', '奇幻', '成长']
        },
        {
            title: '心灵捕手',
            desc: '关于心理治疗与自我救赎的温暖故事',
            icon: 'fas fa-heart',
            duration: '126分钟',
            year: '1997',
            tags: ['励志', '治愈', '成长']
        },
        {
            title: '放牛班的春天',
            desc: '音乐老师用爱心改变孩子们命运的感人故事',
            icon: 'fas fa-music',
            duration: '97分钟',
            year: '2004',
            tags: ['音乐', '温情', '教育']
        },
        {
            title: '美丽人生',
            desc: '在艰难岁月中保持乐观的父爱故事',
            icon: 'fas fa-smile',
            duration: '116分钟',
            year: '1997',
            tags: ['感动', '父爱', '希望']
        }
    ],
    documentaries: [
        {
            title: '地球脉动',
            desc: 'BBC自然纪录片，展现地球壮美风光',
            icon: 'fas fa-globe',
            duration: '50分钟×11集',
            year: '2006',
            tags: ['自然', '治愈', '科普']
        },
        {
            title: '蓝色星球',
            desc: '深入海洋世界，探索神秘的蓝色星球',
            icon: 'fas fa-water',
            duration: '50分钟×8集',
            year: '2001',
            tags: ['海洋', '自然', '放松']
        },
        {
            title: '人类星球',
            desc: '展现人类与自然和谐共处的智慧',
            icon: 'fas fa-users',
            duration: '60分钟×8集',
            year: '2011',
            tags: ['人文', '自然', '思考']
        }
    ],
    animations: [
        {
            title: '龙猫',
            desc: '宫崎骏温馨作品，童真与自然的美好',
            icon: 'fas fa-paw',
            duration: '86分钟',
            year: '1988',
            tags: ['童真', '治愈', '家庭']
        },
        {
            title: '疯狂动物城',
            desc: '动物世界的励志成长故事',
            icon: 'fas fa-rabbit',
            duration: '108分钟',
            year: '2016',
            tags: ['励志', '幽默', '成长']
        },
        {
            title: '寻梦环游记',
            desc: '关于家庭、梦想与传承的温暖故事',
            icon: 'fas fa-guitar',
            duration: '105分钟',
            year: '2017',
            tags: ['音乐', '家庭', '梦想']
        }
    ],
    shorts: [
        {
            title: '落叶',
            desc: '日本短片，讲述秋日的温柔与离别',
            icon: 'fas fa-leaf',
            duration: '15分钟',
            year: '2018',
            tags: ['唯美', '治愈', '季节']
        },
        {
            title: '雨后',
            desc: '雨后彩虹的希望与新生',
            icon: 'fas fa-rainbow',
            duration: '12分钟',
            year: '2019',
            tags: ['希望', '自然', '治愈']
        },
        {
            title: '小确幸',
            desc: '生活中的小美好瞬间',
            icon: 'fas fa-coffee',
            duration: '8分钟',
            year: '2020',
            tags: ['生活', '温暖', '日常']
        }
    ]
};

let currentCategory = 'movies';

function switchCategory(category) {
    currentCategory = category;
    
    // 更新按钮状态
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    event.target.classList.add('active');
    
    // 更新内容
    renderContent(category);
}

function renderContent(category) {
    const grid = document.getElementById('content-grid');
    const data = contentData[category];
    
    grid.innerHTML = data.map(item => `
        <div class="content-card" onclick="openVideoModal('${item.title}', '${item.desc}', '${JSON.stringify(item.tags).replace(/"/g, '&quot;')}')">
            <div class="content-poster">
                <i class="${item.icon}"></i>
            </div>
            <div class="content-info">
                <h3 class="content-title">${item.title}</h3>
                <p class="content-desc">${item.desc}</p>
                <div class="content-meta">
                    <span>${item.year}</span>
                    <span class="content-duration">${item.duration}</span>
                </div>
            </div>
        </div>
    `).join('');
}

function openVideoModal(title, desc, tagsJson) {
    const tags = JSON.parse(tagsJson.replace(/&quot;/g, '"'));
    
    document.getElementById('video-title').textContent = title;
    document.getElementById('video-description').textContent = desc;
    
    const tagsContainer = document.getElementById('video-tags');
    tagsContainer.innerHTML = tags.map(tag => 
        `<span class="video-tag">${tag}</span>`
    ).join('');
    
    document.getElementById('video-modal').style.display = 'flex';
}

function closeVideoModal() {
    document.getElementById('video-modal').style.display = 'none';
}

// 初始化页面
document.addEventListener('DOMContentLoaded', function() {
    renderContent('movies');
});
</script>
@endsection 