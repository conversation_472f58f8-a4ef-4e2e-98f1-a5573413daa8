<?php

use App\Http\Controllers\QuizController;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\KnowledgeController;
use App\Http\Controllers\ConsultationController;
use App\Http\Controllers\MessageController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\ConsultationMessageController;
use App\Http\Controllers\LiveShowController;
use Illuminate\Http\Request;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

// 认证相关路由
Route::middleware('guest')->group(function () {
    // 登录
    Route::get('/login', [AuthController::class, 'showLogin'])->name('login');
    Route::post('/login/password', [AuthController::class, 'loginWithPassword'])->name('login.password');
    Route::post('/login/code', [AuthController::class, 'loginWithCode'])->name('login.code');
    Route::post('/login/send-code', [AuthController::class, 'sendLoginCode'])->name('login.send_code');

    // 注册
    Route::get('/register', [AuthController::class, 'showRegister'])->name('register');
    Route::post('/register', [AuthController::class, 'register'])->name('register.submit');
    Route::post('/register/send-code', [AuthController::class, 'sendRegisterCode'])->name('register.send_code');
});

// 登出
Route::post('/logout', [AuthController::class, 'logout'])->name('logout');

// 保存intended URL
Route::post('/save-intended-url', function(Request $request) {
    if ($request->has('intended_url')) {
        session(['url.intended' => $request->intended_url]);
    }
    return response()->json(['success' => true]);
});

// 首页
Route::get('/', [HomeController::class, 'index'])->name('home');

// 心理减压模块
Route::prefix('stress-relief')->name('stress_relief.')->group(function () {
    // 心理减压首页
    Route::get('/', function () {
        return view('pages.stress_relief.index');
    })->name('index');
    
    // 睡眠调整
    Route::get('/sleep', function () {
        return view('pages.stress_relief.sleep');
    })->name('sleep');
    
    // 呼吸训练  
    Route::get('/breathing', function () {
        return view('pages.stress_relief.breathing'); // 复用冥想页面
    })->name('breathing');
    
    // 音乐疗愈
    Route::get('/music', function () {
        return view('pages.stress_relief.music');
    })->name('music');
    
    // 正念冥想
    Route::get('/meditation', function () {
        return view('pages.stress_relief.meditation');
    })->name('meditation');
    
    // 大脑按摩
    Route::get('/brain-massage', function () {
        return view('pages.stress_relief.massage');
    })->name('brain_massage');
    
    // 影视欣赏
    Route::get('/video', function () {
        return view('pages.stress_relief.entertainment');
    })->name('video');
});

// Route::get('/phpinfo', function () {
//     phpinfo();
// });

// 咨询师系统路由
Route::prefix('counselor')->name('counselor.')->group(function () {
    // 登录相关路由
    Route::get('/login', [App\Http\Controllers\Counselor\AuthController::class, 'showLogin'])->name('login');
    Route::post('/login', [App\Http\Controllers\Counselor\AuthController::class, 'login'])->name('login.post');
    Route::post('/logout', [App\Http\Controllers\Counselor\AuthController::class, 'logout'])->name('logout');

    // 忘记密码
    Route::get('/forgot-password', [App\Http\Controllers\Counselor\ProfileController::class, 'showForgotPassword'])->name('forgot_password');
    Route::post('/forgot-password/send-code', [App\Http\Controllers\Counselor\ProfileController::class, 'sendResetCode'])->name('forgot_password.send_code');
    Route::post('/forgot-password/reset', [App\Http\Controllers\Counselor\ProfileController::class, 'resetPassword'])->name('forgot_password.reset');

    // 需要咨询师登录的路由
    Route::middleware(['counselor.auth'])->group(function () {
        // 个人资料
        Route::get('/profile', [App\Http\Controllers\Counselor\ProfileController::class, 'show'])->name('profile');
        Route::post('/profile', [App\Http\Controllers\Counselor\ProfileController::class, 'update'])->name('profile.update');
        // 仪表盘
        Route::get('/', [App\Http\Controllers\Counselor\DashboardController::class, 'index'])->name('dashboard');

        // 预约管理
        Route::get('/appointments', [App\Http\Controllers\Counselor\AppointmentController::class, 'index'])->name('appointments');
        Route::post('/appointments/{id}/confirm', [App\Http\Controllers\Counselor\AppointmentController::class, 'confirm'])->name('appointments.confirm');
        Route::post('/appointments/{id}/reject', [App\Http\Controllers\Counselor\AppointmentController::class, 'reject'])->name('appointments.reject');
        Route::post('/appointments/{id}/start', [App\Http\Controllers\Counselor\AppointmentController::class, 'start'])->name('appointments.start');
        Route::post('/appointments/{id}/complete', [App\Http\Controllers\Counselor\AppointmentController::class, 'complete'])->name('appointments.complete');
        Route::post('/appointments/{id}/cancel', [App\Http\Controllers\Counselor\AppointmentController::class, 'cancel'])->name('appointments.cancel');

        // 消息管理
        Route::get('/messages', [App\Http\Controllers\Counselor\MessageController::class, 'index'])->name('messages');
        Route::get('/messages/{userId}', [App\Http\Controllers\Counselor\MessageController::class, 'show'])->name('messages.show');
        Route::post('/messages/{userId}/send', [App\Http\Controllers\Counselor\MessageController::class, 'send'])->name('messages.send');
        Route::get('/messages/unread/count', [App\Http\Controllers\Counselor\MessageController::class, 'getUnreadCount'])->name('messages.unread');
        Route::get('/messages/{userId}/check-new', [App\Http\Controllers\Counselor\MessageController::class, 'checkNewMessages'])->name('messages.check-new');
    });
});

// 课程页
Route::get('/courses', function () {
    return view('pages.courses');
})->name('courses');

// 学习页 - 显示用户的课程学习进度
Route::get('/study', [HomeController::class, 'study'])->name('study');

// 个人页
Route::get('/my', function () {
    return view('pages.my');
})->name('my');

// 用户个人管理相关路由
Route::middleware('auth')->prefix('user')->name('user.')->group(function () {
    // 个人资料
    Route::get('/profile', [HomeController::class, 'profile'])->name('profile');
    Route::post('/profile/update', [HomeController::class, 'updateProfile'])->name('profile.update');

    // 账号管理
    Route::get('/account', [HomeController::class, 'account'])->name('account');
    Route::post('/change-password', [HomeController::class, 'changePassword'])->name('change_password');

    // 设置
    Route::get('/settings', [HomeController::class, 'settings'])->name('settings');
    Route::post('/settings/update', [HomeController::class, 'updateSettings'])->name('settings.update');

});

// 帮助中心、联系我们和关于我们
Route::get('/help', [HomeController::class, 'help'])->name('help');
Route::get('/contact', [HomeController::class, 'contact'])->name('contact');
Route::post('/contact/submit', [HomeController::class, 'contactSubmit'])->name('contact.submit');
Route::get('/about', [HomeController::class, 'about'])->name('about');

// 心理科普模块
Route::prefix('knowledge')->name('knowledge.')->group(function () {
    // 心理文章
    Route::get('/articles', [KnowledgeController::class, 'articles'])->name('articles');
    Route::get('/articles/{id}', [KnowledgeController::class, 'articleDetail'])->name('article_detail');
    // 搜索文章
    Route::get('/search', [KnowledgeController::class, 'search'])->name('search');

    // 心理课堂
    Route::get('/courses', [KnowledgeController::class, 'courses'])->name('courses');
    Route::get('/courses/{id}', [KnowledgeController::class, 'courseDetail'])->name('course_detail');
    Route::post('/course_progress/{id}', [KnowledgeController::class, 'updateCourseProgress'])->name('course_progress');
    Route::post('/course_progress/{id}/reset', [KnowledgeController::class, 'resetCourseProgress'])->name('reset_course_progress');

    // 心理视频
    Route::get('/videos', [KnowledgeController::class, 'videos'])->name('videos');
    Route::get('/videos/{id}', [KnowledgeController::class, 'videoDetail'])->name('video_detail');
});

// 心理测评模块
Route::prefix('assessment')->group(function () {
    // 心理问卷测评
    Route::get('/', [App\Http\Controllers\AssessmentController::class, 'index'])->name('assessment.index');
    Route::get('/my-records', [App\Http\Controllers\AssessmentController::class, 'myRecords'])->name('assessment.my-records');
    // Route::post('/submit', [App\Http\Controllers\AssessmentController::class, 'submit'])->name('assessment.submit');
    Route::get('/result/{id}', [App\Http\Controllers\AssessmentController::class, 'result'])->name('assessment.result');
    Route::get('/{id}', [App\Http\Controllers\AssessmentController::class, 'show'])->name('assessment.show');
    Route::get('/{id}/start', [App\Http\Controllers\AssessmentController::class, 'start'])->name('assessment.start');

    // AI分析状态检查
    // Route::get('/ai-analysis-status/{responseId}', [App\Http\Controllers\AssessmentController::class, 'checkAiAnalysisStatus'])->name('assessment.ai_analysis_status');
    // 二维码生成和截图保存
    // Route::get('/qr-code/{responseId}', [App\Http\Controllers\AssessmentController::class, 'generateQrCode'])->name('assessment.generate_qr_code');
    // Route::post('/screenshot/{responseId}', [App\Http\Controllers\AssessmentController::class, 'saveScreenshot'])->name('assessment.save_screenshot');
     // 需要登录的路由
    Route::middleware(['auth'])->group(function () {
        Route::get('/my-records', [App\Http\Controllers\AssessmentController::class, 'myRecords'])->name('assessment.my-records');
        Route::post('/submit', [App\Http\Controllers\AssessmentController::class, 'submit'])->name('assessment.submit');
        Route::get('/ai-analysis-status/{responseId}', [App\Http\Controllers\AssessmentController::class, 'checkAiAnalysisStatus'])->name('assessment.ai_analysis_status');
        Route::get('/qr-code/{responseId}', [App\Http\Controllers\AssessmentController::class, 'generateQrCode'])->name('assessment.generate_qr_code');
        Route::post('/screenshot/{responseId}', [App\Http\Controllers\AssessmentController::class, 'saveScreenshot'])->name('assessment.save_screenshot');
    });

    // 趣味测评
    Route::get('/fun', function () {
        return view('pages.assessment.fun_tests');
    })->name('assessment.fun_tests');

    Route::get('/fun/{id}', function ($id) {
        return view('pages.assessment.fun_test_detail', ['id' => $id]);
    })->name('assessment.fun_test_detail');

    Route::post('/fun/{id}/submit', function ($id) {
        // 处理趣味测评提交
        return redirect()->route('assessment.fun_test_result', ['id' => $id]);
    })->name('assessment.fun_test_submit');

    Route::get('/fun/{id}/result', function ($id) {
        return view('pages.assessment.fun_test_result', ['id' => $id]);
    })->name('assessment.fun_test_result');
});

// 心理咨询模块
Route::prefix('consultation')->name('consultation.')->group(function () {
    // 咨询师列表
    Route::get('/counselors', [ConsultationController::class, 'counselors'])->name('counselors');
    // 咨询师详情
    Route::get('/counselor/{id}', [ConsultationController::class, 'counselorDetail'])->name('counselor_detail');

    // AI心理咨询
    Route::get('/ai-consultation', [ConsultationController::class, 'aiConsultation'])->name('ai_consultation');
    Route::post('/ai-consultation/send', [ConsultationController::class, 'aiConsultationSend'])->name('ai_consultation_send');

    // 线下课程
    Route::get('/offline-courses', [ConsultationController::class, 'offlineCourses'])->name('offline_courses');
    

    // 需要登录的路由
    Route::middleware(['auth'])->group(function () {
        //线下课预约
        Route::get('/offline-course/{id}', [ConsultationController::class, 'offlineCourseDetail'])->name('offline_course_detail');
        // 预约相关
        Route::post('/create-appointment', [ConsultationController::class, 'createAppointment'])->name('create_appointment');
        Route::get('/my-appointments', [ConsultationController::class, 'myAppointments'])->name('my_appointments');
        Route::post('/cancel-appointment/{id}', [ConsultationController::class, 'cancelAppointment'])->name('cancel_appointment');
        Route::get('/start-consultation/{id}', [ConsultationController::class, 'startConsultation'])->name('start_consultation');

        // 课程报名相关
        Route::post('/register-course', [ConsultationController::class, 'registerCourse'])->name('register_course');
        Route::get('/my-courses', [ConsultationController::class, 'myCourses'])->name('my_courses');
        Route::post('/cancel-course-registration/{id}', [ConsultationController::class, 'cancelCourseRegistration'])->name('cancel_course_registration');
    });
});


// 新闻资讯模块
Route::prefix('news')->group(function () {
    Route::get('/', [App\Http\Controllers\NewsController::class, 'index'])->name('news.index');
    Route::get('/{slug}', [App\Http\Controllers\NewsController::class, 'show'])->name('news.show');

    // API路由
    Route::get('/api/recommended', [App\Http\Controllers\NewsController::class, 'recommended'])->name('news.recommended');
    Route::post('/api/{id}/share', [App\Http\Controllers\NewsController::class, 'recordShare'])->name('news.share');
});

// 心理援助热线
Route::get('/hotline', [App\Http\Controllers\HotlineController::class, 'index'])->name('hotline');
Route::post('/hotline/record-usage', [App\Http\Controllers\HotlineController::class, 'recordUsage'])->name('hotline.record_usage');

// 线上直播模块
// 直播模块
Route::prefix('live')->name('live.')->group(function () {
    Route::get('/', [LiveShowController::class, 'index'])->name('index');
    Route::get('/{id}', [LiveShowController::class, 'show'])->name('show');
    Route::post('/{id}/redirect', [LiveShowController::class, 'redirect'])->name('redirect');
});

// 有奖问答模块
Route::prefix('quiz')->group(function () {
    // 问答活动列表
    Route::get('/', [QuizController::class, 'index'])->name('quiz.index');

    // 我的奖品 - 必须放在带参数的路由之前
    Route::get('/my-prizes', [QuizController::class, 'myPrizes'])->name('quiz.my_prizes');

    // 问答活动详情
    Route::get('/{id}', [QuizController::class, 'show'])->name('quiz.show');

    // 开始答题
    Route::get('/{id}/start', [QuizController::class, 'start'])->name('quiz.start');

    // 答题页面
    Route::get('/{id}/attempt/{attempt_id}', [QuizController::class, 'attempt'])->name('quiz.attempt');

    // 提交答案
    Route::post('/{id}/submit/{attempt_id}', [QuizController::class, 'submit'])->name('quiz.submit');

    // 查看结果
    Route::get('/{id}/result/{attempt_id}', [QuizController::class, 'result'])->name('quiz.result');

    // 领取奖品页面
    Route::get('/{id}/claim_prize/{winner_id}', [QuizController::class, 'claimPrize'])->name('quiz.claim_prize');

    // 提交奖品领取信息
    Route::post('/{id}/submit_claim/{winner_id}', [QuizController::class, 'submitClaim'])->name('quiz.submit_claim');
});

// 消息中心模块
Route::prefix('messages')->name('messages.')->middleware(['auth'])->group(function () {
    // 消息列表/会话列表
    Route::get('/', [MessageController::class, 'index'])->name('index');

    // 与咨询师的聊天页面
    Route::get('/{counselorId}', [MessageController::class, 'show'])->name('show');

    // 发送消息 API
    Route::post('/{counselorId}/send', [MessageController::class, 'send'])->name('send');

    // 获取未读消息数 API
    Route::get('/unread-count', [MessageController::class, 'getUnreadCount'])->name('unread_count');

    // 检查新消息
    Route::get('/{counselorId}/check-new', [MessageController::class, 'checkNewMessages'])->name('check-new');
});

// 留言咨询模块
Route::prefix('message-consultation')->name('message_consultation.')->group(function () {
    // 留言咨询首页
    Route::get('/', [ConsultationMessageController::class, 'index'])->name('index');
    
    // 提交留言
    Route::post('/submit', [ConsultationMessageController::class, 'store'])->name('submit');
    
    // 需要登录的路由
    Route::middleware(['auth'])->group(function () {
        // 我的留言列表
        Route::get('/my-consultations', [ConsultationMessageController::class, 'myConsultations'])->name('my_consultations');
        
        // 回复留言
        Route::post('/{consultation}/reply', [ConsultationMessageController::class, 'reply'])->name('reply');
    });
});
