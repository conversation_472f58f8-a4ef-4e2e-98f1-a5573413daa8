<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\Builder;
use Carbon\Carbon;

/**
 * 内容索引模型
 * 
 * @property int $id
 * @property string $indexable_type
 * @property int $indexable_id
 * @property string $type
 * @property string $title
 * @property string|null $summary
 * @property string|null $image
 * @property string|null $url
 * @property array|null $metadata
 * @property string|null $vector
 * @property bool $vector_generated
 * @property Carbon|null $vector_generated_at
 * @property bool $is_active
 * @property int $view_count
 * @property float $relevance_score
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * 
 * @property-read Model $indexable
 * @property-read bool $hasVector
 * @property-read string $typeLabel
 * @property-read array $vectorArray
 */
class ContentIndex extends Model
{
    use HasFactory;

    /**
     * 内容类型常量
     */
    public const TYPE_ARTICLE = 'article';
    public const TYPE_VIDEO = 'video';
    public const TYPE_COURSE_LESSON = 'course_lesson';

    /**
     * 可填充字段
     *
     * @var array<string>
     */
    protected $fillable = [
        'indexable_type',
        'indexable_id',
        'type',
        'title',
        'summary',
        'image',
        'url',
        'metadata',
        'vector',
        'vector_generated',
        'vector_generated_at',
        'is_active',
        'view_count',
        'relevance_score',
    ];

    /**
     * 类型转换
     *
     * @var array<string, string>
     */
    protected $casts = [
        'metadata' => 'array',
        'vector_generated' => 'boolean',
        'vector_generated_at' => 'datetime',
        'is_active' => 'boolean',
        'view_count' => 'integer',
        'relevance_score' => 'decimal:4',
    ];

    /**
     * 多态关联到可索引的内容
     *
     * @return MorphTo
     */
    public function indexable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * 查询作用域：已生成向量的内容
     *
     * @param Builder $query
     * @return Builder
     */
    public function scopeWithVector(Builder $query): Builder
    {
        return $query->where('vector_generated', true)
                    ->whereNotNull('vector');
    }

    /**
     * 查询作用域：未生成向量的内容
     *
     * @param Builder $query
     * @return Builder
     */
    public function scopeWithoutVector(Builder $query): Builder
    {
        return $query->where('vector_generated', false)
                    ->orWhereNull('vector');
    }

    /**
     * 查询作用域：按类型筛选
     *
     * @param Builder $query
     * @param string $type
     * @return Builder
     */
    public function scopeOfType(Builder $query, string $type): Builder
    {
        return $query->where('type', $type);
    }

    /**
     * 查询作用域：激活状态
     *
     * @param Builder $query
     * @return Builder
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }

    /**
     * 查询作用域：按相关性排序
     *
     * @param Builder $query
     * @param string $direction
     * @return Builder
     */
    public function scopeOrderByRelevance(Builder $query, string $direction = 'desc'): Builder
    {
        return $query->orderBy('relevance_score', $direction);
    }

    /**
     * 查询作用域：最近创建
     *
     * @param Builder $query
     * @param int $days
     * @return Builder
     */
    public function scopeRecent(Builder $query, int $days = 30): Builder
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    /**
     * 访问器：是否有向量
     *
     * @return bool
     */
    public function getHasVectorAttribute(): bool
    {
        return $this->vector_generated && !is_null($this->vector);
    }

    /**
     * 访问器：类型标签
     *
     * @return string
     */
    public function getTypeLabelAttribute(): string
    {
        return match ($this->type) {
            self::TYPE_ARTICLE => '文章',
            self::TYPE_VIDEO => '视频',
            self::TYPE_COURSE_LESSON => '课程',
            default => '未知',
        };
    }

    /**
     * 访问器：向量数组
     *
     * @return array|null
     */
    public function getVectorArrayAttribute(): ?array
    {
        if (!$this->hasVector) {
            return null;
        }

        // 将二进制向量数据转换为浮点数组
        $vectorData = unpack('f*', $this->vector);
        return array_values($vectorData);
    }

    /**
     * 获取向量数据（用于外部调用）
     *
     * @return array|null
     */
    public function getVectorData(): ?array
    {
        return $this->vectorArray;
    }

    /**
     * 设置向量数据
     *
     * @param array $vector
     * @return void
     */
    public function setVectorData(array $vector): void
    {
        // 将浮点数组转换为二进制数据
        $this->vector = pack('f*', ...$vector);
        $this->vector_generated = true;
        $this->vector_generated_at = now();
    }

    /**
     * 增加查看次数
     *
     * @param int $count
     * @return bool
     */
    public function incrementViewCount(int $count = 1): bool
    {
        return $this->increment('view_count', $count);
    }

    /**
     * 更新相关性评分
     *
     * @param float $score
     * @return bool
     */
    public function updateRelevanceScore(float $score): bool
    {
        return $this->update(['relevance_score' => $score]);
    }

    /**
     * 获取所有支持的内容类型
     *
     * @return array<string>
     */
    public static function getSupportedTypes(): array
    {
        return [
            self::TYPE_ARTICLE,
            self::TYPE_VIDEO,
            self::TYPE_COURSE_LESSON,
        ];
    }

    /**
     * 获取类型选项（用于表单）
     *
     * @return array<string, string>
     */
    public static function getTypeOptions(): array
    {
        return [
            self::TYPE_ARTICLE => '文章',
            self::TYPE_VIDEO => '视频',
            self::TYPE_COURSE_LESSON => '课程',
        ];
    }

    /**
     * 清除向量数据
     *
     * @return bool
     */
    public function clearVector(): bool
    {
        return $this->update([
            'vector' => null,
            'vector_generated' => false,
            'vector_generated_at' => null,
        ]);
    }

    /**
     * 检查是否需要重新生成向量
     *
     * @return bool
     */
    public function needsVectorRegeneration(): bool
    {
        if (!$this->hasVector) {
            return true;
        }

        // 如果内容更新时间晚于向量生成时间，需要重新生成
        if ($this->indexable && $this->indexable->updated_at > $this->vector_generated_at) {
            return true;
        }

        return false;
    }
} 