@extends('layouts.app')

@section('title', '联系我们 - 心理咨询平台')

@section('custom-styles')
<style>
    body {
        background-color: #f8f9fa;
    }
    
    .container {
        max-width: 960px;
        margin: 0 auto;
        padding-bottom: 80px !important; /* 为底部菜单栏预留空间 */
    }
    
    .page-header {
        display: flex;
        align-items: center;
        background: linear-gradient(135deg, #5b7cef 0%, #3c67e3 100%);
        color: white;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        border-radius: 0 0 10px 10px;
        padding: 12px 15px;
        margin-bottom: 15px;
    }
    
    .back-button {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 30px;
        height: 30px;
    }
    
    .back-icon {
        border-top: 2px solid white;
        border-left: 2px solid white;
        width: 12px;
        height: 12px;
        transform: rotate(-45deg);
    }
    
    .page-header h1 {
        font-size: 18px;
        font-weight: 500;
        margin: 0 auto;
        text-align: center;
        flex-grow: 1;
        margin-left: -30px;
        text-shadow: 0 1px 2px rgba(0,0,0,0.1);
    }
    
    .contact-card {
        background-color: #fff;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        margin: 15px;
        padding: 20px;
        border: 1px solid rgba(0, 0, 0, 0.03);
    }
    
    .section-title {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 20px;
        color: #333;
        border-bottom: 1px solid #f0f0f0;
        padding-bottom: 10px;
    }
    
    .contact-info {
        margin-bottom: 30px;
    }
    
    .contact-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 20px;
        padding-bottom: 20px;
        border-bottom: 1px solid #f0f0f0;
    }
    
    .contact-item:last-child {
        border-bottom: none;
    }
    
    .contact-icon {
        width: 45px;
        height: 45px;
        background-color: #eef1fd;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        flex-shrink: 0;
    }
    
    .contact-icon i {
        color: #5b7cef;
        font-size: 22px;
    }
    
    .contact-details {
        flex-grow: 1;
    }
    
    .contact-label {
        font-weight: 600;
        font-size: 16px;
        color: #333;
        margin-bottom: 5px;
    }
    
    .contact-value {
        color: #555;
        font-size: 15px;
        line-height: 1.5;
    }
    
    .contact-hours {
        color: #888;
        font-size: 13px;
        margin-top: 4px;
    }
    
    .contact-form {
        margin-top: 30px;
    }
    
    .form-group {
        margin-bottom: 20px;
    }
    
    .form-label {
        display: block;
        font-weight: 600;
        font-size: 14px;
        color: #444;
        margin-bottom: 8px;
    }
    
    .form-control {
        width: 100%;
        padding: 12px 15px;
        border: 1px solid #ddd;
        border-radius: 8px;
        font-size: 15px;
        transition: all 0.3s ease;
    }
    
    .form-control:focus {
        border-color: #5b7cef;
        box-shadow: 0 0 0 2px rgba(91, 124, 239, 0.1);
        outline: none;
    }
    
    textarea.form-control {
        min-height: 120px;
        resize: vertical;
    }
    
    .btn-primary {
        display: block;
        width: 100%;
        padding: 14px;
        background: linear-gradient(135deg, #5b7cef 0%, #3c67e3 100%);
        color: white;
        text-align: center;
        border: none;
        border-radius: 8px;
        font-size: 16px;
        font-weight: 600;
        transition: all 0.3s ease;
        cursor: pointer;
    }
    
    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 15px rgba(91, 124, 239, 0.25);
    }
    
    .contact-map {
        border-radius: 10px;
        overflow: hidden;
        height: 220px;
        margin-top: 20px;
    }
    
    .contact-map img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    
    .social-media {
        display: flex;
        justify-content: center;
        margin-top: 30px;
        gap: 15px;
    }
    
    .social-icon {
        width: 45px;
        height: 45px;
        border-radius: 50%;
        background-color: #f0f4ff;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #5b7cef;
        font-size: 20px;
        transition: all 0.3s ease;
    }
    
    .social-icon:hover {
        background-color: #5b7cef;
        color: white;
        transform: translateY(-3px);
    }
</style>
@endsection

@section('content')
<div class="page-header">
    <a href="{{ route('my') }}" class="back-button">
        <div class="back-icon"></div>
    </a>
    <h1>联系我们</h1>
    <div style="width: 30px;"></div>
</div>

<div class="container">
    <div class="contact-card">
        <h2 class="section-title">联系方式</h2>
        
        <div class="contact-info">
            <div class="contact-item">
                <div class="contact-icon">
                    <span class="material-icons">call</span>
                </div>
                <div class="contact-details">
                    <div class="contact-label">客服热线</div>
                    <div class="contact-value">400-123-4567</div>
                    <div class="contact-hours">工作时间：周一至周五 9:00-18:00</div>
                </div>
            </div>
            
            <div class="contact-item">
                <div class="contact-icon">
                    <span class="material-icons">email</span>
                </div>
                <div class="contact-details">
                    <div class="contact-label">客服邮箱</div>
                    <div class="contact-value"><EMAIL></div>
                    <div class="contact-hours">24小时内回复</div>
                </div>
            </div>
            
            <div class="contact-item">
                <div class="contact-icon">
                    <span class="material-icons">wechat</span>
                </div>
                <div class="contact-details">
                    <div class="contact-label">官方微信</div>
                    <div class="contact-value">心理平台（PsychPlatform）</div>
                    <div class="contact-hours">扫描下方二维码关注</div>
                </div>
            </div>
            
            
        </div>
    </div>
    
    
</div>
@endsection
