<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Dcat\Admin\Traits\HasDateTimeFormatter;

class Consultation extends Model
{
    use HasFactory, HasDateTimeFormatter;

    protected $fillable = [
        'user_id',
        'name',
        'phone',
        'email',
        'subject',
        'content',
        'status',
        'admin_reply',
        'replied_at',
        'replied_by'
    ];

    protected $casts = [
        'replied_at' => 'datetime',
    ];

    // 状态常量
    const STATUS_PENDING = 'pending';
    const STATUS_REPLIED = 'replied';
    const STATUS_CLOSED = 'closed';

    public static function getStatusOptions()
    {
        return [
            self::STATUS_PENDING => '待回复',
            self::STATUS_REPLIED => '已回复',
            self::STATUS_CLOSED => '已关闭',
        ];
    }

    public function getStatusTextAttribute()
    {
        return self::getStatusOptions()[$this->status] ?? '未知';
    }

    // 关联用户
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    // 关联回复记录
    public function replies()
    {
        return $this->hasMany(ConsultationReply::class)->orderBy('created_at', 'asc');
    }

    // 获取最新回复
    public function latestReply()
    {
        return $this->hasOne(ConsultationReply::class)->latest();
    }

    // 关联回复人（管理员）
    public function repliedBy()
    {
        return $this->belongsTo(\Dcat\Admin\Models\Administrator::class, 'replied_by');
    }
} 