<?php

namespace App\Console\Commands;

use App\Services\AlibabaVectorService;
use Illuminate\Console\Command;
use Exception;

class TestVectorService extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'vector:test {--text=测试文本内容}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '测试向量服务配置和功能';

    /**
     * Execute the console command.
     */
    public function handle(AlibabaVectorService $vectorService): int
    {
        $this->info('🔍 开始测试向量服务...');
        
        try {
            // 1. 测试配置
            $this->info('📋 检查配置...');
            $this->checkConfiguration();
            
            // 2. 测试DashVector连接
            $this->info('🔗 测试DashVector连接...');
            if ($vectorService->healthCheck()) {
                $this->info('✅ DashVector连接正常');
            } else {
                $this->error('❌ DashVector连接失败');
                return 1;
            }
            
            // 3. 测试Collection初始化
            $this->info('📦 测试Collection初始化...');
            if ($vectorService->initializeCollection()) {
                $this->info('✅ Collection初始化成功');
            } else {
                $this->error('❌ Collection初始化失败');
                return 1;
            }
            
            // 4. 测试向量生成
            $testText = $this->option('text');
            $this->info("🧮 测试向量生成（文本：{$testText}）...");
            
            $vector = $vectorService->generateTextVector($testText);
            
            if (!empty($vector)) {
                $this->info('✅ 向量生成成功');
                $this->info("   向量维度: " . count($vector));
                $this->info("   前5个值: " . implode(', ', array_slice($vector, 0, 5)));
            } else {
                $this->error('❌ 向量生成失败');
                return 1;
            }
            
            // 5. 测试向量存储
            $this->info('💾 测试向量存储...');
            $testId = 'test_' . time();
            
            if ($vectorService->insertDoc($testId, $vector, ['test' => true, 'text' => $testText])) {
                $this->info('✅ 向量存储成功');
                
                // 6. 测试向量搜索
                $this->info('🔍 测试向量搜索...');
                $results = $vectorService->queryDocs($vector, 1);
                
                if (!empty($results)) {
                    $this->info('✅ 向量搜索成功');
                    $this->info("   找到 " . count($results) . " 个结果");
                } else {
                    $this->warn('⚠️  向量搜索无结果（可能是正常的）');
                }
                
                // 7. 清理测试数据
                $this->info('🧹 清理测试数据...');
                $vectorService->deleteDocs([$testId]);
                $this->info('✅ 测试数据清理完成');
                
            } else {
                $this->error('❌ 向量存储失败');
                return 1;
            }
            
            $this->info('');
            $this->info('🎉 所有测试通过！向量服务配置正确。');
            $this->info('');
            $this->info('📝 下一步操作：');
            $this->info('   1. 运行: php artisan content:index --vector');
            $this->info('   2. 启动队列: php artisan queue:work --queue=vector-generation');
            
            return 0;
            
        } catch (Exception $e) {
            $this->error('❌ 测试失败: ' . $e->getMessage());
            $this->error('');
            $this->error('🔧 请检查：');
            $this->error('   1. .env 文件中的向量服务配置');
            $this->error('   2. API密钥是否正确');
            $this->error('   3. 网络连接是否正常');
            
            return 1;
        }
    }
    
    /**
     * 检查配置
     */
    private function checkConfiguration(): void
    {
        $dashvectorConfig = config('services.dashvector');
        $embeddingConfig = config('services.text_embedding');
        
        // 检查DashVector配置
        $requiredDashVector = ['api_key', 'endpoint', 'collection_name'];
        foreach ($requiredDashVector as $key) {
            if (empty($dashvectorConfig[$key])) {
                throw new Exception("DashVector配置缺失: {$key}");
            }
        }
        
        // 检查文本向量化配置
        $provider = $embeddingConfig['provider'];
        if (empty($embeddingConfig['api_key'])) {
            throw new Exception("文本向量化API密钥缺失");
        }
        
        if ($provider === 'dashscope' && empty($embeddingConfig['dashscope_endpoint'])) {
            throw new Exception("阿里云灵积端点配置缺失");
        }
        
        if ($provider === 'openai' && empty($embeddingConfig['openai_endpoint'])) {
            throw new Exception("OpenAI端点配置缺失");
        }
        
        $this->info("✅ 配置检查通过");
        $this->info("   DashVector端点: " . $dashvectorConfig['endpoint']);
        $this->info("   向量化服务: " . $provider);
        $this->info("   Collection: " . $dashvectorConfig['collection_name']);
    }
} 