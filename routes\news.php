<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\NewsController;

// 新闻资讯路由
Route::get('/news', [NewsController::class, 'index'])->name('news.index');
Route::get('/news/{slug}', [NewsController::class, 'show'])->name('news.show');

// API路由
Route::get('/api/news/recommended', [NewsController::class, 'recommended'])->name('news.recommended');
Route::post('/api/news/{id}/share', [NewsController::class, 'recordShare'])->name('news.share');
