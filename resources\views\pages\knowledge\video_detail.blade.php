@extends('layouts.app')

@section('title', '视频详情 - 心理科普')

@section('custom-styles')
<style>
    .container {
        width: 100%;
        max-width: 100%;
        padding: 0;
        margin: 0;
        background-color: #f5f5f5;
    }
    
    .page-header {
        padding: 15px;
        background-color: #fff;
        text-align: center;
        position: relative;
        box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    }
    
    .back-button {
        position: absolute;
        left: 15px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 18px;
        color: #333;
        text-decoration: none;
        display: flex;
        align-items: center;
    }
    
    .back-icon {
        width: 20px;
        height: 20px;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23333'%3E%3Cpath d='M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z'/%3E%3C/svg%3E");
        background-size: contain;
        background-repeat: no-repeat;
    }
    
    .video-container {
        padding: 15px;
        background-color: #fff;
        margin: 10px;
        border-radius: 8px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    }
    
    /* 最后一个容器添加底部边距 */
    .video-container:last-child {
        margin-bottom: 80px; /* 确保底部有足够空间不被导航栏遮挡 */
    }
    
    .video-header {
        margin-bottom: 20px;
    }
    
    .video-title {
        font-size: 20px;
        font-weight: 600;
        color: #333;
        margin-bottom: 15px;
        line-height: 1.4;
    }
    
    .video-meta {
        display: flex;
        justify-content: space-between;
        color: #999;
        font-size: 14px;
        margin-bottom: 15px;
    }
    
    .video-info {
        display: flex;
        align-items: center;
        gap: 15px;
    }
    
    .video-author {
        color: #666;
    }
    
    .video-date {
        color: #999;
    }
    
    .video-views {
        display: flex;
        align-items: center;
        color: #999;
    }
    
    .views-icon {
        width: 14px;
        height: 14px;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999'%3E%3Cpath d='M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z'/%3E%3C/svg%3E");
        background-size: contain;
        background-repeat: no-repeat;
        margin-right: 3px;
    }
    
    .video-category {
        background-color: #4e9cff;
        color: #ffffff;
        padding: 2px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;
        display: inline-flex;
        align-items: center;
    }
    
    .video-category:before {
        content: '';
        display: inline-block;
        width: 10px;
        height: 10px;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23ffffff'%3E%3Cpath d='M10 3H4a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4a1 1 0 0 0-1-1zm10 0h-6a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1V4a1 1 0 0 0-1-1zM10 13H4a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1v-6a1 1 0 0 0-1-1zm10 0h-6a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h6a1 1 0 0 0 1-1v-6a1 1 0 0 0-1-1z'/%3E%3C/svg%3E");
        background-size: contain;
        background-repeat: no-repeat;
        margin-right: 4px;
    }
    
    .video-player-wrapper {
        width: 100%;
        position: relative;
        padding-bottom: 56.25%; /* 16:9 宽高比 */
        height: 0;
        overflow: hidden;
        margin-bottom: 20px;
        border-radius: 8px;
        background-color: #000;
    }
    
    .video-player {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border: none;
    }
    
    .video-placeholder {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        background-size: cover;
        background-position: center;
    }
    
    .play-button {
        width: 60px;
        height: 60px;
        background-color: rgba(0, 0, 0, 0.6);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
    }
    
    .play-button:before {
        content: '';
        width: 0;
        height: 0;
        border-style: solid;
        border-width: 10px 0 10px 16px;
        border-color: transparent transparent transparent #ffffff;
        margin-left: 4px;
    }
    
    .video-description {
        font-size: 16px;
        line-height: 1.7;
        color: #333;
        margin-bottom: 20px;
    }
    
    .video-content {
        font-size: 16px;
        line-height: 1.7;
        color: #333;
    }
    
    .video-content p {
        margin-bottom: 15px;
    }
    
    .video-content h2 {
        font-size: 18px;
        font-weight: 600;
        margin: 25px 0 15px;
        color: #333;
    }
    
    .video-footer {
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid #eee;
    }
    
    .video-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        margin-top: 20px;
    }
    
    .video-tag {
        background-color: #f0f7ff;
        color: #6a8caf;
        font-size: 12px;
        padding: 3px 10px;
        border-radius: 8px;
        border: 1px solid #e0eeff;
        display: inline-flex;
        align-items: center;
    }
    
    .video-tag:before {
        content: '';
        display: inline-block;
        width: 10px;
        height: 10px;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%236a8caf'%3E%3Cpath d='M21.41 11.58l-9-9C12.05 2.22 11.55 2 11 2H4c-1.1 0-2 .9-2 2v7c0 .55.22 1.05.59 1.42l9 9c.36.36.86.58 1.41.58.55 0 1.05-.22 1.41-.59l7-7c.37-.36.59-.86.59-1.41 0-.55-.23-1.06-.59-1.42zM5.5 7C4.67 7 4 6.33 4 5.5S4.67 4 5.5 4 7 4.67 7 5.5 6.33 7 5.5 7z'/%3E%3C/svg%3E");
        background-size: contain;
        background-repeat: no-repeat;
        margin-right: 5px;
    }
    
    .related-videos {
        margin-top: 20px;
    }
    
    .related-title {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 15px;
        color: #333;
    }
    
    .related-item {
        display: flex;
        margin-bottom: 15px;
        padding-bottom: 15px;
        border-bottom: 1px solid #f0f0f0;
        cursor: pointer;
    }
    
    .related-item:last-child {
        border-bottom: none;
        margin-bottom: 0;
        padding-bottom: 0;
    }
    
    .related-image {
        width: 120px;
        height: 68px;
        background-size: cover;
        background-position: center;
        border-radius: 4px;
        margin-right: 10px;
        position: relative;
        flex-shrink: 0;
    }
    
    .related-duration {
        position: absolute;
        right: 5px;
        bottom: 5px;
        background-color: rgba(0, 0, 0, 0.7);
        color: #fff;
        font-size: 12px;
        padding: 1px 4px;
        border-radius: 2px;
    }
    
    .related-content {
        flex: 1;
    }
    
    .related-video-title {
        font-size: 14px;
        font-weight: 500;
        color: #333;
        margin-bottom: 5px;
        line-height: 1.4;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
    
    .related-video-meta {
        font-size: 12px;
        color: #999;
    }
    
    @media (min-width: 768px) {
        .container {
            max-width: 750px;
            margin: 0 auto;
        }
        
        .video-container {
            padding: 25px;
            margin: 15px auto;
            max-width: 720px;
        }
    }
</style>
@endsection

@section('content')
<div class="page-header">
    <a href="{{ route('knowledge.videos') }}" class="back-button">
        <div class="back-icon"></div>
    </a>
    <h1 style="font-size: 18px; margin: 0;">视频详情</h1>
</div>

<div class="video-container">
    <div class="video-header">
        <h1 class="video-title">{{ $video['title'] }}</h1>
        <div class="video-meta">
            <div class="video-info">
                <span class="video-author">{{ $video['author'] }}</span>
                <span class="video-date">{{ $video['date'] }}</span>
                <span class="video-views">
                    <div class="views-icon"></div>
                    {{ $video['views'] }}
                </span>
            </div>
            <span class="video-category">{{ $video['category_name'] }}</span>
        </div>
    </div>
    
    <div class="video-player-wrapper">
        <div class="video-placeholder" style="background-image: url('{{ $video['image'] }}')">
            <div class="play-button" id="playButton"></div>
        </div>
        <iframe class="video-player" id="videoPlayer" src="" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen style="display: none;"></iframe>
    </div>
    
    <div class="video-description">
        {{ $video['description'] }}
    </div>
    
    <div class="video-content">
        <h2>视频内容概要</h2>
        {!! $video['content'] !!}
    </div>
    
    <div class="video-footer">
        <div class="video-tags">
            @foreach($video['tags'] as $tag)
                <span class="video-tag">{{ $tag }}</span>
            @endforeach
        </div>
    </div>
</div>

<div class="video-container">
    <div class="related-videos">
        <h2 class="related-title">相关视频</h2>
        
        @foreach($relatedVideos as $relatedVideo)
        <div class="related-item" data-id="{{ $relatedVideo['id'] }}">
            <div class="related-image" style="background-image: url('{{ $relatedVideo['image'] }}')">
                <div class="related-duration">{{ $relatedVideo['video_duration'] }}</div>
            </div>
            <div class="related-content">
                <div class="related-video-title">{{ $relatedVideo['title'] }}</div>
                <div class="related-video-meta">{{ $relatedVideo['date'] }} · {{ $relatedVideo['views'] }}次观看</div>
            </div>
        </div>
        @endforeach
    </div>
</div>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 视频播放功能
        const playButton = document.getElementById('playButton');
        const videoPlayer = document.getElementById('videoPlayer');
        const videoPlaceholder = document.querySelector('.video-placeholder');
        
        // 使用后端提供的视频URL
        const videoUrl = "{{ $video['video_url'] }}";
        
        playButton.addEventListener('click', function() {
            // 判断是否是YouTube链接
            if(videoUrl.includes('youtube.com/watch?v=')) {
                // 将YouTube观看链接转换为嵌入链接
                const videoId = videoUrl.split('v=')[1].split('&')[0];
                videoPlayer.src = `https://www.youtube.com/embed/${videoId}?autoplay=1`;
            } else if(videoUrl.includes('youtu.be/')) {
                // 处理短链接
                const videoId = videoUrl.split('youtu.be/')[1];
                videoPlayer.src = `https://www.youtube.com/embed/${videoId}?autoplay=1`;
            } else if(videoUrl.includes('youtube.com/embed/')) {
                // 已经是嵌入链接，直接使用
                videoPlayer.src = videoUrl.includes('autoplay=1') ? videoUrl : `${videoUrl}${videoUrl.includes('?') ? '&' : '?'}autoplay=1`;
            } else {
                // 其他视频链接直接使用
                videoPlayer.src = videoUrl;
            }
            
            videoPlayer.style.display = 'block';
            videoPlaceholder.style.display = 'none';
        });
        
        // 相关视频点击事件
        const relatedItems = document.querySelectorAll('.related-item');
        relatedItems.forEach(item => {
            item.addEventListener('click', function() {
                const videoId = this.getAttribute('data-id');
                window.location.href = `{{ route('knowledge.video_detail', ['id' => '__ID__']) }}`.replace('__ID__', videoId);
            });
        });
    });
</script>
@endsection
