<?php

namespace App\Admin\Controllers;

use App\Models\CourseRegistration;
use App\Models\OfflineCourse;
use App\Models\User;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class CourseRegistrationController extends AdminController
{
    /**
     * 设置标题
     */
    protected $title = '课程报名管理';

    /**
     * 列表页面
     */
    protected function grid()
    {
        return Grid::make(new CourseRegistration(), function (Grid $grid) {
            $grid->model()->with(['course', 'user']);
            
            // 获取URL参数，用于筛选特定课程的报名
            $courseId = request()->get('course_id');
            if ($courseId) {
                $grid->model()->where('course_id', $courseId);
                $course = OfflineCourse::find($courseId);
                if ($course) {
                    $grid->tools(function (Grid\Tools $tools) use ($course) {
                        $tools->append('<div class="alert alert-info">当前显示: ' . $course->title . ' 的报名记录</div>');
                    });
                }
            }

            $grid->column('id')->sortable();
            $grid->column('registration_code', '报名编号');
            $grid->column('user.name', '报名人');
            $grid->column('course.title', '课程名称');
            $grid->column('department', '单位');
            $grid->column('participants_count', '参与人数');
            
            $grid->column('status', '状态')->display(function ($status) {
                if ($status == CourseRegistration::STATUS_CANCELLED) {
                    return '<span class="badge badge-danger">已取消</span>';
                } elseif ($status == CourseRegistration::STATUS_PENDING) {
                    return '<span class="badge badge-warning">已驳回</span>';
                } elseif ($status == CourseRegistration::STATUS_CONFIRMED) {
                    return '<span class="badge badge-info">已确认</span>';
                } elseif ($status == CourseRegistration::STATUS_REGISTERED) {
                    return '<span class="badge badge-success">已报名</span>';
                }
                
                return '<span class="badge badge-dark">未知</span>';
            });
            
            // $grid->column('payment_status', '支付状态')->display(function ($status) {
            //     if ($status == CourseRegistration::PAYMENT_UNPAID) {
            //         return '<span class="badge badge-warning">未支付</span>';
            //     } elseif ($status == CourseRegistration::PAYMENT_PAID) {
            //         return '<span class="badge badge-success">已支付</span>';
            //     } elseif ($status == CourseRegistration::PAYMENT_REFUNDED) {
            //         return '<span class="badge badge-info">已退款</span>';
            //     }
                
            //     return '<span class="badge badge-dark">未知</span>';
            // });
            
            $grid->column('price', '价格')->display(function ($price) {
                return $price > 0 ? '¥' . $price : '免费';
            });
            
            $grid->column('created_at', '报名时间');
            
            // 筛选功能
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');
                $filter->like('registration_code', '报名编号');
                $filter->like('user.name', '报名人');
                $filter->equal('course_id', '课程')->select(OfflineCourse::all()->pluck('title', 'id'));
                $filter->equal('status', '状态')->select(CourseRegistration::getStatusMap());
                $filter->equal('payment_status', '支付状态')->select(CourseRegistration::getPaymentStatusMap());
            });
            
            // 默认按创建时间排序
            $grid->model()->orderBy('created_at', 'desc');
            
            // // 添加导出功能
            // $grid->export()->titles([
            //     'registration_code' => '报名编号',
            //     'user.name' => '报名人',
            //     'course.title' => '课程名称',
            //     'department' => '部门',
            //     'participants_count' => '参与人数',
            //     'status_text' => '状态',
            //     'payment_status_text' => '支付状态',
            //     'price' => '价格',
            //     'created_at' => '报名时间',
            // ])->rows(function (array $rows) {
            //     foreach ($rows as $index => &$row) {
            //         $row['status_text'] = CourseRegistration::getStatusMap()[$row['status']] ?? '未知';
            //         $row['payment_status_text'] = CourseRegistration::getPaymentStatusMap()[$row['payment_status']] ?? '未知';
            //     }
            //     return $rows;
            // });
            $grid->disableBatchDelete();
        });
    }

    /**
     * 详情页面
     */
    protected function detail($id)
    {
        return Show::make($id, new CourseRegistration(), function (Show $show) {
            $show->field('id');
            $show->field('registration_code', '报名编号');
            $show->field('user.name', '报名人');
            $show->field('course.title', '课程名称');
            $show->field('course.course_time', '课程时间');
            $show->field('department', '部门');
            $show->field('participants_count', '参与人数');
            
            $show->field('participants_info', '参与人员信息')->unescape()->as(function ($info) {
                if (empty($info)) {
                    return '-';
                }
                
                $html = '<table class="table table-bordered"><thead><tr>
                         <th>姓名</th><th>手机号</th><th>备注</th>
                         </tr></thead><tbody>';
                         
                foreach ($info as $person) {
                    $html .= '<tr>';
                    $html .= '<td>' . $person['name'] ?? '-' . '</td>';
                    $html .= '<td>' . $person['phone'] ?? '-' . '</td>';
                    $html .= '<td>' . $person['remark'] ?? '-' . '</td>';
                    $html .= '</tr>';
                }
                
                $html .= '</tbody></table>';
                
                return $html;
            });
            
            $show->field('remarks', '备注信息');
            $show->field('status', '状态')->using(CourseRegistration::getStatusMap());
            // $show->field('payment_status', '支付状态')->using(CourseRegistration::getPaymentStatusMap());
            $show->field('price', '价格');
            $show->field('created_at', '报名时间');
            $show->field('updated_at', '更新时间');
        });
    }

    /**
     * 表单页面
     */
    protected function form()
    {
        return Form::make(new CourseRegistration(), function (Form $form) {
            $form->display('id');
            
            // 编辑模式下只允许修改状态
            if ($form->isEditing()) {
                $form->display('registration_code', '报名编号');
                $form->display('user.name', '报名人');
                $form->display('course.title', '课程名称');
                $form->display('department', '部门');
                $form->display('participants_count', '参与人数');
                $form->display('price', '价格');
                
                $form->select('status', '状态')
                    ->options(CourseRegistration::getStatusMap())
                    ->required();
                    
                // $form->select('payment_status', '支付状态')
                //     ->options(CourseRegistration::getPaymentStatusMap())
                //     ->required();
                    
                // 状态变更时的逻辑
                $form->saving(function (Form $form) {
                    $original = $form->model()->toArray();
                    
                    // 如果状态从确认变为取消，恢复课程名额
                    if ($original['status'] != CourseRegistration::STATUS_CANCELLED && 
                        $form->status == CourseRegistration::STATUS_CANCELLED) {
                        
                        $course = OfflineCourse::find($form->model()->course_id);
                        if ($course) {
                            $course->current_participants -= $form->model()->participants_count;
                            $course->save();
                        }
                    }
                    
                    // 如果状态从取消变为确认，减少课程名额
                    if ($original['status'] == CourseRegistration::STATUS_CANCELLED && 
                        $form->status != CourseRegistration::STATUS_CANCELLED) {
                        
                        $course = OfflineCourse::find($form->model()->course_id);
                        if ($course) {
                            $course->current_participants += $form->model()->participants_count;
                            $course->save();
                        }
                    }
                });
            } else {
                // 创建模式下的字段
                $form->text('registration_code', '报名编号')
                    ->default(CourseRegistration::generateCode())
                    ->required();
                    
                $form->select('user_id', '报名人')
                    ->options(User::all()->pluck('name', 'id'))
                    ->required();
                    
                $form->select('course_id', '课程')
                    ->options(OfflineCourse::where('status', OfflineCourse::STATUS_PUBLISHED)
                                 ->where('registration_deadline', '>=', now())
                                 ->pluck('title', 'id'))
                    ->required();
                    
                $form->text('department', '部门');
                $form->number('participants_count', '参与人数')
                    ->default(1)
                    ->min(1)
                    ->required();
                    
                $form->table('participants_info', '参与人员信息', function (Form\NestedForm $table) {
                    $table->text('name', '姓名');
                    $table->text('phone', '手机号');
                    $table->text('remark', '备注');
                });
                
                $form->textarea('remarks', '备注信息');
                $form->currency('price', '价格')
                    ->symbol('￥')
                    ->default(0);
                    
                $form->select('status', '状态')
                    ->options(CourseRegistration::getStatusMap())
                    ->default(CourseRegistration::STATUS_PENDING)
                    ->required();
                    
                $form->select('payment_status', '支付状态')
                    ->options(CourseRegistration::getPaymentStatusMap())
                    ->default(CourseRegistration::PAYMENT_UNPAID)
                    ->required();
                
                // 保存时的逻辑处理
                $form->saving(function (Form $form) {
                    // 如果选择的课程是免费的，自动设置为已支付
                    if ($form->course_id) {
                        $course = OfflineCourse::find($form->course_id);
                        if ($course && $course->price <= 0) {
                            $form->payment_status = CourseRegistration::PAYMENT_PAID;
                            $form->price = 0; // 设置价格为0
                        } elseif ($course) {
                            $form->price = $course->price; // 设置课程价格
                        }
                    }
                    
                    // 增加课程参与人数
                    if ($form->course_id && !$form->model()->exists) {
                        $course = OfflineCourse::find($form->course_id);
                        if ($course) {
                            $course->increment('current_participants', $form->participants_count ?: 1);
                        }
                    }
                });
            }
            
            $form->display('created_at', '创建时间');
            $form->display('updated_at', '更新时间');
        });
    }
}
