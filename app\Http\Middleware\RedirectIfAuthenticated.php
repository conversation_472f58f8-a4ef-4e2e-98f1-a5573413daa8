<?php

namespace App\Http\Middleware;

use App\Providers\RouteServiceProvider;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class RedirectIfAuthenticated
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string ...$guards): Response
    {
        $guards = empty($guards) ? [null] : $guards;

        foreach ($guards as $guard) {
            if (Auth::guard($guard)->check()) {
                $redirectUrl = RouteServiceProvider::HOME;

                // 如果 APP_URL 带端口，且 redirect URL 没带，就加上端口
                $appUrl = config('app.url');
                $parsedAppUrl = parse_url($appUrl);
                $hostWithPort = $parsedAppUrl['host'] ?? 'localhost';
                if (isset($parsedAppUrl['port'])) {
                    $hostWithPort .= ':' . $parsedAppUrl['port'];
                }

                // 替换 redirect URL 的 host，确保包含端口
                if (isset($parsedAppUrl['scheme'])) {
                    $scheme = $parsedAppUrl['scheme'];
                    $path   = ltrim(parse_url($redirectUrl, PHP_URL_PATH) ?? '/', '/');
                    $redirectUrl = "{$scheme}://{$hostWithPort}/{$path}";
                }

                return redirect($redirectUrl);
            }
        }

        // ✅ 保存当前未登录访问地址（GET 且非登录页）为 intended，确保带端口
        if ($request->method() === 'GET' && !$request->is('login') && !$request->is('register') && !$request->is('logout')) {
            $fullUrl = $request->getSchemeAndHttpHost() . $request->getRequestUri();
            session(['url.intended' => $fullUrl]);
        }

        return $next($request);
    }
}
