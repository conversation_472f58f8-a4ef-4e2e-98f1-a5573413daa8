<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\URL;
use Dcat\Admin\Admin;
use Dcat\Admin\Form;
use Dcat\Admin\Form\Field\Image;
use Dcat\Admin\Form\Field\File;
use Dcat\Admin\Form\Field\MultipleImage;
use Dcat\Admin\Form\Field\MultipleFile;
use App\Services\AiConsultationRecommendationService;
use App\Services\AlibabaVectorService;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // 注册向量服务
        $this->app->singleton(AlibabaVectorService::class, function ($app) {
            return new AlibabaVectorService();
        });

        // 注册AI咨询推荐服务
        $this->app->singleton(AiConsultationRecommendationService::class, function ($app) {
            return new AiConsultationRecommendationService(
                $app->make(AlibabaVectorService::class)
            );
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
         // 1. 全局强制 URL 生成都用 APP_URL（带 :8004）
        $appUrl = rtrim(config('app.url'), '/');
        URL::forceRootUrl($appUrl);
        URL::forceScheme(parse_url($appUrl, PHP_URL_SCHEME));
        \Schema::defaultStringLength(191);
        
        // —— 全局拦截所有 Form 字段 —— //
        Admin::booted(function () {
            // 设置默认的上传服务器地址
            config([
                'admin.upload.server' => admin_url('upload/image'),
                'admin.upload.file_server' => admin_url('upload/file'),
            ]);
            
            \Log::info('Admin booted, upload server set to: ' . admin_url('upload/image'));
        });
    }
}
