<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Dcat\Admin\Traits\HasDateTimeFormatter;

/**
 * 心理测评题目模型
 */
class AssessmentQuestion extends Model
{
    use HasDateTimeFormatter;

    // 批量赋值字段
    protected $fillable = [
        'questionnaire_id', 'content', 'type', 'sort_order'
    ];

    // 虚拟字段
    protected $appends = ['options_text'];

    /**
     * 该题目所属的问卷
     */
    public function questionnaire(): BelongsTo
    {
        return $this->belongsTo(AssessmentQuestionnaire::class, 'questionnaire_id', 'id');
    }

    /**
     * 该题目的所有选项
     */
    public function options(): HasMany
    {
        return $this->hasMany(AssessmentOption::class, 'question_id');
    }

    /**
     * 该题目的所有答案记录
     */
    public function answers(): HasMany
    {
        return $this->hasMany(AssessmentAnswer::class, 'question_id');
    }

    /**
     * 获取选项文本（虚拟字段）
     */
    public function getOptionsTextAttribute()
    {
        // 如果options关系已加载且有数据
        if ($this->relationLoaded('options') && $this->options && $this->options->count() > 0) {
            $optionsText = '';
            foreach ($this->options->sortBy('id') as $option) {
                $optionsText .= $option->content . '|' . $option->score_value . "\n";
            }
            return trim($optionsText);
        }
        
        // 如果关系未加载，尝试加载
        if (!$this->relationLoaded('options') && $this->exists) {
            $this->load(['options' => function($query) {
                $query->orderBy('id');
            }]);
            if ($this->options && $this->options->count() > 0) {
                $optionsText = '';
                foreach ($this->options as $option) {
                    $optionsText .= $option->content . '|' . $option->score_value . "\n";
                }
                return trim($optionsText);
            }
        }
        
        return '';
    }

    /**
     * 设置选项文本（虚拟字段）
     */
    public function setOptionsTextAttribute($value)
    {
        // 这个方法用于接收表单数据，实际处理在控制器的saved回调中进行
        // 这里只是为了避免错误，不做任何处理
    }
}
