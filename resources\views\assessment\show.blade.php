@extends('layouts.app')

@section('title', $questionnaire->title)

@section('custom-styles')
<link rel="stylesheet" href="{{ asset('css/assessment.css') }}">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
@endsection

@section('content')
<div class="detail-container">
    <!-- 页面头部 -->
    <div class="assessment-header">
        <div class="header-content">
            <button class="back-btn" onclick="history.back()">
                <i class="fas fa-arrow-left"></i>
            </button>
            <h1 class="header-title">测评详情</h1>
            <div class="header-action"></div>
        </div>
    </div>

    <!-- 问卷详情 -->
    <div class="detail-header">
        <h1 class="detail-title">{{ $questionnaire->title }}</h1>
        <p class="detail-description">{{ $questionnaire->description }}</p>
        
        <div class="detail-stats">
            <div class="detail-stat">
                <span class="detail-stat-number">{{ $questionnaire->question_count }}</span>
                <span class="detail-stat-label">题目数量</span>
            </div>
            <div class="detail-stat">
                <span class="detail-stat-number">{{ $questionnaire->est_duration }}</span>
                <span class="detail-stat-label">预计时长(分钟)</span>
            </div>
            <div class="detail-stat">
                <span class="detail-stat-number">{{ $totalResponses }}</span>
                <span class="detail-stat-label">参与人数</span>
            </div>
        </div>
    </div>

    <!-- 测评说明 -->
    <div class="questionnaire-list">
        <div class="questionnaire-card">
            <div class="card-content">
                <h3 class="card-title">
                    <i class="fas fa-info-circle" style="color: #667eea; margin-right: 10px;"></i>
                    测评说明
                </h3>
                <div class="card-description" style="margin-bottom: 20px;">
                    <p style="margin-bottom: 10px;">• 本测评共 {{ $questionnaire->question_count }} 道题目，预计需要 {{ $questionnaire->est_duration }} 分钟完成</p>
                    <p style="margin-bottom: 10px;">• 请根据您的真实情况选择最符合的答案</p>
                    <p style="margin-bottom: 10px;">• 测评过程中请保持专注，一次性完成效果更佳</p>
                    <p style="margin-bottom: 10px;">• 测评结果仅供参考，如有疑问请咨询专业人士</p>
                </div>
                
                @if($hasCompleted)
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        您已完成此测评，可以查看之前的结果或重新测评
                    </div>
                    
                    <div class="card-footer" style="gap: 10px;">
                        <button class="start-btn" onclick="viewResult()" style="flex: 1;">
                            查看结果
                        </button>
                        <button class="start-btn" onclick="startAssessment()" style="flex: 1; background: #6c757d;">
                            重新测评
                        </button>
                    </div>
                @else
                    <div class="card-footer">
                        <button class="start-btn" onclick="startAssessment()" style="width: 100%;">
                            <i class="fas fa-play" style="margin-right: 8px;"></i>
                            开始测评
                        </button>
                    </div>
                @endif
            </div>
        </div>

        <!-- 测评统计 -->
        @if($averageScore > 0)
        <div class="questionnaire-card">
            <div class="card-content">
                <h3 class="card-title">
                    <i class="fas fa-chart-bar" style="color: #667eea; margin-right: 10px;"></i>
                    测评统计
                </h3>
                
                <div class="detail-stats" style="margin-top: 20px;">
                    <div class="detail-stat">
                        <span class="detail-stat-number">{{ number_format($averageScore, 1) }}</span>
                        <span class="detail-stat-label">平均得分</span>
                    </div>
                    <div class="detail-stat">
                        <span class="detail-stat-number">{{ $totalResponses }}</span>
                        <span class="detail-stat-label">参与人数</span>
                    </div>
                    <div class="detail-stat">
                        <span class="detail-stat-number">{{ $questionnaire->domain }}</span>
                        <span class="detail-stat-label">测评领域</span>
                    </div>
                </div>
            </div>
        </div>
        @endif

        <!-- 相关推荐 -->
        @if($relatedQuestionnaires->count() > 0)
        <h2 class="section-title">相关测评</h2>
        @foreach($relatedQuestionnaires as $related)
            <div class="questionnaire-card">
                <div class="card-header" style="height: 80px;">
                    <div class="card-icon" style="font-size: 30px;">
                        <i class="{{ App\Http\Controllers\AssessmentController::getDomainIcon($related->domain) }}"></i>
                    </div>
                    <div class="domain-badge">{{ App\Http\Controllers\AssessmentController::getDomainName($related->domain) }}</div>
                </div>
                
                <div class="card-content">
                    <h3 class="card-title">{{ $related->title }}</h3>
                    <p class="card-description">{{ $related->description }}</p>
                    
                    <div class="card-meta">
                        <div class="meta-item">
                            <i class="fas fa-question-circle"></i>
                            <span>{{ $related->question_count }} 题</span>
                        </div>
                        <div class="meta-item">
                            <i class="fas fa-clock"></i>
                            <span>约 {{ $related->est_duration }} 分钟</span>
                        </div>
                    </div>
                    
                    <div class="card-footer">
                        <span class="response-count">{{ $related->responses_count ?? 0 }} 人已测评</span>
                        <button class="start-btn" onclick="goToQuestionnaire({{ $related->id }})">
                            查看详情
                        </button>
                    </div>
                </div>
            </div>
        @endforeach
        @endif
    </div>
</div>
@endsection

@section('scripts')
<script>
function startAssessment() {
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<span class="loading"></span> 准备中...';
    btn.disabled = true;
    
    setTimeout(() => {
        window.location.href = `/assessment/{{ $questionnaire->id }}/start`;
    }, 800);
}

function viewResult() {
    window.location.href = `/assessment/my-records`;
}

function goToQuestionnaire(id) {
    window.location.href = `/assessment/${id}`;
}

// 页面加载动画
document.addEventListener('DOMContentLoaded', function() {
    // 统计数字动画
    const statNumbers = document.querySelectorAll('.detail-stat-number');
    statNumbers.forEach(number => {
        const text = number.textContent;
        if (!isNaN(text)) {
            const finalValue = parseInt(text);
            let currentValue = 0;
            const increment = finalValue / 20;
            
            const timer = setInterval(() => {
                currentValue += increment;
                if (currentValue >= finalValue) {
                    currentValue = finalValue;
                    clearInterval(timer);
                }
                number.textContent = Math.floor(currentValue);
            }, 50);
        }
    });
});
</script>
@endsection 