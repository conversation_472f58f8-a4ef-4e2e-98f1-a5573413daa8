<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class VideoCategory extends Model
{
    use HasFactory,HasDateTimeFormatter;

    protected $fillable = [
        'name',
        'description',
        'sort',
        'is_active'
    ];

    // 关联视频
    public function videos()
    {
        return $this->hasMany(Video::class, 'category_id');
    }
}
