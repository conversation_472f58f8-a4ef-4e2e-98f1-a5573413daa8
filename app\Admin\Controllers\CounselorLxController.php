<?php

namespace App\Admin\Controllers;

use App\Models\CounselorLx;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Widgets\Card;

class CounselorLxController extends AdminController
{
    /**
     * 设置标题
     */
    protected $title = '线下咨询师管理';

    /**
     * 列表页面
     */
    protected function grid()
    {
        return Grid::make(new CounselorLx(), function (Grid $grid) {
            $grid->column('id')->sortable();
            $grid->column('avatar','头像')->image('', 50, 50);
            $grid->column('name', '姓名');
            $grid->column('gender', '性别')->using(CounselorLx::getGenderMap())->label([
                0 => 'default',
                1 => 'primary',
                2 => 'danger',
            ]);
            $grid->column('title', '职称');
            // $grid->column('phone', '电话');
            // $grid->column('price', '咨询价格')->display(function ($price) {
            //     return $price > 0 ? '¥' . $price : '价格面议';
            // });
            
            // $grid->column('support', '支持方式')->display(function () {
            //     $methods = [];
            //     if ($this->support_text) $methods[] = '文字';
            //     if ($this->support_voice) $methods[] = '语音';
            //     if ($this->support_video) $methods[] = '视频';
            //     return implode('、', $methods);
            // });
            
            // $grid->column('is_active', '状态')->switch();
            $grid->column('sort_order', '排序')->sortable();
            $grid->column('created_at', '创建时间')->sortable();
            
            // 筛选功能
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');
                $filter->like('name', '姓名');
                $filter->equal('gender', '性别')->select(CounselorLx::getGenderMap());
                $filter->like('title', '职称');
                // $filter->equal('is_active', '状态')->select([
                //     0 => '禁用',
                //     1 => '启用'
                // ]);
            });
            
            // 操作按钮
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableView();
            });
        });
    }

    /**
     * 详情页面
     */
    protected function detail($id)
    {
        return Show::make($id, new CounselorLx(), function (Show $show) {
            $show->field('id');
            $show->field('avatar')->image();
            $show->field('name', '姓名');
            $show->field('gender', '性别')->using(CounselorLx::getGenderMap());
            $show->field('title', '职称');
            // $show->field('phone', '电话');
            // $show->field('email', '邮箱');
            $show->field('introduction', '个人简介')->unescape()->as(function ($introduction) {
                return $introduction ? "<div style='max-width:600px'>$introduction</div>" : '';
            });
            $show->field('expertise', '专业领域')->unescape()->as(function ($expertise) {
                return $expertise ? "<div style='max-width:600px'>$expertise</div>" : '';
            });
            $show->field('experience', '从业经历')->unescape()->as(function ($experience) {
                return $experience ? "<div style='max-width:600px'>$experience</div>" : '';
            });
            $show->field('education', '教育背景')->unescape()->as(function ($education) {
                return $education ? "<div style='max-width:600px'>$education</div>" : '';
            });
            // $show->field('price', '价格');
            // $show->field('support_text', '文字咨询')->as(function ($value) {
            //     return $value ? '支持' : '不支持';
            // });
            // $show->field('support_voice', '语音咨询')->as(function ($value) {
            //     return $value ? '支持' : '不支持';
            // });
            // $show->field('support_video', '视频咨询')->as(function ($value) {
            //     return $value ? '支持' : '不支持';
            // });
            // $show->field('is_active', '状态')->as(function ($value) {
            //     return $value ? '启用' : '禁用';
            // });
            $show->field('sort_order', '排序');
            $show->field('created_at', '创建时间');
            $show->field('updated_at', '更新时间');
        });
    }

    /**
     * 表单页面
     */
    protected function form()
    {
        return Form::make(new CounselorLx(), function (Form $form) {
            $form->display('id');
            $form->text('name', '姓名')->required();
            $form->image('avatar', '头像')
                ->uniqueName()
                ->autoUpload()
                ->required();
            $form->radio('gender', '性别')
                ->options(CounselorLx::getGenderMap())
                ->default(0);
            $form->text('title', '职称');
            // $form->text('phone', '手机号')
                // ->rules('nullable|regex:/^1[3-9]\d{9}$/');
            // $form->email('email', '邮箱');
            
            $form->editor('introduction', '个人简介')
                ->help('简短介绍咨询师的个人背景和特点');
            $form->editor('expertise', '专业领域')
                ->help('详细描述咨询师的专业擅长领域');
            $form->editor('experience', '从业经历')
                ->help('列出咨询师的相关工作经验');
            $form->editor('education', '教育背景')
                ->help('列出咨询师的学历和专业教育背景');
            
            // $form->currency('price', '咨询价格')
            //     ->symbol('￥')
            //     ->default(0)
            //     ->help('设置为0表示价格面议');
            
            // $form->divider('咨询方式');
            
            // $form->switch('support_text', '文字咨询')
            //     ->default(true);
            // $form->switch('support_voice', '语音咨询')
            //     ->default(true);
            // $form->switch('support_video', '视频咨询')
            //     ->default(true);
                
            $form->divider('其他设置');
            
            // $form->switch('is_active', '是否启用')
            //     ->default(true);
            $form->number('sort_order', '排序权重')
                ->help('数字越大排序越靠前')
                ->default(0);
                
            $form->display('created_at', '创建时间');
            $form->display('updated_at', '更新时间');
            
            // 保存前处理
            $form->saving(function (Form $form) {
                // 确保价格字段不为空
                if ($form->price === null || $form->price === '') {
                    $form->price = 0;
                }
            });
        });
    }
}
