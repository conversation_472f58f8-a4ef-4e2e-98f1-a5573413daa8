<?php

namespace App\Http\Controllers\Counselor;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\ConsultationAppointment;
use App\Models\Message;
use App\Models\Counselor;
use Carbon\Carbon;

class DashboardController extends Controller
{
    /**
     * 咨询师仪表盘中间件
     */
    public function __construct()
    {
        $this->middleware('counselor.auth');
    }
    
    /**
     * 显示咨询师仪表盘
     */
    public function index(Request $request)
    {
        $counselorId = $request->session()->get('counselor_id');
        
        // 获取今日预约
        $today = Carbon::today();
        $todayAppointments = ConsultationAppointment::where('counselor_id', $counselorId)
            ->whereDate('appointment_time', $today)
            ->orderBy('appointment_time')
            ->with('user')
            ->get();
            
        // 获取未来预约
        $futureAppointments = ConsultationAppointment::where('counselor_id', $counselorId)
            ->whereDate('appointment_time', '>', $today)
            ->whereIn('status', [1, 2]) // 待确认和已确认的预约
            ->orderBy('appointment_time')
            ->with('user')
            ->get();
        
        // 获取未读消息数量
        $unreadMessages = Message::where('counselor_id', $counselorId)
            ->where('sender_type', 'user')
            ->where('is_read', false)
            ->count();
            
        // 按用户分组获取最新消息
        $latestMessages = Message::selectRaw('user_id, MAX(created_at) as latest_time')
            ->where('counselor_id', $counselorId)
            ->groupBy('user_id')
            ->orderBy('latest_time', 'desc')
            ->take(5)
            ->get()
            ->pluck('latest_time', 'user_id')
            ->toArray();
            
        // 获取这些用户的信息
        $userIds = array_keys($latestMessages);
        $usersWithMessages = [];
        
        if (!empty($userIds)) {
            $messageUsers = Message::whereIn('user_id', $userIds)
                ->where('counselor_id', $counselorId)
                ->with('user')
                ->get()
                ->groupBy('user_id');
                
            foreach ($userIds as $userId) {
                if (isset($messageUsers[$userId])) {
                    $messages = $messageUsers[$userId];
                    $user = $messages->first()->user;
                    $latestMessage = $messages->sortByDesc('created_at')->first();
                    $unreadCount = $messages->where('sender_type', 'user')
                        ->where('is_read', false)
                        ->count();
                        
                    $usersWithMessages[] = [
                        'user' => $user,
                        'latest_message' => $latestMessage,
                        'unread_count' => $unreadCount
                    ];
                }
            }
        }
        
        return view('counselor.dashboard', [
            'todayAppointments' => $todayAppointments,
            'futureAppointments' => $futureAppointments,
            'unreadMessages' => $unreadMessages,
            'usersWithMessages' => $usersWithMessages
        ]);
    }
    
    /**
     * 显示所有预约
     */
    public function appointments(Request $request)
    {
        $counselorId = $request->session()->get('counselor_id');
        
        $appointments = ConsultationAppointment::where('counselor_id', $counselorId)
            ->orderBy('created_at', 'desc')
            ->with('user')
            ->paginate(15);
            
        return view('counselor.appointments', [
            'appointments' => $appointments
        ]);
    }
    
    /**
     * 确认预约
     */
    public function confirmAppointment(Request $request, $id)
    {
        $counselorId = $request->session()->get('counselor_id');
        
        $appointment = ConsultationAppointment::where('id', $id)
            ->where('counselor_id', $counselorId)
            ->where('status', 1) // 只能确认待确认的预约
            ->firstOrFail();
            
        $appointment->status = 2; // 已确认
        $appointment->confirmed_at = Carbon::now();
        $appointment->save();
        
        return back()->with('success', '预约已确认');
    }
    
    /**
     * 拒绝预约
     */
    public function rejectAppointment(Request $request, $id)
    {
        $counselorId = $request->session()->get('counselor_id');
        
        $appointment = ConsultationAppointment::where('id', $id)
            ->where('counselor_id', $counselorId)
            ->where('status', 1) // 只能拒绝待确认的预约
            ->firstOrFail();
            
        $appointment->status = 0; // 已取消
        $appointment->cancelled_at = Carbon::now();
        $appointment->save();
        
        // 恢复时间段可用性
        if ($appointment->schedule_id) {
            $schedule = $appointment->schedule;
            if ($schedule) {
                $schedule->is_available = true;
                $schedule->save();
            }
        }
        
        return back()->with('success', '预约已拒绝');
    }
    
    /**
     * 完成预约
     */
    public function completeAppointment(Request $request, $id)
    {
        $counselorId = $request->session()->get('counselor_id');
        
        $appointment = ConsultationAppointment::where('id', $id)
            ->where('counselor_id', $counselorId)
            ->whereIn('status', [2, 3]) // 只能完成已确认或进行中的预约
            ->firstOrFail();
            
        $appointment->status = 4; // 已完成
        $appointment->completed_at = Carbon::now();
        $appointment->save();
        
        return back()->with('success', '预约已标记为完成');
    }
}
