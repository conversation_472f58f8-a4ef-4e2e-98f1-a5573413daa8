<?php

namespace App\Admin\Controllers;

use App\Models\Article;
use App\Models\ArticleCategory;
use App\Models\ArticleTag;
use App\Models\User;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Admin;
use Dcat\Admin\Layout\Content;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ArticleController extends AdminController
{
    /**
     * 页面标题
     *
     * @return string
     */
    protected function title()
    {
        return '文章管理';
    }
    
    /**
     * 表格构建
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(Article::with(['category', 'author', 'tags']), function (Grid $grid) {
            //如果是文章审核角色
            if(Admin::user()->isRole('article_review')){
                //只展示待审核文章
                $grid->model()->where('status', Article::STATUS_PENDING);
            }
            $grid->column('id', 'ID')->sortable();
            $grid->column('title', '文章标题');
            $grid->column('image', '封面图片')->image('', 50, 50);
            $grid->column('category.name', '分类')->display(function ($name) {
                if (!$name) return '-';
                return "<span class='badge badge-primary'>$name</span>";
            });
            
            // 标签显示
            $grid->column('tags', '标签')->display(function ($tags) {
                if (!$tags) return '-';
                
                return collect($tags)->map(function ($tag) {
                    return "<span class='badge badge-info mr-1'>{$tag['name']}</span>";
                })->implode(' ');
            });
            
            $grid->column('author.name', '作者')->display(function ($name) {
                return $name ?: '-';
            });
            $grid->column('views', '浏览量')->sortable();
            $grid->column('is_recommended', '是否推荐')->switch();
            
            // 状态显示
            $grid->column('status', '状态')->using(Article::getStatusMap())->dot([
                'draft' => 'gray',
                'pending' => 'orange',
                'published' => 'green',
                'rejected' => 'red',
            ]);
            
            $grid->column('publish_time', '发布时间')->sortable();
            $grid->column('created_at', '创建时间')->sortable();
            
            // 快速搜索
            $grid->quickSearch('title', 'summary');
            $grid->disableBatchDelete();
            
            // 筛选器
            $grid->filter(function (Grid\Filter $filter) {
                $filter->panel();
                
                $filter->equal('id', 'ID');
                $filter->like('title', '标题');
                $filter->like('summary', '摘要');
                $filter->equal('category_id', '分类')->select(
                    ArticleCategory::pluck('name', 'id')
                );
                $filter->whereBetween('created_at', function ($query) {
                    $start = $this->input['start'] ?? null;
                    $end = $this->input['end'] ?? null;
                    
                    if ($start && $end) {
                        $query->whereBetween('created_at', [$start, $end]);
                    }
                })->datetime()->width(6, 3);
                
                $filter->equal('is_recommended', '是否推荐')->select([
                    0 => '否',
                    1 => '是',
                ]);
                
                $filter->equal('status', '状态')->select(Article::getStatusMap());
            });
            
            // 操作按钮
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                // 基于权限控制显示/隐藏默认按钮
                if (!Admin::user()->can('article_view')) {
                    $actions->disableView();
                }
                
                if (!Admin::user()->can('article_edit')) {
                    $actions->disableEdit();
                }
                
                if (!Admin::user()->can('article_delete')) {
                    $actions->disableDelete();
                }
                
                // 如果是内容审核角色
                if (Admin::user()->can('article_review')) {
                    // 添加审核按钮 - 只显示待审核的文章
                    if ($actions->row->status == Article::STATUS_PENDING) {
                        $actions->append('<a href="'.admin_url('articles/'.$actions->row->id.'/review').'" class="btn btn-sm btn-warning"><i class="feather icon-check"></i> 审核</a>');
                    }
                }
                
                // 如果是内容编辑角色
                if (Admin::user()->can('article_submit_review')) {
                    // 添加提交审核按钮 - 只显示草稿状态的文章
                    if ($actions->row->status == Article::STATUS_DRAFT) {
                        $actions->append('<a href="javascript:void(0);" class="btn btn-sm btn-success submit-review" data-id="'.$actions->row->id.'"><i class="feather icon-send"></i> 提交审核</a>');
                    }
                }
                
                // 移除某些角色的编辑/删除权限
                if (!Admin::user()->can('article_edit')) {
                    // 非编辑角色不能编辑和删除文章
                    $actions->disableEdit();
                    $actions->disableDelete();
                }
            });
            
            // 默认排序
            $grid->model()->orderBy('created_at', 'desc');
            
            // 修复JS脚本处理按钮事件
            Admin::script('
            $(function () {
                $(".submit-review").on("click", function () {
                    var id = $(this).data("id");
                    $.ajax({
                        url: "'.admin_url('articles').'/"+id+"/submit-review",
                        type: "POST",
                        data: {_token: Dcat.token},
                        success: function (data) {
                            if (data.status) {
                                Dcat.success(data.message);
                                setTimeout(function(){
                                    Dcat.reload();
                                }, 1500);
                            } else {
                                Dcat.error(data.message);
                            }
                        },
                        error: function (xhr) {
                            Dcat.error("操作失败: " + xhr.statusText);
                        }
                    });
                });
            });
            ');
        });
    }

    /**
     * 详情页构建
     *
     * @param mixed $id
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new Article(), function (Show $show) {
            $show->field('id', 'ID');
            $show->field('title', '文章标题');
            $show->field('image', '封面图片')->image();
            $show->field('category.name', '分类');
            
            $show->field('tags', '标签')->as(function ($tags) {
                $tags = collect($tags)->pluck('name')->implode('、');
                return $tags ?: '-';
            });
            
            $show->field('author.name', '作者');
            $show->field('summary', '摘要');
            $show->field('content', '内容')->unescape();
            $show->field('views', '浏览量');
            $show->field('is_recommended', '是否推荐')->using([0 => '否', 1 => '是']);
            $show->field('status', '状态')->using(Article::getStatusMap());
            $show->field('publish_time', '发布时间');
            $show->field('source', '来源');
            $show->field('created_at', '创建时间');
            $show->field('updated_at', '更新时间');
            
            // 关联审核记录
            $show->hasMany('reviews','审核记录', function (Grid $grid) {
                $grid->model()->orderBy('created_at', 'desc');
                
                $grid->column('id', 'ID');
                $grid->column('reviewer.name', '审核人');
                $grid->column('status', '审核结果')->using([
                    'pending' => '待审核',
                    'approved' => '通过',
                    'rejected' => '拒绝',
                ])->dot([
                    'pending' => 'orange',
                    'approved' => 'green',
                    'rejected' => 'red',
                ]);
                $grid->column('comments', '审核意见');
                $grid->column('reviewed_at', '审核时间');
                
                $grid->disableCreateButton();
                $grid->disableActions();
            });
        });
    }

    /**
     * 表单构建
     *
     * @return Form
     */
    protected function form()
    {
        // 获取当前用户
        $user = Admin::user();
        $isAdmin = $user->isAdministrator();
        
        // 按照文档示例，使用with预加载关联数据
        return Form::make(Article::with('tags'), function (Form $form) use ($isAdmin) {
            $form->display('id', 'ID');
            $form->text('title', '文章标题')->required()->rules('required|max:255');
            
            $form->image('image', '封面图片')
                ->required()
                ->autoUpload()
                ->rules('required')
                ->help('建议尺寸: 1200 x 675 像素')
                ->accept('jpg,png,jpeg,gif');
                
            $form->select('category_id', '分类')
                ->options(ArticleCategory::where('is_active', 1)->pluck('name', 'id'))
                ->required()
                ->rules('required');
                
            // 完全按照文档示例实现多对多关联
            $form->multipleSelect('tags', '标签')
                ->options(ArticleTag::all()->pluck('name', 'id'))
                ->customFormat(function ($v) {
                    if (!$v) return [];
                    
                    // 这一步非常重要，需要把数据库中查出来的二维数组转化成一维数组
                    return array_column($v, 'id');
                });
            
            $form->textarea('summary', '摘要')
                ->required()
                ->rules('required|max:500')
                ->rows(3);
            
            $form->editor('content', '内容')->required()->rules('required');
            
            $form->text('source', '来源')->help('选填，如转载文章请注明来源');
            
            // 恢复作者选择功能
            if ($form->isCreating()) {
                // 检查是否存在用户
                if (\App\Models\User::count() > 0) {
                    // 如果有用户，提供下拉选择
                    $form->select('author_id', '作者')
                        ->options(\App\Models\AdminUser::pluck('name', 'id'))
                        ->default(\Dcat\Admin\Admin::user()->id);
                } else {
                    // 无用户时自动创建默认用户
                    $form->hidden('author_id');
                }
            } else {
                // 编辑模式下显示作者名称
                $form->display('author.name', '作者')->default('未指定');
            }
            
            $form->display('views', '浏览量');
            
            // 管理员可以设置推荐
            if ($isAdmin) {
                $form->switch('is_recommended', '是否推荐')->default(0);
            } else {
                $form->display('is_recommended', '是否推荐')->with(function ($value) {
                    return $value ? '是' : '否';
                });
            }
            
            // 根据角色限制对状态的编辑权限
            if ($isAdmin) {
                // 管理员可以设置任何状态
                $form->select('status', '状态')
                    ->options(Article::getStatusMap())
                    ->default('draft')
                    ->when('published', function (Form $form) {
                        $form->datetime('publish_time', '发布时间')->default(now());
                    });
            } else {
                // 编辑者只能看到状态，无法修改
                $form->display('status', '状态')->with(function ($value) {
                    return Article::getStatusMap()[$value] ?? '草稿';
                });
            }
            
            $form->display('created_at', '创建时间');
            $form->display('updated_at', '更新时间');
            
            // 表单提交前回调
            $form->saving(function (Form $form) {
                // 摘要为空时自动提取内容的前200个字符作为摘要
                if (empty($form->summary) && !empty($form->content)) {
                    $summary = strip_tags($form->content);
                    $form->summary = mb_substr($summary, 0, 200);
                }
                
                // 设置发布时间
                if ($form->status == Article::STATUS_PUBLISHED && empty($form->publish_time)) {
                    $form->publish_time = now();
                }

                // 创建文章时，如果没有选择作者或用户不存在，尝试创建默认用户
                if ($form->isCreating() && (empty($form->author_id) || !\App\Models\User::find($form->author_id))) {
                    $userExists = \DB::table('admin_users')->where('id', 1)->exists();
                    if ($userExists) {
                        $form->author_id = 1; // 使用ID为1的用户
                    } else {
                        // 如果ID为1的用户不存在，则尝试创建
                        try {
                            $userId = \DB::table('admin_users')->insertGetId([
                                'name' => 'Admin',
                                'email' => '<EMAIL>',
                                'password' => bcrypt('password'),
                                'created_at' => now(),
                                'updated_at' => now(),
                            ]);
                            $form->author_id = $userId;
                        } catch (\Exception $e) {
                            // 创建失败时，设置为null
                            $form->author_id = null;
                        }
                    }
                }
            });
        
            return $form;
        });
    }
    
    /**
     * 提交审核
     */
    public function submitReview($id, Request $request)
    {
        if (!Admin::user()->can('article_submit_review')) {
            return response()->json(['status' => false, 'message' => '您没有提交审核的权限']);
        }
        
        $article = Article::find($id);
        
        if (!$article) {
            return response()->json(['status' => false, 'message' => '文章不存在']);
        }
        
        if ($article->status != Article::STATUS_DRAFT) {
            return response()->json(['status' => false, 'message' => '只有草稿状态的文章才能提交审核']);
        }
        
        DB::beginTransaction();
        
        try {
            // 更新文章状态
            $article->status = Article::STATUS_PENDING;
            $article->save();
            
            // 创建审核记录
            $article->reviews()->create([
                'reviewer_id' => Admin::user()->id,
                'status' => 'pending',
                'comments' => '提交审核',
                'reviewed_at' => now(),
            ]);
            
            DB::commit();
            
            return response()->json(['status' => true, 'message' => '提交审核成功']);
        } catch (\Exception $e) {
            DB::rollBack();
            
            return response()->json(['status' => false, 'message' => '提交失败：' . $e->getMessage()]);
        }
    }
    
    /**
     * 审核页面
     */
    public function review($id, Content $content)
    {
        if (!Admin::user()->can('article_review')) {
            admin_error('错误', '您没有审核文章的权限');
            return redirect(admin_url('articles'));
        }
        
        // 使用load代替with，确保即使关联不存在也不会报错
        $article = Article::find($id);
        
        if (!$article) {
            admin_error('错误', '文章不存在');
            return redirect(admin_url('articles'));
        }
        
        // 安全加载关联数据
        try {
            $article->load(['category', 'author', 'tags']);
        } catch (\Exception $e) {
            // 忽略加载关联数据时的错误
        }
        
        if ($article->status != Article::STATUS_PENDING) {
            admin_error('错误', '只能审核待审核状态的文章');
            return redirect(admin_url('articles'));
        }
        
        return $content
            ->title('文章审核')
            ->description('审核文章内容')
            ->body(view('admin.articles.review', compact('article')));
    }
    
    /**
     * 处理审核操作
     */
    public function handleReview($id, Request $request)
    {
        if (!Admin::user()->can('article_review')) {
            return response()->json(['status' => false, 'message' => '您没有审核文章的权限']);
        }
        
        $status = $request->input('status');
        $comments = $request->input('comments');
        
        $article = Article::find($id);
        
        if (!$article) {
            return response()->json(['status' => false, 'message' => '文章不存在']);
        }
        
        if ($article->status != Article::STATUS_PENDING) {
            return response()->json(['status' => false, 'message' => '只能审核待审核状态的文章']);
        }
        
        DB::beginTransaction();
        
        try {
            // 更新文章状态
            if ($status == 'approved') {
                $article->status = Article::STATUS_PUBLISHED;
                $article->publish_time = now();
            } else {
                $article->status = Article::STATUS_REJECTED;
            }
            
            $article->save();
            
            // 创建审核记录
            $article->reviews()->create([
                'reviewer_id' => Admin::user()->id,
                'status' => $status,
                'comments' => $comments,
                'reviewed_at' => now(),
            ]);
            
            DB::commit();
            
            return response()->json(['status' => true, 'message' => '审核操作成功']);
        } catch (\Exception $e) {
            DB::rollBack();
            
            return response()->json(['status' => false, 'message' => '审核失败：' . $e->getMessage()]);
        }
    }
}
