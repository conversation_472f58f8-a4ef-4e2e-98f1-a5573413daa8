<?php

namespace App\Admin\Controllers;

use App\Models\VideoTag;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class VideoTagController extends AdminController
{
    /**
     * 页面标题
     *
     * @return string
     */
    protected function title()
    {
        return '视频标签管理';
    }

    /**
     * 表格构建
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new VideoTag(), function (Grid $grid) {
            $grid->column('id', 'ID')->sortable();
            $grid->column('name', '标签名称');
            $grid->column('created_at', '创建时间')->sortable();
            $grid->column('updated_at', '更新时间')->sortable();
            
            // 快速搜索
            $grid->quickSearch('name');
            
            // 筛选器
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id', 'ID');
                $filter->like('name', '标签名称');
            });
            
            // 操作设置
            $grid->disableViewButton();
            
            // 启用弹窗编辑功能
            $grid->showQuickEditButton();
            
            // 正确的弹窗实现方式
            $grid->enableDialogCreate();
        });
    }

    /**
     * 详情页构建
     *
     * @param mixed $id
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new VideoTag(), function (Show $show) {
            $show->field('id', 'ID');
            $show->field('name', '标签名称');
            $show->field('created_at', '创建时间');
            $show->field('updated_at', '更新时间');
        });
    }

    /**
     * 表单构建
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new VideoTag(), function (Form $form) {
            $form->display('id', 'ID');
            $form->text('name', '标签名称')->required()->rules('required|max:50');
            
            $form->display('created_at', '创建时间');
            $form->display('updated_at', '更新时间');
        });
    }
}
