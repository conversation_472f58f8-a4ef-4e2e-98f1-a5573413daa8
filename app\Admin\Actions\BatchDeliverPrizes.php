<?php

namespace App\Admin\Actions;

use App\Models\QuizPrizeWinner;
use Dcat\Admin\Grid\BatchAction;
use Illuminate\Http\Request;
use Dcat\Admin\Admin;

class BatchDeliverPrizes extends BatchAction
{
    protected $title = '批量标记送达';
    
    // 按照Dcat Admin文档的要求修改方法签名
    public function handle(Request $request)
    {
        // 获取选中的行的ID数组
        $keys = $this->getKey();
        
        // 查询所有选中的数据
        $models = QuizPrizeWinner::find($keys);
        
        $count = 0;
        
        foreach ($models as $model) {
            // 只处理已发货的奖品
            if ($model->status === 'shipped') {
                $model->status = 'delivered';
                $model->admin_notes = ($model->admin_notes ? $model->admin_notes . "\n" : '') . 
                                      Admin::user()->name . ' 于 ' . now() . ' 标记为已送达';
                $model->save();
                $count++;
            }
        }
        
        return $this->response()
                    ->success("成功将 {$count} 件奖品标记为已送达")
                    ->refresh();
    }
    
    public function confirm()
    {
        return '确定要将选中的奖品标记为已送达吗？';
    }
}
