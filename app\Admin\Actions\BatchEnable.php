<?php

namespace App\Admin\Actions;

use Dcat\Admin\Grid\BatchAction;
use Illuminate\Http\Request;

class BatchEnable extends BatchAction
{
    protected $title = '批量启用';
    
    public function confirm()
    {
        return '确定要启用选中的热线吗？';
    }
    
    public function handle(Request $request)
    {
        $model = $request->get('model');
        
        // 获取选中的行ID
        $keys = $this->getKey();
        
        // 获取模型类
        $class = get_class($model);
        
        // 批量更新
        foreach ($keys as $key) {
            $class::find($key)->update(['is_enabled' => true]);
        }
        
        return $this->response()
            ->success('已成功启用 ' . count($keys) . ' 条记录')
            ->refresh();
    }
}
