@extends('layouts.app')

@section('title', '首页 - 心理咨询平台')

@section('custom-styles')
<link rel="stylesheet" href="{{ asset('css/knowledge-section.css') }}">
<style>
    .container {
        width: 100%;
        max-width: 100%;
        padding: 0;
        margin: 0;
        overflow-x: hidden;
    }
    
    .search-bar {
        padding: 10px 15px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        box-sizing: border-box;
    }
    
    .search-input-wrap {
        flex: 1;
        background-color: #f5f5f5;
        border-radius: 20px;
        display: flex;
        align-items: center;
        padding: 5px 15px;
        margin-right: 10px;
    }
    
    .search-icon {
        width: 20px;
        height: 20px;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999'%3E%3Cpath d='M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z'/%3E%3C/svg%3E");
        background-size: contain;
        background-repeat: no-repeat;
        margin-right: 5px;
    }
    
    .search-input {
        flex: 1;
        border: none;
        background: transparent;
        padding: 5px 0;
        font-size: 14px;
    }
    
    .search-input:focus {
        outline: none;
    }
    
    .message-icon {
        width: 24px;
        height: 24px;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23666'%3E%3Cpath d='M20 2H4c-1.1 0-1.99.9-1.99 2L2 22l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-5 14h-3v-2h3v2zm0-3h-3v-2h3v2zm0-3h-3v-2h3v2z'/%3E%3C/svg%3E");
        background-size: contain;
        background-repeat: no-repeat;
    }
    
    .top-banner-section {
        width: 100%;
        background-size: cover;
        background-position: center;
        padding: 15px;
        box-sizing: border-box;
        /*margin-bottom:8px ;*/
        margin: 0;
    }
    
    .banner-content {
        width: 100%;
    }
    
    .banner-main-image {
        text-align: center;
        margin-bottom: 15px;
        position: relative; /* 添加相对定位，以便子元素可以绝对定位 */
    }
    
    .banner-main-image img {
        max-width: 100%;
        height: auto;
        border-radius: 10px;
        display: block; /* 确保图片不会有额外间距 */
    }
    
    .nav-icons-container {
        width: 90% !important; /* 控制宽度，使其在主图内部 */
        padding: 0 !important;
        position: absolute !important; /* 绝对定位，相对于主图 */
        left: 5% !important; /* 水平居中 */
        right: 5% !important;
        bottom: 20% !important; /* 放在图片中间位置 */
        transform: translateY(50%) !important; /* 调整垂直位置 */
        z-index: 10 !important; /* 提高z-index确保在最上层 */
        background-color: rgba(255, 255, 255, 0.85) !important; /* 半透明白色背景 */
        border-radius: 15px !important; /* 圆角边框 */
        padding: 8px 5px 5px !important; /* 内边距 */
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15) !important; /* 阴影效果 */
    }
    
    .nav-icons-row {
        display: flex !important;
        justify-content: space-around !important;
        margin-bottom: 6px !important; /* 减小行间距 */
        width: 100% !important;
        gap: 0 !important;
        padding: 0 !important;
    }
    
    .nav-icon-item {
        display: flex !important;
        flex-direction: column !important;
        align-items: center !important;
        width: 25% !important; /* 四个图标平均分布 */
    }
    
    .nav-icon-image {
        width: 45px !important; /* 缩小图标容器尺寸 */
        height: 45px !important;
        display: flex !important;
        justify-content: center !important;
        align-items: center !important;
        margin-bottom: 3px !important; /* 减小底部间距 */
        overflow: hidden !important;
        border-radius: 50% !important; /* 使用圆形图标 */
        position: relative !important;
    }
    
    .nav-icon-text {
        font-size: 10px !important; /* 缩小文字 */
        color: #333 !important;
        text-align: center !important;
        line-height: 1.2 !important; /* 减少行高 */
    }
    
    /* 响应式调整 */
    @media (max-width: 360px) {
        .nav-icon-image {
            width: 40px;
            height: 40px;
        }
        
        .nav-icon-text {
            font-size: 10px !important;
        }
        
        .nav-icon-image img {
            width: 24px;
            height: 24px;
        }
    }
    
    @media (max-width: 320px) {
        .nav-icon-image {
            width: 36px;
            height: 36px;
        }
        
        .nav-icon-image img {
            width: 22px;
            height: 22px;
        }
    }
    
    @media (min-width: 768px) {
        .container {
            max-width: 750px;
            margin: 0 auto;
        }
    }
    
    /* 开发中弹窗样式 */
    .dev-popup {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background-color: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        z-index: 1000;
        display: none;
        text-align: center;
        font-size: 16px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        animation: fadeIn 0.3s ease-out;
    }
    
    /* 热线弹窗样式 */
    .hotline-popup {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 1000;
        align-items: center;
        justify-content: center;
        animation: fadeIn 0.3s ease-out;
    }
    
    .hotline-popup-content {
        width: 90%;
        max-width: 420px;
        max-height: 90vh;
        background-color: #f5f5f7;
        border-radius: 14px;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    }
    
    .hotline-popup-header {
        background: linear-gradient(135deg, #5b7cef 0%, #3c67e3 100%);
        padding: 20px;
        color: white;
        position: relative;
        overflow: hidden;
    }
    
    .hotline-popup-header h1 {
        font-size: 22px;
        font-weight: 600;
        margin: 0 0 5px 0;
        position: relative;
        z-index: 1;
    }
    
    .hotline-popup-header p {
        font-size: 14px;
        margin: 0;
        opacity: 0.9;
        position: relative;
        z-index: 1;
        line-height: 1.4;
    }
    
    .hotline-pattern {
        position: absolute;
        top: 0;
        right: 0;
        width: 100%;
        height: 100%;
        opacity: 0.1;
        background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMDAlIiBoZWlnaHQ9IjEwMCUiPjxkZWZzPjxwYXR0ZXJuIGlkPSJwYXR0ZXJuIiB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHBhdHRlcm5Vbml0cz0idXNlclNwYWNlT25Vc2UiIHBhdHRlcm5UcmFuc2Zvcm09InJvdGF0ZSgxMzUpIj48cmVjdCBpZD0icGF0dGVybi1iZyIgd2lkdGg9IjQwMCUiIGhlaWdodD0iNDAwJSIgZmlsbD0icmdiYSgyNTUsMjU1LDI1NSwwLjEpIj48L3JlY3Q+PHBhdGggZmlsbD0icmdiYSgyNTUsMjU1LDI1NSwwLjA1KSIgZD0iTTAgMGg0MHY0MEgweiI+PC9wYXRoPjwvcGF0dGVybj48L2RlZnM+PHJlY3QgZmlsbD0idXJsKCNwYXR0ZXJuKSIgaGVpZ2h0PSIxMDAlIiB3aWR0aD0iMTAwJSI+PC9yZWN0Pjwvc3ZnPg==');
    }
    
    .popup-close {
        position: absolute;
        top: 15px;
        right: 15px;
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        z-index: 2;
    }
    
    .popup-close img {
        width: 20px;
        height: 20px;
    }
    
    .hotline-popup-body {
        padding: 10px 15px;
        overflow-y: auto;
        flex: 1;
        max-height: 60vh;
    }
    
    .hotline-item {
        background-color: white;
        border-radius: 10px;
        margin-bottom: 12px;
        padding: 15px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        border: 1px solid rgba(0, 0, 0, 0.05);
        display: flex;
        align-items: center;
        position: relative;
    }
    
    .hotline-icon {
        width: 45px;
        height: 45px;
        border-radius: 22.5px;
        background-color: #f0f7ff;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        flex-shrink: 0;
    }
    
    .hotline-icon img {
        width: 22px;
        height: 22px;
    }
    
    .hotline-info {
        flex: 1;
    }
    
    .hotline-name {
        font-size: 15px;
        font-weight: 600;
        margin-bottom: 3px;
        color: #333;
    }
    
    .hotline-description {
        font-size: 12px;
        color: #666;
        margin-bottom: 4px;
        line-height: 1.3;
    }
    
    .hotline-status {
        display: inline-block;
        font-size: 11px;
        padding: 2px 8px;
        border-radius: 10px;
        margin-right: 6px;
    }
    
    .status-active {
        background-color: #e9f7ef;
        color: #27ae60;
    }
    
    .status-busy {
        background-color: #fef5e9;
        color: #f39c12;
    }
    
    .status-closed {
        background-color: #f9ebeb;
        color: #e74c3c;
    }
    
    .hotline-hours {
        font-size: 11px;
        color: #999;
    }
    
    .call-btn {
        background-color: #4e9cff;
        width: 36px;
        height: 36px;
        border-radius: 18px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        box-shadow: 0 2px 5px rgba(78, 156, 255, 0.2);
        border: none;
        cursor: pointer;
    }
    
    .loading-indicator {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 30px 15px;
    }
    
    .spinner {
        width: 30px;
        height: 30px;
        border: 3px solid rgba(78, 156, 255, 0.2);
        border-top-color: #4e9cff;
        border-radius: 50%;
        animation: spin 0.8s linear infinite;
        margin-bottom: 10px;
    }
    
    @keyframes spin {
        to {
            transform: rotate(360deg);
        }
    }
    
    .loading-indicator p {
        font-size: 14px;
        color: #666;
        margin: 0;
    }
    
    .hotline-popup-footer {
        padding: 10px 15px 15px;
        display: flex;
        justify-content: center;
        border-top: 1px solid rgba(0, 0, 0, 0.05);
    }
    
    .pagination {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 5px;
    }
    
    .pagination-item {
        width: 30px;
        height: 30px;
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f5f5f5;
        color: #666;
        font-size: 14px;
        text-decoration: none;
        cursor: pointer;
    }
    
    .pagination-item.active {
        background-color: #4e9cff;
        color: white;
    }
    
    .pagination-item.disabled {
        opacity: 0.5;
        pointer-events: none;
    }
    
    .hotline-modal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 1100;
        align-items: center;
        justify-content: center;
    }
    
    .modal-content {
        width: 85%;
        max-width: 340px;
        background-color: white;
        border-radius: 14px;
        overflow: hidden;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    }
    
    .modal-header {
        background-color: #f5f5f7;
        padding: 15px 20px;
        position: relative;
        text-align: center;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }
    
    .modal-title {
        font-size: 18px;
        font-weight: 600;
        margin: 0 0 5px 0;
    }
    
    .modal-subtitle {
        font-size: 13px;
        opacity: 0.9;
        margin: 0;
    }
    
    .modal-body {
        padding: 20px;
    }
    
    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }
    
    /* 响应式布局样式 - 只添加必要的自适应样式，不改变现有设计 */
    
    /* 通用响应式容器 */
    .module-section {
        width: 100%;
        box-sizing: border-box;
    }
    
    .module-header {
        width: 100%;
        box-sizing: border-box;
    }
    
    .module-content {
        width: 100%;
        box-sizing: border-box;
    }
    
    /* 今日推荐响应式布局 */
    .today-recommend {
        display: flex;
        flex-direction: column;
        gap: 15px;
        width: 100%;
    }
    
    .recommend-left {
        width: 100%;
        height: 120px;
        position: relative;
        overflow: hidden;
        border-radius: 8px;
    }
    
    .recommend-right {
        display: flex;
        gap: 15px;
        width: 100%;
    }
    
    .recommend-item {
        flex: 1;
        height: 100px;
        border-radius: 8px;
        overflow: hidden;
        position: relative;
    }
    
    /* 心理咨询师列表响应式 */
    .service_style_3 {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
        width: 100%;
    }
    
    /* 课程列表响应式 */
    .tabs-nav {
        display: flex;
        overflow-x: auto;
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* IE/Edge */
    }
    
    .tabs-nav::-webkit-scrollbar {
        display: none; /* Chrome/Safari */
    }
    
    .course-item {
        width: 100%;
        margin-bottom: 15px;
    }
    
    /* 趣味AI测评响应式 */
    .ai-test-container {
        display: flex;
        flex-direction: column;
        gap: 15px;
        width: 100%;
    }
    
    .ai-test-sub {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }
    
    /* 直播讲堂响应式 */
    .live-course-list {
        display: grid;
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .live-course-item {
        width: 100%;
    }
    
    /* 媒体查询 - 小屏幕优化 */
    @media (max-width: 359px) {
        .nav-icon-image {
            width: 40px;
            height: 40px;
        }
        
        .nav-icon-text {
            font-size: 10px !important;
        }
        
        .service_style_3 {
            grid-template-columns: 1fr;
        }
        
        .recommend-right {
            flex-direction: column;
            gap: 10px;
        }
        
        .recommend-item {
            height: 80px;
        }
    }
    
    /* 媒体查询 - 中等屏幕 */
    @media (min-width: 576px) {
        .today-recommend {
            flex-direction: row;
        }
        
        .recommend-left {
            width: 60%;
            height: 180px;
        }
        
        .recommend-right {
            width: 40%;
            flex-direction: column;
        }
        
        .service_style_3 {
            grid-template-columns: repeat(3, 1fr);
        }
        
        .live-course-list {
            grid-template-columns: repeat(2, 1fr);
        }
    }
    
    /* 媒体查询 - 较大屏幕 */
    @media (min-width: 768px) {
        .service_style_3 {
            grid-template-columns: repeat(4, 1fr);
        }
    }
/* ====== 改进版 滚动公告 ====== */
    .scrolling-notice {
      /* 用 flex 布局，图标／文字／关闭按钮各占一块 */
      display: flex;
      align-items: center;
      padding: 0 16px;
      height: 24px;
      background: linear-gradient(120deg, #7F66DA 0%, #A58CF2 100%);
      /*border-radius: 40px;*/
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      overflow: hidden;
      gap: 8px;           /* 图标、文字、关闭按钮之间留空 */
    }
    
    .scrolling-notice .notice-icon {
      flex: 0 0 auto;
      font-size: 20px;
      color: #fff;
      animation: icon-glow 2s ease-in-out infinite alternate;
    }
    
    @keyframes icon-glow {
      from { text-shadow: 0 0 4px rgba(255,255,255,0.4); }
      to   { text-shadow: 0 0 10px rgba(255,255,255,0.7); }
    }
    
    .scrolling-notice .notice-text {
      flex: 1 1 auto;
      display: inline-block;
      white-space: nowrap;
      /* 纯白色＋强阴影，保证上下对比度 */
      color: #fff;
      font-size: 12px;
      font-weight: 600;
      text-shadow: 0 0 6px rgba(0,0,0,0.6);
      /* 文字滚动 */
      animation: scrollLTR 10s linear infinite;
    }
    
    .scrolling-notice:hover .notice-text {
      animation-play-state: paused;
    }
    
    @keyframes scrollLTR {
      0%   { transform: translateX(100%); }
      100% { transform: translateX(-100%); }
    }
    
    .scrolling-notice .close-btn {
      flex: 0 0 auto;
      font-size: 18px;
      color: rgba(255,255,255,0.85);
      cursor: pointer;
      transition: color .3s;
    }
    .scrolling-notice .close-btn:hover {
      color: #fff;
    }
    
    
    
    .feature-entries {
        display: grid;
        grid-template-columns: repeat(2, 1fr); /* 默认两列 */
        gap: 12px;
        padding: 0 15px;
        margin-bottom: 3px;
        background-color: #7F66DA;
    }
    
    .feature-entry {
        background: linear-gradient(135deg,#fcb356 , #f9d227);
        color: white;
        border-radius: 12px;
        padding: 16px;
        cursor: pointer;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        transition: transform 0.2s;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
/*.feature-entries {*/
/*    display: grid*/
/*;*/
/*    grid-template-columns: repeat(2, 1fr);*/
/*    gap: 12px;*/
/*    padding: 15px;*/
/*    margin-bottom: 3px;*/
/*    background-color: #7F66DA;*/
/*}*/

/*.feature-entry {*/
/*    background: linear-gradient(135deg,#fcb356 , #f9d227);*/
/*    color: white;*/
/*    border-radius: 12px;*/
/*    padding: 16px;*/
/*    cursor: pointer;*/
/*    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);*/
/*    transition: transform 0.2s;*/
/*    display: flex*/
/*;*/
/*    align-items: center;*/
/*    justify-content: center;*/
/*}*/
    
    .feature-entry:hover {
        transform: translateY(-2px);
    }
    
    .feature-title {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 6px;
    }
    
    .feature-desc {
        /*font-size: 13px;*/
        font-weight: 600;
        opacity: 0.85;
    }
    
    @media (max-width: 360px) {
        .feature-entries {
            grid-template-columns: 1fr; /* 小屏单列 */
        }
    }


</style>
@endsection

@section('content')
<!-- 搜索功能暂时隐藏
<div class="search-bar">
    <div class="search-input-wrap">
        <div class="search-icon"></div>
        <input type="text" class="search-input" placeholder="搜索心理咨询师、课程">
    </div>
    <div class="message-icon"></div>
</div>
-->

<div class="container">
    <!-- 顶部背景图和导航 -->
    <!--<div class="top-banner-section" style="background-image: url('https://3880611.s148i.faieduusr.com/2/101/AJQBCAAQAhgAIPGFwcAGKMaN-7cEMNwLOMwJ.jpg')">-->
    <div class="top-banner-section" style="background-image: url('{{ config('app.url') }}/storage/3b1b56d2bf5c86dc15aff3270a6c1e3.jpg')">
        
        <div class="banner-content">
            <div class="banner-main-image">
                <!-- 移除了主图片 -->
                <div style="width: 98%; margin: 130px auto 20px; background-color: #fff; border-radius: 15px; padding: 20px 5px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); position: relative; z-index: 10;">
                    <div style="display: flex; justify-content: space-around; margin-bottom: 25px;">
                        
                        <div class="nav-icon-item" data-url="{{ route('knowledge.articles') }}"onclick="window.location.href='{{ route("knowledge.articles") }}'" style="cursor: pointer;">
                            <div class="nav-icon-image" style="background-color: #a994fa; width: 32px; height: 32px; border-radius: 50%; display: flex; justify-content: center; align-items: center; margin-bottom: 2px;">
                                <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z'/%3E%3C/svg%3E" alt="心理科普" style="width: 18px; height: 18px;">
                            </div>
                            <div style="font-size: 10px; color: #333; text-align: center; line-height: 1.1;">心理科普</div>
                        </div>
                        
                        
                        <div class="nav-icon-item" data-url="{{ route('assessment.index') }}" onclick="window.location.href='{{ route("assessment.index") }}'" style="cursor: pointer;">
                            <div class="nav-icon-image" style="background-color: #ff7f7f; width: 32px; height: 32px; border-radius: 50%; display: flex; justify-content: center; align-items: center; margin-bottom: 2px;">
                                <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M19 3H5C3.9 3 3 3.9 3 5v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14h-3v-2h3v2zm0-3h-3v-2h3v2zm0-3h-3v-2h3v2z'/%3E%3C/svg%3E" alt="心理测评" style="width: 18px; height: 18px;">
                            </div>
                            <div style="font-size: 10px; color: #333; text-align: center; line-height: 1.1;">心理测评</div>
                        </div>
                        
                        
                        <!--<div class="nav-icon-item dev-feature" >-->
                        <!--    <div class="nav-icon-image" style="background-color: #ff7f7f; width: 32px; height: 32px; border-radius: 50%; display: flex; justify-content: center; align-items: center; margin-bottom: 2px;">-->
                        <!--        <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M19 3H5C3.9 3 3 3.9 3 5v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14h-3v-2h3v2zm0-3h-3v-2h3v2zm0-3h-3v-2h3v2z'/%3E%3C/svg%3E" alt="心理测评" style="width: 18px; height: 18px;">-->
                        <!--    </div>-->
                        <!--    <div style="font-size: 10px; color: #333; text-align: center; line-height: 1.1;">心理测评</div>-->
                        <!--</div>-->
                        
                        
                        <div class="nav-icon-item" data-url="{{ route('consultation.counselors') }}" onclick="window.location.href='{{ route("consultation.counselors") }}'" style="cursor: pointer;">
                            <div class="nav-icon-image" style="background-color: #6aa6fb; width: 32px; height: 32px; border-radius: 50%; display: flex; justify-content: center; align-items: center; margin-bottom: 2px;">
                                <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M21 6h-2v9H6v2c0 .55.45 1 1 1h11l4 4V7c0-.55-.45-1-1-1zm-4 6V3c0-.55-.45-1-1-1H3c-.55 0-1 .45-1 1v14l4-4h10c.55 0 1-.45 1-1z'/%3E%3C/svg%3E" alt="心理咨询" style="width: 18px; height: 18px;">
                            </div>
                            <div style="font-size: 10px; color: #333; text-align: center; line-height: 1.1;">心理咨询</div>
                        </div>
                        <div class="nav-icon-item" data-url="{{ route('news.index') }}" onclick="window.location.href='{{ route("news.index") }}'" style="cursor: pointer;">
                            <div class="nav-icon-image" style="background-color: #ffa64d; width: 32px; height: 32px; border-radius: 50%; display: flex; justify-content: center; align-items: center; margin-bottom: 2px;">
                                <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M20 4H4c-1.1 0-1.99.9-1.99 2L2 22l4-4h14c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm-5 14H4v-6h11v6zm0-8H4V6h11v4zm0-3H4V6h11v2z'/%3E%3C/svg%3E" alt="新闻资讯" style="width: 18px; height: 18px;">
                            </div>
                            <div style="font-size: 10px; color: #333; text-align: center; line-height: 1.1;">新闻资讯</div>
                        </div>
                    </div>
                    <div style="display: flex; justify-content: space-around;">
                        <!--<div class="nav-icon-item" onclick="showHotlinePopup()" style="cursor: pointer;">-->
                        <!--    <div class="nav-icon-image" style="background-color: #66cc99; width: 32px; height: 32px; border-radius: 50%; display: flex; justify-content: center; align-items: center; margin-bottom: 2px;">-->
                        <!--        <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M20 15.5c-1.25 0-2.45-.2-3.57-.57-.35-.11-.74-.03-1.02.24l-2.2 2.2c-2.83-1.44-5.15-3.75-6.59-6.59l2.2-2.21c.28-.26.36-.65.25-1C8.7 6.45 8.5 5.25 8.5 4c0-.55-.45-1-1-1H4c-.55 0-1 .45-1 1 0 9.39 7.61 17 17 17 .55 0 1-.45 1-1v-3.5c0-.55-.45-1-1-1z'/%3E%3C/svg%3E" alt="心理热线" style="width: 18px; height: 18px;">-->
                        <!--    </div>-->
                        <!--    <div style="font-size: 10px; color: #333; text-align: center; line-height: 1.1;">心理热线</div>-->
                        <!--</div>-->
                        
                        <!--<div class="nav-icon-item dev-feature" data-url="{{ route('live.index') }}">-->
                        <!--    <div class="nav-icon-image" style="background-color: #9c6ade; width: 32px; height: 32px; border-radius: 50%; display: flex; justify-content: center; align-items: center; margin-bottom: 2px;">-->
                        <!--        <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M17 10.5V7c0-.55-.45-1-1-1H4c-.55 0-1 .45-1 1v14l4-4h14c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm-5 14H4v-6h11v6zm0-8H4V6h11v4zm0-3H4V6h11v2z'/%3E%3C/svg%3E" alt="线上直播" style="width: 18px; height: 18px;">-->
                        <!--    </div>-->
                        <!--    <div style="font-size: 10px; color: #333; text-align: center; line-height: 1.1;">线上直播</div>-->
                        <!--</div>-->
                        
                        <div class="nav-icon-item" data-url="{{ route('consultation.offline_courses') }}" onclick="window.location.href='{{ route("consultation.offline_courses") }}'" style="cursor: pointer;">    
                            <div class="nav-icon-image" style="background-color: #9c6ade; width: 32px; height: 32px; border-radius: 50%; display: flex; justify-content: center; align-items: center; margin-bottom: 2px;">
                                <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M17 10.5V7c0-.55-.45-1-1-1H4c-.55 0-1 .45-1 1v14l4-4h14c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm-5 14H4v-6h11v6zm0-8H4V6h11v4zm0-3H4V6h11v2z'/%3E%3C/svg%3E" alt="讲座预约" style="width: 18px; height: 18px;">
                            </div>
                            <div style="font-size: 10px; color: #333; text-align: center; line-height: 1.1;">讲座预约</div>
                        </div>
                        <div class="nav-icon-item" onclick="window.location.href='{{ route("live.index") }}'" style="cursor: pointer;">
                            <div class="nav-icon-image" style="background-color: #667eea; width: 32px; height: 32px; border-radius: 50%; display: flex; justify-content: center; align-items: center; margin-bottom: 2px;">
                                <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z'/%3E%3C/svg%3E" alt="直播预告" style="width: 18px; height: 18px;">
                            </div>
                            <div style="font-size: 10px; color: #333; text-align: center; line-height: 1.1;">直播预告</div>
                        </div>
                        <!--<div class="nav-icon-item" data-url="{{ route('consultation.ai_consultation') }}" onclick="window.location.href='{{ route("consultation.ai_consultation") }}'" style="cursor: pointer;">-->
                        <!--    <div class="nav-icon-image" style="background-color: #ed664c; width: 32px; height: 32px; border-radius: 50%; display: flex; justify-content: center; align-items: center; margin-bottom: 2px;">-->
                        <!--        <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8zm.5-13H11v6l5.25 3.15.75-1.23-4.5-2.67z'/%3E%3C/svg%3E" alt="AI陪伴" style="width: 18px; height: 18px;">-->
                        <!--    </div>-->
                        <!--    <div style="font-size: 10px; color: #333; text-align: center; line-height: 1.1;">AI陪伴</div>-->
                        <!--</div>-->
                        <div class="nav-icon-item" data-url="{{ route('quiz.index') }}" onclick="window.location.href='{{ route("quiz.index") }}'" style="cursor: pointer;">
                            <div class="nav-icon-image" style="background-color: #ed664c; width: 32px; height: 32px; border-radius: 50%; display: flex; justify-content: center; align-items: center; margin-bottom: 2px;">
                                <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8zm.5-13H11v6l5.25 3.15.75-1.23-4.5-2.67z'/%3E%3C/svg%3E" alt="有奖问答" style="width: 18px; height: 18px;">
                            </div>
                            <div style="font-size: 10px; color: #333; text-align: center; line-height: 1.1;">有奖问答</div>
                        </div>
                        <!--<div class="nav-icon-item dev-feature">-->
                        <!--    <div class="nav-icon-image" style="background-color: #8c8c8c; width: 32px; height: 32px; border-radius: 50%; display: flex; justify-content: center; align-items: center; margin-bottom: 2px;">-->
                        <!--        <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M4 8h4V4H4v4zm6 12h4v-4h-4v4zm-6 0h4v-4H4v4zm0-6h4v-4H4v4zm6 0h4v-4h-4v4zm6-10v4h4V4h-4zm-6 4h4V4h-4v4zm-6 6h4v-4H4v4zm0 6h4v-4H4v4z'/%3E%3C/svg%3E" alt="更多功能" style="width: 18px; height: 18px;">-->
                        <!--    </div>-->
                        <!--    <div style="font-size: 10px; color: #333; text-align: center; line-height: 1.1;">更多功能</div>-->
                        <!--</div>-->
                        <div class="nav-icon-item" data-url="{{ route('stress_relief.index') }}" onclick="window.location.href='{{ route("stress_relief.index") }}'" style="cursor: pointer;">
                            <div class="nav-icon-image" style="background-color: #8c8c8c; width: 32px; height: 32px; border-radius: 50%; display: flex; justify-content: center; align-items: center; margin-bottom: 2px;">
                                <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M4 8h4V4H4v4zm6 12h4v-4h-4v4zm-6 0h4v-4H4v4zm0-6h4v-4H4v4zm6 0h4v-4h-4v4zm6-10v4h4V4h-4zm-6 4h4V4h-4v4zm-6 6h4v-4H4v4zm0 6h4v-4H4v4z'/%3E%3C/svg%3E" alt="心理减压" style="width: 18px; height: 18px;">
                            </div>
                            <div style="font-size: 10px; color: #333; text-align: center; line-height: 1.1;">心理减压</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 新增：两个长方形功能模块入口 -->
    <div class="feature-entries">
        <div class="feature-entry" onclick="window.location.href='{{ route("consultation.ai_consultation") }}'">
            <!--<div class="feature-title">AI陪伴咨询</div>-->
            <!--<div class="feature-desc">24小时智能心理陪伴助手</div>-->
            <div class="feature-desc">
              <svg t="1752655958154" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1643" width="20" height="20" margin-top="5px">
                <path d="M966.392 448.741L851.745 249.689a27.039 27.039 0 0 0-23.431-13.544h-42.126l-78.386-135.769a27.04 27.04 0 0 0-23.417-13.52H454.539a27.039 27.039 0 0 0-23.4 13.49l-20.99 36.249-156.729 0.025a27.04 27.04 0 0 0-23.413 13.52L115.084 349.191a27.041 27.041 0 0 0 0 27.04l20.925 36.243-78.386 135.769a27.041 27.041 0 0 0 0 27.04l114.923 199.052a27.04 27.04 0 0 0 23.417 13.52h41.85l78.386 135.769a27.04 27.04 0 0 0 23.417 13.52h229.845a27.04 27.04 0 0 0 23.417-13.52l20.925-36.243h156.773a27.04 27.04 0 0 0 23.417-13.52l114.923-199.052a27.041 27.041 0 0 0 0-27.04l-20.925-36.243 78.386-135.769a27.04 27.04 0 0 0 0.015-27.016zM767.96 557.282l-219.424-0.026 82.291-142.529a4.517 4.517 0 0 0 0-4.507L462.345 118.403h219.439l84.889 147.035 0.003 0.004 84.888 147.032-83.604 144.808z m62.68-90.538h104.516l-78.388 135.767L804.51 512l26.13-45.256z m-208.921-54.27L540.73 552.748 517.204 512l27.429-47.509c0.031-0.054 0.042-0.115 0.07-0.17 0.125-0.24 0.228-0.49 0.31-0.753 0.028-0.091 0.062-0.179 0.084-0.271 0.082-0.341 0.139-0.693 0.139-1.059a4.46 4.46 0 0 0-0.139-1.059c-0.022-0.092-0.056-0.181-0.084-0.272a4.565 4.565 0 0 0-0.31-0.753c-0.029-0.055-0.039-0.116-0.07-0.17L429.711 260.932a4.509 4.509 0 0 0-3.904-2.254h-49.653l78.384-135.768 167.181 289.564zM365.744 258.678h-52.255l-52.259-90.514 156.787-0.024-52.273 90.538z m-62.664 0h-49.655a4.508 4.508 0 0 0-3.904 2.254l-82.29 142.528-23.526-40.748 109.719-190.038 49.656 86.004z m-131.946 156.05l84.891-147.036h54.856l0.009 0.001 0.009-0.001h112.307l109.718 190.039H368.347a4.508 4.508 0 0 0-3.904 2.254L195.961 751.802 86.244 561.763l84.89-147.035z m199.814 52.016h161.975l-23.527 40.749h-54.859c-0.037 0-0.073 0.014-0.11 0.015a4.484 4.484 0 0 0-2.044 0.548c-0.08 0.044-0.154 0.093-0.231 0.142a4.454 4.454 0 0 0-0.652 0.499c-0.068 0.063-0.139 0.122-0.203 0.189a4.439 4.439 0 0 0-0.662 0.86L335.712 708.799a4.515 4.515 0 0 0 0 4.506l24.828 43.003H203.769l167.179-289.564z m-5.205 298.578l26.129 45.257-52.258 90.511-78.384-135.768h104.513z m31.334 54.272l24.826 43.001a4.508 4.508 0 0 0 3.904 2.253h164.579l-23.524 40.749h-219.44l49.655-86.003z m31.331 36.241L344.82 711.052l109.719-190.038 82.288 142.528a4.508 4.508 0 0 0 3.904 2.253h336.963l-109.718 190.04H428.408z m114.923-199.053l-80.987-140.276h47.055l27.427 47.51c0.044 0.076 0.103 0.137 0.152 0.21 0.087 0.134 0.177 0.264 0.279 0.389 0.095 0.115 0.197 0.221 0.301 0.326 0.1 0.1 0.2 0.197 0.311 0.288 0.13 0.107 0.266 0.201 0.406 0.292 0.069 0.046 0.128 0.102 0.2 0.144 0.034 0.02 0.073 0.025 0.107 0.044 0.314 0.17 0.64 0.315 0.984 0.406 0.035 0.01 0.071 0.009 0.107 0.018 0.302 0.072 0.61 0.11 0.92 0.119 0.044 0.001 0.088 0.017 0.132 0.017 0.051 0 0.099-0.018 0.15-0.019 0.062 0.002 0.119 0.018 0.18 0.018l229.505 0.027a4.509 4.509 0 0 0 3.904-2.253l24.841-43.027 53.556 92.759 0.001 0.003 24.83 43.005H543.331z m292.512-199.051l24.828-43.003a4.517 4.517 0 0 0 0-4.507l-82.289-142.529h47.325l109.457 190.039h-99.321z" p-id="1644"></path>
              </svg>
              AI陪伴咨询
            </div>

        </div>
        <!--<div class="feature-entry" onclick="window.location.href='{{ route("assessment.index") }}'">-->
        <div class="feature-entry" onclick="showHotlinePopup()" style="cursor: pointer;">
            <!--<div class="feature-title">心理援助热线</div>-->
            <!--<div class="feature-desc">快速获得心理援助咨询</div>-->
            <svg t="1752659915011" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="3696" width="20" height="20"><path d="M213.667 98.582c43.784 57.076 88.04 113.43 132.049 170.53 0.993 0.724 1.49 1.244 1.963 2.691 5.845 8.322 10.765 17.639 13.203 27.204 2.686 10.787 3.43 22.048 1.715 33.559-1.715 11.51-5.371 22.523-11.239 31.863-0.247 0.475-0.745 1.447-1.219 1.967-5.642 8.548-12.729 16.146-21.531 23.021-0.743 0.475-1.241 0.971-1.963 1.447-21.508 15.219-43.039 30.89-64.299 46.584-3.679 9.045-8.576 29.873 9.028 55.11 28.369 40.659 59.672 78.153 94.407 112.955 35.207 35.029 72.602 67.14 112.956 95.068 25.187 17.616 45.973 13.206 55.023 8.819 15.888-22.048 32.544-44.617 48.185-66.168 27.873-38.693 82.625-46.313 119.818-17.141h0.226c56.492 43.848 113.476 88.193 169.966 132.064 0.97 0.724 1.693 1.696 2.911 2.668 37.893 32.134 39.383 91.653 2.686 125.708-10.765 10.29-24.216 21.326-40.579 30.62-0.722 0.497-1.241 0.995-2.21 1.47-15.394 9.067-33.72 18.136-55.519 24.988-93.392 31.139-193.146 25.486-289.019-6.354-101.47-34.056-198.538-97.261-278.523-176.908C132.011 680.229 68.909 582.47 35.191 481.295 3.143 385.007-2.476 285.032 28.578 191.434c7.583-22.523 16.633-41.632 26.157-57.574v-0.249c9.772-16.418 20.538-29.873 30.581-40.931 9.028-9.045 18.8-16.396 29.813-20.307 0.993-0.497 1.963-1.493 2.686-1.493 10.517-3.416 21.531-4.885 33.018-4.387 30.561 0.971 41.821 11.262 62.834 32.089z m585.39 63.703c57.709 0 111.016 18.136 149.158 47.511 40.624 30.393 65.044 72.793 65.044 120.802 0 47.534-24.42 89.935-65.044 120.554l-1.444 1.221c-38.39 28.425-90.727 46.064-147.713 46.064-8.304 0-15.64-0.226-23.472-0.995-5.37-0.475-10.246-0.724-15.64-1.696l-61.613 36.996c-9.795 5.88-22.501 2.691-28.369-6.626-1.715-3.166-2.46-5.88-2.934-9.045l-9.298-59.791c-21.034-14.45-38.638-32.111-51.344-52.441-14.173-22.772-22.276-47.76-22.276-74.241 0-48.009 24.939-90.409 65.314-120.802 38.639-29.375 91.45-47.511 149.631-47.511z m124.716 80.617c-31.8-24.016-75.786-38.963-124.716-38.963-49.153 0-92.916 14.948-124.941 38.963-29.834 23.021-48.41 53.888-48.41 87.696 0 18.633 5.62 36.271 15.416 51.944 10.741 17.164 26.631 32.586 46.445 44.843 4.897 3.189 8.554 8.096 9.276 13.975l6.364 40.411 41.346-24.988c4.153-2.691 9.524-4.161 14.895-2.691a252.529 252.529 0 0 0 19.319 2.216c6.613 0.475 13.203 0.972 20.289 0.972 47.936 0 91.697-14.45 123.249-37.991l1.467-1.47c29.836-22.026 48.185-53.391 48.185-87.221 0.001-33.807-18.348-64.675-48.184-87.696zM717.877 366.396c-19.071-19.855-24.442-43.397-20.064-64.698 4.922-23.022 21.034-43.374 42.588-53.414 17.085-9.068 37.645-10.787 58.656-2.442 20.064-8.344 41.098-6.626 58.702 2.442v-0.497c45.499 22.296 58.679 81.341 21.531 119.083l-1.241 0.974-56.738 54.635-0.97 0.724c-6.115 5.653-13.948 8.842-21.282 8.842-8.304 0-16.633-3.686-22.479-9.566l-58.703-56.083z m29.34-28.946l0.519 0.52 51.322 48.732 50.375-48.732h0.495c27.376-27.702-8.802-70.329-40.579-51.22-6.364 3.663-14.196 4.161-21.034 0-31.552-19.108-67.256 23.519-41.098 50.7z m-455.779-26.21A73237.52 73237.52 0 0 1 159.41 140.959v-0.226c-5.89-7.348-17.13-7.846-23.494-0.994-7.333 7.598-14.918 17.661-22.003 29.398-7.337 12.279-14.423 27.204-20.065 43.848-26.157 78.65-21.034 163.678 6.364 246.512 30.333 90.884 87.545 178.603 160.399 251.622 72.898 73.246 160.441 130.074 251.393 160.964 82.151 27.182 167.235 32.338 246.001 6.377 16.114-5.405 30.31-12.257 42.023-19.606 0.497 0 1.016-0.498 1.241-0.746 11.984-7.101 21.758-14.45 29.588-21.8 6.364-5.88 6.84-16.666 0-22.546-0.497 0-0.497-0.499-0.97-0.972-56.737-44.345-113.454-88.216-170.192-132.561-6.837-5.382-16.882-4.161-21.756 3.188h-0.519a5386.289 5386.289 0 0 1-51.817 71.55c-2.687 3.912-6.366 7.824-10.993 10.787-0.248 0-60.891 43.102-134.984-8.593-44.504-31.094-85.58-65.421-122.278-102.645-37.397-37.492-71.632-78.4-102.463-122.52-52.066-74.241-8.531-135.23-8.531-135.23 2.189-4.161 5.349-7.349 9.298-10.063 23.449-17.141 46.943-34.282 70.144-51.672 7.109-5.902 12.481-13.003 6.613-22.048-0.497-0.996-0.723-1.493-0.971-1.743z" p-id="3697"></path></svg>
            <div class="feature-desc">心理援助热线</div>
        </div>
    </div>

    
    
    <!-- 文字滚动播报开始 -->
    <div 
      class="scrolling-notice" 
      id="scrollingNotice"
      @if($scrollLink) onclick="window.location.href='{{ $scrollLink }}'" style="cursor:pointer;" @endif
    >
      <div class="notice-text">
        {{ $scrollText }}
      </div>
    </div>
    <!-- 文字滚动播报结束 -->

    <!-- 心理科普 -->
    <div class="module-section">
        <div class="module-header" style="background-image: url('https://2678402.s81i.faiusr.com/2/101/AFEIgr2jARACGAAgxdGojwYo3sr0hgcw7gU4RkBl.jpg')">
            <div class="module-title">心理科普</div>
        </div>
        
        <!-- 分类导航区域 -->
        <div class="kp-category-section">
            <div class="kp-category-tabs-container">
                <div class="kp-category-tabs">
                    <div class="kp-category-tab active" data-category="articles">心理文章</div>
                    <div class="kp-category-tab" data-category="videos">心理视频</div>
                    <div class="kp-category-tab" data-category="courses">心理课堂</div>
                </div>
            </div>
        </div>
        
        <!-- 内容区域 -->
        <div class="knowledge-content-area">
            <!-- 心理文章内容 -->
            <div class="knowledge-content-section active" id="articles-content">
                <div class="knowledge-list">
                    @foreach($articles as $article)
                    <div class="knowledge-item" onclick="window.location.href='{{ route("knowledge.article_detail", ["id" => $article["id"]]) }}'" style="cursor: pointer;" data-url="{{ route('knowledge.article_detail', ['id' => $article['id']]) }}">
                        <div class="knowledge-image">
                            <img src="{{ $article['image'] }}" alt="{{ $article['title'] }}">
                        </div>
                        <div class="knowledge-info">
                            <div class="knowledge-title">{{ $article['title'] }}</div>
                            <div class="knowledge-meta">
                                <span class="knowledge-date">{{ $article['date'] }}</span>
                                <span class="knowledge-views">{{ $article['views'] }}阅读</span>
                            </div>
                        </div>
                    </div>
                    @endforeach
                    <div class="view-more-btn">
                        <a href="{{ route('knowledge.articles') }}" class="view-more-link">查看更多心理文章</a>
                    </div>
                </div>
            </div>
            
            <!-- 心理视频内容 -->
            <div class="knowledge-content-section" id="videos-content">
                <div class="knowledge-list">
                    @foreach($videos as $video)
                    <div class="knowledge-item" onclick="window.location.href='{{ route("knowledge.video_detail", ["id" => $video["id"]]) }}'" style="cursor: pointer;" data-url="{{ route('knowledge.video_detail', ['id' => $video['id']]) }}">
                        <div class="knowledge-image">
                            <img src="{{ $video['image'] }}" alt="{{ $video['title'] }}">
                            <div class="video-duration">{{ $video['duration'] }}</div>
                        </div>
                        <div class="knowledge-info">
                            <div class="knowledge-title">{{ $video['title'] }}</div>
                            <div class="knowledge-meta">
                                <span class="knowledge-date">{{ $video['date'] }}</span>
                                <span class="knowledge-views">{{ $video['views'] }}观看</span>
                            </div>
                        </div>
                    </div>
                    @endforeach
                    <div class="view-more-btn">
                        <a href="{{ route('knowledge.videos') }}" class="view-more-link">查看更多心理视频</a>
                    </div>
                </div>
            </div>
            
            <!-- 心理课堂内容 -->
            <div class="knowledge-content-section" id="courses-content">
                <div class="knowledge-list">
                    @foreach($coursesForKnowledge as $course)
                    <div class="knowledge-item" data-url="{{ route('knowledge.course_detail', ['id' => $course['id']]) }}">
                        <div class="knowledge-image">
                            <img src="{{ $course['image'] }}" alt="{{ $course['title'] }}">
                            <div class="course-lessons">{{ $course['lessons'] }}课时</div>
                        </div>
                        <div class="knowledge-info">
                            <div class="knowledge-title">{{ $course['title'] }}</div>
                            <div class="knowledge-meta">
                                <span class="knowledge-date">{{ $course['date'] }}</span>
                                <span class="knowledge-views">{{ $course['views'] }}人学习</span>
                            </div>
                        </div>
                    </div>
                    @endforeach
                    <div class="view-more-btn">
                        <a href="{{ route('knowledge.courses') }}" class="view-more-link">查看更多心理课堂</a>
                    </div>
                </div>
            </div>
        </div>
        </div>
    </div>

    <!-- 心理咨询 -->
    <div class="module-section">
        <div class="module-header" style="background-image: url('https://2678402.s81i.faiusr.com/2/101/AFEIgr2jARACGAAgxdGojwYo3sr0hgcw7gU4RkBl.jpg')">
            <div class="module-title">心理咨询</div>
        </div>
        <div class="module-content">
            <div class="service_style_3">
                @if(count($counselors) > 0)
                    @foreach($counselors as $counselor)
                    <div class="service-item" onclick="window.location.href='{{ route("consultation.counselor_detail", ["id" => $counselor->id]) }}'" style="cursor: pointer;">
                        <div class="img-wrapper bd-show">
                            <div class="comImageBox item-img c-img-base">
                                <img src="{{ $counselor->avatar ? asset('storage/'.$counselor->avatar) : asset('images/default-avatar.jpg') }}" alt="{{ $counselor->name }}">
                            </div>
                        </div>
                        <div class="service-info">
                            <div class="title-wrap">
                                <div class="ellipsis title">{{ $counselor->name }}</div>
                                <div class="ellipsis desc">{{ $counselor->title ?: '心理咨询师' }}</div>
                            </div>
                            <div class="price-wrapper">
                                <!-- <div class="price2">{{ $counselor->price > 0 ? '¥'.$counselor->price.'/次' : '价格面议' }}</div> -->
                                <div>
                                    <div class="orderCount">
                                        @if($counselor->support_text || $counselor->support_voice || $counselor->support_video)
                                            <span style="font-size: 10px;">
                                                @if($counselor->support_text)
                                                    文字
                                                @endif
                                                @if($counselor->support_voice)
                                                    @if($counselor->support_text) · @endif
                                                    语音
                                                @endif
                                                @if($counselor->support_video)
                                                    @if($counselor->support_text || $counselor->support_voice) · @endif
                                                    视频
                                                @endif
                                            </span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endforeach
                @else
                    <div style="width: 100%; text-align: center; padding: 20px; color: #666;">
                        暂无可咨询的心理医师
                    </div>
                @endif
            </div>
            
            <!-- 查看更多按钮 -->
            <div class="view-more-btn">
                <a href="{{ route('consultation.counselors') }}" class="view-more-link">查看更多心理咨询师</a>
            </div>
        </div>
    </div>

    <!-- 推荐课程 -->
    <div class="module-section">
        <div class="module-header" style="background-image: url('https://2678402.s81i.faiusr.com/2/101/AFEIgr2jARACGAAgxdGojwYo3sr0hgcw7gU4RkBl.jpg')">
            <div class="module-title">推荐课程</div>
        </div>
        <div class="module-content">
            <div class="tabs-container">
                <div class="tabs-nav">
                    @foreach($courses as $index => $categoryData)
                    <div class="tab-item {{ $index == 0 ? 'active' : '' }}">{{ $categoryData['category']->name }}</div>
                    @endforeach
                </div>
                <div class="tabs-content">
                    @foreach($courses as $index => $categoryData)
                    <div class="tab-pane {{ $index == 0 ? 'active' : '' }}">
                        @foreach($categoryData['courses'] as $course)
                        <div class="course-item" onclick="window.location.href='{{ route("knowledge.course_detail", ["id" => $course["id"]]) }}'">
                            <div class="course-item-image" style="position: relative;">
                                <img src="{{ $course['image'] }}" alt="{{ $course['title'] }}">
                                <div class="course-type-badge" style="position: absolute; top: 5px; right: 5px; background-color: rgba(0,0,0,0.6); color: white; padding: 2px 6px; border-radius: 4px; font-size: 12px;">
                                    @if(isset($course['type']) && $course['type'] == 2)
                                    <i class="material-icons" style="font-size: 14px; vertical-align: middle;">videocam</i> 视频
                                    @else
                                    <i class="material-icons" style="font-size: 14px; vertical-align: middle;">article</i> 图文
                                    @endif
                                </div>
                            </div>
                            <div class="course-item-info">
                                <div class="course-item-title">{{ $course['title'] }}</div>
                                <div class="course-item-desc">{{ $course['description'] ?? '' }}</div>
                                <div class="course-item-price">
                                    <!-- <span class="price-free">免费</span> -->
                                    <!-- <span class="subscription">{{ $course['views'] ?? 0 }} 人订阅</span> -->
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                    @endforeach
                </div>
            </div>
            <!-- 查看更多按钮 -->
            <div class="view-more-btn">
                <a href="{{ route('knowledge.courses') }}" class="view-more-link">查看更多课程</a>
            </div>
        </div>
    </div>
    
    
        <!-- 最新资讯 -->
    <div class="module-section">
        <div class="module-header" style="background-image: url('https://2678402.s81i.faiusr.com/2/101/AFEIgr2jARACGAAgxdGojwYo3sr0hgcw7gU4RkBl.jpg')">
            <div class="module-title">最新资讯</div>
        </div>
        <div class="module-content">
            <div class="tabs-container">
                <div class="tabs-content">
                        @foreach($news as $new)
                        <div class="course-item" onclick="window.location.href='{{ route("news.show", ["slug" => $new["slug"]]) }}'">
                            <div class="course-item-image" style="position: relative;">
                                <img src="{{ $new['image'] }}" alt="{{ $new['title'] }}">
                                <div class="course-type-badge" style="position: absolute; top: 5px; right: 5px; background-color: rgba(0,0,0,0.6); color: white; padding: 2px 6px; border-radius: 4px; font-size: 12px;">
                                </div>
                            </div>
                            <div class="course-item-info">
                                <div class="course-item-title">{{ $new['title'] }}</div>
                                <div class="course-item-desc">{{ $new['description'] ?? '' }}</div>
                                <div class="course-item-price">
                                     <span class="price-free">{{ $new['date'] ?? '' }}</span> 
                                     <span class="subscription">{{ $new['views'] ?? 0 }} 人查看</span> 
                                </div>
                            </div>
                        </div>
                        @endforeach
                </div>
            </div>
            <!-- 查看更多按钮 -->
            <div class="view-more-btn">
                <a href="{{ route('news.index') }}" class="view-more-link">查看更多新闻</a>
            </div>
        </div>
    </div>
    
    <!-- 直播间 -->
    @if(count($liveShows) > 0)
    <div class="module-section">
        <div class="module-header" style="background-image: url('https://2678402.s81i.faiusr.com/2/101/AFEIgr2jARACGAAgxdGojwYo3sr0hgcw7gU4RkBl.jpg')">
            <div class="module-title">
                直播预告
                <!--<span style="font-size: 12px; margin-left: 8px; color: #fff;">-->
                <!--    @if($liveShows->where('status', 'live')->count() > 0)-->
                <!--    🔴 {{ $liveShows->where('status', 'live')->count() }}场直播中-->
                <!--    @else-->
                <!--    📅 {{ $liveShows->where('status', 'upcoming')->count() }}场即将开始-->
                <!--    @endif-->
                <!--</span>-->
            </div>
        </div>
        <div class="module-content">
            <div class="live-shows-container" style="display: flex; flex-direction: column; gap: 12px; padding: 0 15px;">
                @foreach($liveShows as $liveShow)
                <div class="live-show-card" 
                     data-url="{{ route('live.show',["id"=>$liveShow["id"]]) }}"onclick="window.location.href='{{ route("live.show",["id"=>$liveShow["id"]]) }}'"
                     style="display: flex; background: #fff; border-radius: 12px; overflow: hidden; border: 1px solid #e5e7eb; cursor: pointer; transition: all 0.3s ease; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                    
                    <!-- 封面图片 -->
                    <div style="position: relative; width: 120px; height: 90px; flex-shrink: 0;">
                        <img src="{{ $liveShow->cover_image ? asset('storage/' . $liveShow->cover_image) : asset('images/default-live-cover.jpg') }}" 
                             alt="{{ $liveShow->title }}" 
                             style="width: 100%; height: 100%; object-fit: cover;">
                        
                        <!-- 状态标签 -->
                        <div style="position: absolute; top: 6px; left: 6px;">
                            @if($liveShow->status === 'live')
                            <span style="background: #ef4444; color: white; padding: 2px 6px; border-radius: 12px; font-size: 10px; font-weight: 500; display: flex; align-items: center;">
                                <span style="width: 6px; height: 6px; background: white; border-radius: 50%; margin-right: 4px; animation: pulse 2s infinite;"></span>
                                直播中
                            </span>
                            @elseif($liveShow->status === 'upcoming')
                            <span style="background: #f97316; color: white; padding: 2px 6px; border-radius: 12px; font-size: 10px; font-weight: 500;">
                                即将开始
                            </span>
                            @else
                            <span style="background: #6b7280; color: white; padding: 2px 6px; border-radius: 12px; font-size: 10px; font-weight: 500;">
                                已结束
                            </span>
                            @endif
                        </div>

                        <!-- 精选标签 -->
                        @if($liveShow->is_featured)
                        <div style="position: absolute; top: 6px; right: 6px;">
                            <span style="background: #eab308; color: white; padding: 2px 6px; border-radius: 12px; font-size: 10px; font-weight: 500;">
                                精选
                            </span>
                        </div>
                        @endif

                        <!-- 点击提示 -->
                        @if($liveShow->live_url && $liveShow->status !== 'ended')
                        <div style="position: absolute; inset: 0; background: rgba(0,0,0,0.2); display: flex; align-items: center; justify-content: center; opacity: 0; transition: opacity 0.3s;">
                            <div style="background: rgba(255,255,255,0.9); border-radius: 50%; padding: 8px;">
                                <svg style="width: 20px; height: 20px; color: #3b82f6;" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                        </div>
                        @endif
                    </div>

                    <!-- 内容信息 -->
                    <div style="flex: 1; padding: 12px; display: flex; flex-direction: column; justify-content: space-between;">
                        <!-- 标题和描述 -->
                        <div>
                            <h3 style="font-size: 15px; font-weight: 600; color: #111827; margin: 0 0 6px 0; line-height: 1.3; 
                                       display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical; overflow: hidden;">
                                {{ $liveShow->title }}
                            </h3>
                            
                            @if($liveShow->description)
                            <p style="font-size: 12px; color: #6b7280; margin: 0 0 8px 0; line-height: 1.4;
                                      display: -webkit-box; -webkit-line-clamp: 1; -webkit-box-orient: vertical; overflow: hidden;">
                                {{ $liveShow->description }}
                            </p>
                            @endif
                        </div>

                        <!-- 底部信息 -->
                        <div style="display: flex; justify-content: space-between; align-items: flex-end;">
                            <div>
                                <!-- 咨询师信息 -->
                                @if($liveShow->counselor)
                                <div style="display: flex; align-items: center; margin-bottom: 4px;">
                                    <img src="{{ $liveShow->counselor->avatar ? asset('storage/' . $liveShow->counselor->avatar) : asset('images/default-avatar.jpg') }}" 
                                         alt="{{ $liveShow->counselor->name }}" 
                                         style="width: 16px; height: 16px; border-radius: 50%; margin-right: 4px;">
                                    <span style="font-size: 11px; color: #374151;">{{ $liveShow->counselor->name }}</span>
                                </div>
                                @endif

                                <!-- 时间信息 -->
                                <div style="font-size: 11px; color: #3b82f6;">
                                    {{ $liveShow->formatted_scheduled_at }}
                                </div>
                            </div>

                            <!-- 状态提示 -->
                            <div style="font-size: 10px; color: {{ $liveShow->live_url && $liveShow->status !== 'ended' ? '#10b981' : '#6b7280' }}; 
                                        display: flex; align-items: center;">
                                @if($liveShow->live_url && $liveShow->status !== 'ended')
                                <svg style="width: 12px; height: 12px; margin-right: 2px;" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                点击{{ $liveShow->status === 'live' ? '进入' : '预约' }}
                                @elseif(!$liveShow->live_url)
                                <svg style="width: 12px; height: 12px; margin-right: 2px;" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                </svg>
                                暂未设置
                                @else
                                <svg style="width: 12px; height: 12px; margin-right: 2px;" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                                </svg>
                                已结束
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>
            
            <!-- 查看更多按钮 -->
            <div class="view-more-btn">
                <a href="{{ route('live.index') }}" class="view-more-link">查看更多直播</a>
            </div>
        </div>
    </div>
    @endif

    <!-- 趣味AI测评
    <div class="module-section">
        <div class="module-header" style="background-image: url('https://2678402.s81i.faiusr.com/2/101/AFEIgr2jARACGAAgxdGojwYo3sr0hgcw7gU4RkBl.jpg')">
            <div class="module-title">趣味AI测评</div>
        </div>
        <div class="module-content">
            <div class="ai-test-container">
                <div class="ai-test-main dev-feature" data-url="{{ route('assessment.fun_tests') }}">
                    <img src="https://3880611.s148i.faieduusr.com/4/101/AJQBCAAQAhgAIMPdksAGKMqA9b0FMK4FONwB!1000x1000.png?_tm=3" alt="AI测评">
                </div>
                <div class="ai-test-sub">
                    <div class="ai-test-item dev-feature" data-url="{{ route('assessment.fun_tests') }}">
                        <img src="https://3880611.s148i.faieduusr.com/4/101/AJQBCAAQAhgAIJ-bksAGKI3v1K4DMM4COKAB!600x600.png?_tm=3" alt="电梯情境心理测试">
                        <div class="ai-test-title">电梯情境心理测试</div>
                    </div>
                    <div class="ai-test-item dev-feature" data-url="{{ route('assessment.fun_tests') }}">
                        <img src="https://3880611.s148i.faieduusr.com/4/101/AJQBCAAQAhgAIJ-bksAGKJew5JkDMM4COKAB!600x600.png?_tm=3" alt="房子心理测试">
                        <div class="ai-test-title">房子心理测试</div>
                    </div>
                </div>
            </div>
        </div>
    </div> -->

    <!-- 直播讲堂 -->
    <!--<div class="module-section">-->
    <!--    <div class="module-header" style="background-image: url('https://2678402.s81i.faiusr.com/2/101/AFEIgr2jARACGAAgxdGojwYo3sr0hgcw7gU4RkBl.jpg')">-->
    <!--        <div class="module-title">直播讲堂</div>-->
    <!--    </div>-->
    <!--    <div class="module-content">-->
    <!--        <div class="live-course-list">-->
    <!--            <div class="live-course-item">-->
    <!--                <div class="live-course-image">-->
    <!--                    <img src="https://img.oneself.icu/uploads/682064bcd5b12_d240764088e3e068.jpg" alt="直播课程">-->
    <!--                    <div class="live-status ended">-->
    <!--                        <span class="status-dot"></span>-->
    <!--                        <span>已结束</span>-->
    <!--                    </div>-->
    <!--                </div>-->
    <!--                <div class="live-course-info">-->
    <!--                    <div class="live-course-title">刘天天《学习认真知行为治疗图解指</div>-->
                        <!-- <div class="live-course-price">免费</div> -->
    <!--                </div>-->
    <!--            </div>-->
    <!--            <div class="live-course-item">-->
    <!--                <div class="live-course-image">-->
    <!--                    <img src="https://img.oneself.icu/uploads/682064bcd5b12_d240764088e3e068.jpg" alt="直播课程">-->
    <!--                    <div class="live-status upcoming">-->
    <!--                        <span class="status-clock"></span>-->
    <!--                        <span>开播预约</span>-->
    <!--                        <span class="status-divider"></span>-->
    <!--                        <span>还有02天</span>-->
    <!--                    </div>-->
    <!--                </div>-->
    <!--                <div class="live-course-info">-->
    <!--                    <div class="live-course-title">刘天天《学习认真知行为治疗图解指222</div>-->
                        <!-- <div class="live-course-price">免费</div> -->
    <!--                </div>-->
    <!--            </div>-->
    <!--        </div>-->
    <!--    </div>-->
    <!--</div>-->
</div>

<!-- 添加底部空间 -->
<div style="height: 80px; width: 100%;"></div>

<!-- 开发中提示 -->
<div class="dev-popup" id="devPopup">功能开发中，敬请期待</div>

<!-- 心理援助热线弹窗 -->
<div class="hotline-popup" id="hotlinePopup">
    <div class="hotline-popup-content">
        <div class="hotline-popup-header">
            <div class="hotline-pattern"></div>
            <h1>心理援助热线</h1>
            <p>专业心理咨询师为您提供心理支持和帮助</p>
            <div class="popup-close" onclick="closeHotlinePopup()">
                <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z'/%3E%3C/svg%3E" alt="关闭">
            </div>
        </div>
        <div class="hotline-popup-body" id="hotlineList">
            <!-- 热线列表将通过AJAX加载 -->
            <div class="loading-indicator">
                <div class="spinner"></div>
                <p>加载中...</p>
            </div>
        </div>
        <div class="hotline-popup-footer" id="hotlinePagination">
            <!-- 分页将通过AJAX加载 -->
        </div>
    </div>
</div>

@endsection

@section('scripts')
<script src="{{ asset('js/knowledge-tabs.js') }}"></script>
<script>
    // 热线弹窗相关函数
    let currentPage = 1;
    let hotlineData = [];
    
    // 显示热线弹窗
    function showHotlinePopup() {
        document.getElementById('hotlinePopup').style.display = 'flex';
        loadHotlineData(1);
    }
    
    // 关闭热线弹窗
    function closeHotlinePopup() {
        document.getElementById('hotlinePopup').style.display = 'none';
    }
    
    // 加载热线数据
    function loadHotlineData(page) {
        currentPage = page;
        const hotlineList = document.getElementById('hotlineList');
        
        // 显示加载中
        hotlineList.innerHTML = `
            <div class="loading-indicator">
                <div class="spinner"></div>
                <p>加载中...</p>
            </div>
        `;
        
        // 使用AJAX请求热线数据
        fetch(`{{ route('hotline') }}?page=${page}`)
            .then(response => response.text())
            .then(html => {
                // 创建临时元素存储响应HTML
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = html;
                
                // 提取热线列表
                const hotlineItems = tempDiv.querySelectorAll('.hotline-item');
                let hotlineHtml = '';
                
                if (hotlineItems.length > 0) {
                    hotlineItems.forEach(item => {
                        hotlineHtml += item.outerHTML;
                    });
                } else {
                    hotlineHtml = `
                        <div class="hotline-item" style="justify-content: center; padding: 30px 15px;">
                            <p style="margin: 0; color: #999; text-align: center;">暂无可用的心理援助热线</p>
                        </div>
                    `;
                }
                
                hotlineList.innerHTML = hotlineHtml;
                
                // 绑定热线项点击事件
                bindHotlineEvents();
                
                // 提取并显示分页
                const pagination = tempDiv.querySelector('.pagination');
                const paginationContainer = document.getElementById('hotlinePagination');
                
                if (pagination) {
                    paginationContainer.innerHTML = pagination.outerHTML;
                    
                    // 绑定分页点击事件
                    const pageLinks = paginationContainer.querySelectorAll('.pagination-item');
                    pageLinks.forEach(link => {
                        link.addEventListener('click', function(e) {
                            e.preventDefault();
                            
                            if (!this.classList.contains('disabled') && !this.classList.contains('active')) {
                                const pageNum = parseInt(this.dataset.page || this.textContent);
                                loadHotlineData(pageNum);
                            }
                        });
                    });
                } else {
                    paginationContainer.innerHTML = '';
                }
            })
            .catch(error => {
                console.error('加载热线数据出错:', error);
                hotlineList.innerHTML = `
                    <div class="hotline-item" style="justify-content: center; padding: 30px 15px;">
                        <p style="margin: 0; color: #999; text-align: center;">加载失败，请重试</p>
                    </div>
                `;
            });
    }
    
    // 绑定热线项点击事件
    function bindHotlineEvents() {
        const callButtons = document.querySelectorAll('#hotlineList .call-btn');
        callButtons.forEach(button => {
            // 清除旧事件
            const newButton = button.cloneNode(true);
            button.parentNode.replaceChild(newButton, button);
            
            // 添加新事件
            newButton.addEventListener('click', function(e) {
                e.stopPropagation();
                const item = this.closest('.hotline-item');
                showHotlineModal(
                    item.dataset.id,
                    item.dataset.number,
                    item.dataset.name
                );
            });
        });
    }
    
    // 热线拨打弹窗
    function showHotlineModal(id, phoneNumber, name) {
        // 创建热线拨打弹窗（如果不存在）
        let modal = document.getElementById('hotlineModal');
        if (!modal) {
            const modalHtml = `
                <div class="hotline-modal" id="hotlineModal">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h2 class="modal-title">心理援助热线</h2>
                            <p class="modal-subtitle">专业咨询师为您提供帮助</p>
                            <div class="modal-close" onclick="closeHotlineModal()">
                                <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 24 24' fill='white'%3E%3Cpath d='M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z'/%3E%3C/svg%3E" alt="关闭">
                            </div>
                        </div>
                        <div class="modal-body">
                            <div style="text-align: center; margin-bottom: 15px;">
                                <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='48' height='48' viewBox='0 0 24 24' fill='%234e9cff'%3E%3Cpath d='M20 15.5c-1.25 0-2.45-.2-3.57-.57-.35-.11-.74-.03-1.02.24l-2.2 2.2c-2.83-1.44-5.15-3.75-6.59-6.59l2.2-2.21c.28-.26.36-.65.25-1C8.7 6.45 8.5 5.25 8.5 4c0-.55-.45-1-1-1H4c-.55 0-1 .45-1 1 0 9.39 7.61 17 17 17 .55 0 1-.45 1-1v-3.5c0-.55-.45-1-1-1z'/%3E%3C/svg%3E" alt="热线电话">
                            </div>
                            <div class="hotline-number" id="modalPhoneNumber" style="margin-top: 0;"></div>
                            <div class="modal-buttons">
                                <button class="modal-btn btn-call" id="callButton">
                                    <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='white' width='18' height='18'%3E%3Cpath d='M20 15.5c-1.25 0-2.45-.2-3.57-.57-.35-.11-.74-.03-1.02.24l-2.2 2.2c-2.83-1.44-5.15-3.75-6.59-6.59l2.2-2.21c.28-.26.36-.65.25-1C8.7 6.45 8.5 5.25 8.5 4c0-.55-.45-1-1-1H4c-.55 0-1 .45-1 1 0 9.39 7.61 17 17 17 .55 0 1-.45 1-1v-3.5c0-.55-.45-1-1-1z'/%3E%3C/svg%3E" alt="拨打">
                                    立即拨打
                                </button>
                                <button class="modal-btn btn-chat" id="chatButton" style="opacity: 0.85; background-color: #f8f8f8; border: 1px dashed #ddd; color: #888;">
                                    <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23888' width='18' height='18'%3E%3Cpath d='M20 2H4c-1.1 0-1.99.9-1.99 2L2 22l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zM6 9h12v2H6V9zm8 5H6v-2h8v2zm4-6H6V6h12v2z'/%3E%3C/svg%3E" alt="聊天">
                                    在线援助 <span class="dev-badge">开发中</span>
                                </button>
                            </div>
                            <div class="modal-separator"></div>
                            <div class="modal-footer">
                                所有通话内容严格保密，请放心咨询
                            </div>
                        </div>
                    </div>
                </div>
            `;
            document.body.insertAdjacentHTML('beforeend', modalHtml);
            modal = document.getElementById('hotlineModal');
        }
        
        // 设置电话号码
        document.getElementById('modalPhoneNumber').textContent = phoneNumber;
        
        // 设置拨打按钮事件
        document.getElementById('callButton').onclick = function() {
            window.location.href = 'tel:' + phoneNumber;
            
            // 记录拨打记录
            fetch('{{ route("hotline.record_usage") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                body: JSON.stringify({
                    hotline_id: id,
                    action: 'call'
                })
            });
            
            closeHotlineModal();
        };
        
        // 设置在线援助按钮事件（开发中提示）
        document.getElementById('chatButton').onclick = function() {
            const devPopup = document.getElementById('devPopup');
            devPopup.style.display = 'block';
            
            setTimeout(function() {
                devPopup.style.display = 'none';
                closeHotlineModal();
            }, 2000);
            
            // 记录聊天记录
            fetch('{{ route("hotline.record_usage") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                body: JSON.stringify({
                    hotline_id: id,
                    action: 'chat'
                })
            });
        };
        
        // 显示弹窗
        modal.style.display = 'flex';
    }
    
    function closeHotlineModal() {
        const modal = document.getElementById('hotlineModal');
        if (modal) {
            modal.style.display = 'none';
        }
    }
    
    // 点击弹窗外部关闭弹窗
    document.addEventListener('click', function(event) {
        const popup = document.getElementById('hotlinePopup');
        const modal = document.getElementById('hotlineModal');
        
        if (event.target === popup) {
            closeHotlinePopup();
        }
        
        if (modal && event.target === modal) {
            closeHotlineModal();
        }
    });
    
    // 选项卡切换
    document.addEventListener('DOMContentLoaded', function() {
        const tabItems = document.querySelectorAll('.tab-item');
        const tabPanes = document.querySelectorAll('.tab-pane');

        tabItems.forEach((tab, index) => {
            tab.addEventListener('click', () => {
                // 移除所有活动状态
                tabItems.forEach(item => item.classList.remove('active'));
                tabPanes.forEach(pane => pane.classList.remove('active'));

                // 添加当前活动状态
                tab.classList.add('active');
                if (tabPanes[index]) {
                    tabPanes[index].classList.add('active');
                }
            });
        });
    });

    // 心理咖普导航标签切换
    document.addEventListener('DOMContentLoaded', function() {
        const navItems = document.querySelectorAll('.knowledge-nav-item');
        const contentSections = document.querySelectorAll('.knowledge-content-section');
        
        navItems.forEach((navItem, index) => {
            navItem.addEventListener('click', function() {
                // 移除所有导航项的活动状态
                navItems.forEach(item => {
                    item.classList.remove('active');
                });
                
                // 添加当前导航项的活动状态
                navItem.classList.add('active');
                
                // 隐藏所有内容区域
                contentSections.forEach(section => {
                    section.classList.remove('active');
                });
                
                // 显示当前内容区域
                contentSections[index].classList.add('active');
            });
        });
        
        // 推荐课程标签切换
        const courseTabItems = document.querySelectorAll('.tabs-container .tab-item');
        const courseTabPanes = document.querySelectorAll('.tabs-container .tab-pane');
        
        courseTabItems.forEach((tabItem, index) => {
            tabItem.addEventListener('click', function() {
                // 移除所有标签的活动状态
                courseTabItems.forEach(item => {
                    item.classList.remove('active');
                });
                
                // 添加当前标签的活动状态
                tabItem.classList.add('active');
                
                // 隐藏所有标签面板
                courseTabPanes.forEach(pane => {
                    pane.classList.remove('active');
                });
                
                // 显示当前标签面板
                courseTabPanes[index].classList.add('active');
            });
        });
    });

    // 心理科普导航图标点击事件
    document.addEventListener('DOMContentLoaded', function() {
        const knowledgeIcon = document.querySelector('.nav-icon-item[data-url="{{ route('knowledge.articles') }}"]');
        if (knowledgeIcon) {
            knowledgeIcon.addEventListener('click', function(e) {
                e.preventDefault();
                const url = this.getAttribute('data-url');
                if (url) {
                    window.location.href = url;
                }
            });
        }
        
        // 心理文章项目点击事件
        const articleItems = document.querySelectorAll('#articles-content .knowledge-item');
        articleItems.forEach(item => {
            item.addEventListener('click', function(e) {
                const url = this.getAttribute('data-url');
                if (url) {
                    window.location.href = url;
                }
            });
        });
        
        // 心理视频项目点击事件
        const videoItems = document.querySelectorAll('#videos-content .knowledge-item');
        videoItems.forEach(item => {
            item.addEventListener('click', function(e) {
                const url = this.getAttribute('data-url');
                if (url) {
                    window.location.href = url;
                }
            });
        });
    });

    // 心理咨询导航图标点击事件
    document.addEventListener('DOMContentLoaded', function() {
        const consultationIcon = document.querySelector('.nav-icon-item[data-url="{{ route("consultation.counselors") }}"]');
        if (consultationIcon) {
            consultationIcon.addEventListener('click', function(e) {
                e.preventDefault();
                const url = this.getAttribute('data-url');
                if (url) {
                    window.location.href = url;
                }
            });
        }
    });

    // 心理测评导航图标点击事件
    document.addEventListener('DOMContentLoaded', function() {
        const assessmentIcon = document.querySelector('.nav-icon-item[data-url="{{ route("assessment.index") }}"]');
        if (assessmentIcon) {
            assessmentIcon.addEventListener('click', function(e) {
                e.preventDefault();
                const url = this.getAttribute('data-url');
                if (url) {
                    window.location.href = url;
                }
            });
        }
    });

    // 开发中功能点击事件
    document.addEventListener('DOMContentLoaded', function() {
        const devFeatures = document.querySelectorAll('.dev-feature');
        const devPopup = document.getElementById('devPopup');
        
        devFeatures.forEach(feature => {
            feature.addEventListener('click', function(e) {
                e.preventDefault();
                // 显示弹窗
                devPopup.style.display = 'block';
                
                // 3秒后自动隐藏
                setTimeout(function() {
                    devPopup.style.display = 'none';
                    
                    // 如果功能已开发完成，可以取消下面的注释来实现跳转
                    // const url = feature.getAttribute('data-url');
                    // if (url) {
                    //     window.location.href = url;
                    // }
                }, 2000);
            });
        });
        
        // 今日推荐中的按钮也添加开发中提示
        const consultBtn = document.querySelector('.consult-btn');
        if (consultBtn) {
            consultBtn.addEventListener('click', function(e) {
                e.preventDefault();
                devPopup.style.display = 'block';
                
                setTimeout(function() {
                    devPopup.style.display = 'none';
                    
                    // 如果功能已开发完成，可以取消下面的注释来实现跳转
                    // const url = consultBtn.getAttribute('data-url');
                    // if (url) {
                    //     window.location.href = url;
                    // }
                }, 2000);
            });
        }
        
        // 推荐项目点击也添加开发中提示
        const recommendItems = document.querySelectorAll('.recommend-item');
        if (recommendItems.length > 0) {
            recommendItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    e.preventDefault();
                    devPopup.style.display = 'block';
                    
                    setTimeout(function() {
                        devPopup.style.display = 'none';
                        
                        // 如果功能已开发完成，可以取消下面的注释来实现跳转
                        // const url = item.getAttribute('data-url');
                        // if (url) {
                        //     window.location.href = url;
                        // }
                    }, 2000);
                });
            });
        }
    });
    
    document.getElementById('scrollingNotice')
  .querySelector('.close-btn')
  .addEventListener('click', function(){
    document.getElementById('scrollingNotice')
      .style.display = 'none';
  });
</script>
@endsection
