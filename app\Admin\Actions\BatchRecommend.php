<?php

namespace App\Admin\Actions;

use Dcat\Admin\Grid\BatchAction;
use Illuminate\Http\Request;
use Dcat\Admin\Admin;

class BatchRecommend extends BatchAction
{
    protected $title = '批量推荐';
    
    protected $model;
    
    public function __construct($model = null)
    {
        $this->model = $model;
    }
    
    // 确认弹窗信息
    public function confirm()
    {
        return '您确定要批量推荐选中的记录吗？';
    }
    
    // 处理请求
    public function handle(Request $request)
    {
        // 获取选中的行ID
        $keys = $this->getKey();
        
        // 获取模型
        $model = $request->get('model');
        $model = "\\App\\Models\\{$model}";
        
        // 更新记录
        $model::whereIn('id', $keys)->update(['is_recommended' => 1]);
        
        // 返回响应
        return $this->response()
            ->success('已成功推荐 ' . count($keys) . ' 条记录')
            ->refresh();
    }
    
    // 表单参数
    public function parameters()
    {
        return [
            'model' => $this->model ?: (new \ReflectionClass($this->parent->model()))->getShortName(),
        ];
    }
}
