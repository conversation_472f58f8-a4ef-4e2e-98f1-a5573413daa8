@extends('layouts.app')

@section('title', '帮助中心 - 心理咨询平台')

@section('custom-styles')
<style>
    body {
        background-color: #f8f9fa;
    }
    
    .container {
        max-width: 960px;
        margin: 0 auto;
        padding-bottom: 80px !important; /* 为底部菜单栏预留空间 */
    }
    
    .page-header {
        display: flex;
        align-items: center;
        background: linear-gradient(135deg, #5b7cef 0%, #3c67e3 100%);
        color: white;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        border-radius: 0 0 10px 10px;
        padding: 12px 15px;
        margin-bottom: 15px;
    }
    
    .back-button {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 30px;
        height: 30px;
    }
    
    .back-icon {
        border-top: 2px solid white;
        border-left: 2px solid white;
        width: 12px;
        height: 12px;
        transform: rotate(-45deg);
    }
    
    .page-header h1 {
        font-size: 18px;
        font-weight: 500;
        margin: 0 auto;
        text-align: center;
        flex-grow: 1;
        margin-left: -30px;
        text-shadow: 0 1px 2px rgba(0,0,0,0.1);
    }
    
    .help-card {
        background-color: #fff;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        margin: 15px;
        padding: 20px;
        border: 1px solid rgba(0, 0, 0, 0.03);
    }
    
    .section-title {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 20px;
        color: #333;
        border-bottom: 1px solid #f0f0f0;
        padding-bottom: 10px;
    }
    
    .faq-item {
        margin-bottom: 20px;
    }
    
    .faq-question {
        font-weight: 600;
        font-size: 16px;
        color: #333;
        margin-bottom: 8px;
        display: flex;
        align-items: center;
        cursor: pointer;
    }
    
    .faq-question:before {
        content: "Q";
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        background-color: #5b7cef;
        color: white;
        border-radius: 50%;
        margin-right: 8px;
        font-size: 14px;
        font-weight: bold;
    }
    
    .faq-answer {
        color: #555;
        font-size: 15px;
        line-height: 1.5;
        border-left: 3px solid #5b7cef;
        padding-left: 15px;
        margin-left: 12px;
        margin-top: 10px;
        position: relative;
    }
    
    .faq-answer:before {
        content: "A";
        position: absolute;
        left: -15px;
        top: -1px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 24px;
        height: 24px;
        background-color: #ff6b6b;
        color: white;
        border-radius: 50%;
        font-size: 14px;
        font-weight: bold;
    }
    
    .guide-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 20px;
        padding-bottom: 20px;
        border-bottom: 1px solid #f0f0f0;
    }
    
    .guide-item:last-child {
        border-bottom: none;
    }
    
    .guide-icon {
        width: 40px;
        height: 40px;
        background-color: #f0f4ff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        flex-shrink: 0;
    }
    
    .guide-text {
        flex-grow: 1;
    }
    
    .guide-title {
        font-weight: 600;
        font-size: 16px;
        color: #333;
        margin-bottom: 5px;
    }
    
    .guide-desc {
        color: #555;
        font-size: 14px;
        line-height: 1.5;
    }
    
    .nav-tabs {
        display: flex;
        border-bottom: 1px solid #dee2e6;
        list-style: none;
        padding: 0;
        margin: 0 0 20px 0;
    }
    
    .nav-item {
        margin-bottom: -1px;
    }
    
    .nav-link {
        display: block;
        padding: 10px 15px;
        text-decoration: none;
        color: #495057;
        border: 1px solid transparent;
        border-top-left-radius: 6px;
        border-top-right-radius: 6px;
        font-weight: 500;
    }
    
    .nav-link.active {
        color: #5b7cef;
        border-color: #dee2e6 #dee2e6 #fff;
        font-weight: 600;
    }
    
    .nav-link:hover {
        color: #5b7cef;
    }
    
    .tab-content {
        padding-top: 10px;
    }
    
    .tab-pane {
        display: none;
    }
    
    .tab-pane.active {
        display: block;
    }
</style>
@endsection

@section('content')
<div class="page-header">
    <a href="{{ route('my') }}" class="back-button">
        <div class="back-icon"></div>
    </a>
    <h1>帮助中心</h1>
    <div style="width: 30px;"></div>
</div>

<div class="container">
    <div class="help-card">
        <ul class="nav-tabs" id="helpTabs">
            <li class="nav-item">
                <a class="nav-link active" id="faq-tab" data-toggle="tab" href="#faq">常见问题</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" id="guide-tab" data-toggle="tab" href="#guide">使用指南</a>
            </li>
            <li class="nav-item">
                <a class="nav-link" id="contact-tab" data-toggle="tab" href="#contact">联系我们</a>
            </li>
        </ul>
        
        <div class="tab-content">
            <div class="tab-pane active" id="faq">
                <h2 class="section-title">常见问题解答</h2>
                
                <div class="faq-item">
                    <div class="faq-question">如何修改个人头像？</div>
                    <div class="faq-answer">
                        在"我的"页面点击头像，或进入"个人资料"页面后点击头像即可上传新的头像图片。支持jpg、png、gif等常见图片格式，大小不超过1MB。
                    </div>
                </div>
                
                <div class="faq-item">
                    <div class="faq-question">忘记密码怎么办？</div>
                    <div class="faq-answer">
                        在登录页面点击"忘记密码"，输入您的注册手机号，通过短信验证码验证身份后，即可重新设置密码。
                    </div>
                </div>
                
                <div class="faq-item">
                    <div class="faq-question">如何预约心理咨询？</div>
                    <div class="faq-answer">
                        在"咨询"页面，浏览并选择您心仪的咨询师，点击进入咨询师详情页，选择合适的咨询时间段，填写咨询信息后提交即可完成预约。
                    </div>
                </div>
                
                <div class="faq-item">
                    <div class="faq-question">课程学习进度丢失怎么办？</div>
                    <div class="faq-answer">
                        课程学习进度会自动保存在云端。如遇进度异常，您可以尝试刷新页面，或点击课程详情页中的"重新学习"按钮重置进度。如问题仍未解决，请联系客服。
                    </div>
                </div>
                
                <div class="faq-item">
                    <div class="faq-question">如何与咨询师沟通？</div>
                    <div class="faq-answer">
                        成功预约咨询后，您可以在"我的咨询"中找到对应的咨询记录，点击进入聊天界面与咨询师进行文字交流。系统会在咨询开始前发送提醒通知。
                    </div>
                </div>
                
                <div class="faq-item">
                    <div class="faq-question">为什么我无法上传头像？</div>
                    <div class="faq-answer">
                        头像上传失败可能是因为：1. 图片格式不支持，请使用jpg、png等常见格式；2. 图片尺寸过大，请确保小于1MB；3. 网络连接不稳定，请切换网络环境后重试；4. 存储权限问题，请确保APP有访问相册的权限。
                    </div>
                </div>
                
                <div class="faq-item">
                    <div class="faq-question">如何保障我的隐私安全？</div>
                    <div class="faq-answer">
                        系统采用强加密算法保护用户数据；咨询内容严格保密，未经您的同意不会向第三方透露；您还可以在设置中开启"隐私模式"，增强个人信息保护。如有隐私方面的疑虑，请随时联系我们的客服团队。
                    </div>
                </div>
            </div>
            
            <div class="tab-pane" id="guide">
                <h2 class="section-title">平台使用指南</h2>
                
                <div class="guide-item">
                    <div class="guide-icon">
                        <span class="material-icons" style="color: #5b7cef;">person</span>
                    </div>
                    <div class="guide-text">
                        <div class="guide-title">个人中心</div>
                        <div class="guide-desc">
                            在"我的"页面，您可以查看个人信息，管理个人资料、账号安全和隐私设置。点击头像可上传或更换个人头像，点击菜单项可进入对应的功能页面。
                        </div>
                    </div>
                </div>
                
                <div class="guide-item">
                    <div class="guide-icon">
                        <span class="material-icons" style="color: #5b7cef;">psychology</span>
                    </div>
                    <div class="guide-text">
                        <div class="guide-title">心理咨询</div>
                        <div class="guide-desc">
                            在"咨询"页面，您可以浏览咨询师信息，根据专长和评价选择合适的咨询师。预约成功后，系统会在咨询开始前发送提醒。咨询过程中，您可以通过文字与咨询师交流，咨询结束后可以对服务进行评价。
                        </div>
                    </div>
                </div>
                
                <div class="guide-item">
                    <div class="guide-icon">
                        <span class="material-icons" style="color: #5b7cef;">school</span>
                    </div>
                    <div class="guide-text">
                        <div class="guide-title">心理课堂</div>
                        <div class="guide-desc">
                            在"课程"页面，您可以浏览各类心理学课程，包括图文和视频课程。点击课程卡片进入详情页，可以查看课程内容和进行学习。系统会自动记录学习进度，您可以随时暂停和继续学习。
                        </div>
                    </div>
                </div>
                
                <div class="guide-item">
                    <div class="guide-icon">
                        <span class="material-icons" style="color: #5b7cef;">article</span>
                    </div>
                    <div class="guide-text">
                        <div class="guide-title">心理科普</div>
                        <div class="guide-desc">
                            在"首页"的心理科普区域，您可以浏览最新的心理学知识文章和视频。点击感兴趣的内容可查看详情，还可以通过搜索功能查找特定主题的内容。
                        </div>
                    </div>
                </div>
                
                <div class="guide-item">
                    <div class="guide-icon">
                        <span class="material-icons" style="color: #5b7cef;">chat</span>
                    </div>
                    <div class="guide-text">
                        <div class="guide-title">AI助手</div>
                        <div class="guide-desc">
                            在"AI聊天"功能中，您可以与智能心理助手进行24小时对话，获取基础的心理支持和建议。请注意，AI助手仅提供一般性的心理知识，不能替代专业心理咨询师的诊断和治疗。
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="tab-pane" id="contact">
                <h2 class="section-title">联系我们</h2>
                
                <div class="faq-item">
                    <div class="faq-question">客服热线</div>
                    <div class="faq-answer">
                        400-123-4567（工作时间：周一至周五 9:00-18:00）
                    </div>
                </div>
                
                <div class="faq-item">
                    <div class="faq-question">客服邮箱</div>
                    <div class="faq-answer">
                        <EMAIL>
                    </div>
                </div>
                
                <div class="faq-item">
                    <div class="faq-question">官方微信</div>
                    <div class="faq-answer">
                        心理平台（PsychPlatform）
                    </div>
                </div>
                
                <div class="faq-item">
                    <div class="faq-question">总部地址</div>
                    <div class="faq-answer">
                        北京市海淀区中关村科技园区8号楼2层
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    // 选项卡切换功能
    const tabLinks = document.querySelectorAll('.nav-link');
    const tabPanes = document.querySelectorAll('.tab-pane');
    
    tabLinks.forEach(link => {
        link.addEventListener('click', (e) => {
            e.preventDefault();
            
            // 移除所有激活状态
            tabLinks.forEach(item => item.classList.remove('active'));
            tabPanes.forEach(pane => pane.classList.remove('active'));
            
            // 设置当前激活状态
            e.target.classList.add('active');
            const target = e.target.getAttribute('href').substring(1);
            document.getElementById(target).classList.add('active');
        });
    });
    
    // FAQ问题点击展开/收起效果
    const faqQuestions = document.querySelectorAll('.faq-question');
    
    faqQuestions.forEach(question => {
        question.addEventListener('click', () => {
            const answer = question.nextElementSibling;
            if (answer.style.display === 'none') {
                answer.style.display = 'block';
            } else {
                answer.style.display = 'none';
            }
        });
    });
</script>
@endsection
