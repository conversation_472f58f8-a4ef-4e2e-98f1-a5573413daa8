<?php

namespace App\Http\Controllers;

use App\Models\LiveShow;
use Illuminate\Http\Request;

class LiveShowController extends Controller
{
    /**
     * 直播列表页
     */
    public function index(Request $request)
    {
        // 1. 取 status，默认 live，并校验
        $validStatuses = ['live', 'upcoming', 'ended'];
        $status = $request->query('status', 'live');
        if (! in_array($status, $validStatuses)) {
            $status = 'live';
        }
    
        // 2. 一次查询得到所有状态的数量
        $rawCounts = LiveShow::active()
            ->selectRaw('status, COUNT(*) as cnt')
            ->groupBy('status')
            ->pluck('cnt', 'status')
            ->toArray();
        // 确保三个 key 都存在
        $statusCounts = array_merge(
            array_fill_keys($validStatuses, 0),
            $rawCounts
        );
    
        // 3. 构造查询，根据当前 $status 追加 where 和排序
        $query = LiveShow::active()
            ->with('counselor')
            ->where('status', $status);
    
        switch ($status) {
            case 'live':
                $query->orderByDesc('sort_order')
                      ->orderBy('scheduled_at', 'asc');
                break;
    
            case 'upcoming':
                $query->orderBy('scheduled_at', 'asc')
                      ->orderByDesc('sort_order');
                break;
    
            case 'ended':
                $query->orderByDesc('scheduled_at')
                      ->orderByDesc('sort_order');
                break;
        }
    
        // 4. 分页并保留所有 query string（包括 status）
        $liveShows = $query->paginate(12)
                           ->withQueryString();
    
        // 5. 传给视图
        return view('pages.live.index', compact('liveShows', 'status', 'statusCounts'));
    }

    
    /**
     * 直播详情页（显示二维码、介绍等）
     */
    public function show($id)
    {
        $liveShow = LiveShow::with(['counselor'])
            ->active()
            ->findOrFail($id);
            
        return view('pages.live.show', compact('liveShow'));
    }
    
    /**
     * 直接跳转到微信直播
     */
    public function redirect($id)
    {
        $liveShow = LiveShow::active()->findOrFail($id);
        
        if (!$liveShow->live_url) {
            if (request()->expectsJson()) {
                return response()->json(['error' => '直播链接未设置'], 400);
            }
            return redirect()->back()->with('error', '直播链接未设置');
        }
        
        // 记录点击统计（可选）
        // 这里可以增加一个点击次数字段来统计
        
        if (request()->expectsJson()) {
            return response()->json([
                'url' => $liveShow->live_url,
                'title' => $liveShow->title
            ]);
        }
        
        // 直接跳转
        return redirect($liveShow->live_url);
    }
    
    /**
     * 获取首页直播数据（供首页控制器调用）
     */
    public static function getHomeLiveShows($limit = 4)
    {
        return LiveShow::with(['counselor'])
            ->active()
            ->featured()
            ->whereIn('status', ['upcoming', 'live'])
            ->orderByRaw("CASE WHEN status = 'live' THEN 1 ELSE 2 END")
            ->orderBy('sort_order', 'desc')
            ->orderBy('scheduled_at', 'asc')
            ->take($limit)
            ->get();
    }
} 