<?php

namespace App\Services\Sms;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class JinanSmsService implements SmsServiceInterface
{
    protected $config;

    public function __construct()
    {
        $this->config = config('sms.jinan');
    }

    /**
     * 发送验证码
     *
     * @param string $mobile 手机号
     * @param string $code 验证码
     * @param int $type 类型：1注册，2登录，3找回密码，4绑定手机
     * @return array
     */
    public function sendVerificationCode(string $mobile, string $code, int $type = 1): array
    {
        try {
            // 根据类型生成短信内容
            $content = $this->getVerificationContent($code, $type);
            
            // 根据类型获取模块名称
            $module = $this->getModuleByType($type);
            
            // 发送短信
            $response = $this->sendSms($mobile, $content, $module);

            if ($response['success']) {
                Log::info('济南数字工会短信验证码发送成功', [
                    'mobile' => $mobile,
                    'type' => $type,
                    'module' => $module,
                    'content' => $content
                ]);

                return [
                    'success' => true,
                    'message' => '验证码发送成功'
                ];
            } else {
                Log::error('济南数字工会短信验证码发送失败', [
                    'mobile' => $mobile,
                    'type' => $type,
                    'error' => $response['message']
                ]);

                return [
                    'success' => false,
                    'message' => $response['message']
                ];
            }
        } catch (\Exception $e) {
            Log::error('济南数字工会短信验证码发送异常', [
                'mobile' => $mobile,
                'type' => $type,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => '短信发送失败，请稍后重试'
            ];
        }
    }

    /**
     * 发送通知短信
     *
     * @param string $mobile 手机号
     * @param array $params 短信参数
     * @param string $templateCode 模板代码
     * @return array
     */
    public function sendNotification(string $mobile, array $params, string $templateCode): array
    {
        try {
            // 根据模板代码生成短信内容
            $content = $this->getNotificationContent($params, $templateCode);
            
            // 发送短信
            $response = $this->sendSms($mobile, $content, $templateCode);

            if ($response['success']) {
                Log::info('济南数字工会通知短信发送成功', [
                    'mobile' => $mobile,
                    'template_code' => $templateCode,
                    'content' => $content
                ]);

                return [
                    'success' => true,
                    'message' => '通知发送成功'
                ];
            } else {
                Log::error('济南数字工会通知短信发送失败', [
                    'mobile' => $mobile,
                    'template_code' => $templateCode,
                    'error' => $response['message']
                ]);

                return [
                    'success' => false,
                    'message' => $response['message']
                ];
            }
        } catch (\Exception $e) {
            Log::error('济南数字工会通知短信发送异常', [
                'mobile' => $mobile,
                'template_code' => $templateCode,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => '短信发送失败，请稍后重试'
            ];
        }
    }

    /**
     * 获取提供商名称
     *
     * @return string
     */
    public function getProviderName(): string
    {
        return '济南数字工会';
    }

    /**
     * 发送短信的核心方法
     *
     * @param string $mobile 手机号
     * @param string $content 短信内容
     * @param string $module 调用模块
     * @return array
     */
    protected function sendSms(string $mobile, string $content, string $module): array
    {
        try {
            // 构建请求参数（根据API文档）
            $params = [
                'content' => $content,
                'mobile' => $mobile,
                'appName' => $this->config['app_name'] ?? 'mental-health',
                'appModule' => $module
            ];

            // 发送HTTP请求，添加认证头
            $response = Http::timeout(30)
                ->withHeaders([
                    'AccessKey' => $this->config['access_key'],
                    'SecretKey' => $this->config['secret_key'],
                    'Content-Type' => 'application/json'
                ])
                ->post($this->config['api_url'], $params);

            if ($response->successful()) {
                $result = $response->json();
                
                // 根据济南数字工会API的响应格式解析结果
                if (isset($result['code']) && $result['code'] == 200) {
                    return [
                        'success' => true,
                        'message' => $result['msg'] ?? '发送成功',
                        'data' => $result['data'] ?? null
                    ];
                } else {
                    return [
                        'success' => false,
                        'message' => $result['msg'] ?? '发送失败'
                    ];
                }
            } else {
                return [
                    'success' => false,
                    'message' => 'HTTP请求失败：' . $response->status()
                ];
            }
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => '请求异常：' . $e->getMessage()
            ];
        }
    }

    /**
     * 根据验证码类型获取模块名称
     *
     * @param int $type 验证码类型
     * @return string
     */
    protected function getModuleByType(int $type): string
    {
        $modules = [
            1 => 'register',
            2 => 'login',
            3 => 'password',
            4 => 'binding'
        ];
        
        return $modules[$type] ?? 'verification';
    }

    /**
     * 根据验证码类型生成短信内容
     *
     * @param string $code 验证码
     * @param int $type 验证码类型
     * @return string
     */
    protected function getVerificationContent(string $code, int $type): string
    {
        $typeTexts = [
            1 => '注册',
            2 => '登录', 
            3 => '找回密码',
            4 => '绑定手机'
        ];
        
        $typeText = $typeTexts[$type] ?? '验证';
        
        // return "【济南数字工会】您的{$typeText}验证码是：{$code}，有效期10分钟，请勿泄露给他人。";
        
        return "您的{$typeText}验证码是：{$code}，有效期10分钟，请勿泄露给他人。";
    }

    /**
     * 根据模板代码生成通知短信内容
     *
     * @param array $params 参数
     * @param string $templateCode 模板代码
     * @return string
     */
    protected function getNotificationContent(array $params, string $templateCode): string
    {
        switch ($templateCode) {
            case 'appointment':
                $name = $params['name'] ?? '用户';
                $time = $params['time'] ?? '指定时间';
                return "【济南数字工会】尊敬的{$name}，您的心理咨询预约已确认，时间：{$time}，请准时参加。";
                
            case 'remind':
                $content = $params['content'] ?? '您有一个预约即将开始';
                return "【济南数字工会】温馨提醒：{$content}，请做好准备。";
                
            case 'cancel':
                $reason = $params['reason'] ?? '系统原因';
                return "【济南数字工会】很抱歉，您的预约因{$reason}已取消，如有疑问请联系客服。";
                
            default:
                return "【济南数字工会】" . ($params['message'] ?? '您有新的消息，请及时查看。');
        }
    }
} 